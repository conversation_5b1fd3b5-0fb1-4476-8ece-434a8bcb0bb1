const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/premium-DZfa0MBj.js","assets/user-config-BaX6AisS.js","assets/premium-CMLoDjLA.css","assets/dagre-OKDRZEBW-BaeKrtYa.js","assets/graph-B0N4amsr.js","assets/reduce-DteGjNFQ.js","assets/_baseUniq-CDtg80eS.js","assets/layout-Dkg-3tcv.js","assets/map-CcMwK8nF.js","assets/min-DmxjSKgW.js","assets/_flatRest-CBW7P83M.js","assets/merge-RIL0UuGj.js","assets/clone-DVQebAoj.js","assets/c4Diagram-VJAJSXHY-BpORWx-R.js","assets/chunk-D6G4REZN-DU3WvR8M.js","assets/flowDiagram-4HSFHLVR-BDtL0AhX.js","assets/chunk-RZ5BOZE2-MW50_vUu.js","assets/channel-Cil-W15_.js","assets/erDiagram-Q7BY3M3F-BOrTxCpr.js","assets/gitGraphDiagram-7IBYFJ6S-BlG8464A.js","assets/chunk-4BMEZGHF-BTmQP7Vv.js","assets/chunk-XZIHB7SX-D8EdEkgM.js","assets/radar-MK3ICKWK-FbtA6Xak.js","assets/uniqBy-DkZXKWgj.js","assets/ganttDiagram-APWFNJXF-Byqdn0gf.js","assets/linear-0Qd9xWSP.js","assets/init-DBeb8MCR.js","assets/infoDiagram-PH2N3AL5-7ojxUhJu.js","assets/pieDiagram-IB7DONF6-BgEzCo4h.js","assets/arc-hoH25aLV.js","assets/ordinal-CDnGzUfX.js","assets/quadrantDiagram-7GDLP6J5-DgcbtikI.js","assets/xychartDiagram-VJFVF3MP-BnERav4O.js","assets/requirementDiagram-KVF5MWMF-BYmmNXP7.js","assets/sequenceDiagram-X6HHIX6F-BrsGcVKa.js","assets/classDiagram-GIVACNV2-CaJ5MAn_.js","assets/chunk-A2AXSNBT-OPlxFMkZ.js","assets/classDiagram-v2-COTLJTTW-B2L3uNbr.js","assets/stateDiagram-DGXRK772-BbGcuAkE.js","assets/chunk-AEK57VVT-B5hRp5WO.js","assets/stateDiagram-v2-YXO3MK2T-TmqRN8B9.js","assets/journeyDiagram-U35MCT3I-BFdVcXAy.js","assets/timeline-definition-BDJGKUSR-COIi1rQS.js","assets/mindmap-definition-ALO5MXBD-DDnbJ6SA.js","assets/cytoscape.esm-crFNGOXw.js","assets/kanban-definition-NDS4AKOZ-Cg5OAQPf.js","assets/sankeyDiagram-QLVOVGJD-NpPsrDzZ.js","assets/diagram-VNBRO52H-Dj2Qe8wC.js","assets/diagram-SSKATNLV-CKsRMtof.js","assets/blockDiagram-JOT3LUYC-Q10idWxD.js","assets/architectureDiagram-IEHRJDOE-DrTkLy5C.js"])))=>i.map(i=>d[i]);
var Tg=Object.defineProperty;var Lg=(e,t,r)=>t in e?Tg(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var gt=(e,t,r)=>Lg(e,typeof t!="symbol"?t+"":t,r);import{_ as pt,c as Jo,r as tl,j as Bg}from"./premium-DZfa0MBj.js";import{R as Ag,l as Mg,m as Eg,T as Ds}from"./user-config-BaX6AisS.js";import{m as Fg}from"./merge-RIL0UuGj.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},t=new e.Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="1f38681b-e0c3-49a9-9867-743e961ab884",e._sentryDebugIdIdentifier="sentry-dbid-1f38681b-e0c3-49a9-9867-743e961ab884")}catch{}})();var $g=0;function Dg(e){var t=++$g;return Ag(e)+t}var pc={exports:{}};(function(e,t){(function(r,i){e.exports=i()})(Mg,function(){var r=1e3,i=6e4,n=36e5,a="millisecond",o="second",s="minute",c="hour",l="day",h="week",u="month",f="quarter",d="year",g="date",m="Invalid Date",y=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,x=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,b={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(E){var M=["th","st","nd","rd"],L=E%100;return"["+E+(M[(L-20)%10]||M[L]||M[0])+"]"}},C=function(E,M,L){var F=String(E);return!F||F.length>=M?E:""+Array(M+1-F.length).join(L)+E},v={s:C,z:function(E){var M=-E.utcOffset(),L=Math.abs(M),F=Math.floor(L/60),B=L%60;return(M<=0?"+":"-")+C(F,2,"0")+":"+C(B,2,"0")},m:function E(M,L){if(M.date()<L.date())return-E(L,M);var F=12*(L.year()-M.year())+(L.month()-M.month()),B=M.clone().add(F,u),W=L-B<0,j=M.clone().add(F+(W?-1:1),u);return+(-(F+(L-B)/(W?B-j:j-B))||0)},a:function(E){return E<0?Math.ceil(E)||0:Math.floor(E)},p:function(E){return{M:u,y:d,w:h,d:l,D:g,h:c,m:s,s:o,ms:a,Q:f}[E]||String(E||"").toLowerCase().replace(/s$/,"")},u:function(E){return E===void 0}},S="en",_={};_[S]=b;var k="$isDayjsObject",R=function(E){return E instanceof z||!(!E||!E[k])},P=function E(M,L,F){var B;if(!M)return S;if(typeof M=="string"){var W=M.toLowerCase();_[W]&&(B=W),L&&(_[W]=L,B=W);var j=M.split("-");if(!B&&j.length>1)return E(j[0])}else{var J=M.name;_[J]=M,B=J}return!F&&B&&(S=B),B||!F&&S},$=function(E,M){if(R(E))return E.clone();var L=typeof M=="object"?M:{};return L.date=E,L.args=arguments,new z(L)},A=v;A.l=P,A.i=R,A.w=function(E,M){return $(E,{locale:M.$L,utc:M.$u,x:M.$x,$offset:M.$offset})};var z=function(){function E(L){this.$L=P(L.locale,null,!0),this.parse(L),this.$x=this.$x||L.x||{},this[k]=!0}var M=E.prototype;return M.parse=function(L){this.$d=function(F){var B=F.date,W=F.utc;if(B===null)return new Date(NaN);if(A.u(B))return new Date;if(B instanceof Date)return new Date(B);if(typeof B=="string"&&!/Z$/i.test(B)){var j=B.match(y);if(j){var J=j[2]-1||0,tt=(j[7]||"0").substring(0,3);return W?new Date(Date.UTC(j[1],J,j[3]||1,j[4]||0,j[5]||0,j[6]||0,tt)):new Date(j[1],J,j[3]||1,j[4]||0,j[5]||0,j[6]||0,tt)}}return new Date(B)}(L),this.init()},M.init=function(){var L=this.$d;this.$y=L.getFullYear(),this.$M=L.getMonth(),this.$D=L.getDate(),this.$W=L.getDay(),this.$H=L.getHours(),this.$m=L.getMinutes(),this.$s=L.getSeconds(),this.$ms=L.getMilliseconds()},M.$utils=function(){return A},M.isValid=function(){return this.$d.toString()!==m},M.isSame=function(L,F){var B=$(L);return this.startOf(F)<=B&&B<=this.endOf(F)},M.isAfter=function(L,F){return $(L)<this.startOf(F)},M.isBefore=function(L,F){return this.endOf(F)<$(L)},M.$g=function(L,F,B){return A.u(L)?this[F]:this.set(B,L)},M.unix=function(){return Math.floor(this.valueOf()/1e3)},M.valueOf=function(){return this.$d.getTime()},M.startOf=function(L,F){var B=this,W=!!A.u(F)||F,j=A.p(L),J=function(Jt,xt){var ae=A.w(B.$u?Date.UTC(B.$y,xt,Jt):new Date(B.$y,xt,Jt),B);return W?ae:ae.endOf(l)},tt=function(Jt,xt){return A.w(B.toDate()[Jt].apply(B.toDate("s"),(W?[0,0,0,0]:[23,59,59,999]).slice(xt)),B)},et=this.$W,ht=this.$M,nt=this.$D,vt="set"+(this.$u?"UTC":"");switch(j){case d:return W?J(1,0):J(31,11);case u:return W?J(1,ht):J(0,ht+1);case h:var Lt=this.$locale().weekStart||0,ne=(et<Lt?et+7:et)-Lt;return J(W?nt-ne:nt+(6-ne),ht);case l:case g:return tt(vt+"Hours",0);case c:return tt(vt+"Minutes",1);case s:return tt(vt+"Seconds",2);case o:return tt(vt+"Milliseconds",3);default:return this.clone()}},M.endOf=function(L){return this.startOf(L,!1)},M.$set=function(L,F){var B,W=A.p(L),j="set"+(this.$u?"UTC":""),J=(B={},B[l]=j+"Date",B[g]=j+"Date",B[u]=j+"Month",B[d]=j+"FullYear",B[c]=j+"Hours",B[s]=j+"Minutes",B[o]=j+"Seconds",B[a]=j+"Milliseconds",B)[W],tt=W===l?this.$D+(F-this.$W):F;if(W===u||W===d){var et=this.clone().set(g,1);et.$d[J](tt),et.init(),this.$d=et.set(g,Math.min(this.$D,et.daysInMonth())).$d}else J&&this.$d[J](tt);return this.init(),this},M.set=function(L,F){return this.clone().$set(L,F)},M.get=function(L){return this[A.p(L)]()},M.add=function(L,F){var B,W=this;L=Number(L);var j=A.p(F),J=function(ht){var nt=$(W);return A.w(nt.date(nt.date()+Math.round(ht*L)),W)};if(j===u)return this.set(u,this.$M+L);if(j===d)return this.set(d,this.$y+L);if(j===l)return J(1);if(j===h)return J(7);var tt=(B={},B[s]=i,B[c]=n,B[o]=r,B)[j]||1,et=this.$d.getTime()+L*tt;return A.w(et,this)},M.subtract=function(L,F){return this.add(-1*L,F)},M.format=function(L){var F=this,B=this.$locale();if(!this.isValid())return B.invalidDate||m;var W=L||"YYYY-MM-DDTHH:mm:ssZ",j=A.z(this),J=this.$H,tt=this.$m,et=this.$M,ht=B.weekdays,nt=B.months,vt=B.meridiem,Lt=function(xt,ae,te,_e){return xt&&(xt[ae]||xt(F,W))||te[ae].slice(0,_e)},ne=function(xt){return A.s(J%12||12,xt,"0")},Jt=vt||function(xt,ae,te){var _e=xt<12?"AM":"PM";return te?_e.toLowerCase():_e};return W.replace(x,function(xt,ae){return ae||function(te){switch(te){case"YY":return String(F.$y).slice(-2);case"YYYY":return A.s(F.$y,4,"0");case"M":return et+1;case"MM":return A.s(et+1,2,"0");case"MMM":return Lt(B.monthsShort,et,nt,3);case"MMMM":return Lt(nt,et);case"D":return F.$D;case"DD":return A.s(F.$D,2,"0");case"d":return String(F.$W);case"dd":return Lt(B.weekdaysMin,F.$W,ht,2);case"ddd":return Lt(B.weekdaysShort,F.$W,ht,3);case"dddd":return ht[F.$W];case"H":return String(J);case"HH":return A.s(J,2,"0");case"h":return ne(1);case"hh":return ne(2);case"a":return Jt(J,tt,!0);case"A":return Jt(J,tt,!1);case"m":return String(tt);case"mm":return A.s(tt,2,"0");case"s":return String(F.$s);case"ss":return A.s(F.$s,2,"0");case"SSS":return A.s(F.$ms,3,"0");case"Z":return j}return null}(xt)||j.replace(":","")})},M.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},M.diff=function(L,F,B){var W,j=this,J=A.p(F),tt=$(L),et=(tt.utcOffset()-this.utcOffset())*i,ht=this-tt,nt=function(){return A.m(j,tt)};switch(J){case d:W=nt()/12;break;case u:W=nt();break;case f:W=nt()/3;break;case h:W=(ht-et)/6048e5;break;case l:W=(ht-et)/864e5;break;case c:W=ht/n;break;case s:W=ht/i;break;case o:W=ht/r;break;default:W=ht}return B?W:A.a(W)},M.daysInMonth=function(){return this.endOf(u).$D},M.$locale=function(){return _[this.$L]},M.locale=function(L,F){if(!L)return this.$L;var B=this.clone(),W=P(L,F,!0);return W&&(B.$L=W),B},M.clone=function(){return A.w(this.$d,this)},M.toDate=function(){return new Date(this.valueOf())},M.toJSON=function(){return this.isValid()?this.toISOString():null},M.toISOString=function(){return this.$d.toISOString()},M.toString=function(){return this.$d.toUTCString()},E}(),O=z.prototype;return $.prototype=O,[["$ms",a],["$s",o],["$m",s],["$H",c],["$W",l],["$M",u],["$y",d],["$D",g]].forEach(function(E){O[E[1]]=function(M){return this.$g(M,E[0],E[1])}}),$.extend=function(E,M){return E.$i||(E(M,z,$),E.$i=!0),$},$.locale=P,$.isDayjs=R,$.unix=function(E){return $(1e3*E)},$.en=_[S],$.Ls=_,$.p={},$})})(pc);var Rg=pc.exports;const Ov=Eg(Rg),en={min:{r:0,g:0,b:0,s:0,l:0,a:0},max:{r:255,g:255,b:255,h:360,s:100,l:100,a:1},clamp:{r:e=>e>=255?255:e<0?0:e,g:e=>e>=255?255:e<0?0:e,b:e=>e>=255?255:e<0?0:e,h:e=>e%360,s:e=>e>=100?100:e<0?0:e,l:e=>e>=100?100:e<0?0:e,a:e=>e>=1?1:e<0?0:e},toLinear:e=>{const t=e/255;return e>.03928?Math.pow((t+.055)/1.055,2.4):t/12.92},hue2rgb:(e,t,r)=>(r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e),hsl2rgb:({h:e,s:t,l:r},i)=>{if(!t)return r*2.55;e/=360,t/=100,r/=100;const n=r<.5?r*(1+t):r+t-r*t,a=2*r-n;switch(i){case"r":return en.hue2rgb(a,n,e+1/3)*255;case"g":return en.hue2rgb(a,n,e)*255;case"b":return en.hue2rgb(a,n,e-1/3)*255}},rgb2hsl:({r:e,g:t,b:r},i)=>{e/=255,t/=255,r/=255;const n=Math.max(e,t,r),a=Math.min(e,t,r),o=(n+a)/2;if(i==="l")return o*100;if(n===a)return 0;const s=n-a,c=o>.5?s/(2-n-a):s/(n+a);if(i==="s")return c*100;switch(n){case e:return((t-r)/s+(t<r?6:0))*60;case t:return((r-e)/s+2)*60;case r:return((e-t)/s+4)*60;default:return-1}}},Og={clamp:(e,t,r)=>t>r?Math.min(t,Math.max(r,e)):Math.min(r,Math.max(t,e)),round:e=>Math.round(e*1e10)/1e10},Ig={dec2hex:e=>{const t=Math.round(e).toString(16);return t.length>1?t:`0${t}`}},at={channel:en,lang:Og,unit:Ig},Pe={};for(let e=0;e<=255;e++)Pe[e]=at.unit.dec2hex(e);const $t={ALL:0,RGB:1,HSL:2};class Pg{constructor(){this.type=$t.ALL}get(){return this.type}set(t){if(this.type&&this.type!==t)throw new Error("Cannot change both RGB and HSL channels at the same time");this.type=t}reset(){this.type=$t.ALL}is(t){return this.type===t}}class Ng{constructor(t,r){this.color=r,this.changed=!1,this.data=t,this.type=new Pg}set(t,r){return this.color=r,this.changed=!1,this.data=t,this.type.type=$t.ALL,this}_ensureHSL(){const t=this.data,{h:r,s:i,l:n}=t;r===void 0&&(t.h=at.channel.rgb2hsl(t,"h")),i===void 0&&(t.s=at.channel.rgb2hsl(t,"s")),n===void 0&&(t.l=at.channel.rgb2hsl(t,"l"))}_ensureRGB(){const t=this.data,{r,g:i,b:n}=t;r===void 0&&(t.r=at.channel.hsl2rgb(t,"r")),i===void 0&&(t.g=at.channel.hsl2rgb(t,"g")),n===void 0&&(t.b=at.channel.hsl2rgb(t,"b"))}get r(){const t=this.data,r=t.r;return!this.type.is($t.HSL)&&r!==void 0?r:(this._ensureHSL(),at.channel.hsl2rgb(t,"r"))}get g(){const t=this.data,r=t.g;return!this.type.is($t.HSL)&&r!==void 0?r:(this._ensureHSL(),at.channel.hsl2rgb(t,"g"))}get b(){const t=this.data,r=t.b;return!this.type.is($t.HSL)&&r!==void 0?r:(this._ensureHSL(),at.channel.hsl2rgb(t,"b"))}get h(){const t=this.data,r=t.h;return!this.type.is($t.RGB)&&r!==void 0?r:(this._ensureRGB(),at.channel.rgb2hsl(t,"h"))}get s(){const t=this.data,r=t.s;return!this.type.is($t.RGB)&&r!==void 0?r:(this._ensureRGB(),at.channel.rgb2hsl(t,"s"))}get l(){const t=this.data,r=t.l;return!this.type.is($t.RGB)&&r!==void 0?r:(this._ensureRGB(),at.channel.rgb2hsl(t,"l"))}get a(){return this.data.a}set r(t){this.type.set($t.RGB),this.changed=!0,this.data.r=t}set g(t){this.type.set($t.RGB),this.changed=!0,this.data.g=t}set b(t){this.type.set($t.RGB),this.changed=!0,this.data.b=t}set h(t){this.type.set($t.HSL),this.changed=!0,this.data.h=t}set s(t){this.type.set($t.HSL),this.changed=!0,this.data.s=t}set l(t){this.type.set($t.HSL),this.changed=!0,this.data.l=t}set a(t){this.changed=!0,this.data.a=t}}const Vn=new Ng({r:0,g:0,b:0,a:0},"transparent"),wr={re:/^#((?:[a-f0-9]{2}){2,4}|[a-f0-9]{3})$/i,parse:e=>{if(e.charCodeAt(0)!==35)return;const t=e.match(wr.re);if(!t)return;const r=t[1],i=parseInt(r,16),n=r.length,a=n%4===0,o=n>4,s=o?1:17,c=o?8:4,l=a?0:-1,h=o?255:15;return Vn.set({r:(i>>c*(l+3)&h)*s,g:(i>>c*(l+2)&h)*s,b:(i>>c*(l+1)&h)*s,a:a?(i&h)*s/255:1},e)},stringify:e=>{const{r:t,g:r,b:i,a:n}=e;return n<1?`#${Pe[Math.round(t)]}${Pe[Math.round(r)]}${Pe[Math.round(i)]}${Pe[Math.round(n*255)]}`:`#${Pe[Math.round(t)]}${Pe[Math.round(r)]}${Pe[Math.round(i)]}`}},Ke={re:/^hsla?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(?:deg|grad|rad|turn)?)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(%)?))?\s*?\)$/i,hueRe:/^(.+?)(deg|grad|rad|turn)$/i,_hue2deg:e=>{const t=e.match(Ke.hueRe);if(t){const[,r,i]=t;switch(i){case"grad":return at.channel.clamp.h(parseFloat(r)*.9);case"rad":return at.channel.clamp.h(parseFloat(r)*180/Math.PI);case"turn":return at.channel.clamp.h(parseFloat(r)*360)}}return at.channel.clamp.h(parseFloat(e))},parse:e=>{const t=e.charCodeAt(0);if(t!==104&&t!==72)return;const r=e.match(Ke.re);if(!r)return;const[,i,n,a,o,s]=r;return Vn.set({h:Ke._hue2deg(i),s:at.channel.clamp.s(parseFloat(n)),l:at.channel.clamp.l(parseFloat(a)),a:o?at.channel.clamp.a(s?parseFloat(o)/100:parseFloat(o)):1},e)},stringify:e=>{const{h:t,s:r,l:i,a:n}=e;return n<1?`hsla(${at.lang.round(t)}, ${at.lang.round(r)}%, ${at.lang.round(i)}%, ${n})`:`hsl(${at.lang.round(t)}, ${at.lang.round(r)}%, ${at.lang.round(i)}%)`}},gi={colors:{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyanaqua:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",transparent:"#00000000",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},parse:e=>{e=e.toLowerCase();const t=gi.colors[e];if(t)return wr.parse(t)},stringify:e=>{const t=wr.stringify(e);for(const r in gi.colors)if(gi.colors[r]===t)return r}},li={re:/^rgba?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?)))?\s*?\)$/i,parse:e=>{const t=e.charCodeAt(0);if(t!==114&&t!==82)return;const r=e.match(li.re);if(!r)return;const[,i,n,a,o,s,c,l,h]=r;return Vn.set({r:at.channel.clamp.r(n?parseFloat(i)*2.55:parseFloat(i)),g:at.channel.clamp.g(o?parseFloat(a)*2.55:parseFloat(a)),b:at.channel.clamp.b(c?parseFloat(s)*2.55:parseFloat(s)),a:l?at.channel.clamp.a(h?parseFloat(l)/100:parseFloat(l)):1},e)},stringify:e=>{const{r:t,g:r,b:i,a:n}=e;return n<1?`rgba(${at.lang.round(t)}, ${at.lang.round(r)}, ${at.lang.round(i)}, ${at.lang.round(n)})`:`rgb(${at.lang.round(t)}, ${at.lang.round(r)}, ${at.lang.round(i)})`}},ye={format:{keyword:gi,hex:wr,rgb:li,rgba:li,hsl:Ke,hsla:Ke},parse:e=>{if(typeof e!="string")return e;const t=wr.parse(e)||li.parse(e)||Ke.parse(e)||gi.parse(e);if(t)return t;throw new Error(`Unsupported color format: "${e}"`)},stringify:e=>!e.changed&&e.color?e.color:e.type.is($t.HSL)||e.data.r===void 0?Ke.stringify(e):e.a<1||!Number.isInteger(e.r)||!Number.isInteger(e.g)||!Number.isInteger(e.b)?li.stringify(e):wr.stringify(e)},gc=(e,t)=>{const r=ye.parse(e);for(const i in t)r[i]=at.channel.clamp[i](t[i]);return ye.stringify(r)},mi=(e,t,r=0,i=1)=>{if(typeof e!="number")return gc(e,{a:t});const n=Vn.set({r:at.channel.clamp.r(e),g:at.channel.clamp.g(t),b:at.channel.clamp.b(r),a:at.channel.clamp.a(i)});return ye.stringify(n)},zg=e=>{const{r:t,g:r,b:i}=ye.parse(e),n=.2126*at.channel.toLinear(t)+.7152*at.channel.toLinear(r)+.0722*at.channel.toLinear(i);return at.lang.round(n)},Wg=e=>zg(e)>=.5,Mi=e=>!Wg(e),mc=(e,t,r)=>{const i=ye.parse(e),n=i[t],a=at.channel.clamp[t](n+r);return n!==a&&(i[t]=a),ye.stringify(i)},q=(e,t)=>mc(e,"l",t),Q=(e,t)=>mc(e,"l",-t),T=(e,t)=>{const r=ye.parse(e),i={};for(const n in t)t[n]&&(i[n]=r[n]+t[n]);return gc(e,i)},qg=(e,t,r=50)=>{const{r:i,g:n,b:a,a:o}=ye.parse(e),{r:s,g:c,b:l,a:h}=ye.parse(t),u=r/100,f=u*2-1,d=o-h,m=((f*d===-1?f:(f+d)/(1+f*d))+1)/2,y=1-m,x=i*m+s*y,b=n*m+c*y,C=a*m+l*y,v=o*u+h*(1-u);return mi(x,b,C,v)},N=(e,t=100)=>{const r=ye.parse(e);return r.r=255-r.r,r.g=255-r.g,r.b=255-r.b,qg(r,e,t)};/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:yc,setPrototypeOf:el,isFrozen:Hg,getPrototypeOf:Yg,getOwnPropertyDescriptor:jg}=Object;let{freeze:qt,seal:re,create:xc}=Object,{apply:Ya,construct:ja}=typeof Reflect<"u"&&Reflect;qt||(qt=function(t){return t});re||(re=function(t){return t});Ya||(Ya=function(t,r,i){return t.apply(r,i)});ja||(ja=function(t,r){return new t(...r)});const Yi=Ht(Array.prototype.forEach),Ug=Ht(Array.prototype.lastIndexOf),rl=Ht(Array.prototype.pop),Kr=Ht(Array.prototype.push),Gg=Ht(Array.prototype.splice),rn=Ht(String.prototype.toLowerCase),Sa=Ht(String.prototype.toString),il=Ht(String.prototype.match),Qr=Ht(String.prototype.replace),Vg=Ht(String.prototype.indexOf),Xg=Ht(String.prototype.trim),se=Ht(Object.prototype.hasOwnProperty),It=Ht(RegExp.prototype.test),Jr=Zg(TypeError);function Ht(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];return Ya(e,t,i)}}function Zg(e){return function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];return ja(e,r)}}function ot(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:rn;el&&el(e,null);let i=t.length;for(;i--;){let n=t[i];if(typeof n=="string"){const a=r(n);a!==n&&(Hg(t)||(t[i]=a),n=a)}e[n]=!0}return e}function Kg(e){for(let t=0;t<e.length;t++)se(e,t)||(e[t]=null);return e}function Le(e){const t=xc(null);for(const[r,i]of yc(e))se(e,r)&&(Array.isArray(i)?t[r]=Kg(i):i&&typeof i=="object"&&i.constructor===Object?t[r]=Le(i):t[r]=i);return t}function ti(e,t){for(;e!==null;){const i=jg(e,t);if(i){if(i.get)return Ht(i.get);if(typeof i.value=="function")return Ht(i.value)}e=Yg(e)}function r(){return null}return r}const nl=qt(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ta=qt(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),La=qt(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Qg=qt(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ba=qt(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Jg=qt(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),al=qt(["#text"]),sl=qt(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Aa=qt(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),ol=qt(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),ji=qt(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),tm=re(/\{\{[\w\W]*|[\w\W]*\}\}/gm),em=re(/<%[\w\W]*|[\w\W]*%>/gm),rm=re(/\$\{[\w\W]*/gm),im=re(/^data-[\-\w.\u00B7-\uFFFF]+$/),nm=re(/^aria-[\-\w]+$/),bc=re(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),am=re(/^(?:\w+script|data):/i),sm=re(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),_c=re(/^html$/i),om=re(/^[a-z][.\w]*(-[.\w]+)+$/i);var ll=Object.freeze({__proto__:null,ARIA_ATTR:nm,ATTR_WHITESPACE:sm,CUSTOM_ELEMENT:om,DATA_ATTR:im,DOCTYPE_NAME:_c,ERB_EXPR:em,IS_ALLOWED_URI:bc,IS_SCRIPT_OR_DATA:am,MUSTACHE_EXPR:tm,TMPLIT_EXPR:rm});const ei={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},lm=function(){return typeof window>"u"?null:window},cm=function(t,r){if(typeof t!="object"||typeof t.createPolicy!="function")return null;let i=null;const n="data-tt-policy-suffix";r&&r.hasAttribute(n)&&(i=r.getAttribute(n));const a="dompurify"+(i?"#"+i:"");try{return t.createPolicy(a,{createHTML(o){return o},createScriptURL(o){return o}})}catch{return null}},cl=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function Cc(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:lm();const t=K=>Cc(K);if(t.version="3.2.6",t.removed=[],!e||!e.document||e.document.nodeType!==ei.document||!e.Element)return t.isSupported=!1,t;let{document:r}=e;const i=r,n=i.currentScript,{DocumentFragment:a,HTMLTemplateElement:o,Node:s,Element:c,NodeFilter:l,NamedNodeMap:h=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:u,DOMParser:f,trustedTypes:d}=e,g=c.prototype,m=ti(g,"cloneNode"),y=ti(g,"remove"),x=ti(g,"nextSibling"),b=ti(g,"childNodes"),C=ti(g,"parentNode");if(typeof o=="function"){const K=r.createElement("template");K.content&&K.content.ownerDocument&&(r=K.content.ownerDocument)}let v,S="";const{implementation:_,createNodeIterator:k,createDocumentFragment:R,getElementsByTagName:P}=r,{importNode:$}=i;let A=cl();t.isSupported=typeof yc=="function"&&typeof C=="function"&&_&&_.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:z,ERB_EXPR:O,TMPLIT_EXPR:E,DATA_ATTR:M,ARIA_ATTR:L,IS_SCRIPT_OR_DATA:F,ATTR_WHITESPACE:B,CUSTOM_ELEMENT:W}=ll;let{IS_ALLOWED_URI:j}=ll,J=null;const tt=ot({},[...nl,...Ta,...La,...Ba,...al]);let et=null;const ht=ot({},[...sl,...Aa,...ol,...ji]);let nt=Object.seal(xc(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),vt=null,Lt=null,ne=!0,Jt=!0,xt=!1,ae=!0,te=!1,_e=!0,Ge=!1,ma=!1,ya=!1,ur=!1,Pi=!1,Ni=!1,Ro=!0,Oo=!1;const xg="user-content-";let xa=!0,Vr=!1,fr={},dr=null;const Io=ot({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Po=null;const No=ot({},["audio","video","img","source","image","track"]);let ba=null;const zo=ot({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),zi="http://www.w3.org/1998/Math/MathML",Wi="http://www.w3.org/2000/svg",Ce="http://www.w3.org/1999/xhtml";let pr=Ce,_a=!1,Ca=null;const bg=ot({},[zi,Wi,Ce],Sa);let qi=ot({},["mi","mo","mn","ms","mtext"]),Hi=ot({},["annotation-xml"]);const _g=ot({},["title","style","font","a","script"]);let Xr=null;const Cg=["application/xhtml+xml","text/html"],wg="text/html";let St=null,gr=null;const kg=r.createElement("form"),Wo=function(w){return w instanceof RegExp||w instanceof Function},wa=function(){let w=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(gr&&gr===w)){if((!w||typeof w!="object")&&(w={}),w=Le(w),Xr=Cg.indexOf(w.PARSER_MEDIA_TYPE)===-1?wg:w.PARSER_MEDIA_TYPE,St=Xr==="application/xhtml+xml"?Sa:rn,J=se(w,"ALLOWED_TAGS")?ot({},w.ALLOWED_TAGS,St):tt,et=se(w,"ALLOWED_ATTR")?ot({},w.ALLOWED_ATTR,St):ht,Ca=se(w,"ALLOWED_NAMESPACES")?ot({},w.ALLOWED_NAMESPACES,Sa):bg,ba=se(w,"ADD_URI_SAFE_ATTR")?ot(Le(zo),w.ADD_URI_SAFE_ATTR,St):zo,Po=se(w,"ADD_DATA_URI_TAGS")?ot(Le(No),w.ADD_DATA_URI_TAGS,St):No,dr=se(w,"FORBID_CONTENTS")?ot({},w.FORBID_CONTENTS,St):Io,vt=se(w,"FORBID_TAGS")?ot({},w.FORBID_TAGS,St):Le({}),Lt=se(w,"FORBID_ATTR")?ot({},w.FORBID_ATTR,St):Le({}),fr=se(w,"USE_PROFILES")?w.USE_PROFILES:!1,ne=w.ALLOW_ARIA_ATTR!==!1,Jt=w.ALLOW_DATA_ATTR!==!1,xt=w.ALLOW_UNKNOWN_PROTOCOLS||!1,ae=w.ALLOW_SELF_CLOSE_IN_ATTR!==!1,te=w.SAFE_FOR_TEMPLATES||!1,_e=w.SAFE_FOR_XML!==!1,Ge=w.WHOLE_DOCUMENT||!1,ur=w.RETURN_DOM||!1,Pi=w.RETURN_DOM_FRAGMENT||!1,Ni=w.RETURN_TRUSTED_TYPE||!1,ya=w.FORCE_BODY||!1,Ro=w.SANITIZE_DOM!==!1,Oo=w.SANITIZE_NAMED_PROPS||!1,xa=w.KEEP_CONTENT!==!1,Vr=w.IN_PLACE||!1,j=w.ALLOWED_URI_REGEXP||bc,pr=w.NAMESPACE||Ce,qi=w.MATHML_TEXT_INTEGRATION_POINTS||qi,Hi=w.HTML_INTEGRATION_POINTS||Hi,nt=w.CUSTOM_ELEMENT_HANDLING||{},w.CUSTOM_ELEMENT_HANDLING&&Wo(w.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(nt.tagNameCheck=w.CUSTOM_ELEMENT_HANDLING.tagNameCheck),w.CUSTOM_ELEMENT_HANDLING&&Wo(w.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(nt.attributeNameCheck=w.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),w.CUSTOM_ELEMENT_HANDLING&&typeof w.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(nt.allowCustomizedBuiltInElements=w.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),te&&(Jt=!1),Pi&&(ur=!0),fr&&(J=ot({},al),et=[],fr.html===!0&&(ot(J,nl),ot(et,sl)),fr.svg===!0&&(ot(J,Ta),ot(et,Aa),ot(et,ji)),fr.svgFilters===!0&&(ot(J,La),ot(et,Aa),ot(et,ji)),fr.mathMl===!0&&(ot(J,Ba),ot(et,ol),ot(et,ji))),w.ADD_TAGS&&(J===tt&&(J=Le(J)),ot(J,w.ADD_TAGS,St)),w.ADD_ATTR&&(et===ht&&(et=Le(et)),ot(et,w.ADD_ATTR,St)),w.ADD_URI_SAFE_ATTR&&ot(ba,w.ADD_URI_SAFE_ATTR,St),w.FORBID_CONTENTS&&(dr===Io&&(dr=Le(dr)),ot(dr,w.FORBID_CONTENTS,St)),xa&&(J["#text"]=!0),Ge&&ot(J,["html","head","body"]),J.table&&(ot(J,["tbody"]),delete vt.tbody),w.TRUSTED_TYPES_POLICY){if(typeof w.TRUSTED_TYPES_POLICY.createHTML!="function")throw Jr('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof w.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Jr('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');v=w.TRUSTED_TYPES_POLICY,S=v.createHTML("")}else v===void 0&&(v=cm(d,n)),v!==null&&typeof S=="string"&&(S=v.createHTML(""));qt&&qt(w),gr=w}},qo=ot({},[...Ta,...La,...Qg]),Ho=ot({},[...Ba,...Jg]),vg=function(w){let I=C(w);(!I||!I.tagName)&&(I={namespaceURI:pr,tagName:"template"});const U=rn(w.tagName),mt=rn(I.tagName);return Ca[w.namespaceURI]?w.namespaceURI===Wi?I.namespaceURI===Ce?U==="svg":I.namespaceURI===zi?U==="svg"&&(mt==="annotation-xml"||qi[mt]):!!qo[U]:w.namespaceURI===zi?I.namespaceURI===Ce?U==="math":I.namespaceURI===Wi?U==="math"&&Hi[mt]:!!Ho[U]:w.namespaceURI===Ce?I.namespaceURI===Wi&&!Hi[mt]||I.namespaceURI===zi&&!qi[mt]?!1:!Ho[U]&&(_g[U]||!qo[U]):!!(Xr==="application/xhtml+xml"&&Ca[w.namespaceURI]):!1},ue=function(w){Kr(t.removed,{element:w});try{C(w).removeChild(w)}catch{y(w)}},mr=function(w,I){try{Kr(t.removed,{attribute:I.getAttributeNode(w),from:I})}catch{Kr(t.removed,{attribute:null,from:I})}if(I.removeAttribute(w),w==="is")if(ur||Pi)try{ue(I)}catch{}else try{I.setAttribute(w,"")}catch{}},Yo=function(w){let I=null,U=null;if(ya)w="<remove></remove>"+w;else{const Ct=il(w,/^[\r\n\t ]+/);U=Ct&&Ct[0]}Xr==="application/xhtml+xml"&&pr===Ce&&(w='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+w+"</body></html>");const mt=v?v.createHTML(w):w;if(pr===Ce)try{I=new f().parseFromString(mt,Xr)}catch{}if(!I||!I.documentElement){I=_.createDocument(pr,"template",null);try{I.documentElement.innerHTML=_a?S:mt}catch{}}const Et=I.body||I.documentElement;return w&&U&&Et.insertBefore(r.createTextNode(U),Et.childNodes[0]||null),pr===Ce?P.call(I,Ge?"html":"body")[0]:Ge?I.documentElement:Et},jo=function(w){return k.call(w.ownerDocument||w,w,l.SHOW_ELEMENT|l.SHOW_COMMENT|l.SHOW_TEXT|l.SHOW_PROCESSING_INSTRUCTION|l.SHOW_CDATA_SECTION,null)},ka=function(w){return w instanceof u&&(typeof w.nodeName!="string"||typeof w.textContent!="string"||typeof w.removeChild!="function"||!(w.attributes instanceof h)||typeof w.removeAttribute!="function"||typeof w.setAttribute!="function"||typeof w.namespaceURI!="string"||typeof w.insertBefore!="function"||typeof w.hasChildNodes!="function")},Uo=function(w){return typeof s=="function"&&w instanceof s};function we(K,w,I){Yi(K,U=>{U.call(t,w,I,gr)})}const Go=function(w){let I=null;if(we(A.beforeSanitizeElements,w,null),ka(w))return ue(w),!0;const U=St(w.nodeName);if(we(A.uponSanitizeElement,w,{tagName:U,allowedTags:J}),_e&&w.hasChildNodes()&&!Uo(w.firstElementChild)&&It(/<[/\w!]/g,w.innerHTML)&&It(/<[/\w!]/g,w.textContent)||w.nodeType===ei.progressingInstruction||_e&&w.nodeType===ei.comment&&It(/<[/\w]/g,w.data))return ue(w),!0;if(!J[U]||vt[U]){if(!vt[U]&&Xo(U)&&(nt.tagNameCheck instanceof RegExp&&It(nt.tagNameCheck,U)||nt.tagNameCheck instanceof Function&&nt.tagNameCheck(U)))return!1;if(xa&&!dr[U]){const mt=C(w)||w.parentNode,Et=b(w)||w.childNodes;if(Et&&mt){const Ct=Et.length;for(let Yt=Ct-1;Yt>=0;--Yt){const ke=m(Et[Yt],!0);ke.__removalCount=(w.__removalCount||0)+1,mt.insertBefore(ke,x(w))}}}return ue(w),!0}return w instanceof c&&!vg(w)||(U==="noscript"||U==="noembed"||U==="noframes")&&It(/<\/no(script|embed|frames)/i,w.innerHTML)?(ue(w),!0):(te&&w.nodeType===ei.text&&(I=w.textContent,Yi([z,O,E],mt=>{I=Qr(I,mt," ")}),w.textContent!==I&&(Kr(t.removed,{element:w.cloneNode()}),w.textContent=I)),we(A.afterSanitizeElements,w,null),!1)},Vo=function(w,I,U){if(Ro&&(I==="id"||I==="name")&&(U in r||U in kg))return!1;if(!(Jt&&!Lt[I]&&It(M,I))){if(!(ne&&It(L,I))){if(!et[I]||Lt[I]){if(!(Xo(w)&&(nt.tagNameCheck instanceof RegExp&&It(nt.tagNameCheck,w)||nt.tagNameCheck instanceof Function&&nt.tagNameCheck(w))&&(nt.attributeNameCheck instanceof RegExp&&It(nt.attributeNameCheck,I)||nt.attributeNameCheck instanceof Function&&nt.attributeNameCheck(I))||I==="is"&&nt.allowCustomizedBuiltInElements&&(nt.tagNameCheck instanceof RegExp&&It(nt.tagNameCheck,U)||nt.tagNameCheck instanceof Function&&nt.tagNameCheck(U))))return!1}else if(!ba[I]){if(!It(j,Qr(U,B,""))){if(!((I==="src"||I==="xlink:href"||I==="href")&&w!=="script"&&Vg(U,"data:")===0&&Po[w])){if(!(xt&&!It(F,Qr(U,B,"")))){if(U)return!1}}}}}}return!0},Xo=function(w){return w!=="annotation-xml"&&il(w,W)},Zo=function(w){we(A.beforeSanitizeAttributes,w,null);const{attributes:I}=w;if(!I||ka(w))return;const U={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:et,forceKeepAttr:void 0};let mt=I.length;for(;mt--;){const Et=I[mt],{name:Ct,namespaceURI:Yt,value:ke}=Et,Zr=St(Ct),va=ke;let Ft=Ct==="value"?va:Xg(va);if(U.attrName=Zr,U.attrValue=Ft,U.keepAttr=!0,U.forceKeepAttr=void 0,we(A.uponSanitizeAttribute,w,U),Ft=U.attrValue,Oo&&(Zr==="id"||Zr==="name")&&(mr(Ct,w),Ft=xg+Ft),_e&&It(/((--!?|])>)|<\/(style|title)/i,Ft)){mr(Ct,w);continue}if(U.forceKeepAttr)continue;if(!U.keepAttr){mr(Ct,w);continue}if(!ae&&It(/\/>/i,Ft)){mr(Ct,w);continue}te&&Yi([z,O,E],Qo=>{Ft=Qr(Ft,Qo," ")});const Ko=St(w.nodeName);if(!Vo(Ko,Zr,Ft)){mr(Ct,w);continue}if(v&&typeof d=="object"&&typeof d.getAttributeType=="function"&&!Yt)switch(d.getAttributeType(Ko,Zr)){case"TrustedHTML":{Ft=v.createHTML(Ft);break}case"TrustedScriptURL":{Ft=v.createScriptURL(Ft);break}}if(Ft!==va)try{Yt?w.setAttributeNS(Yt,Ct,Ft):w.setAttribute(Ct,Ft),ka(w)?ue(w):rl(t.removed)}catch{mr(Ct,w)}}we(A.afterSanitizeAttributes,w,null)},Sg=function K(w){let I=null;const U=jo(w);for(we(A.beforeSanitizeShadowDOM,w,null);I=U.nextNode();)we(A.uponSanitizeShadowNode,I,null),Go(I),Zo(I),I.content instanceof a&&K(I.content);we(A.afterSanitizeShadowDOM,w,null)};return t.sanitize=function(K){let w=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},I=null,U=null,mt=null,Et=null;if(_a=!K,_a&&(K="<!-->"),typeof K!="string"&&!Uo(K))if(typeof K.toString=="function"){if(K=K.toString(),typeof K!="string")throw Jr("dirty is not a string, aborting")}else throw Jr("toString is not a function");if(!t.isSupported)return K;if(ma||wa(w),t.removed=[],typeof K=="string"&&(Vr=!1),Vr){if(K.nodeName){const ke=St(K.nodeName);if(!J[ke]||vt[ke])throw Jr("root node is forbidden and cannot be sanitized in-place")}}else if(K instanceof s)I=Yo("<!---->"),U=I.ownerDocument.importNode(K,!0),U.nodeType===ei.element&&U.nodeName==="BODY"||U.nodeName==="HTML"?I=U:I.appendChild(U);else{if(!ur&&!te&&!Ge&&K.indexOf("<")===-1)return v&&Ni?v.createHTML(K):K;if(I=Yo(K),!I)return ur?null:Ni?S:""}I&&ya&&ue(I.firstChild);const Ct=jo(Vr?K:I);for(;mt=Ct.nextNode();)Go(mt),Zo(mt),mt.content instanceof a&&Sg(mt.content);if(Vr)return K;if(ur){if(Pi)for(Et=R.call(I.ownerDocument);I.firstChild;)Et.appendChild(I.firstChild);else Et=I;return(et.shadowroot||et.shadowrootmode)&&(Et=$.call(i,Et,!0)),Et}let Yt=Ge?I.outerHTML:I.innerHTML;return Ge&&J["!doctype"]&&I.ownerDocument&&I.ownerDocument.doctype&&I.ownerDocument.doctype.name&&It(_c,I.ownerDocument.doctype.name)&&(Yt="<!DOCTYPE "+I.ownerDocument.doctype.name+`>
`+Yt),te&&Yi([z,O,E],ke=>{Yt=Qr(Yt,ke," ")}),v&&Ni?v.createHTML(Yt):Yt},t.setConfig=function(){let K=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};wa(K),ma=!0},t.clearConfig=function(){gr=null,ma=!1},t.isValidAttribute=function(K,w,I){gr||wa({});const U=St(K),mt=St(w);return Vo(U,mt,I)},t.addHook=function(K,w){typeof w=="function"&&Kr(A[K],w)},t.removeHook=function(K,w){if(w!==void 0){const I=Ug(A[K],w);return I===-1?void 0:Gg(A[K],I,1)[0]}return rl(A[K])},t.removeHooks=function(K){A[K]=[]},t.removeAllHooks=function(){A=cl()},t}var $r=Cc(),wc=Object.defineProperty,p=(e,t)=>wc(e,"name",{value:t,configurable:!0}),hm=(e,t)=>{for(var r in t)wc(e,r,{get:t[r],enumerable:!0})},ve={trace:0,debug:1,info:2,warn:3,error:4,fatal:5},D={trace:p((...e)=>{},"trace"),debug:p((...e)=>{},"debug"),info:p((...e)=>{},"info"),warn:p((...e)=>{},"warn"),error:p((...e)=>{},"error"),fatal:p((...e)=>{},"fatal")},Rs=p(function(e="fatal"){let t=ve.fatal;typeof e=="string"?e.toLowerCase()in ve&&(t=ve[e]):typeof e=="number"&&(t=e),D.trace=()=>{},D.debug=()=>{},D.info=()=>{},D.warn=()=>{},D.error=()=>{},D.fatal=()=>{},t<=ve.fatal&&(D.fatal=void 0),t<=ve.error&&(D.error=void 0),t<=ve.warn&&(D.warn=void 0),t<=ve.info&&(D.info=void 0),t<=ve.debug&&(D.debug=void 0),t<=ve.trace&&(D.trace=void 0)},"setLogLevel");var kc=/^-{3}\s*[\n\r](.*?)[\n\r]-{3}\s*[\n\r]+/s,yi=/%{2}{\s*(?:(\w+)\s*:|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,um=/\s*%%.*\n/gm,Sr,vc=(Sr=class extends Error{constructor(t){super(t),this.name="UnknownDiagramError"}},p(Sr,"UnknownDiagramError"),Sr),Dr={},Os=p(function(e,t){e=e.replace(kc,"").replace(yi,"").replace(um,`
`);for(const[r,{detector:i}]of Object.entries(Dr))if(i(e,t))return r;throw new vc(`No diagram type detected matching given configuration for text: ${e}`)},"detectType"),Sc=p((...e)=>{for(const{id:t,detector:r,loader:i}of e)Tc(t,r,i)},"registerLazyLoadedDiagrams"),Tc=p((e,t,r)=>{Dr[e]&&D.warn(`Detector with key ${e} already exists. Overwriting.`),Dr[e]={detector:t,loader:r},D.debug(`Detector with key ${e} added${r?" with loader":""}`)},"addDetector"),fm=p(e=>Dr[e].loader,"getDiagramLoader"),Ua=p((e,t,{depth:r=2,clobber:i=!1}={})=>{const n={depth:r,clobber:i};return Array.isArray(t)&&!Array.isArray(e)?(t.forEach(a=>Ua(e,a,n)),e):Array.isArray(t)&&Array.isArray(e)?(t.forEach(a=>{e.includes(a)||e.push(a)}),e):e===void 0||r<=0?e!=null&&typeof e=="object"&&typeof t=="object"?Object.assign(e,t):t:(t!==void 0&&typeof e=="object"&&typeof t=="object"&&Object.keys(t).forEach(a=>{typeof t[a]=="object"&&(e[a]===void 0||typeof e[a]=="object")?(e[a]===void 0&&(e[a]=Array.isArray(t[a])?[]:{}),e[a]=Ua(e[a],t[a],{depth:r-1,clobber:i})):(i||typeof e[a]!="object"&&typeof t[a]!="object")&&(e[a]=t[a])}),e)},"assignWithDepth"),Mt=Ua,Xn="#ffffff",Zn="#f2f2f2",Pt=p((e,t)=>t?T(e,{s:-40,l:10}):T(e,{s:-40,l:-10}),"mkBorder"),Tr,dm=(Tr=class{constructor(){this.background="#f4f4f4",this.primaryColor="#fff4dd",this.noteBkgColor="#fff5ad",this.noteTextColor="#333",this.THEME_COLOR_LIMIT=12,this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px"}updateColors(){var r,i,n,a,o,s,c,l,h,u,f,d,g,m,y,x,b,C,v,S,_;if(this.primaryTextColor=this.primaryTextColor||(this.darkMode?"#eee":"#333"),this.secondaryColor=this.secondaryColor||T(this.primaryColor,{h:-120}),this.tertiaryColor=this.tertiaryColor||T(this.primaryColor,{h:180,l:5}),this.primaryBorderColor=this.primaryBorderColor||Pt(this.primaryColor,this.darkMode),this.secondaryBorderColor=this.secondaryBorderColor||Pt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=this.tertiaryBorderColor||Pt(this.tertiaryColor,this.darkMode),this.noteBorderColor=this.noteBorderColor||Pt(this.noteBkgColor,this.darkMode),this.noteBkgColor=this.noteBkgColor||"#fff5ad",this.noteTextColor=this.noteTextColor||"#333",this.secondaryTextColor=this.secondaryTextColor||N(this.secondaryColor),this.tertiaryTextColor=this.tertiaryTextColor||N(this.tertiaryColor),this.lineColor=this.lineColor||N(this.background),this.arrowheadColor=this.arrowheadColor||N(this.background),this.textColor=this.textColor||this.primaryTextColor,this.border2=this.border2||this.tertiaryBorderColor,this.nodeBkg=this.nodeBkg||this.primaryColor,this.mainBkg=this.mainBkg||this.primaryColor,this.nodeBorder=this.nodeBorder||this.primaryBorderColor,this.clusterBkg=this.clusterBkg||this.tertiaryColor,this.clusterBorder=this.clusterBorder||this.tertiaryBorderColor,this.defaultLinkColor=this.defaultLinkColor||this.lineColor,this.titleColor=this.titleColor||this.tertiaryTextColor,this.edgeLabelBackground=this.edgeLabelBackground||(this.darkMode?Q(this.secondaryColor,30):this.secondaryColor),this.nodeTextColor=this.nodeTextColor||this.primaryTextColor,this.actorBorder=this.actorBorder||this.primaryBorderColor,this.actorBkg=this.actorBkg||this.mainBkg,this.actorTextColor=this.actorTextColor||this.primaryTextColor,this.actorLineColor=this.actorLineColor||this.actorBorder,this.labelBoxBkgColor=this.labelBoxBkgColor||this.actorBkg,this.signalColor=this.signalColor||this.textColor,this.signalTextColor=this.signalTextColor||this.textColor,this.labelBoxBorderColor=this.labelBoxBorderColor||this.actorBorder,this.labelTextColor=this.labelTextColor||this.actorTextColor,this.loopTextColor=this.loopTextColor||this.actorTextColor,this.activationBorderColor=this.activationBorderColor||Q(this.secondaryColor,10),this.activationBkgColor=this.activationBkgColor||this.secondaryColor,this.sequenceNumberColor=this.sequenceNumberColor||N(this.lineColor),this.sectionBkgColor=this.sectionBkgColor||this.tertiaryColor,this.altSectionBkgColor=this.altSectionBkgColor||"white",this.sectionBkgColor=this.sectionBkgColor||this.secondaryColor,this.sectionBkgColor2=this.sectionBkgColor2||this.primaryColor,this.excludeBkgColor=this.excludeBkgColor||"#eeeeee",this.taskBorderColor=this.taskBorderColor||this.primaryBorderColor,this.taskBkgColor=this.taskBkgColor||this.primaryColor,this.activeTaskBorderColor=this.activeTaskBorderColor||this.primaryColor,this.activeTaskBkgColor=this.activeTaskBkgColor||q(this.primaryColor,23),this.gridColor=this.gridColor||"lightgrey",this.doneTaskBkgColor=this.doneTaskBkgColor||"lightgrey",this.doneTaskBorderColor=this.doneTaskBorderColor||"grey",this.critBorderColor=this.critBorderColor||"#ff8888",this.critBkgColor=this.critBkgColor||"red",this.todayLineColor=this.todayLineColor||"red",this.taskTextColor=this.taskTextColor||this.textColor,this.taskTextOutsideColor=this.taskTextOutsideColor||this.textColor,this.taskTextLightColor=this.taskTextLightColor||this.textColor,this.taskTextColor=this.taskTextColor||this.primaryTextColor,this.taskTextDarkColor=this.taskTextDarkColor||this.textColor,this.taskTextClickableColor=this.taskTextClickableColor||"#003163",this.personBorder=this.personBorder||this.primaryBorderColor,this.personBkg=this.personBkg||this.mainBkg,this.darkMode?(this.rowOdd=this.rowOdd||Q(this.mainBkg,5)||"#ffffff",this.rowEven=this.rowEven||Q(this.mainBkg,10)):(this.rowOdd=this.rowOdd||q(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||q(this.mainBkg,5)),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||this.tertiaryColor,this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.specialStateColor=this.lineColor,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||T(this.primaryColor,{h:30}),this.cScale4=this.cScale4||T(this.primaryColor,{h:60}),this.cScale5=this.cScale5||T(this.primaryColor,{h:90}),this.cScale6=this.cScale6||T(this.primaryColor,{h:120}),this.cScale7=this.cScale7||T(this.primaryColor,{h:150}),this.cScale8=this.cScale8||T(this.primaryColor,{h:210,l:150}),this.cScale9=this.cScale9||T(this.primaryColor,{h:270}),this.cScale10=this.cScale10||T(this.primaryColor,{h:300}),this.cScale11=this.cScale11||T(this.primaryColor,{h:330}),this.darkMode)for(let k=0;k<this.THEME_COLOR_LIMIT;k++)this["cScale"+k]=Q(this["cScale"+k],75);else for(let k=0;k<this.THEME_COLOR_LIMIT;k++)this["cScale"+k]=Q(this["cScale"+k],25);for(let k=0;k<this.THEME_COLOR_LIMIT;k++)this["cScaleInv"+k]=this["cScaleInv"+k]||N(this["cScale"+k]);for(let k=0;k<this.THEME_COLOR_LIMIT;k++)this.darkMode?this["cScalePeer"+k]=this["cScalePeer"+k]||q(this["cScale"+k],10):this["cScalePeer"+k]=this["cScalePeer"+k]||Q(this["cScale"+k],10);this.scaleLabelColor=this.scaleLabelColor||this.labelTextColor;for(let k=0;k<this.THEME_COLOR_LIMIT;k++)this["cScaleLabel"+k]=this["cScaleLabel"+k]||this.scaleLabelColor;const t=this.darkMode?-4:-1;for(let k=0;k<5;k++)this["surface"+k]=this["surface"+k]||T(this.mainBkg,{h:180,s:-15,l:t*(5+k*3)}),this["surfacePeer"+k]=this["surfacePeer"+k]||T(this.mainBkg,{h:180,s:-15,l:t*(8+k*3)});this.classText=this.classText||this.textColor,this.fillType0=this.fillType0||this.primaryColor,this.fillType1=this.fillType1||this.secondaryColor,this.fillType2=this.fillType2||T(this.primaryColor,{h:64}),this.fillType3=this.fillType3||T(this.secondaryColor,{h:64}),this.fillType4=this.fillType4||T(this.primaryColor,{h:-64}),this.fillType5=this.fillType5||T(this.secondaryColor,{h:-64}),this.fillType6=this.fillType6||T(this.primaryColor,{h:128}),this.fillType7=this.fillType7||T(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||T(this.primaryColor,{l:-10}),this.pie5=this.pie5||T(this.secondaryColor,{l:-10}),this.pie6=this.pie6||T(this.tertiaryColor,{l:-10}),this.pie7=this.pie7||T(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||T(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||T(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||T(this.primaryColor,{h:60,l:-20}),this.pie11=this.pie11||T(this.primaryColor,{h:-60,l:-20}),this.pie12=this.pie12||T(this.primaryColor,{h:120,l:-10}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.radar={axisColor:((r=this.radar)==null?void 0:r.axisColor)||this.lineColor,axisStrokeWidth:((i=this.radar)==null?void 0:i.axisStrokeWidth)||2,axisLabelFontSize:((n=this.radar)==null?void 0:n.axisLabelFontSize)||12,curveOpacity:((a=this.radar)==null?void 0:a.curveOpacity)||.5,curveStrokeWidth:((o=this.radar)==null?void 0:o.curveStrokeWidth)||2,graticuleColor:((s=this.radar)==null?void 0:s.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((c=this.radar)==null?void 0:c.graticuleStrokeWidth)||1,graticuleOpacity:((l=this.radar)==null?void 0:l.graticuleOpacity)||.3,legendBoxSize:((h=this.radar)==null?void 0:h.legendBoxSize)||12,legendFontSize:((u=this.radar)==null?void 0:u.legendFontSize)||12},this.archEdgeColor=this.archEdgeColor||"#777",this.archEdgeArrowColor=this.archEdgeArrowColor||"#777",this.archEdgeWidth=this.archEdgeWidth||"3",this.archGroupBorderColor=this.archGroupBorderColor||"#000",this.archGroupBorderWidth=this.archGroupBorderWidth||"2px",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||T(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||T(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||T(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||T(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||T(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||T(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Mi(this.quadrant1Fill)?q(this.quadrant1Fill):Q(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((f=this.xyChart)==null?void 0:f.backgroundColor)||this.background,titleColor:((d=this.xyChart)==null?void 0:d.titleColor)||this.primaryTextColor,xAxisTitleColor:((g=this.xyChart)==null?void 0:g.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((m=this.xyChart)==null?void 0:m.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((y=this.xyChart)==null?void 0:y.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((x=this.xyChart)==null?void 0:x.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((b=this.xyChart)==null?void 0:b.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((C=this.xyChart)==null?void 0:C.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((v=this.xyChart)==null?void 0:v.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((S=this.xyChart)==null?void 0:S.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((_=this.xyChart)==null?void 0:_.plotColorPalette)||"#FFF4DD,#FFD8B1,#FFA07A,#ECEFF1,#D6DBDF,#C3E0A8,#FFB6A4,#FFD74D,#738FA7,#FFFFF0"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?Q(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||T(this.primaryColor,{h:-30}),this.git4=this.git4||T(this.primaryColor,{h:-60}),this.git5=this.git5||T(this.primaryColor,{h:-90}),this.git6=this.git6||T(this.primaryColor,{h:60}),this.git7=this.git7||T(this.primaryColor,{h:120}),this.darkMode?(this.git0=q(this.git0,25),this.git1=q(this.git1,25),this.git2=q(this.git2,25),this.git3=q(this.git3,25),this.git4=q(this.git4,25),this.git5=q(this.git5,25),this.git6=q(this.git6,25),this.git7=q(this.git7,25)):(this.git0=Q(this.git0,25),this.git1=Q(this.git1,25),this.git2=Q(this.git2,25),this.git3=Q(this.git3,25),this.git4=Q(this.git4,25),this.git5=Q(this.git5,25),this.git6=Q(this.git6,25),this.git7=Q(this.git7,25)),this.gitInv0=this.gitInv0||N(this.git0),this.gitInv1=this.gitInv1||N(this.git1),this.gitInv2=this.gitInv2||N(this.git2),this.gitInv3=this.gitInv3||N(this.git3),this.gitInv4=this.gitInv4||N(this.git4),this.gitInv5=this.gitInv5||N(this.git5),this.gitInv6=this.gitInv6||N(this.git6),this.gitInv7=this.gitInv7||N(this.git7),this.branchLabelColor=this.branchLabelColor||(this.darkMode?"black":this.labelTextColor),this.gitBranchLabel0=this.gitBranchLabel0||this.branchLabelColor,this.gitBranchLabel1=this.gitBranchLabel1||this.branchLabelColor,this.gitBranchLabel2=this.gitBranchLabel2||this.branchLabelColor,this.gitBranchLabel3=this.gitBranchLabel3||this.branchLabelColor,this.gitBranchLabel4=this.gitBranchLabel4||this.branchLabelColor,this.gitBranchLabel5=this.gitBranchLabel5||this.branchLabelColor,this.gitBranchLabel6=this.gitBranchLabel6||this.branchLabelColor,this.gitBranchLabel7=this.gitBranchLabel7||this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Xn,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Zn}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},p(Tr,"Theme"),Tr),pm=p(e=>{const t=new dm;return t.calculate(e),t},"getThemeVariables"),Lr,gm=(Lr=class{constructor(){this.background="#333",this.primaryColor="#1f2020",this.secondaryColor=q(this.primaryColor,16),this.tertiaryColor=T(this.primaryColor,{h:-160}),this.primaryBorderColor=N(this.background),this.secondaryBorderColor=Pt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Pt(this.tertiaryColor,this.darkMode),this.primaryTextColor=N(this.primaryColor),this.secondaryTextColor=N(this.secondaryColor),this.tertiaryTextColor=N(this.tertiaryColor),this.lineColor=N(this.background),this.textColor=N(this.background),this.mainBkg="#1f2020",this.secondBkg="calculated",this.mainContrastColor="lightgrey",this.darkTextColor=q(N("#323D47"),10),this.lineColor="calculated",this.border1="#ccc",this.border2=mi(255,255,255,.25),this.arrowheadColor="calculated",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="#181818",this.textColor="#ccc",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#F9FFFE",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="calculated",this.activationBkgColor="calculated",this.sequenceNumberColor="black",this.sectionBkgColor=Q("#EAE8D9",30),this.altSectionBkgColor="calculated",this.sectionBkgColor2="#EAE8D9",this.excludeBkgColor=Q(this.sectionBkgColor,10),this.taskBorderColor=mi(255,255,255,70),this.taskBkgColor="calculated",this.taskTextColor="calculated",this.taskTextLightColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor=mi(255,255,255,50),this.activeTaskBkgColor="#81B1DB",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="grey",this.critBorderColor="#E83737",this.critBkgColor="#E83737",this.taskTextDarkColor="calculated",this.todayLineColor="#DB5757",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd=this.rowOdd||q(this.mainBkg,5)||"#ffffff",this.rowEven=this.rowEven||Q(this.mainBkg,10),this.labelColor="calculated",this.errorBkgColor="#a44141",this.errorTextColor="#ddd"}updateColors(){var t,r,i,n,a,o,s,c,l,h,u,f,d,g,m,y,x,b,C,v,S;this.secondBkg=q(this.mainBkg,16),this.lineColor=this.mainContrastColor,this.arrowheadColor=this.mainContrastColor,this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.edgeLabelBackground=q(this.labelBackground,25),this.actorBorder=this.border1,this.actorBkg=this.mainBkg,this.actorTextColor=this.mainContrastColor,this.actorLineColor=this.actorBorder,this.signalColor=this.mainContrastColor,this.signalTextColor=this.mainContrastColor,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.mainContrastColor,this.loopTextColor=this.mainContrastColor,this.noteBorderColor=this.secondaryBorderColor,this.noteBkgColor=this.secondBkg,this.noteTextColor=this.secondaryTextColor,this.activationBorderColor=this.border1,this.activationBkgColor=this.secondBkg,this.altSectionBkgColor=this.background,this.taskBkgColor=q(this.mainBkg,23),this.taskTextColor=this.darkTextColor,this.taskTextLightColor=this.mainContrastColor,this.taskTextOutsideColor=this.taskTextLightColor,this.gridColor=this.mainContrastColor,this.doneTaskBkgColor=this.mainContrastColor,this.taskTextDarkColor=this.darkTextColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#555",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#f4f4f4",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=T(this.primaryColor,{h:64}),this.fillType3=T(this.secondaryColor,{h:64}),this.fillType4=T(this.primaryColor,{h:-64}),this.fillType5=T(this.secondaryColor,{h:-64}),this.fillType6=T(this.primaryColor,{h:128}),this.fillType7=T(this.secondaryColor,{h:128}),this.cScale1=this.cScale1||"#0b0000",this.cScale2=this.cScale2||"#4d1037",this.cScale3=this.cScale3||"#3f5258",this.cScale4=this.cScale4||"#4f2f1b",this.cScale5=this.cScale5||"#6e0a0a",this.cScale6=this.cScale6||"#3b0048",this.cScale7=this.cScale7||"#995a01",this.cScale8=this.cScale8||"#154706",this.cScale9=this.cScale9||"#161722",this.cScale10=this.cScale10||"#00296f",this.cScale11=this.cScale11||"#01629c",this.cScale12=this.cScale12||"#010029",this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||T(this.primaryColor,{h:30}),this.cScale4=this.cScale4||T(this.primaryColor,{h:60}),this.cScale5=this.cScale5||T(this.primaryColor,{h:90}),this.cScale6=this.cScale6||T(this.primaryColor,{h:120}),this.cScale7=this.cScale7||T(this.primaryColor,{h:150}),this.cScale8=this.cScale8||T(this.primaryColor,{h:210}),this.cScale9=this.cScale9||T(this.primaryColor,{h:270}),this.cScale10=this.cScale10||T(this.primaryColor,{h:300}),this.cScale11=this.cScale11||T(this.primaryColor,{h:330});for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleInv"+_]=this["cScaleInv"+_]||N(this["cScale"+_]);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScalePeer"+_]=this["cScalePeer"+_]||q(this["cScale"+_],10);for(let _=0;_<5;_++)this["surface"+_]=this["surface"+_]||T(this.mainBkg,{h:30,s:-30,l:-(-10+_*4)}),this["surfacePeer"+_]=this["surfacePeer"+_]||T(this.mainBkg,{h:30,s:-30,l:-(-7+_*4)});this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleLabel"+_]=this["cScaleLabel"+_]||this.scaleLabelColor;for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["pie"+_]=this["cScale"+_];this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||T(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||T(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||T(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||T(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||T(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||T(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Mi(this.quadrant1Fill)?q(this.quadrant1Fill):Q(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((t=this.xyChart)==null?void 0:t.backgroundColor)||this.background,titleColor:((r=this.xyChart)==null?void 0:r.titleColor)||this.primaryTextColor,xAxisTitleColor:((i=this.xyChart)==null?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((n=this.xyChart)==null?void 0:n.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((a=this.xyChart)==null?void 0:a.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((o=this.xyChart)==null?void 0:o.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((s=this.xyChart)==null?void 0:s.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((c=this.xyChart)==null?void 0:c.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((l=this.xyChart)==null?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((h=this.xyChart)==null?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((u=this.xyChart)==null?void 0:u.plotColorPalette)||"#3498db,#2ecc71,#e74c3c,#f1c40f,#bdc3c7,#ffffff,#34495e,#9b59b6,#1abc9c,#e67e22"},this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.background},this.radar={axisColor:((f=this.radar)==null?void 0:f.axisColor)||this.lineColor,axisStrokeWidth:((d=this.radar)==null?void 0:d.axisStrokeWidth)||2,axisLabelFontSize:((g=this.radar)==null?void 0:g.axisLabelFontSize)||12,curveOpacity:((m=this.radar)==null?void 0:m.curveOpacity)||.5,curveStrokeWidth:((y=this.radar)==null?void 0:y.curveStrokeWidth)||2,graticuleColor:((x=this.radar)==null?void 0:x.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((b=this.radar)==null?void 0:b.graticuleStrokeWidth)||1,graticuleOpacity:((C=this.radar)==null?void 0:C.graticuleOpacity)||.3,legendBoxSize:((v=this.radar)==null?void 0:v.legendBoxSize)||12,legendFontSize:((S=this.radar)==null?void 0:S.legendFontSize)||12},this.classText=this.primaryTextColor,this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?Q(this.secondaryColor,30):this.secondaryColor),this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=q(this.secondaryColor,20),this.git1=q(this.pie2||this.secondaryColor,20),this.git2=q(this.pie3||this.tertiaryColor,20),this.git3=q(this.pie4||T(this.primaryColor,{h:-30}),20),this.git4=q(this.pie5||T(this.primaryColor,{h:-60}),20),this.git5=q(this.pie6||T(this.primaryColor,{h:-90}),10),this.git6=q(this.pie7||T(this.primaryColor,{h:60}),10),this.git7=q(this.pie8||T(this.primaryColor,{h:120}),20),this.gitInv0=this.gitInv0||N(this.git0),this.gitInv1=this.gitInv1||N(this.git1),this.gitInv2=this.gitInv2||N(this.git2),this.gitInv3=this.gitInv3||N(this.git3),this.gitInv4=this.gitInv4||N(this.git4),this.gitInv5=this.gitInv5||N(this.git5),this.gitInv6=this.gitInv6||N(this.git6),this.gitInv7=this.gitInv7||N(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||N(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||N(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||q(this.background,12),this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||q(this.background,2),this.nodeBorder=this.nodeBorder||"#999"}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},p(Lr,"Theme"),Lr),mm=p(e=>{const t=new gm;return t.calculate(e),t},"getThemeVariables"),Br,ym=(Br=class{constructor(){this.background="#f4f4f4",this.primaryColor="#ECECFF",this.secondaryColor=T(this.primaryColor,{h:120}),this.secondaryColor="#ffffde",this.tertiaryColor=T(this.primaryColor,{h:-160}),this.primaryBorderColor=Pt(this.primaryColor,this.darkMode),this.secondaryBorderColor=Pt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Pt(this.tertiaryColor,this.darkMode),this.primaryTextColor=N(this.primaryColor),this.secondaryTextColor=N(this.secondaryColor),this.tertiaryTextColor=N(this.tertiaryColor),this.lineColor=N(this.background),this.textColor=N(this.background),this.background="white",this.mainBkg="#ECECFF",this.secondBkg="#ffffde",this.lineColor="#333333",this.border1="#9370DB",this.border2="#aaaa33",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.labelBackground="rgba(232,232,232, 0.8)",this.textColor="#333",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="calculated",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="calculated",this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="calculated",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="calculated",this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor="calculated",this.taskTextOutsideColor=this.taskTextDarkColor,this.taskTextClickableColor="calculated",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBorderColor="calculated",this.critBkgColor="calculated",this.todayLineColor="calculated",this.sectionBkgColor=mi(102,102,255,.49),this.altSectionBkgColor="white",this.sectionBkgColor2="#fff400",this.taskBorderColor="#534fbc",this.taskBkgColor="#8a90dd",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="#534fbc",this.activeTaskBkgColor="#bfc7ff",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd="calculated",this.rowEven="calculated",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222",this.updateColors()}updateColors(){var t,r,i,n,a,o,s,c,l,h,u,f,d,g,m,y,x,b,C,v,S;this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||T(this.primaryColor,{h:30}),this.cScale4=this.cScale4||T(this.primaryColor,{h:60}),this.cScale5=this.cScale5||T(this.primaryColor,{h:90}),this.cScale6=this.cScale6||T(this.primaryColor,{h:120}),this.cScale7=this.cScale7||T(this.primaryColor,{h:150}),this.cScale8=this.cScale8||T(this.primaryColor,{h:210}),this.cScale9=this.cScale9||T(this.primaryColor,{h:270}),this.cScale10=this.cScale10||T(this.primaryColor,{h:300}),this.cScale11=this.cScale11||T(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||Q(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||Q(this.tertiaryColor,40);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScale"+_]=Q(this["cScale"+_],10),this["cScalePeer"+_]=this["cScalePeer"+_]||Q(this["cScale"+_],25);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleInv"+_]=this["cScaleInv"+_]||T(this["cScale"+_],{h:180});for(let _=0;_<5;_++)this["surface"+_]=this["surface"+_]||T(this.mainBkg,{h:30,l:-(5+_*5)}),this["surfacePeer"+_]=this["surfacePeer"+_]||T(this.mainBkg,{h:30,l:-(7+_*5)});if(this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor,this.labelTextColor!=="calculated"){this.cScaleLabel0=this.cScaleLabel0||N(this.labelTextColor),this.cScaleLabel3=this.cScaleLabel3||N(this.labelTextColor);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleLabel"+_]=this["cScaleLabel"+_]||this.labelTextColor}this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.textColor,this.edgeLabelBackground=this.labelBackground,this.actorBorder=q(this.border1,23),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.signalColor=this.textColor,this.signalTextColor=this.textColor,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.actorLineColor=this.actorBorder,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.rowOdd=this.rowOdd||q(this.primaryColor,75)||"#ffffff",this.rowEven=this.rowEven||q(this.primaryColor,1),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.nodeBorder,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=T(this.primaryColor,{h:64}),this.fillType3=T(this.secondaryColor,{h:64}),this.fillType4=T(this.primaryColor,{h:-64}),this.fillType5=T(this.secondaryColor,{h:-64}),this.fillType6=T(this.primaryColor,{h:128}),this.fillType7=T(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||T(this.tertiaryColor,{l:-40}),this.pie4=this.pie4||T(this.primaryColor,{l:-10}),this.pie5=this.pie5||T(this.secondaryColor,{l:-30}),this.pie6=this.pie6||T(this.tertiaryColor,{l:-20}),this.pie7=this.pie7||T(this.primaryColor,{h:60,l:-20}),this.pie8=this.pie8||T(this.primaryColor,{h:-60,l:-40}),this.pie9=this.pie9||T(this.primaryColor,{h:120,l:-40}),this.pie10=this.pie10||T(this.primaryColor,{h:60,l:-40}),this.pie11=this.pie11||T(this.primaryColor,{h:-90,l:-40}),this.pie12=this.pie12||T(this.primaryColor,{h:120,l:-30}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||T(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||T(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||T(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||T(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||T(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||T(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Mi(this.quadrant1Fill)?q(this.quadrant1Fill):Q(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.radar={axisColor:((t=this.radar)==null?void 0:t.axisColor)||this.lineColor,axisStrokeWidth:((r=this.radar)==null?void 0:r.axisStrokeWidth)||2,axisLabelFontSize:((i=this.radar)==null?void 0:i.axisLabelFontSize)||12,curveOpacity:((n=this.radar)==null?void 0:n.curveOpacity)||.5,curveStrokeWidth:((a=this.radar)==null?void 0:a.curveStrokeWidth)||2,graticuleColor:((o=this.radar)==null?void 0:o.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((s=this.radar)==null?void 0:s.graticuleStrokeWidth)||1,graticuleOpacity:((c=this.radar)==null?void 0:c.graticuleOpacity)||.3,legendBoxSize:((l=this.radar)==null?void 0:l.legendBoxSize)||12,legendFontSize:((h=this.radar)==null?void 0:h.legendFontSize)||12},this.xyChart={backgroundColor:((u=this.xyChart)==null?void 0:u.backgroundColor)||this.background,titleColor:((f=this.xyChart)==null?void 0:f.titleColor)||this.primaryTextColor,xAxisTitleColor:((d=this.xyChart)==null?void 0:d.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((g=this.xyChart)==null?void 0:g.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((m=this.xyChart)==null?void 0:m.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((y=this.xyChart)==null?void 0:y.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((x=this.xyChart)==null?void 0:x.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((b=this.xyChart)==null?void 0:b.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((C=this.xyChart)==null?void 0:C.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((v=this.xyChart)==null?void 0:v.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((S=this.xyChart)==null?void 0:S.plotColorPalette)||"#ECECFF,#8493A6,#FFC3A0,#DCDDE1,#B8E994,#D1A36F,#C3CDE6,#FFB6C1,#496078,#F8F3E3"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.labelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||T(this.primaryColor,{h:-30}),this.git4=this.git4||T(this.primaryColor,{h:-60}),this.git5=this.git5||T(this.primaryColor,{h:-90}),this.git6=this.git6||T(this.primaryColor,{h:60}),this.git7=this.git7||T(this.primaryColor,{h:120}),this.darkMode?(this.git0=q(this.git0,25),this.git1=q(this.git1,25),this.git2=q(this.git2,25),this.git3=q(this.git3,25),this.git4=q(this.git4,25),this.git5=q(this.git5,25),this.git6=q(this.git6,25),this.git7=q(this.git7,25)):(this.git0=Q(this.git0,25),this.git1=Q(this.git1,25),this.git2=Q(this.git2,25),this.git3=Q(this.git3,25),this.git4=Q(this.git4,25),this.git5=Q(this.git5,25),this.git6=Q(this.git6,25),this.git7=Q(this.git7,25)),this.gitInv0=this.gitInv0||Q(N(this.git0),25),this.gitInv1=this.gitInv1||N(this.git1),this.gitInv2=this.gitInv2||N(this.git2),this.gitInv3=this.gitInv3||N(this.git3),this.gitInv4=this.gitInv4||N(this.git4),this.gitInv5=this.gitInv5||N(this.git5),this.gitInv6=this.gitInv6||N(this.git6),this.gitInv7=this.gitInv7||N(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||N(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||N(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Xn,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Zn}calculate(t){if(Object.keys(this).forEach(i=>{this[i]==="calculated"&&(this[i]=void 0)}),typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},p(Br,"Theme"),Br),xm=p(e=>{const t=new ym;return t.calculate(e),t},"getThemeVariables"),Ar,bm=(Ar=class{constructor(){this.background="#f4f4f4",this.primaryColor="#cde498",this.secondaryColor="#cdffb2",this.background="white",this.mainBkg="#cde498",this.secondBkg="#cdffb2",this.lineColor="green",this.border1="#13540c",this.border2="#6eaa49",this.arrowheadColor="green",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.tertiaryColor=q("#cde498",10),this.primaryBorderColor=Pt(this.primaryColor,this.darkMode),this.secondaryBorderColor=Pt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Pt(this.tertiaryColor,this.darkMode),this.primaryTextColor=N(this.primaryColor),this.secondaryTextColor=N(this.secondaryColor),this.tertiaryTextColor=N(this.primaryColor),this.lineColor=N(this.background),this.textColor=N(this.background),this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="#333",this.edgeLabelBackground="#e8e8e8",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="black",this.actorLineColor="calculated",this.signalColor="#333",this.signalTextColor="#333",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="#326932",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="#fff5ad",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="#6eaa49",this.altSectionBkgColor="white",this.sectionBkgColor2="#6eaa49",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="#487e3a",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="black",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="lightgrey",this.doneTaskBkgColor="lightgrey",this.doneTaskBorderColor="grey",this.critBorderColor="#ff8888",this.critBkgColor="red",this.todayLineColor="red",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){var t,r,i,n,a,o,s,c,l,h,u,f,d,g,m,y,x,b,C,v,S;this.actorBorder=Q(this.mainBkg,20),this.actorBkg=this.mainBkg,this.labelBoxBkgColor=this.actorBkg,this.labelTextColor=this.actorTextColor,this.loopTextColor=this.actorTextColor,this.noteBorderColor=this.border2,this.noteTextColor=this.actorTextColor,this.actorLineColor=this.actorBorder,this.cScale0=this.cScale0||this.primaryColor,this.cScale1=this.cScale1||this.secondaryColor,this.cScale2=this.cScale2||this.tertiaryColor,this.cScale3=this.cScale3||T(this.primaryColor,{h:30}),this.cScale4=this.cScale4||T(this.primaryColor,{h:60}),this.cScale5=this.cScale5||T(this.primaryColor,{h:90}),this.cScale6=this.cScale6||T(this.primaryColor,{h:120}),this.cScale7=this.cScale7||T(this.primaryColor,{h:150}),this.cScale8=this.cScale8||T(this.primaryColor,{h:210}),this.cScale9=this.cScale9||T(this.primaryColor,{h:270}),this.cScale10=this.cScale10||T(this.primaryColor,{h:300}),this.cScale11=this.cScale11||T(this.primaryColor,{h:330}),this.cScalePeer1=this.cScalePeer1||Q(this.secondaryColor,45),this.cScalePeer2=this.cScalePeer2||Q(this.tertiaryColor,40);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScale"+_]=Q(this["cScale"+_],10),this["cScalePeer"+_]=this["cScalePeer"+_]||Q(this["cScale"+_],25);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleInv"+_]=this["cScaleInv"+_]||T(this["cScale"+_],{h:180});this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor;for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleLabel"+_]=this["cScaleLabel"+_]||this.scaleLabelColor;for(let _=0;_<5;_++)this["surface"+_]=this["surface"+_]||T(this.mainBkg,{h:30,s:-30,l:-(5+_*5)}),this["surfacePeer"+_]=this["surfacePeer"+_]||T(this.mainBkg,{h:30,s:-30,l:-(8+_*5)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.taskBorderColor=this.border1,this.taskTextColor=this.taskTextLightColor,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.rowOdd=this.rowOdd||q(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||q(this.mainBkg,20),this.transitionColor=this.transitionColor||this.lineColor,this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f0f0f0",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.compositeBorder=this.compositeBorder||this.nodeBorder,this.innerEndBackground=this.primaryBorderColor,this.specialStateColor=this.lineColor,this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.transitionColor=this.transitionColor||this.lineColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=T(this.primaryColor,{h:64}),this.fillType3=T(this.secondaryColor,{h:64}),this.fillType4=T(this.primaryColor,{h:-64}),this.fillType5=T(this.secondaryColor,{h:-64}),this.fillType6=T(this.primaryColor,{h:128}),this.fillType7=T(this.secondaryColor,{h:128}),this.pie1=this.pie1||this.primaryColor,this.pie2=this.pie2||this.secondaryColor,this.pie3=this.pie3||this.tertiaryColor,this.pie4=this.pie4||T(this.primaryColor,{l:-30}),this.pie5=this.pie5||T(this.secondaryColor,{l:-30}),this.pie6=this.pie6||T(this.tertiaryColor,{h:40,l:-40}),this.pie7=this.pie7||T(this.primaryColor,{h:60,l:-10}),this.pie8=this.pie8||T(this.primaryColor,{h:-60,l:-10}),this.pie9=this.pie9||T(this.primaryColor,{h:120,l:0}),this.pie10=this.pie10||T(this.primaryColor,{h:60,l:-50}),this.pie11=this.pie11||T(this.primaryColor,{h:-60,l:-50}),this.pie12=this.pie12||T(this.primaryColor,{h:120,l:-50}),this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||T(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||T(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||T(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||T(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||T(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||T(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Mi(this.quadrant1Fill)?q(this.quadrant1Fill):Q(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.mainBkg},this.radar={axisColor:((t=this.radar)==null?void 0:t.axisColor)||this.lineColor,axisStrokeWidth:((r=this.radar)==null?void 0:r.axisStrokeWidth)||2,axisLabelFontSize:((i=this.radar)==null?void 0:i.axisLabelFontSize)||12,curveOpacity:((n=this.radar)==null?void 0:n.curveOpacity)||.5,curveStrokeWidth:((a=this.radar)==null?void 0:a.curveStrokeWidth)||2,graticuleColor:((o=this.radar)==null?void 0:o.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((s=this.radar)==null?void 0:s.graticuleStrokeWidth)||1,graticuleOpacity:((c=this.radar)==null?void 0:c.graticuleOpacity)||.3,legendBoxSize:((l=this.radar)==null?void 0:l.legendBoxSize)||12,legendFontSize:((h=this.radar)==null?void 0:h.legendFontSize)||12},this.xyChart={backgroundColor:((u=this.xyChart)==null?void 0:u.backgroundColor)||this.background,titleColor:((f=this.xyChart)==null?void 0:f.titleColor)||this.primaryTextColor,xAxisTitleColor:((d=this.xyChart)==null?void 0:d.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((g=this.xyChart)==null?void 0:g.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((m=this.xyChart)==null?void 0:m.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((y=this.xyChart)==null?void 0:y.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((x=this.xyChart)==null?void 0:x.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((b=this.xyChart)==null?void 0:b.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((C=this.xyChart)==null?void 0:C.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((v=this.xyChart)==null?void 0:v.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((S=this.xyChart)==null?void 0:S.plotColorPalette)||"#CDE498,#FF6B6B,#A0D2DB,#D7BDE2,#F0F0F0,#FFC3A0,#7FD8BE,#FF9A8B,#FAF3E0,#FFF176"},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=this.git0||this.primaryColor,this.git1=this.git1||this.secondaryColor,this.git2=this.git2||this.tertiaryColor,this.git3=this.git3||T(this.primaryColor,{h:-30}),this.git4=this.git4||T(this.primaryColor,{h:-60}),this.git5=this.git5||T(this.primaryColor,{h:-90}),this.git6=this.git6||T(this.primaryColor,{h:60}),this.git7=this.git7||T(this.primaryColor,{h:120}),this.darkMode?(this.git0=q(this.git0,25),this.git1=q(this.git1,25),this.git2=q(this.git2,25),this.git3=q(this.git3,25),this.git4=q(this.git4,25),this.git5=q(this.git5,25),this.git6=q(this.git6,25),this.git7=q(this.git7,25)):(this.git0=Q(this.git0,25),this.git1=Q(this.git1,25),this.git2=Q(this.git2,25),this.git3=Q(this.git3,25),this.git4=Q(this.git4,25),this.git5=Q(this.git5,25),this.git6=Q(this.git6,25),this.git7=Q(this.git7,25)),this.gitInv0=this.gitInv0||N(this.git0),this.gitInv1=this.gitInv1||N(this.git1),this.gitInv2=this.gitInv2||N(this.git2),this.gitInv3=this.gitInv3||N(this.git3),this.gitInv4=this.gitInv4||N(this.git4),this.gitInv5=this.gitInv5||N(this.git5),this.gitInv6=this.gitInv6||N(this.git6),this.gitInv7=this.gitInv7||N(this.git7),this.gitBranchLabel0=this.gitBranchLabel0||N(this.labelTextColor),this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor,this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor,this.gitBranchLabel3=this.gitBranchLabel3||N(this.labelTextColor),this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor,this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor,this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor,this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Xn,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Zn}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},p(Ar,"Theme"),Ar),_m=p(e=>{const t=new bm;return t.calculate(e),t},"getThemeVariables"),Mr,Cm=(Mr=class{constructor(){this.primaryColor="#eee",this.contrast="#707070",this.secondaryColor=q(this.contrast,55),this.background="#ffffff",this.tertiaryColor=T(this.primaryColor,{h:-160}),this.primaryBorderColor=Pt(this.primaryColor,this.darkMode),this.secondaryBorderColor=Pt(this.secondaryColor,this.darkMode),this.tertiaryBorderColor=Pt(this.tertiaryColor,this.darkMode),this.primaryTextColor=N(this.primaryColor),this.secondaryTextColor=N(this.secondaryColor),this.tertiaryTextColor=N(this.tertiaryColor),this.lineColor=N(this.background),this.textColor=N(this.background),this.mainBkg="#eee",this.secondBkg="calculated",this.lineColor="#666",this.border1="#999",this.border2="calculated",this.note="#ffa",this.text="#333",this.critical="#d42",this.done="#bbb",this.arrowheadColor="#333333",this.fontFamily='"trebuchet ms", verdana, arial, sans-serif',this.fontSize="16px",this.THEME_COLOR_LIMIT=12,this.nodeBkg="calculated",this.nodeBorder="calculated",this.clusterBkg="calculated",this.clusterBorder="calculated",this.defaultLinkColor="calculated",this.titleColor="calculated",this.edgeLabelBackground="white",this.actorBorder="calculated",this.actorBkg="calculated",this.actorTextColor="calculated",this.actorLineColor=this.actorBorder,this.signalColor="calculated",this.signalTextColor="calculated",this.labelBoxBkgColor="calculated",this.labelBoxBorderColor="calculated",this.labelTextColor="calculated",this.loopTextColor="calculated",this.noteBorderColor="calculated",this.noteBkgColor="calculated",this.noteTextColor="calculated",this.activationBorderColor="#666",this.activationBkgColor="#f4f4f4",this.sequenceNumberColor="white",this.sectionBkgColor="calculated",this.altSectionBkgColor="white",this.sectionBkgColor2="calculated",this.excludeBkgColor="#eeeeee",this.taskBorderColor="calculated",this.taskBkgColor="calculated",this.taskTextLightColor="white",this.taskTextColor="calculated",this.taskTextDarkColor="calculated",this.taskTextOutsideColor="calculated",this.taskTextClickableColor="#003163",this.activeTaskBorderColor="calculated",this.activeTaskBkgColor="calculated",this.gridColor="calculated",this.doneTaskBkgColor="calculated",this.doneTaskBorderColor="calculated",this.critBkgColor="calculated",this.critBorderColor="calculated",this.todayLineColor="calculated",this.personBorder=this.primaryBorderColor,this.personBkg=this.mainBkg,this.archEdgeColor="calculated",this.archEdgeArrowColor="calculated",this.archEdgeWidth="3",this.archGroupBorderColor=this.primaryBorderColor,this.archGroupBorderWidth="2px",this.rowOdd=this.rowOdd||q(this.mainBkg,75)||"#ffffff",this.rowEven=this.rowEven||"#f4f4f4",this.labelColor="black",this.errorBkgColor="#552222",this.errorTextColor="#552222"}updateColors(){var t,r,i,n,a,o,s,c,l,h,u,f,d,g,m,y,x,b,C,v,S;this.secondBkg=q(this.contrast,55),this.border2=this.contrast,this.actorBorder=q(this.border1,23),this.actorBkg=this.mainBkg,this.actorTextColor=this.text,this.actorLineColor=this.actorBorder,this.signalColor=this.text,this.signalTextColor=this.text,this.labelBoxBkgColor=this.actorBkg,this.labelBoxBorderColor=this.actorBorder,this.labelTextColor=this.text,this.loopTextColor=this.text,this.noteBorderColor="#999",this.noteBkgColor="#666",this.noteTextColor="#fff",this.cScale0=this.cScale0||"#555",this.cScale1=this.cScale1||"#F4F4F4",this.cScale2=this.cScale2||"#555",this.cScale3=this.cScale3||"#BBB",this.cScale4=this.cScale4||"#777",this.cScale5=this.cScale5||"#999",this.cScale6=this.cScale6||"#DDD",this.cScale7=this.cScale7||"#FFF",this.cScale8=this.cScale8||"#DDD",this.cScale9=this.cScale9||"#BBB",this.cScale10=this.cScale10||"#999",this.cScale11=this.cScale11||"#777";for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleInv"+_]=this["cScaleInv"+_]||N(this["cScale"+_]);for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this.darkMode?this["cScalePeer"+_]=this["cScalePeer"+_]||q(this["cScale"+_],10):this["cScalePeer"+_]=this["cScalePeer"+_]||Q(this["cScale"+_],10);this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor),this.cScaleLabel0=this.cScaleLabel0||this.cScale1,this.cScaleLabel2=this.cScaleLabel2||this.cScale1;for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["cScaleLabel"+_]=this["cScaleLabel"+_]||this.scaleLabelColor;for(let _=0;_<5;_++)this["surface"+_]=this["surface"+_]||T(this.mainBkg,{l:-(5+_*5)}),this["surfacePeer"+_]=this["surfacePeer"+_]||T(this.mainBkg,{l:-(8+_*5)});this.nodeBkg=this.mainBkg,this.nodeBorder=this.border1,this.clusterBkg=this.secondBkg,this.clusterBorder=this.border2,this.defaultLinkColor=this.lineColor,this.titleColor=this.text,this.sectionBkgColor=q(this.contrast,30),this.sectionBkgColor2=q(this.contrast,30),this.taskBorderColor=Q(this.contrast,10),this.taskBkgColor=this.contrast,this.taskTextColor=this.taskTextLightColor,this.taskTextDarkColor=this.text,this.taskTextOutsideColor=this.taskTextDarkColor,this.activeTaskBorderColor=this.taskBorderColor,this.activeTaskBkgColor=this.mainBkg,this.gridColor=q(this.border1,30),this.doneTaskBkgColor=this.done,this.doneTaskBorderColor=this.lineColor,this.critBkgColor=this.critical,this.critBorderColor=Q(this.critBkgColor,10),this.todayLineColor=this.critBkgColor,this.archEdgeColor=this.lineColor,this.archEdgeArrowColor=this.lineColor,this.transitionColor=this.transitionColor||"#000",this.transitionLabelColor=this.transitionLabelColor||this.textColor,this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor,this.stateBkg=this.stateBkg||this.mainBkg,this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg,this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor,this.altBackground=this.altBackground||"#f4f4f4",this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg,this.stateBorder=this.stateBorder||"#000",this.innerEndBackground=this.primaryBorderColor,this.specialStateColor="#222",this.errorBkgColor=this.errorBkgColor||this.tertiaryColor,this.errorTextColor=this.errorTextColor||this.tertiaryTextColor,this.classText=this.primaryTextColor,this.fillType0=this.primaryColor,this.fillType1=this.secondaryColor,this.fillType2=T(this.primaryColor,{h:64}),this.fillType3=T(this.secondaryColor,{h:64}),this.fillType4=T(this.primaryColor,{h:-64}),this.fillType5=T(this.secondaryColor,{h:-64}),this.fillType6=T(this.primaryColor,{h:128}),this.fillType7=T(this.secondaryColor,{h:128});for(let _=0;_<this.THEME_COLOR_LIMIT;_++)this["pie"+_]=this["cScale"+_];this.pie12=this.pie0,this.pieTitleTextSize=this.pieTitleTextSize||"25px",this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor,this.pieSectionTextSize=this.pieSectionTextSize||"17px",this.pieSectionTextColor=this.pieSectionTextColor||this.textColor,this.pieLegendTextSize=this.pieLegendTextSize||"17px",this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor,this.pieStrokeColor=this.pieStrokeColor||"black",this.pieStrokeWidth=this.pieStrokeWidth||"2px",this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px",this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black",this.pieOpacity=this.pieOpacity||"0.7",this.quadrant1Fill=this.quadrant1Fill||this.primaryColor,this.quadrant2Fill=this.quadrant2Fill||T(this.primaryColor,{r:5,g:5,b:5}),this.quadrant3Fill=this.quadrant3Fill||T(this.primaryColor,{r:10,g:10,b:10}),this.quadrant4Fill=this.quadrant4Fill||T(this.primaryColor,{r:15,g:15,b:15}),this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor,this.quadrant2TextFill=this.quadrant2TextFill||T(this.primaryTextColor,{r:-5,g:-5,b:-5}),this.quadrant3TextFill=this.quadrant3TextFill||T(this.primaryTextColor,{r:-10,g:-10,b:-10}),this.quadrant4TextFill=this.quadrant4TextFill||T(this.primaryTextColor,{r:-15,g:-15,b:-15}),this.quadrantPointFill=this.quadrantPointFill||Mi(this.quadrant1Fill)?q(this.quadrant1Fill):Q(this.quadrant1Fill),this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor,this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor,this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor,this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor,this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor,this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor,this.xyChart={backgroundColor:((t=this.xyChart)==null?void 0:t.backgroundColor)||this.background,titleColor:((r=this.xyChart)==null?void 0:r.titleColor)||this.primaryTextColor,xAxisTitleColor:((i=this.xyChart)==null?void 0:i.xAxisTitleColor)||this.primaryTextColor,xAxisLabelColor:((n=this.xyChart)==null?void 0:n.xAxisLabelColor)||this.primaryTextColor,xAxisTickColor:((a=this.xyChart)==null?void 0:a.xAxisTickColor)||this.primaryTextColor,xAxisLineColor:((o=this.xyChart)==null?void 0:o.xAxisLineColor)||this.primaryTextColor,yAxisTitleColor:((s=this.xyChart)==null?void 0:s.yAxisTitleColor)||this.primaryTextColor,yAxisLabelColor:((c=this.xyChart)==null?void 0:c.yAxisLabelColor)||this.primaryTextColor,yAxisTickColor:((l=this.xyChart)==null?void 0:l.yAxisTickColor)||this.primaryTextColor,yAxisLineColor:((h=this.xyChart)==null?void 0:h.yAxisLineColor)||this.primaryTextColor,plotColorPalette:((u=this.xyChart)==null?void 0:u.plotColorPalette)||"#EEE,#6BB8E4,#8ACB88,#C7ACD6,#E8DCC2,#FFB2A8,#FFF380,#7E8D91,#FFD8B1,#FAF3E0"},this.radar={axisColor:((f=this.radar)==null?void 0:f.axisColor)||this.lineColor,axisStrokeWidth:((d=this.radar)==null?void 0:d.axisStrokeWidth)||2,axisLabelFontSize:((g=this.radar)==null?void 0:g.axisLabelFontSize)||12,curveOpacity:((m=this.radar)==null?void 0:m.curveOpacity)||.5,curveStrokeWidth:((y=this.radar)==null?void 0:y.curveStrokeWidth)||2,graticuleColor:((x=this.radar)==null?void 0:x.graticuleColor)||"#DEDEDE",graticuleStrokeWidth:((b=this.radar)==null?void 0:b.graticuleStrokeWidth)||1,graticuleOpacity:((C=this.radar)==null?void 0:C.graticuleOpacity)||.3,legendBoxSize:((v=this.radar)==null?void 0:v.legendBoxSize)||12,legendFontSize:((S=this.radar)==null?void 0:S.legendFontSize)||12},this.requirementBackground=this.requirementBackground||this.primaryColor,this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor,this.requirementBorderSize=this.requirementBorderSize||"1",this.requirementTextColor=this.requirementTextColor||this.primaryTextColor,this.relationColor=this.relationColor||this.lineColor,this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground,this.relationLabelColor=this.relationLabelColor||this.actorTextColor,this.git0=Q(this.pie1,25)||this.primaryColor,this.git1=this.pie2||this.secondaryColor,this.git2=this.pie3||this.tertiaryColor,this.git3=this.pie4||T(this.primaryColor,{h:-30}),this.git4=this.pie5||T(this.primaryColor,{h:-60}),this.git5=this.pie6||T(this.primaryColor,{h:-90}),this.git6=this.pie7||T(this.primaryColor,{h:60}),this.git7=this.pie8||T(this.primaryColor,{h:120}),this.gitInv0=this.gitInv0||N(this.git0),this.gitInv1=this.gitInv1||N(this.git1),this.gitInv2=this.gitInv2||N(this.git2),this.gitInv3=this.gitInv3||N(this.git3),this.gitInv4=this.gitInv4||N(this.git4),this.gitInv5=this.gitInv5||N(this.git5),this.gitInv6=this.gitInv6||N(this.git6),this.gitInv7=this.gitInv7||N(this.git7),this.branchLabelColor=this.branchLabelColor||this.labelTextColor,this.gitBranchLabel0=this.branchLabelColor,this.gitBranchLabel1="white",this.gitBranchLabel2=this.branchLabelColor,this.gitBranchLabel3="white",this.gitBranchLabel4=this.branchLabelColor,this.gitBranchLabel5=this.branchLabelColor,this.gitBranchLabel6=this.branchLabelColor,this.gitBranchLabel7=this.branchLabelColor,this.tagLabelColor=this.tagLabelColor||this.primaryTextColor,this.tagLabelBackground=this.tagLabelBackground||this.primaryColor,this.tagLabelBorder=this.tagBorder||this.primaryBorderColor,this.tagLabelFontSize=this.tagLabelFontSize||"10px",this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor,this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor,this.commitLabelFontSize=this.commitLabelFontSize||"10px",this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||Xn,this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||Zn}calculate(t){if(typeof t!="object"){this.updateColors();return}const r=Object.keys(t);r.forEach(i=>{this[i]=t[i]}),this.updateColors(),r.forEach(i=>{this[i]=t[i]})}},p(Mr,"Theme"),Mr),wm=p(e=>{const t=new Cm;return t.calculate(e),t},"getThemeVariables"),Fe={base:{getThemeVariables:pm},dark:{getThemeVariables:mm},default:{getThemeVariables:xm},forest:{getThemeVariables:_m},neutral:{getThemeVariables:wm}},Se={flowchart:{useMaxWidth:!0,titleTopMargin:25,subGraphTitleMargin:{top:0,bottom:0},diagramPadding:8,htmlLabels:!0,nodeSpacing:50,rankSpacing:50,curve:"basis",padding:15,defaultRenderer:"dagre-wrapper",wrappingWidth:200},sequence:{useMaxWidth:!0,hideUnusedParticipants:!1,activationWidth:10,diagramMarginX:50,diagramMarginY:10,actorMargin:50,width:150,height:65,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",mirrorActors:!0,forceMenus:!1,bottomMarginAdj:1,rightAngles:!1,showSequenceNumbers:!1,actorFontSize:14,actorFontFamily:'"Open Sans", sans-serif',actorFontWeight:400,noteFontSize:14,noteFontFamily:'"trebuchet ms", verdana, arial, sans-serif',noteFontWeight:400,noteAlign:"center",messageFontSize:16,messageFontFamily:'"trebuchet ms", verdana, arial, sans-serif',messageFontWeight:400,wrap:!1,wrapPadding:10,labelBoxWidth:50,labelBoxHeight:20},gantt:{useMaxWidth:!0,titleTopMargin:25,barHeight:20,barGap:4,topPadding:50,rightPadding:75,leftPadding:75,gridLineStartPadding:35,fontSize:11,sectionFontSize:11,numberSectionStyles:4,axisFormat:"%Y-%m-%d",topAxis:!1,displayMode:"",weekday:"sunday"},journey:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"]},class:{useMaxWidth:!0,titleTopMargin:25,arrowMarkerAbsolute:!1,dividerMargin:10,padding:5,textHeight:10,defaultRenderer:"dagre-wrapper",htmlLabels:!1,hideEmptyMembersBox:!1},state:{useMaxWidth:!0,titleTopMargin:25,dividerMargin:10,sizeUnit:5,padding:8,textHeight:10,titleShift:-15,noteMargin:10,forkWidth:70,forkHeight:7,miniPadding:2,fontSizeFactor:5.02,fontSize:24,labelHeight:16,edgeLengthFactor:"20",compositTitleSize:35,radius:5,defaultRenderer:"dagre-wrapper"},er:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:20,layoutDirection:"TB",minEntityWidth:100,minEntityHeight:75,entityPadding:15,nodeSpacing:140,rankSpacing:80,stroke:"gray",fill:"honeydew",fontSize:12},pie:{useMaxWidth:!0,textPosition:.75},quadrantChart:{useMaxWidth:!0,chartWidth:500,chartHeight:500,titleFontSize:20,titlePadding:10,quadrantPadding:5,xAxisLabelPadding:5,yAxisLabelPadding:5,xAxisLabelFontSize:16,yAxisLabelFontSize:16,quadrantLabelFontSize:16,quadrantTextTopPadding:5,pointTextPadding:5,pointLabelFontSize:12,pointRadius:5,xAxisPosition:"top",yAxisPosition:"left",quadrantInternalBorderStrokeWidth:1,quadrantExternalBorderStrokeWidth:2},xyChart:{useMaxWidth:!0,width:700,height:500,titleFontSize:20,titlePadding:10,showTitle:!0,xAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},yAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:!0,labelFontSize:14,labelPadding:5,showTitle:!0,titleFontSize:16,titlePadding:5,showTick:!0,tickLength:5,tickWidth:2,showAxisLine:!0,axisLineWidth:2},chartOrientation:"vertical",plotReservedSpacePercent:50},requirement:{useMaxWidth:!0,rect_fill:"#f9f9f9",text_color:"#333",rect_border_size:"0.5px",rect_border_color:"#bbb",rect_min_width:200,rect_min_height:200,fontSize:14,rect_padding:10,line_height:20},mindmap:{useMaxWidth:!0,padding:10,maxNodeWidth:200},kanban:{useMaxWidth:!0,padding:8,sectionWidth:200,ticketBaseUrl:""},timeline:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:!1,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"],disableMulticolor:!1},gitGraph:{useMaxWidth:!0,titleTopMargin:25,diagramPadding:8,nodeLabel:{width:75,height:100,x:-25,y:0},mainBranchName:"main",mainBranchOrder:0,showCommitLabel:!0,showBranches:!0,rotateCommitLabel:!0,parallelCommits:!1,arrowMarkerAbsolute:!1},c4:{useMaxWidth:!0,diagramMarginX:50,diagramMarginY:10,c4ShapeMargin:50,c4ShapePadding:20,width:216,height:60,boxMargin:10,c4ShapeInRow:4,nextLinePaddingX:0,c4BoundaryInRow:2,personFontSize:14,personFontFamily:'"Open Sans", sans-serif',personFontWeight:"normal",external_personFontSize:14,external_personFontFamily:'"Open Sans", sans-serif',external_personFontWeight:"normal",systemFontSize:14,systemFontFamily:'"Open Sans", sans-serif',systemFontWeight:"normal",external_systemFontSize:14,external_systemFontFamily:'"Open Sans", sans-serif',external_systemFontWeight:"normal",system_dbFontSize:14,system_dbFontFamily:'"Open Sans", sans-serif',system_dbFontWeight:"normal",external_system_dbFontSize:14,external_system_dbFontFamily:'"Open Sans", sans-serif',external_system_dbFontWeight:"normal",system_queueFontSize:14,system_queueFontFamily:'"Open Sans", sans-serif',system_queueFontWeight:"normal",external_system_queueFontSize:14,external_system_queueFontFamily:'"Open Sans", sans-serif',external_system_queueFontWeight:"normal",boundaryFontSize:14,boundaryFontFamily:'"Open Sans", sans-serif',boundaryFontWeight:"normal",messageFontSize:12,messageFontFamily:'"Open Sans", sans-serif',messageFontWeight:"normal",containerFontSize:14,containerFontFamily:'"Open Sans", sans-serif',containerFontWeight:"normal",external_containerFontSize:14,external_containerFontFamily:'"Open Sans", sans-serif',external_containerFontWeight:"normal",container_dbFontSize:14,container_dbFontFamily:'"Open Sans", sans-serif',container_dbFontWeight:"normal",external_container_dbFontSize:14,external_container_dbFontFamily:'"Open Sans", sans-serif',external_container_dbFontWeight:"normal",container_queueFontSize:14,container_queueFontFamily:'"Open Sans", sans-serif',container_queueFontWeight:"normal",external_container_queueFontSize:14,external_container_queueFontFamily:'"Open Sans", sans-serif',external_container_queueFontWeight:"normal",componentFontSize:14,componentFontFamily:'"Open Sans", sans-serif',componentFontWeight:"normal",external_componentFontSize:14,external_componentFontFamily:'"Open Sans", sans-serif',external_componentFontWeight:"normal",component_dbFontSize:14,component_dbFontFamily:'"Open Sans", sans-serif',component_dbFontWeight:"normal",external_component_dbFontSize:14,external_component_dbFontFamily:'"Open Sans", sans-serif',external_component_dbFontWeight:"normal",component_queueFontSize:14,component_queueFontFamily:'"Open Sans", sans-serif',component_queueFontWeight:"normal",external_component_queueFontSize:14,external_component_queueFontFamily:'"Open Sans", sans-serif',external_component_queueFontWeight:"normal",wrap:!0,wrapPadding:10,person_bg_color:"#08427B",person_border_color:"#073B6F",external_person_bg_color:"#686868",external_person_border_color:"#8A8A8A",system_bg_color:"#1168BD",system_border_color:"#3C7FC0",system_db_bg_color:"#1168BD",system_db_border_color:"#3C7FC0",system_queue_bg_color:"#1168BD",system_queue_border_color:"#3C7FC0",external_system_bg_color:"#999999",external_system_border_color:"#8A8A8A",external_system_db_bg_color:"#999999",external_system_db_border_color:"#8A8A8A",external_system_queue_bg_color:"#999999",external_system_queue_border_color:"#8A8A8A",container_bg_color:"#438DD5",container_border_color:"#3C7FC0",container_db_bg_color:"#438DD5",container_db_border_color:"#3C7FC0",container_queue_bg_color:"#438DD5",container_queue_border_color:"#3C7FC0",external_container_bg_color:"#B3B3B3",external_container_border_color:"#A6A6A6",external_container_db_bg_color:"#B3B3B3",external_container_db_border_color:"#A6A6A6",external_container_queue_bg_color:"#B3B3B3",external_container_queue_border_color:"#A6A6A6",component_bg_color:"#85BBF0",component_border_color:"#78A8D8",component_db_bg_color:"#85BBF0",component_db_border_color:"#78A8D8",component_queue_bg_color:"#85BBF0",component_queue_border_color:"#78A8D8",external_component_bg_color:"#CCCCCC",external_component_border_color:"#BFBFBF",external_component_db_bg_color:"#CCCCCC",external_component_db_border_color:"#BFBFBF",external_component_queue_bg_color:"#CCCCCC",external_component_queue_border_color:"#BFBFBF"},sankey:{useMaxWidth:!0,width:600,height:400,linkColor:"gradient",nodeAlignment:"justify",showValues:!0,prefix:"",suffix:""},block:{useMaxWidth:!0,padding:8},packet:{useMaxWidth:!0,rowHeight:32,bitWidth:32,bitsPerRow:32,showBits:!0,paddingX:5,paddingY:5},architecture:{useMaxWidth:!0,padding:40,iconSize:80,fontSize:16},radar:{useMaxWidth:!0,width:600,height:600,marginTop:50,marginRight:50,marginBottom:50,marginLeft:50,axisScaleFactor:1,axisLabelFactor:1.05,curveTension:.17},theme:"default",look:"classic",handDrawnSeed:0,layout:"dagre",maxTextSize:5e4,maxEdges:500,darkMode:!1,fontFamily:'"trebuchet ms", verdana, arial, sans-serif;',logLevel:5,securityLevel:"strict",startOnLoad:!0,arrowMarkerAbsolute:!1,secure:["secure","securityLevel","startOnLoad","maxTextSize","suppressErrorRendering","maxEdges"],legacyMathML:!1,forceLegacyMathML:!1,deterministicIds:!1,fontSize:16,markdownAutoWrap:!0,suppressErrorRendering:!1},Lc={...Se,deterministicIDSeed:void 0,elk:{mergeEdges:!1,nodePlacementStrategy:"BRANDES_KOEPF"},themeCSS:void 0,themeVariables:Fe.default.getThemeVariables(),sequence:{...Se.sequence,messageFont:p(function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},"messageFont"),noteFont:p(function(){return{fontFamily:this.noteFontFamily,fontSize:this.noteFontSize,fontWeight:this.noteFontWeight}},"noteFont"),actorFont:p(function(){return{fontFamily:this.actorFontFamily,fontSize:this.actorFontSize,fontWeight:this.actorFontWeight}},"actorFont")},class:{hideEmptyMembersBox:!1},gantt:{...Se.gantt,tickInterval:void 0,useWidth:void 0},c4:{...Se.c4,useWidth:void 0,personFont:p(function(){return{fontFamily:this.personFontFamily,fontSize:this.personFontSize,fontWeight:this.personFontWeight}},"personFont"),external_personFont:p(function(){return{fontFamily:this.external_personFontFamily,fontSize:this.external_personFontSize,fontWeight:this.external_personFontWeight}},"external_personFont"),systemFont:p(function(){return{fontFamily:this.systemFontFamily,fontSize:this.systemFontSize,fontWeight:this.systemFontWeight}},"systemFont"),external_systemFont:p(function(){return{fontFamily:this.external_systemFontFamily,fontSize:this.external_systemFontSize,fontWeight:this.external_systemFontWeight}},"external_systemFont"),system_dbFont:p(function(){return{fontFamily:this.system_dbFontFamily,fontSize:this.system_dbFontSize,fontWeight:this.system_dbFontWeight}},"system_dbFont"),external_system_dbFont:p(function(){return{fontFamily:this.external_system_dbFontFamily,fontSize:this.external_system_dbFontSize,fontWeight:this.external_system_dbFontWeight}},"external_system_dbFont"),system_queueFont:p(function(){return{fontFamily:this.system_queueFontFamily,fontSize:this.system_queueFontSize,fontWeight:this.system_queueFontWeight}},"system_queueFont"),external_system_queueFont:p(function(){return{fontFamily:this.external_system_queueFontFamily,fontSize:this.external_system_queueFontSize,fontWeight:this.external_system_queueFontWeight}},"external_system_queueFont"),containerFont:p(function(){return{fontFamily:this.containerFontFamily,fontSize:this.containerFontSize,fontWeight:this.containerFontWeight}},"containerFont"),external_containerFont:p(function(){return{fontFamily:this.external_containerFontFamily,fontSize:this.external_containerFontSize,fontWeight:this.external_containerFontWeight}},"external_containerFont"),container_dbFont:p(function(){return{fontFamily:this.container_dbFontFamily,fontSize:this.container_dbFontSize,fontWeight:this.container_dbFontWeight}},"container_dbFont"),external_container_dbFont:p(function(){return{fontFamily:this.external_container_dbFontFamily,fontSize:this.external_container_dbFontSize,fontWeight:this.external_container_dbFontWeight}},"external_container_dbFont"),container_queueFont:p(function(){return{fontFamily:this.container_queueFontFamily,fontSize:this.container_queueFontSize,fontWeight:this.container_queueFontWeight}},"container_queueFont"),external_container_queueFont:p(function(){return{fontFamily:this.external_container_queueFontFamily,fontSize:this.external_container_queueFontSize,fontWeight:this.external_container_queueFontWeight}},"external_container_queueFont"),componentFont:p(function(){return{fontFamily:this.componentFontFamily,fontSize:this.componentFontSize,fontWeight:this.componentFontWeight}},"componentFont"),external_componentFont:p(function(){return{fontFamily:this.external_componentFontFamily,fontSize:this.external_componentFontSize,fontWeight:this.external_componentFontWeight}},"external_componentFont"),component_dbFont:p(function(){return{fontFamily:this.component_dbFontFamily,fontSize:this.component_dbFontSize,fontWeight:this.component_dbFontWeight}},"component_dbFont"),external_component_dbFont:p(function(){return{fontFamily:this.external_component_dbFontFamily,fontSize:this.external_component_dbFontSize,fontWeight:this.external_component_dbFontWeight}},"external_component_dbFont"),component_queueFont:p(function(){return{fontFamily:this.component_queueFontFamily,fontSize:this.component_queueFontSize,fontWeight:this.component_queueFontWeight}},"component_queueFont"),external_component_queueFont:p(function(){return{fontFamily:this.external_component_queueFontFamily,fontSize:this.external_component_queueFontSize,fontWeight:this.external_component_queueFontWeight}},"external_component_queueFont"),boundaryFont:p(function(){return{fontFamily:this.boundaryFontFamily,fontSize:this.boundaryFontSize,fontWeight:this.boundaryFontWeight}},"boundaryFont"),messageFont:p(function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}},"messageFont")},pie:{...Se.pie,useWidth:984},xyChart:{...Se.xyChart,useWidth:void 0},requirement:{...Se.requirement,useWidth:void 0},packet:{...Se.packet},radar:{...Se.radar}},Bc=p((e,t="")=>Object.keys(e).reduce((r,i)=>Array.isArray(e[i])?r:typeof e[i]=="object"&&e[i]!==null?[...r,t+i,...Bc(e[i],"")]:[...r,t+i],[]),"keyify"),km=new Set(Bc(Lc,"")),Ac=Lc,yn=p(e=>{if(D.debug("sanitizeDirective called with",e),!(typeof e!="object"||e==null)){if(Array.isArray(e)){e.forEach(t=>yn(t));return}for(const t of Object.keys(e)){if(D.debug("Checking key",t),t.startsWith("__")||t.includes("proto")||t.includes("constr")||!km.has(t)||e[t]==null){D.debug("sanitize deleting key: ",t),delete e[t];continue}if(typeof e[t]=="object"){D.debug("sanitizing object",t),yn(e[t]);continue}const r=["themeCSS","fontFamily","altFontFamily"];for(const i of r)t.includes(i)&&(D.debug("sanitizing css option",t),e[t]=vm(e[t]))}if(e.themeVariables)for(const t of Object.keys(e.themeVariables)){const r=e.themeVariables[t];r!=null&&r.match&&!r.match(/^[\d "#%(),.;A-Za-z]+$/)&&(e.themeVariables[t]="")}D.debug("After sanitization",e)}},"sanitizeDirective"),vm=p(e=>{let t=0,r=0;for(const i of e){if(t<r)return"{ /* ERROR: Unbalanced CSS */ }";i==="{"?t++:i==="}"&&r++}return t!==r?"{ /* ERROR: Unbalanced CSS */ }":e},"sanitizeCss"),Rr=Object.freeze(Ac),Ut=Mt({},Rr),Mc,Or=[],xi=Mt({},Rr),Kn=p((e,t)=>{let r=Mt({},e),i={};for(const n of t)$c(n),i=Mt(i,n);if(r=Mt(r,i),i.theme&&i.theme in Fe){const n=Mt({},Mc),a=Mt(n.themeVariables||{},i.themeVariables);r.theme&&r.theme in Fe&&(r.themeVariables=Fe[r.theme].getThemeVariables(a))}return xi=r,Dc(xi),xi},"updateCurrentConfig"),Sm=p(e=>(Ut=Mt({},Rr),Ut=Mt(Ut,e),e.theme&&Fe[e.theme]&&(Ut.themeVariables=Fe[e.theme].getThemeVariables(e.themeVariables)),Kn(Ut,Or),Ut),"setSiteConfig"),Tm=p(e=>{Mc=Mt({},e)},"saveConfigFromInitialize"),Lm=p(e=>(Ut=Mt(Ut,e),Kn(Ut,Or),Ut),"updateSiteConfig"),Ec=p(()=>Mt({},Ut),"getSiteConfig"),Fc=p(e=>(Dc(e),Mt(xi,e),Xt()),"setConfig"),Xt=p(()=>Mt({},xi),"getConfig"),$c=p(e=>{e&&(["secure",...Ut.secure??[]].forEach(t=>{Object.hasOwn(e,t)&&(D.debug(`Denied attempt to modify a secure key ${t}`,e[t]),delete e[t])}),Object.keys(e).forEach(t=>{t.startsWith("__")&&delete e[t]}),Object.keys(e).forEach(t=>{typeof e[t]=="string"&&(e[t].includes("<")||e[t].includes(">")||e[t].includes("url(data:"))&&delete e[t],typeof e[t]=="object"&&$c(e[t])}))},"sanitize"),Bm=p(e=>{var t;yn(e),e.fontFamily&&!((t=e.themeVariables)!=null&&t.fontFamily)&&(e.themeVariables={...e.themeVariables,fontFamily:e.fontFamily}),Or.push(e),Kn(Ut,Or)},"addDirective"),xn=p((e=Ut)=>{Or=[],Kn(e,Or)},"reset"),Am={LAZY_LOAD_DEPRECATED:"The configuration options lazyLoadedDiagrams and loadExternalDiagramsAtStartup are deprecated. Please use registerExternalDiagrams instead."},hl={},Mm=p(e=>{hl[e]||(D.warn(Am[e]),hl[e]=!0)},"issueWarning"),Dc=p(e=>{e&&(e.lazyLoadedDiagrams||e.loadExternalDiagramsAtStartup)&&Mm("LAZY_LOAD_DEPRECATED")},"checkConfig"),Ei=/<br\s*\/?>/gi,Em=p(e=>e?Ic(e).replace(/\\n/g,"#br#").split("#br#"):[""],"getRows"),Fm=(()=>{let e=!1;return()=>{e||(Rc(),e=!0)}})();function Rc(){const e="data-temp-href-target";$r.addHook("beforeSanitizeAttributes",t=>{t instanceof Element&&t.tagName==="A"&&t.hasAttribute("target")&&t.setAttribute(e,t.getAttribute("target")??"")}),$r.addHook("afterSanitizeAttributes",t=>{t instanceof Element&&t.tagName==="A"&&t.hasAttribute(e)&&(t.setAttribute("target",t.getAttribute(e)??""),t.removeAttribute(e),t.getAttribute("target")==="_blank"&&t.setAttribute("rel","noopener"))})}p(Rc,"setupDompurifyHooks");var Oc=p(e=>(Fm(),$r.sanitize(e)),"removeScript"),ul=p((e,t)=>{var r;if(((r=t.flowchart)==null?void 0:r.htmlLabels)!==!1){const i=t.securityLevel;i==="antiscript"||i==="strict"?e=Oc(e):i!=="loose"&&(e=Ic(e),e=e.replace(/</g,"&lt;").replace(/>/g,"&gt;"),e=e.replace(/=/g,"&equals;"),e=Om(e))}return e},"sanitizeMore"),nr=p((e,t)=>e&&(t.dompurifyConfig?e=$r.sanitize(ul(e,t),t.dompurifyConfig).toString():e=$r.sanitize(ul(e,t),{FORBID_TAGS:["style"]}).toString(),e),"sanitizeText"),$m=p((e,t)=>typeof e=="string"?nr(e,t):e.flat().map(r=>nr(r,t)),"sanitizeTextOrArray"),Dm=p(e=>Ei.test(e),"hasBreaks"),Rm=p(e=>e.split(Ei),"splitBreaks"),Om=p(e=>e.replace(/#br#/g,"<br/>"),"placeholderToBreak"),Ic=p(e=>e.replace(Ei,"#br#"),"breakToPlaceholder"),Im=p(e=>{let t="";return e&&(t=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,t=t.replaceAll(/\(/g,"\\("),t=t.replaceAll(/\)/g,"\\)")),t},"getUrl"),kt=p(e=>!(e===!1||["false","null","0"].includes(String(e).trim().toLowerCase())),"evaluate"),Pm=p(function(...e){const t=e.filter(r=>!isNaN(r));return Math.max(...t)},"getMax"),Nm=p(function(...e){const t=e.filter(r=>!isNaN(r));return Math.min(...t)},"getMin"),fl=p(function(e){const t=e.split(/(,)/),r=[];for(let i=0;i<t.length;i++){let n=t[i];if(n===","&&i>0&&i+1<t.length){const a=t[i-1],o=t[i+1];zm(a,o)&&(n=a+","+o,i++,r.pop())}r.push(Wm(n))}return r.join("")},"parseGenericTypes"),Ga=p((e,t)=>Math.max(0,e.split(t).length-1),"countOccurrence"),zm=p((e,t)=>{const r=Ga(e,"~"),i=Ga(t,"~");return r===1&&i===1},"shouldCombineSets"),Wm=p(e=>{const t=Ga(e,"~");let r=!1;if(t<=1)return e;t%2!==0&&e.startsWith("~")&&(e=e.substring(1),r=!0);const i=[...e];let n=i.indexOf("~"),a=i.lastIndexOf("~");for(;n!==-1&&a!==-1&&n!==a;)i[n]="<",i[a]=">",n=i.indexOf("~"),a=i.lastIndexOf("~");return r&&i.unshift("~"),i.join("")},"processSet"),dl=p(()=>window.MathMLElement!==void 0,"isMathMLSupported"),Va=/\$\$(.*)\$\$/g,Ir=p(e=>{var t;return(((t=e.match(Va))==null?void 0:t.length)??0)>0},"hasKatex"),Iv=p(async(e,t)=>{e=await Is(e,t);const r=document.createElement("div");r.innerHTML=e,r.id="katex-temp",r.style.visibility="hidden",r.style.position="absolute",r.style.top="0";const i=document.querySelector("body");i==null||i.insertAdjacentElement("beforeend",r);const n={width:r.clientWidth,height:r.clientHeight};return r.remove(),n},"calculateMathMLDimensions"),Is=p(async(e,t)=>{if(!Ir(e))return e;if(!(dl()||t.legacyMathML||t.forceLegacyMathML))return e.replace(Va,"MathML is unsupported in this environment.");const{default:r}=await pt(async()=>{const{default:n}=await import("./premium-DZfa0MBj.js").then(a=>a.dO);return{default:n}},__vite__mapDeps([0,1,2])),i=t.forceLegacyMathML||!dl()&&t.legacyMathML?"htmlAndMathml":"mathml";return e.split(Ei).map(n=>Ir(n)?`<div style="display: flex; align-items: center; justify-content: center; white-space: nowrap;">${n}</div>`:`<div>${n}</div>`).join("").replace(Va,(n,a)=>r.renderToString(a,{throwOnError:!0,displayMode:!0,output:i}).replace(/\n/g," ").replace(/<annotation.*<\/annotation>/g,""))},"renderKatex"),Yr={getRows:Em,sanitizeText:nr,sanitizeTextOrArray:$m,hasBreaks:Dm,splitBreaks:Rm,lineBreakRegex:Ei,removeScript:Oc,getUrl:Im,evaluate:kt,getMax:Pm,getMin:Nm},qm=p(function(e,t){for(let r of t)e.attr(r[0],r[1])},"d3Attrs"),Hm=p(function(e,t,r){let i=new Map;return r?(i.set("width","100%"),i.set("style",`max-width: ${t}px;`)):(i.set("height",e),i.set("width",t)),i},"calculateSvgSizeAttrs"),Pc=p(function(e,t,r,i){const n=Hm(t,r,i);qm(e,n)},"configureSvgSize"),Ym=p(function(e,t,r,i){const n=t.node().getBBox(),a=n.width,o=n.height;D.info(`SVG bounds: ${a}x${o}`,n);let s=0,c=0;D.info(`Graph bounds: ${s}x${c}`,e),s=a+r*2,c=o+r*2,D.info(`Calculated bounds: ${s}x${c}`),Pc(t,c,s,i);const l=`${n.x-r} ${n.y-r} ${n.width+2*r} ${n.height+2*r}`;t.attr("viewBox",l)},"setupGraphViewbox"),nn={},jm=p((e,t,r)=>{let i="";return e in nn&&nn[e]?i=nn[e](r):D.warn(`No theme found for ${e}`),` & {
    font-family: ${r.fontFamily};
    font-size: ${r.fontSize};
    fill: ${r.textColor}
  }
  @keyframes edge-animation-frame {
    from {
      stroke-dashoffset: 0;
    }
  }
  @keyframes dash {
    to {
      stroke-dashoffset: 0;
    }
  }
  & .edge-animation-slow {
    stroke-dasharray: 9,5 !important;
    stroke-dashoffset: 900;
    animation: dash 50s linear infinite;
    stroke-linecap: round;
  }
  & .edge-animation-fast {
    stroke-dasharray: 9,5 !important;
    stroke-dashoffset: 900;
    animation: dash 20s linear infinite;
    stroke-linecap: round;
  }
  /* Classes common for multiple diagrams */

  & .error-icon {
    fill: ${r.errorBkgColor};
  }
  & .error-text {
    fill: ${r.errorTextColor};
    stroke: ${r.errorTextColor};
  }

  & .edge-thickness-normal {
    stroke-width: 1px;
  }
  & .edge-thickness-thick {
    stroke-width: 3.5px
  }
  & .edge-pattern-solid {
    stroke-dasharray: 0;
  }
  & .edge-thickness-invisible {
    stroke-width: 0;
    fill: none;
  }
  & .edge-pattern-dashed{
    stroke-dasharray: 3;
  }
  .edge-pattern-dotted {
    stroke-dasharray: 2;
  }

  & .marker {
    fill: ${r.lineColor};
    stroke: ${r.lineColor};
  }
  & .marker.cross {
    stroke: ${r.lineColor};
  }

  & svg {
    font-family: ${r.fontFamily};
    font-size: ${r.fontSize};
  }
   & p {
    margin: 0
   }

  ${i}

  ${t}
`},"getStyles"),Um=p((e,t)=>{t!==void 0&&(nn[e]=t)},"addStylesForDiagram"),Gm=jm,Nc={};hm(Nc,{clear:()=>Vm,getAccDescription:()=>Qm,getAccTitle:()=>Zm,getDiagramTitle:()=>t0,setAccDescription:()=>Km,setAccTitle:()=>Xm,setDiagramTitle:()=>Jm});var Ps="",Ns="",zs="",Ws=p(e=>nr(e,Xt()),"sanitizeText"),Vm=p(()=>{Ps="",zs="",Ns=""},"clear"),Xm=p(e=>{Ps=Ws(e).replace(/^\s+/g,"")},"setAccTitle"),Zm=p(()=>Ps,"getAccTitle"),Km=p(e=>{zs=Ws(e).replace(/\n\s+/g,`
`)},"setAccDescription"),Qm=p(()=>zs,"getAccDescription"),Jm=p(e=>{Ns=Ws(e)},"setDiagramTitle"),t0=p(()=>Ns,"getDiagramTitle"),pl=D,e0=Rs,ut=Xt,Pv=Fc,Nv=Rr,qs=p(e=>nr(e,ut()),"sanitizeText"),r0=Ym,i0=p(()=>Nc,"getCommonDb"),bn={},_n=p((e,t,r)=>{var i;bn[e]&&pl.warn(`Diagram with id ${e} already registered. Overwriting.`),bn[e]=t,r&&Tc(e,r),Um(e,t.styles),(i=t.injectUtils)==null||i.call(t,pl,e0,ut,qs,r0,i0(),()=>{})},"registerDiagram"),Xa=p(e=>{if(e in bn)return bn[e];throw new n0(e)},"getDiagram"),Er,n0=(Er=class extends Error{constructor(t){super(`Diagram ${t} not found.`)}},p(Er,"DiagramNotFoundError"),Er);function Hs(e){return typeof e>"u"||e===null}p(Hs,"isNothing");function zc(e){return typeof e=="object"&&e!==null}p(zc,"isObject");function Wc(e){return Array.isArray(e)?e:Hs(e)?[]:[e]}p(Wc,"toArray");function qc(e,t){var r,i,n,a;if(t)for(a=Object.keys(t),r=0,i=a.length;r<i;r+=1)n=a[r],e[n]=t[n];return e}p(qc,"extend");function Hc(e,t){var r="",i;for(i=0;i<t;i+=1)r+=e;return r}p(Hc,"repeat");function Yc(e){return e===0&&Number.NEGATIVE_INFINITY===1/e}p(Yc,"isNegativeZero");var a0=Hs,s0=zc,o0=Wc,l0=Hc,c0=Yc,h0=qc,wt={isNothing:a0,isObject:s0,toArray:o0,repeat:l0,isNegativeZero:c0,extend:h0};function Ys(e,t){var r="",i=e.reason||"(unknown reason)";return e.mark?(e.mark.name&&(r+='in "'+e.mark.name+'" '),r+="("+(e.mark.line+1)+":"+(e.mark.column+1)+")",!t&&e.mark.snippet&&(r+=`

`+e.mark.snippet),i+" "+r):i}p(Ys,"formatError");function Pr(e,t){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=t,this.message=Ys(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack||""}p(Pr,"YAMLException$1");Pr.prototype=Object.create(Error.prototype);Pr.prototype.constructor=Pr;Pr.prototype.toString=p(function(t){return this.name+": "+Ys(this,t)},"toString");var Gt=Pr;function an(e,t,r,i,n){var a="",o="",s=Math.floor(n/2)-1;return i-t>s&&(a=" ... ",t=i-s+a.length),r-i>s&&(o=" ...",r=i+s-o.length),{str:a+e.slice(t,r).replace(/\t/g,"→")+o,pos:i-t+a.length}}p(an,"getLine");function sn(e,t){return wt.repeat(" ",t-e.length)+e}p(sn,"padStart");function jc(e,t){if(t=Object.create(t||null),!e.buffer)return null;t.maxLength||(t.maxLength=79),typeof t.indent!="number"&&(t.indent=1),typeof t.linesBefore!="number"&&(t.linesBefore=3),typeof t.linesAfter!="number"&&(t.linesAfter=2);for(var r=/\r?\n|\r|\0/g,i=[0],n=[],a,o=-1;a=r.exec(e.buffer);)n.push(a.index),i.push(a.index+a[0].length),e.position<=a.index&&o<0&&(o=i.length-2);o<0&&(o=i.length-1);var s="",c,l,h=Math.min(e.line+t.linesAfter,n.length).toString().length,u=t.maxLength-(t.indent+h+3);for(c=1;c<=t.linesBefore&&!(o-c<0);c++)l=an(e.buffer,i[o-c],n[o-c],e.position-(i[o]-i[o-c]),u),s=wt.repeat(" ",t.indent)+sn((e.line-c+1).toString(),h)+" | "+l.str+`
`+s;for(l=an(e.buffer,i[o],n[o],e.position,u),s+=wt.repeat(" ",t.indent)+sn((e.line+1).toString(),h)+" | "+l.str+`
`,s+=wt.repeat("-",t.indent+h+3+l.pos)+`^
`,c=1;c<=t.linesAfter&&!(o+c>=n.length);c++)l=an(e.buffer,i[o+c],n[o+c],e.position-(i[o]-i[o+c]),u),s+=wt.repeat(" ",t.indent)+sn((e.line+c+1).toString(),h)+" | "+l.str+`
`;return s.replace(/\n$/,"")}p(jc,"makeSnippet");var u0=jc,f0=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],d0=["scalar","sequence","mapping"];function Uc(e){var t={};return e!==null&&Object.keys(e).forEach(function(r){e[r].forEach(function(i){t[String(i)]=r})}),t}p(Uc,"compileStyleAliases");function Gc(e,t){if(t=t||{},Object.keys(t).forEach(function(r){if(f0.indexOf(r)===-1)throw new Gt('Unknown option "'+r+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(r){return r},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=Uc(t.styleAliases||null),d0.indexOf(this.kind)===-1)throw new Gt('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}p(Gc,"Type$1");var Rt=Gc;function Za(e,t){var r=[];return e[t].forEach(function(i){var n=r.length;r.forEach(function(a,o){a.tag===i.tag&&a.kind===i.kind&&a.multi===i.multi&&(n=o)}),r[n]=i}),r}p(Za,"compileList");function Vc(){var e={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},t,r;function i(n){n.multi?(e.multi[n.kind].push(n),e.multi.fallback.push(n)):e[n.kind][n.tag]=e.fallback[n.tag]=n}for(p(i,"collectType"),t=0,r=arguments.length;t<r;t+=1)arguments[t].forEach(i);return e}p(Vc,"compileMap");function Cn(e){return this.extend(e)}p(Cn,"Schema$1");Cn.prototype.extend=p(function(t){var r=[],i=[];if(t instanceof Rt)i.push(t);else if(Array.isArray(t))i=i.concat(t);else if(t&&(Array.isArray(t.implicit)||Array.isArray(t.explicit)))t.implicit&&(r=r.concat(t.implicit)),t.explicit&&(i=i.concat(t.explicit));else throw new Gt("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");r.forEach(function(a){if(!(a instanceof Rt))throw new Gt("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(a.loadKind&&a.loadKind!=="scalar")throw new Gt("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(a.multi)throw new Gt("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}),i.forEach(function(a){if(!(a instanceof Rt))throw new Gt("Specified list of YAML types (or a single Type object) contains a non-Type object.")});var n=Object.create(Cn.prototype);return n.implicit=(this.implicit||[]).concat(r),n.explicit=(this.explicit||[]).concat(i),n.compiledImplicit=Za(n,"implicit"),n.compiledExplicit=Za(n,"explicit"),n.compiledTypeMap=Vc(n.compiledImplicit,n.compiledExplicit),n},"extend");var p0=Cn,g0=new Rt("tag:yaml.org,2002:str",{kind:"scalar",construct:p(function(e){return e!==null?e:""},"construct")}),m0=new Rt("tag:yaml.org,2002:seq",{kind:"sequence",construct:p(function(e){return e!==null?e:[]},"construct")}),y0=new Rt("tag:yaml.org,2002:map",{kind:"mapping",construct:p(function(e){return e!==null?e:{}},"construct")}),x0=new p0({explicit:[g0,m0,y0]});function Xc(e){if(e===null)return!0;var t=e.length;return t===1&&e==="~"||t===4&&(e==="null"||e==="Null"||e==="NULL")}p(Xc,"resolveYamlNull");function Zc(){return null}p(Zc,"constructYamlNull");function Kc(e){return e===null}p(Kc,"isNull");var b0=new Rt("tag:yaml.org,2002:null",{kind:"scalar",resolve:Xc,construct:Zc,predicate:Kc,represent:{canonical:p(function(){return"~"},"canonical"),lowercase:p(function(){return"null"},"lowercase"),uppercase:p(function(){return"NULL"},"uppercase"),camelcase:p(function(){return"Null"},"camelcase"),empty:p(function(){return""},"empty")},defaultStyle:"lowercase"});function Qc(e){if(e===null)return!1;var t=e.length;return t===4&&(e==="true"||e==="True"||e==="TRUE")||t===5&&(e==="false"||e==="False"||e==="FALSE")}p(Qc,"resolveYamlBoolean");function Jc(e){return e==="true"||e==="True"||e==="TRUE"}p(Jc,"constructYamlBoolean");function th(e){return Object.prototype.toString.call(e)==="[object Boolean]"}p(th,"isBoolean");var _0=new Rt("tag:yaml.org,2002:bool",{kind:"scalar",resolve:Qc,construct:Jc,predicate:th,represent:{lowercase:p(function(e){return e?"true":"false"},"lowercase"),uppercase:p(function(e){return e?"TRUE":"FALSE"},"uppercase"),camelcase:p(function(e){return e?"True":"False"},"camelcase")},defaultStyle:"lowercase"});function eh(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}p(eh,"isHexCode");function rh(e){return 48<=e&&e<=55}p(rh,"isOctCode");function ih(e){return 48<=e&&e<=57}p(ih,"isDecCode");function nh(e){if(e===null)return!1;var t=e.length,r=0,i=!1,n;if(!t)return!1;if(n=e[r],(n==="-"||n==="+")&&(n=e[++r]),n==="0"){if(r+1===t)return!0;if(n=e[++r],n==="b"){for(r++;r<t;r++)if(n=e[r],n!=="_"){if(n!=="0"&&n!=="1")return!1;i=!0}return i&&n!=="_"}if(n==="x"){for(r++;r<t;r++)if(n=e[r],n!=="_"){if(!eh(e.charCodeAt(r)))return!1;i=!0}return i&&n!=="_"}if(n==="o"){for(r++;r<t;r++)if(n=e[r],n!=="_"){if(!rh(e.charCodeAt(r)))return!1;i=!0}return i&&n!=="_"}}if(n==="_")return!1;for(;r<t;r++)if(n=e[r],n!=="_"){if(!ih(e.charCodeAt(r)))return!1;i=!0}return!(!i||n==="_")}p(nh,"resolveYamlInteger");function ah(e){var t=e,r=1,i;if(t.indexOf("_")!==-1&&(t=t.replace(/_/g,"")),i=t[0],(i==="-"||i==="+")&&(i==="-"&&(r=-1),t=t.slice(1),i=t[0]),t==="0")return 0;if(i==="0"){if(t[1]==="b")return r*parseInt(t.slice(2),2);if(t[1]==="x")return r*parseInt(t.slice(2),16);if(t[1]==="o")return r*parseInt(t.slice(2),8)}return r*parseInt(t,10)}p(ah,"constructYamlInteger");function sh(e){return Object.prototype.toString.call(e)==="[object Number]"&&e%1===0&&!wt.isNegativeZero(e)}p(sh,"isInteger");var C0=new Rt("tag:yaml.org,2002:int",{kind:"scalar",resolve:nh,construct:ah,predicate:sh,represent:{binary:p(function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},"binary"),octal:p(function(e){return e>=0?"0o"+e.toString(8):"-0o"+e.toString(8).slice(1)},"octal"),decimal:p(function(e){return e.toString(10)},"decimal"),hexadecimal:p(function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)},"hexadecimal")},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),w0=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function oh(e){return!(e===null||!w0.test(e)||e[e.length-1]==="_")}p(oh,"resolveYamlFloat");function lh(e){var t,r;return t=e.replace(/_/g,"").toLowerCase(),r=t[0]==="-"?-1:1,"+-".indexOf(t[0])>=0&&(t=t.slice(1)),t===".inf"?r===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:t===".nan"?NaN:r*parseFloat(t,10)}p(lh,"constructYamlFloat");var k0=/^[-+]?[0-9]+e/;function ch(e,t){var r;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(wt.isNegativeZero(e))return"-0.0";return r=e.toString(10),k0.test(r)?r.replace("e",".e"):r}p(ch,"representYamlFloat");function hh(e){return Object.prototype.toString.call(e)==="[object Number]"&&(e%1!==0||wt.isNegativeZero(e))}p(hh,"isFloat");var v0=new Rt("tag:yaml.org,2002:float",{kind:"scalar",resolve:oh,construct:lh,predicate:hh,represent:ch,defaultStyle:"lowercase"}),uh=x0.extend({implicit:[b0,_0,C0,v0]}),S0=uh,fh=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),dh=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function ph(e){return e===null?!1:fh.exec(e)!==null||dh.exec(e)!==null}p(ph,"resolveYamlTimestamp");function gh(e){var t,r,i,n,a,o,s,c=0,l=null,h,u,f;if(t=fh.exec(e),t===null&&(t=dh.exec(e)),t===null)throw new Error("Date resolve error");if(r=+t[1],i=+t[2]-1,n=+t[3],!t[4])return new Date(Date.UTC(r,i,n));if(a=+t[4],o=+t[5],s=+t[6],t[7]){for(c=t[7].slice(0,3);c.length<3;)c+="0";c=+c}return t[9]&&(h=+t[10],u=+(t[11]||0),l=(h*60+u)*6e4,t[9]==="-"&&(l=-l)),f=new Date(Date.UTC(r,i,n,a,o,s,c)),l&&f.setTime(f.getTime()-l),f}p(gh,"constructYamlTimestamp");function mh(e){return e.toISOString()}p(mh,"representYamlTimestamp");var T0=new Rt("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:ph,construct:gh,instanceOf:Date,represent:mh});function yh(e){return e==="<<"||e===null}p(yh,"resolveYamlMerge");var L0=new Rt("tag:yaml.org,2002:merge",{kind:"scalar",resolve:yh}),js=`ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
\r`;function xh(e){if(e===null)return!1;var t,r,i=0,n=e.length,a=js;for(r=0;r<n;r++)if(t=a.indexOf(e.charAt(r)),!(t>64)){if(t<0)return!1;i+=6}return i%8===0}p(xh,"resolveYamlBinary");function bh(e){var t,r,i=e.replace(/[\r\n=]/g,""),n=i.length,a=js,o=0,s=[];for(t=0;t<n;t++)t%4===0&&t&&(s.push(o>>16&255),s.push(o>>8&255),s.push(o&255)),o=o<<6|a.indexOf(i.charAt(t));return r=n%4*6,r===0?(s.push(o>>16&255),s.push(o>>8&255),s.push(o&255)):r===18?(s.push(o>>10&255),s.push(o>>2&255)):r===12&&s.push(o>>4&255),new Uint8Array(s)}p(bh,"constructYamlBinary");function _h(e){var t="",r=0,i,n,a=e.length,o=js;for(i=0;i<a;i++)i%3===0&&i&&(t+=o[r>>18&63],t+=o[r>>12&63],t+=o[r>>6&63],t+=o[r&63]),r=(r<<8)+e[i];return n=a%3,n===0?(t+=o[r>>18&63],t+=o[r>>12&63],t+=o[r>>6&63],t+=o[r&63]):n===2?(t+=o[r>>10&63],t+=o[r>>4&63],t+=o[r<<2&63],t+=o[64]):n===1&&(t+=o[r>>2&63],t+=o[r<<4&63],t+=o[64],t+=o[64]),t}p(_h,"representYamlBinary");function Ch(e){return Object.prototype.toString.call(e)==="[object Uint8Array]"}p(Ch,"isBinary");var B0=new Rt("tag:yaml.org,2002:binary",{kind:"scalar",resolve:xh,construct:bh,predicate:Ch,represent:_h}),A0=Object.prototype.hasOwnProperty,M0=Object.prototype.toString;function wh(e){if(e===null)return!0;var t=[],r,i,n,a,o,s=e;for(r=0,i=s.length;r<i;r+=1){if(n=s[r],o=!1,M0.call(n)!=="[object Object]")return!1;for(a in n)if(A0.call(n,a))if(!o)o=!0;else return!1;if(!o)return!1;if(t.indexOf(a)===-1)t.push(a);else return!1}return!0}p(wh,"resolveYamlOmap");function kh(e){return e!==null?e:[]}p(kh,"constructYamlOmap");var E0=new Rt("tag:yaml.org,2002:omap",{kind:"sequence",resolve:wh,construct:kh}),F0=Object.prototype.toString;function vh(e){if(e===null)return!0;var t,r,i,n,a,o=e;for(a=new Array(o.length),t=0,r=o.length;t<r;t+=1){if(i=o[t],F0.call(i)!=="[object Object]"||(n=Object.keys(i),n.length!==1))return!1;a[t]=[n[0],i[n[0]]]}return!0}p(vh,"resolveYamlPairs");function Sh(e){if(e===null)return[];var t,r,i,n,a,o=e;for(a=new Array(o.length),t=0,r=o.length;t<r;t+=1)i=o[t],n=Object.keys(i),a[t]=[n[0],i[n[0]]];return a}p(Sh,"constructYamlPairs");var $0=new Rt("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:vh,construct:Sh}),D0=Object.prototype.hasOwnProperty;function Th(e){if(e===null)return!0;var t,r=e;for(t in r)if(D0.call(r,t)&&r[t]!==null)return!1;return!0}p(Th,"resolveYamlSet");function Lh(e){return e!==null?e:{}}p(Lh,"constructYamlSet");var R0=new Rt("tag:yaml.org,2002:set",{kind:"mapping",resolve:Th,construct:Lh}),Bh=S0.extend({implicit:[T0,L0],explicit:[B0,E0,$0,R0]}),qe=Object.prototype.hasOwnProperty,wn=1,Ah=2,Mh=3,kn=4,Ma=1,O0=2,gl=3,I0=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,P0=/[\x85\u2028\u2029]/,N0=/[,\[\]\{\}]/,Eh=/^(?:!|!!|![a-z\-]+!)$/i,Fh=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function Ka(e){return Object.prototype.toString.call(e)}p(Ka,"_class");function le(e){return e===10||e===13}p(le,"is_EOL");function We(e){return e===9||e===32}p(We,"is_WHITE_SPACE");function Nt(e){return e===9||e===32||e===10||e===13}p(Nt,"is_WS_OR_EOL");function Qe(e){return e===44||e===91||e===93||e===123||e===125}p(Qe,"is_FLOW_INDICATOR");function $h(e){var t;return 48<=e&&e<=57?e-48:(t=e|32,97<=t&&t<=102?t-97+10:-1)}p($h,"fromHexCode");function Dh(e){return e===120?2:e===117?4:e===85?8:0}p(Dh,"escapedHexLen");function Rh(e){return 48<=e&&e<=57?e-48:-1}p(Rh,"fromDecimalCode");function Qa(e){return e===48?"\0":e===97?"\x07":e===98?"\b":e===116||e===9?"	":e===110?`
`:e===118?"\v":e===102?"\f":e===114?"\r":e===101?"\x1B":e===32?" ":e===34?'"':e===47?"/":e===92?"\\":e===78?"":e===95?" ":e===76?"\u2028":e===80?"\u2029":""}p(Qa,"simpleEscapeSequence");function Oh(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}p(Oh,"charFromCodepoint");var Ih=new Array(256),Ph=new Array(256);for(Ve=0;Ve<256;Ve++)Ih[Ve]=Qa(Ve)?1:0,Ph[Ve]=Qa(Ve);var Ve;function Nh(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||Bh,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}p(Nh,"State$1");function Us(e,t){var r={name:e.filename,buffer:e.input.slice(0,-1),position:e.position,line:e.line,column:e.position-e.lineStart};return r.snippet=u0(r),new Gt(t,r)}p(Us,"generateError");function Z(e,t){throw Us(e,t)}p(Z,"throwError");function _i(e,t){e.onWarning&&e.onWarning.call(null,Us(e,t))}p(_i,"throwWarning");var ml={YAML:p(function(t,r,i){var n,a,o;t.version!==null&&Z(t,"duplication of %YAML directive"),i.length!==1&&Z(t,"YAML directive accepts exactly one argument"),n=/^([0-9]+)\.([0-9]+)$/.exec(i[0]),n===null&&Z(t,"ill-formed argument of the YAML directive"),a=parseInt(n[1],10),o=parseInt(n[2],10),a!==1&&Z(t,"unacceptable YAML version of the document"),t.version=i[0],t.checkLineBreaks=o<2,o!==1&&o!==2&&_i(t,"unsupported YAML version of the document")},"handleYamlDirective"),TAG:p(function(t,r,i){var n,a;i.length!==2&&Z(t,"TAG directive accepts exactly two arguments"),n=i[0],a=i[1],Eh.test(n)||Z(t,"ill-formed tag handle (first argument) of the TAG directive"),qe.call(t.tagMap,n)&&Z(t,'there is a previously declared suffix for "'+n+'" tag handle'),Fh.test(a)||Z(t,"ill-formed tag prefix (second argument) of the TAG directive");try{a=decodeURIComponent(a)}catch{Z(t,"tag prefix is malformed: "+a)}t.tagMap[n]=a},"handleTagDirective")};function $e(e,t,r,i){var n,a,o,s;if(t<r){if(s=e.input.slice(t,r),i)for(n=0,a=s.length;n<a;n+=1)o=s.charCodeAt(n),o===9||32<=o&&o<=1114111||Z(e,"expected valid JSON character");else I0.test(s)&&Z(e,"the stream contains non-printable characters");e.result+=s}}p($e,"captureSegment");function Ja(e,t,r,i){var n,a,o,s;for(wt.isObject(r)||Z(e,"cannot merge mappings; the provided source object is unacceptable"),n=Object.keys(r),o=0,s=n.length;o<s;o+=1)a=n[o],qe.call(t,a)||(t[a]=r[a],i[a]=!0)}p(Ja,"mergeMappings");function Je(e,t,r,i,n,a,o,s,c){var l,h;if(Array.isArray(n))for(n=Array.prototype.slice.call(n),l=0,h=n.length;l<h;l+=1)Array.isArray(n[l])&&Z(e,"nested arrays are not supported inside keys"),typeof n=="object"&&Ka(n[l])==="[object Object]"&&(n[l]="[object Object]");if(typeof n=="object"&&Ka(n)==="[object Object]"&&(n="[object Object]"),n=String(n),t===null&&(t={}),i==="tag:yaml.org,2002:merge")if(Array.isArray(a))for(l=0,h=a.length;l<h;l+=1)Ja(e,t,a[l],r);else Ja(e,t,a,r);else!e.json&&!qe.call(r,n)&&qe.call(t,n)&&(e.line=o||e.line,e.lineStart=s||e.lineStart,e.position=c||e.position,Z(e,"duplicated mapping key")),n==="__proto__"?Object.defineProperty(t,n,{configurable:!0,enumerable:!0,writable:!0,value:a}):t[n]=a,delete r[n];return t}p(Je,"storeMappingPair");function Qn(e){var t;t=e.input.charCodeAt(e.position),t===10?e.position++:t===13?(e.position++,e.input.charCodeAt(e.position)===10&&e.position++):Z(e,"a line break is expected"),e.line+=1,e.lineStart=e.position,e.firstTabInLine=-1}p(Qn,"readLineBreak");function bt(e,t,r){for(var i=0,n=e.input.charCodeAt(e.position);n!==0;){for(;We(n);)n===9&&e.firstTabInLine===-1&&(e.firstTabInLine=e.position),n=e.input.charCodeAt(++e.position);if(t&&n===35)do n=e.input.charCodeAt(++e.position);while(n!==10&&n!==13&&n!==0);if(le(n))for(Qn(e),n=e.input.charCodeAt(e.position),i++,e.lineIndent=0;n===32;)e.lineIndent++,n=e.input.charCodeAt(++e.position);else break}return r!==-1&&i!==0&&e.lineIndent<r&&_i(e,"deficient indentation"),i}p(bt,"skipSeparationSpace");function Fi(e){var t=e.position,r;return r=e.input.charCodeAt(t),!!((r===45||r===46)&&r===e.input.charCodeAt(t+1)&&r===e.input.charCodeAt(t+2)&&(t+=3,r=e.input.charCodeAt(t),r===0||Nt(r)))}p(Fi,"testDocumentSeparator");function Jn(e,t){t===1?e.result+=" ":t>1&&(e.result+=wt.repeat(`
`,t-1))}p(Jn,"writeFoldedLines");function zh(e,t,r){var i,n,a,o,s,c,l,h,u=e.kind,f=e.result,d;if(d=e.input.charCodeAt(e.position),Nt(d)||Qe(d)||d===35||d===38||d===42||d===33||d===124||d===62||d===39||d===34||d===37||d===64||d===96||(d===63||d===45)&&(n=e.input.charCodeAt(e.position+1),Nt(n)||r&&Qe(n)))return!1;for(e.kind="scalar",e.result="",a=o=e.position,s=!1;d!==0;){if(d===58){if(n=e.input.charCodeAt(e.position+1),Nt(n)||r&&Qe(n))break}else if(d===35){if(i=e.input.charCodeAt(e.position-1),Nt(i))break}else{if(e.position===e.lineStart&&Fi(e)||r&&Qe(d))break;if(le(d))if(c=e.line,l=e.lineStart,h=e.lineIndent,bt(e,!1,-1),e.lineIndent>=t){s=!0,d=e.input.charCodeAt(e.position);continue}else{e.position=o,e.line=c,e.lineStart=l,e.lineIndent=h;break}}s&&($e(e,a,o,!1),Jn(e,e.line-c),a=o=e.position,s=!1),We(d)||(o=e.position+1),d=e.input.charCodeAt(++e.position)}return $e(e,a,o,!1),e.result?!0:(e.kind=u,e.result=f,!1)}p(zh,"readPlainScalar");function Wh(e,t){var r,i,n;if(r=e.input.charCodeAt(e.position),r!==39)return!1;for(e.kind="scalar",e.result="",e.position++,i=n=e.position;(r=e.input.charCodeAt(e.position))!==0;)if(r===39)if($e(e,i,e.position,!0),r=e.input.charCodeAt(++e.position),r===39)i=e.position,e.position++,n=e.position;else return!0;else le(r)?($e(e,i,n,!0),Jn(e,bt(e,!1,t)),i=n=e.position):e.position===e.lineStart&&Fi(e)?Z(e,"unexpected end of the document within a single quoted scalar"):(e.position++,n=e.position);Z(e,"unexpected end of the stream within a single quoted scalar")}p(Wh,"readSingleQuotedScalar");function qh(e,t){var r,i,n,a,o,s;if(s=e.input.charCodeAt(e.position),s!==34)return!1;for(e.kind="scalar",e.result="",e.position++,r=i=e.position;(s=e.input.charCodeAt(e.position))!==0;){if(s===34)return $e(e,r,e.position,!0),e.position++,!0;if(s===92){if($e(e,r,e.position,!0),s=e.input.charCodeAt(++e.position),le(s))bt(e,!1,t);else if(s<256&&Ih[s])e.result+=Ph[s],e.position++;else if((o=Dh(s))>0){for(n=o,a=0;n>0;n--)s=e.input.charCodeAt(++e.position),(o=$h(s))>=0?a=(a<<4)+o:Z(e,"expected hexadecimal character");e.result+=Oh(a),e.position++}else Z(e,"unknown escape sequence");r=i=e.position}else le(s)?($e(e,r,i,!0),Jn(e,bt(e,!1,t)),r=i=e.position):e.position===e.lineStart&&Fi(e)?Z(e,"unexpected end of the document within a double quoted scalar"):(e.position++,i=e.position)}Z(e,"unexpected end of the stream within a double quoted scalar")}p(qh,"readDoubleQuotedScalar");function Hh(e,t){var r=!0,i,n,a,o=e.tag,s,c=e.anchor,l,h,u,f,d,g=Object.create(null),m,y,x,b;if(b=e.input.charCodeAt(e.position),b===91)h=93,d=!1,s=[];else if(b===123)h=125,d=!0,s={};else return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=s),b=e.input.charCodeAt(++e.position);b!==0;){if(bt(e,!0,t),b=e.input.charCodeAt(e.position),b===h)return e.position++,e.tag=o,e.anchor=c,e.kind=d?"mapping":"sequence",e.result=s,!0;r?b===44&&Z(e,"expected the node content, but found ','"):Z(e,"missed comma between flow collection entries"),y=m=x=null,u=f=!1,b===63&&(l=e.input.charCodeAt(e.position+1),Nt(l)&&(u=f=!0,e.position++,bt(e,!0,t))),i=e.line,n=e.lineStart,a=e.position,ar(e,t,wn,!1,!0),y=e.tag,m=e.result,bt(e,!0,t),b=e.input.charCodeAt(e.position),(f||e.line===i)&&b===58&&(u=!0,b=e.input.charCodeAt(++e.position),bt(e,!0,t),ar(e,t,wn,!1,!0),x=e.result),d?Je(e,s,g,y,m,x,i,n,a):u?s.push(Je(e,null,g,y,m,x,i,n,a)):s.push(m),bt(e,!0,t),b=e.input.charCodeAt(e.position),b===44?(r=!0,b=e.input.charCodeAt(++e.position)):r=!1}Z(e,"unexpected end of the stream within a flow collection")}p(Hh,"readFlowCollection");function Yh(e,t){var r,i,n=Ma,a=!1,o=!1,s=t,c=0,l=!1,h,u;if(u=e.input.charCodeAt(e.position),u===124)i=!1;else if(u===62)i=!0;else return!1;for(e.kind="scalar",e.result="";u!==0;)if(u=e.input.charCodeAt(++e.position),u===43||u===45)Ma===n?n=u===43?gl:O0:Z(e,"repeat of a chomping mode identifier");else if((h=Rh(u))>=0)h===0?Z(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):o?Z(e,"repeat of an indentation width identifier"):(s=t+h-1,o=!0);else break;if(We(u)){do u=e.input.charCodeAt(++e.position);while(We(u));if(u===35)do u=e.input.charCodeAt(++e.position);while(!le(u)&&u!==0)}for(;u!==0;){for(Qn(e),e.lineIndent=0,u=e.input.charCodeAt(e.position);(!o||e.lineIndent<s)&&u===32;)e.lineIndent++,u=e.input.charCodeAt(++e.position);if(!o&&e.lineIndent>s&&(s=e.lineIndent),le(u)){c++;continue}if(e.lineIndent<s){n===gl?e.result+=wt.repeat(`
`,a?1+c:c):n===Ma&&a&&(e.result+=`
`);break}for(i?We(u)?(l=!0,e.result+=wt.repeat(`
`,a?1+c:c)):l?(l=!1,e.result+=wt.repeat(`
`,c+1)):c===0?a&&(e.result+=" "):e.result+=wt.repeat(`
`,c):e.result+=wt.repeat(`
`,a?1+c:c),a=!0,o=!0,c=0,r=e.position;!le(u)&&u!==0;)u=e.input.charCodeAt(++e.position);$e(e,r,e.position,!1)}return!0}p(Yh,"readBlockScalar");function ts(e,t){var r,i=e.tag,n=e.anchor,a=[],o,s=!1,c;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=a),c=e.input.charCodeAt(e.position);c!==0&&(e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,Z(e,"tab characters must not be used in indentation")),!(c!==45||(o=e.input.charCodeAt(e.position+1),!Nt(o))));){if(s=!0,e.position++,bt(e,!0,-1)&&e.lineIndent<=t){a.push(null),c=e.input.charCodeAt(e.position);continue}if(r=e.line,ar(e,t,Mh,!1,!0),a.push(e.result),bt(e,!0,-1),c=e.input.charCodeAt(e.position),(e.line===r||e.lineIndent>t)&&c!==0)Z(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break}return s?(e.tag=i,e.anchor=n,e.kind="sequence",e.result=a,!0):!1}p(ts,"readBlockSequence");function jh(e,t,r){var i,n,a,o,s,c,l=e.tag,h=e.anchor,u={},f=Object.create(null),d=null,g=null,m=null,y=!1,x=!1,b;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=u),b=e.input.charCodeAt(e.position);b!==0;){if(!y&&e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,Z(e,"tab characters must not be used in indentation")),i=e.input.charCodeAt(e.position+1),a=e.line,(b===63||b===58)&&Nt(i))b===63?(y&&(Je(e,u,f,d,g,null,o,s,c),d=g=m=null),x=!0,y=!0,n=!0):y?(y=!1,n=!0):Z(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,b=i;else{if(o=e.line,s=e.lineStart,c=e.position,!ar(e,r,Ah,!1,!0))break;if(e.line===a){for(b=e.input.charCodeAt(e.position);We(b);)b=e.input.charCodeAt(++e.position);if(b===58)b=e.input.charCodeAt(++e.position),Nt(b)||Z(e,"a whitespace character is expected after the key-value separator within a block mapping"),y&&(Je(e,u,f,d,g,null,o,s,c),d=g=m=null),x=!0,y=!1,n=!1,d=e.tag,g=e.result;else if(x)Z(e,"can not read an implicit mapping pair; a colon is missed");else return e.tag=l,e.anchor=h,!0}else if(x)Z(e,"can not read a block mapping entry; a multiline key may not be an implicit key");else return e.tag=l,e.anchor=h,!0}if((e.line===a||e.lineIndent>t)&&(y&&(o=e.line,s=e.lineStart,c=e.position),ar(e,t,kn,!0,n)&&(y?g=e.result:m=e.result),y||(Je(e,u,f,d,g,m,o,s,c),d=g=m=null),bt(e,!0,-1),b=e.input.charCodeAt(e.position)),(e.line===a||e.lineIndent>t)&&b!==0)Z(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return y&&Je(e,u,f,d,g,null,o,s,c),x&&(e.tag=l,e.anchor=h,e.kind="mapping",e.result=u),x}p(jh,"readBlockMapping");function Uh(e){var t,r=!1,i=!1,n,a,o;if(o=e.input.charCodeAt(e.position),o!==33)return!1;if(e.tag!==null&&Z(e,"duplication of a tag property"),o=e.input.charCodeAt(++e.position),o===60?(r=!0,o=e.input.charCodeAt(++e.position)):o===33?(i=!0,n="!!",o=e.input.charCodeAt(++e.position)):n="!",t=e.position,r){do o=e.input.charCodeAt(++e.position);while(o!==0&&o!==62);e.position<e.length?(a=e.input.slice(t,e.position),o=e.input.charCodeAt(++e.position)):Z(e,"unexpected end of the stream within a verbatim tag")}else{for(;o!==0&&!Nt(o);)o===33&&(i?Z(e,"tag suffix cannot contain exclamation marks"):(n=e.input.slice(t-1,e.position+1),Eh.test(n)||Z(e,"named tag handle cannot contain such characters"),i=!0,t=e.position+1)),o=e.input.charCodeAt(++e.position);a=e.input.slice(t,e.position),N0.test(a)&&Z(e,"tag suffix cannot contain flow indicator characters")}a&&!Fh.test(a)&&Z(e,"tag name cannot contain such characters: "+a);try{a=decodeURIComponent(a)}catch{Z(e,"tag name is malformed: "+a)}return r?e.tag=a:qe.call(e.tagMap,n)?e.tag=e.tagMap[n]+a:n==="!"?e.tag="!"+a:n==="!!"?e.tag="tag:yaml.org,2002:"+a:Z(e,'undeclared tag handle "'+n+'"'),!0}p(Uh,"readTagProperty");function Gh(e){var t,r;if(r=e.input.charCodeAt(e.position),r!==38)return!1;for(e.anchor!==null&&Z(e,"duplication of an anchor property"),r=e.input.charCodeAt(++e.position),t=e.position;r!==0&&!Nt(r)&&!Qe(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&Z(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}p(Gh,"readAnchorProperty");function Vh(e){var t,r,i;if(i=e.input.charCodeAt(e.position),i!==42)return!1;for(i=e.input.charCodeAt(++e.position),t=e.position;i!==0&&!Nt(i)&&!Qe(i);)i=e.input.charCodeAt(++e.position);return e.position===t&&Z(e,"name of an alias node must contain at least one character"),r=e.input.slice(t,e.position),qe.call(e.anchorMap,r)||Z(e,'unidentified alias "'+r+'"'),e.result=e.anchorMap[r],bt(e,!0,-1),!0}p(Vh,"readAlias");function ar(e,t,r,i,n){var a,o,s,c=1,l=!1,h=!1,u,f,d,g,m,y;if(e.listener!==null&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,a=o=s=kn===r||Mh===r,i&&bt(e,!0,-1)&&(l=!0,e.lineIndent>t?c=1:e.lineIndent===t?c=0:e.lineIndent<t&&(c=-1)),c===1)for(;Uh(e)||Gh(e);)bt(e,!0,-1)?(l=!0,s=a,e.lineIndent>t?c=1:e.lineIndent===t?c=0:e.lineIndent<t&&(c=-1)):s=!1;if(s&&(s=l||n),(c===1||kn===r)&&(wn===r||Ah===r?m=t:m=t+1,y=e.position-e.lineStart,c===1?s&&(ts(e,y)||jh(e,y,m))||Hh(e,m)?h=!0:(o&&Yh(e,m)||Wh(e,m)||qh(e,m)?h=!0:Vh(e)?(h=!0,(e.tag!==null||e.anchor!==null)&&Z(e,"alias node should not have any properties")):zh(e,m,wn===r)&&(h=!0,e.tag===null&&(e.tag="?")),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):c===0&&(h=s&&ts(e,y))),e.tag===null)e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);else if(e.tag==="?"){for(e.result!==null&&e.kind!=="scalar"&&Z(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),u=0,f=e.implicitTypes.length;u<f;u+=1)if(g=e.implicitTypes[u],g.resolve(e.result)){e.result=g.construct(e.result),e.tag=g.tag,e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);break}}else if(e.tag!=="!"){if(qe.call(e.typeMap[e.kind||"fallback"],e.tag))g=e.typeMap[e.kind||"fallback"][e.tag];else for(g=null,d=e.typeMap.multi[e.kind||"fallback"],u=0,f=d.length;u<f;u+=1)if(e.tag.slice(0,d[u].tag.length)===d[u].tag){g=d[u];break}g||Z(e,"unknown tag !<"+e.tag+">"),e.result!==null&&g.kind!==e.kind&&Z(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+g.kind+'", not "'+e.kind+'"'),g.resolve(e.result,e.tag)?(e.result=g.construct(e.result,e.tag),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):Z(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")}return e.listener!==null&&e.listener("close",e),e.tag!==null||e.anchor!==null||h}p(ar,"composeNode");function Xh(e){var t=e.position,r,i,n,a=!1,o;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap=Object.create(null),e.anchorMap=Object.create(null);(o=e.input.charCodeAt(e.position))!==0&&(bt(e,!0,-1),o=e.input.charCodeAt(e.position),!(e.lineIndent>0||o!==37));){for(a=!0,o=e.input.charCodeAt(++e.position),r=e.position;o!==0&&!Nt(o);)o=e.input.charCodeAt(++e.position);for(i=e.input.slice(r,e.position),n=[],i.length<1&&Z(e,"directive name must not be less than one character in length");o!==0;){for(;We(o);)o=e.input.charCodeAt(++e.position);if(o===35){do o=e.input.charCodeAt(++e.position);while(o!==0&&!le(o));break}if(le(o))break;for(r=e.position;o!==0&&!Nt(o);)o=e.input.charCodeAt(++e.position);n.push(e.input.slice(r,e.position))}o!==0&&Qn(e),qe.call(ml,i)?ml[i](e,i,n):_i(e,'unknown document directive "'+i+'"')}if(bt(e,!0,-1),e.lineIndent===0&&e.input.charCodeAt(e.position)===45&&e.input.charCodeAt(e.position+1)===45&&e.input.charCodeAt(e.position+2)===45?(e.position+=3,bt(e,!0,-1)):a&&Z(e,"directives end mark is expected"),ar(e,e.lineIndent-1,kn,!1,!0),bt(e,!0,-1),e.checkLineBreaks&&P0.test(e.input.slice(t,e.position))&&_i(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&Fi(e)){e.input.charCodeAt(e.position)===46&&(e.position+=3,bt(e,!0,-1));return}if(e.position<e.length-1)Z(e,"end of the stream or a document separator is expected");else return}p(Xh,"readDocument");function Gs(e,t){e=String(e),t=t||{},e.length!==0&&(e.charCodeAt(e.length-1)!==10&&e.charCodeAt(e.length-1)!==13&&(e+=`
`),e.charCodeAt(0)===65279&&(e=e.slice(1)));var r=new Nh(e,t),i=e.indexOf("\0");for(i!==-1&&(r.position=i,Z(r,"null byte is not allowed in input")),r.input+="\0";r.input.charCodeAt(r.position)===32;)r.lineIndent+=1,r.position+=1;for(;r.position<r.length-1;)Xh(r);return r.documents}p(Gs,"loadDocuments");function Zh(e,t,r){t!==null&&typeof t=="object"&&typeof r>"u"&&(r=t,t=null);var i=Gs(e,r);if(typeof t!="function")return i;for(var n=0,a=i.length;n<a;n+=1)t(i[n])}p(Zh,"loadAll$1");function Kh(e,t){var r=Gs(e,t);if(r.length!==0){if(r.length===1)return r[0];throw new Gt("expected a single document in the stream, but found more")}}p(Kh,"load$1");var z0=Zh,W0=Kh,q0={loadAll:z0,load:W0},Qh=Object.prototype.toString,Jh=Object.prototype.hasOwnProperty,Vs=65279,H0=9,Ci=10,Y0=13,j0=32,U0=33,G0=34,es=35,V0=37,X0=38,Z0=39,K0=42,tu=44,Q0=45,vn=58,J0=61,ty=62,ey=63,ry=64,eu=91,ru=93,iy=96,iu=123,ny=124,nu=125,Ot={};Ot[0]="\\0";Ot[7]="\\a";Ot[8]="\\b";Ot[9]="\\t";Ot[10]="\\n";Ot[11]="\\v";Ot[12]="\\f";Ot[13]="\\r";Ot[27]="\\e";Ot[34]='\\"';Ot[92]="\\\\";Ot[133]="\\N";Ot[160]="\\_";Ot[8232]="\\L";Ot[8233]="\\P";var ay=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],sy=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function au(e,t){var r,i,n,a,o,s,c;if(t===null)return{};for(r={},i=Object.keys(t),n=0,a=i.length;n<a;n+=1)o=i[n],s=String(t[o]),o.slice(0,2)==="!!"&&(o="tag:yaml.org,2002:"+o.slice(2)),c=e.compiledTypeMap.fallback[o],c&&Jh.call(c.styleAliases,s)&&(s=c.styleAliases[s]),r[o]=s;return r}p(au,"compileStyleMap");function su(e){var t,r,i;if(t=e.toString(16).toUpperCase(),e<=255)r="x",i=2;else if(e<=65535)r="u",i=4;else if(e<=4294967295)r="U",i=8;else throw new Gt("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+r+wt.repeat("0",i-t.length)+t}p(su,"encodeHex");var oy=1,wi=2;function ou(e){this.schema=e.schema||Bh,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=wt.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=au(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.quotingType=e.quotingType==='"'?wi:oy,this.forceQuotes=e.forceQuotes||!1,this.replacer=typeof e.replacer=="function"?e.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}p(ou,"State");function rs(e,t){for(var r=wt.repeat(" ",t),i=0,n=-1,a="",o,s=e.length;i<s;)n=e.indexOf(`
`,i),n===-1?(o=e.slice(i),i=s):(o=e.slice(i,n+1),i=n+1),o.length&&o!==`
`&&(a+=r),a+=o;return a}p(rs,"indentString");function Sn(e,t){return`
`+wt.repeat(" ",e.indent*t)}p(Sn,"generateNextLine");function lu(e,t){var r,i,n;for(r=0,i=e.implicitTypes.length;r<i;r+=1)if(n=e.implicitTypes[r],n.resolve(t))return!0;return!1}p(lu,"testImplicitResolving");function ki(e){return e===j0||e===H0}p(ki,"isWhitespace");function Nr(e){return 32<=e&&e<=126||161<=e&&e<=55295&&e!==8232&&e!==8233||57344<=e&&e<=65533&&e!==Vs||65536<=e&&e<=1114111}p(Nr,"isPrintable");function is(e){return Nr(e)&&e!==Vs&&e!==Y0&&e!==Ci}p(is,"isNsCharOrWhitespace");function ns(e,t,r){var i=is(e),n=i&&!ki(e);return(r?i:i&&e!==tu&&e!==eu&&e!==ru&&e!==iu&&e!==nu)&&e!==es&&!(t===vn&&!n)||is(t)&&!ki(t)&&e===es||t===vn&&n}p(ns,"isPlainSafe");function cu(e){return Nr(e)&&e!==Vs&&!ki(e)&&e!==Q0&&e!==ey&&e!==vn&&e!==tu&&e!==eu&&e!==ru&&e!==iu&&e!==nu&&e!==es&&e!==X0&&e!==K0&&e!==U0&&e!==ny&&e!==J0&&e!==ty&&e!==Z0&&e!==G0&&e!==V0&&e!==ry&&e!==iy}p(cu,"isPlainSafeFirst");function hu(e){return!ki(e)&&e!==vn}p(hu,"isPlainSafeLast");function Cr(e,t){var r=e.charCodeAt(t),i;return r>=55296&&r<=56319&&t+1<e.length&&(i=e.charCodeAt(t+1),i>=56320&&i<=57343)?(r-55296)*1024+i-56320+65536:r}p(Cr,"codePointAt");function Xs(e){var t=/^\n* /;return t.test(e)}p(Xs,"needIndentIndicator");var uu=1,as=2,fu=3,du=4,br=5;function pu(e,t,r,i,n,a,o,s){var c,l=0,h=null,u=!1,f=!1,d=i!==-1,g=-1,m=cu(Cr(e,0))&&hu(Cr(e,e.length-1));if(t||o)for(c=0;c<e.length;l>=65536?c+=2:c++){if(l=Cr(e,c),!Nr(l))return br;m=m&&ns(l,h,s),h=l}else{for(c=0;c<e.length;l>=65536?c+=2:c++){if(l=Cr(e,c),l===Ci)u=!0,d&&(f=f||c-g-1>i&&e[g+1]!==" ",g=c);else if(!Nr(l))return br;m=m&&ns(l,h,s),h=l}f=f||d&&c-g-1>i&&e[g+1]!==" "}return!u&&!f?m&&!o&&!n(e)?uu:a===wi?br:as:r>9&&Xs(e)?br:o?a===wi?br:as:f?du:fu}p(pu,"chooseScalarStyle");function gu(e,t,r,i,n){e.dump=function(){if(t.length===0)return e.quotingType===wi?'""':"''";if(!e.noCompatMode&&(ay.indexOf(t)!==-1||sy.test(t)))return e.quotingType===wi?'"'+t+'"':"'"+t+"'";var a=e.indent*Math.max(1,r),o=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-a),s=i||e.flowLevel>-1&&r>=e.flowLevel;function c(l){return lu(e,l)}switch(p(c,"testAmbiguity"),pu(t,s,e.indent,o,c,e.quotingType,e.forceQuotes&&!i,n)){case uu:return t;case as:return"'"+t.replace(/'/g,"''")+"'";case fu:return"|"+ss(t,e.indent)+os(rs(t,a));case du:return">"+ss(t,e.indent)+os(rs(mu(t,o),a));case br:return'"'+yu(t)+'"';default:throw new Gt("impossible error: invalid scalar style")}}()}p(gu,"writeScalar");function ss(e,t){var r=Xs(e)?String(t):"",i=e[e.length-1]===`
`,n=i&&(e[e.length-2]===`
`||e===`
`),a=n?"+":i?"":"-";return r+a+`
`}p(ss,"blockHeader");function os(e){return e[e.length-1]===`
`?e.slice(0,-1):e}p(os,"dropEndingNewline");function mu(e,t){for(var r=/(\n+)([^\n]*)/g,i=function(){var l=e.indexOf(`
`);return l=l!==-1?l:e.length,r.lastIndex=l,ls(e.slice(0,l),t)}(),n=e[0]===`
`||e[0]===" ",a,o;o=r.exec(e);){var s=o[1],c=o[2];a=c[0]===" ",i+=s+(!n&&!a&&c!==""?`
`:"")+ls(c,t),n=a}return i}p(mu,"foldString");function ls(e,t){if(e===""||e[0]===" ")return e;for(var r=/ [^ ]/g,i,n=0,a,o=0,s=0,c="";i=r.exec(e);)s=i.index,s-n>t&&(a=o>n?o:s,c+=`
`+e.slice(n,a),n=a+1),o=s;return c+=`
`,e.length-n>t&&o>n?c+=e.slice(n,o)+`
`+e.slice(o+1):c+=e.slice(n),c.slice(1)}p(ls,"foldLine");function yu(e){for(var t="",r=0,i,n=0;n<e.length;r>=65536?n+=2:n++)r=Cr(e,n),i=Ot[r],!i&&Nr(r)?(t+=e[n],r>=65536&&(t+=e[n+1])):t+=i||su(r);return t}p(yu,"escapeString");function xu(e,t,r){var i="",n=e.tag,a,o,s;for(a=0,o=r.length;a<o;a+=1)s=r[a],e.replacer&&(s=e.replacer.call(r,String(a),s)),(xe(e,t,s,!1,!1)||typeof s>"u"&&xe(e,t,null,!1,!1))&&(i!==""&&(i+=","+(e.condenseFlow?"":" ")),i+=e.dump);e.tag=n,e.dump="["+i+"]"}p(xu,"writeFlowSequence");function cs(e,t,r,i){var n="",a=e.tag,o,s,c;for(o=0,s=r.length;o<s;o+=1)c=r[o],e.replacer&&(c=e.replacer.call(r,String(o),c)),(xe(e,t+1,c,!0,!0,!1,!0)||typeof c>"u"&&xe(e,t+1,null,!0,!0,!1,!0))&&((!i||n!=="")&&(n+=Sn(e,t)),e.dump&&Ci===e.dump.charCodeAt(0)?n+="-":n+="- ",n+=e.dump);e.tag=a,e.dump=n||"[]"}p(cs,"writeBlockSequence");function bu(e,t,r){var i="",n=e.tag,a=Object.keys(r),o,s,c,l,h;for(o=0,s=a.length;o<s;o+=1)h="",i!==""&&(h+=", "),e.condenseFlow&&(h+='"'),c=a[o],l=r[c],e.replacer&&(l=e.replacer.call(r,c,l)),xe(e,t,c,!1,!1)&&(e.dump.length>1024&&(h+="? "),h+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),xe(e,t,l,!1,!1)&&(h+=e.dump,i+=h));e.tag=n,e.dump="{"+i+"}"}p(bu,"writeFlowMapping");function _u(e,t,r,i){var n="",a=e.tag,o=Object.keys(r),s,c,l,h,u,f;if(e.sortKeys===!0)o.sort();else if(typeof e.sortKeys=="function")o.sort(e.sortKeys);else if(e.sortKeys)throw new Gt("sortKeys must be a boolean or a function");for(s=0,c=o.length;s<c;s+=1)f="",(!i||n!=="")&&(f+=Sn(e,t)),l=o[s],h=r[l],e.replacer&&(h=e.replacer.call(r,l,h)),xe(e,t+1,l,!0,!0,!0)&&(u=e.tag!==null&&e.tag!=="?"||e.dump&&e.dump.length>1024,u&&(e.dump&&Ci===e.dump.charCodeAt(0)?f+="?":f+="? "),f+=e.dump,u&&(f+=Sn(e,t)),xe(e,t+1,h,!0,u)&&(e.dump&&Ci===e.dump.charCodeAt(0)?f+=":":f+=": ",f+=e.dump,n+=f));e.tag=a,e.dump=n||"{}"}p(_u,"writeBlockMapping");function hs(e,t,r){var i,n,a,o,s,c;for(n=r?e.explicitTypes:e.implicitTypes,a=0,o=n.length;a<o;a+=1)if(s=n[a],(s.instanceOf||s.predicate)&&(!s.instanceOf||typeof t=="object"&&t instanceof s.instanceOf)&&(!s.predicate||s.predicate(t))){if(r?s.multi&&s.representName?e.tag=s.representName(t):e.tag=s.tag:e.tag="?",s.represent){if(c=e.styleMap[s.tag]||s.defaultStyle,Qh.call(s.represent)==="[object Function]")i=s.represent(t,c);else if(Jh.call(s.represent,c))i=s.represent[c](t,c);else throw new Gt("!<"+s.tag+'> tag resolver accepts not "'+c+'" style');e.dump=i}return!0}return!1}p(hs,"detectType");function xe(e,t,r,i,n,a,o){e.tag=null,e.dump=r,hs(e,r,!1)||hs(e,r,!0);var s=Qh.call(e.dump),c=i,l;i&&(i=e.flowLevel<0||e.flowLevel>t);var h=s==="[object Object]"||s==="[object Array]",u,f;if(h&&(u=e.duplicates.indexOf(r),f=u!==-1),(e.tag!==null&&e.tag!=="?"||f||e.indent!==2&&t>0)&&(n=!1),f&&e.usedDuplicates[u])e.dump="*ref_"+u;else{if(h&&f&&!e.usedDuplicates[u]&&(e.usedDuplicates[u]=!0),s==="[object Object]")i&&Object.keys(e.dump).length!==0?(_u(e,t,e.dump,n),f&&(e.dump="&ref_"+u+e.dump)):(bu(e,t,e.dump),f&&(e.dump="&ref_"+u+" "+e.dump));else if(s==="[object Array]")i&&e.dump.length!==0?(e.noArrayIndent&&!o&&t>0?cs(e,t-1,e.dump,n):cs(e,t,e.dump,n),f&&(e.dump="&ref_"+u+e.dump)):(xu(e,t,e.dump),f&&(e.dump="&ref_"+u+" "+e.dump));else if(s==="[object String]")e.tag!=="?"&&gu(e,e.dump,t,a,c);else{if(s==="[object Undefined]")return!1;if(e.skipInvalid)return!1;throw new Gt("unacceptable kind of an object to dump "+s)}e.tag!==null&&e.tag!=="?"&&(l=encodeURI(e.tag[0]==="!"?e.tag.slice(1):e.tag).replace(/!/g,"%21"),e.tag[0]==="!"?l="!"+l:l.slice(0,18)==="tag:yaml.org,2002:"?l="!!"+l.slice(18):l="!<"+l+">",e.dump=l+" "+e.dump)}return!0}p(xe,"writeNode");function Cu(e,t){var r=[],i=[],n,a;for(Tn(e,r,i),n=0,a=i.length;n<a;n+=1)t.duplicates.push(r[i[n]]);t.usedDuplicates=new Array(a)}p(Cu,"getDuplicateReferences");function Tn(e,t,r){var i,n,a;if(e!==null&&typeof e=="object")if(n=t.indexOf(e),n!==-1)r.indexOf(n)===-1&&r.push(n);else if(t.push(e),Array.isArray(e))for(n=0,a=e.length;n<a;n+=1)Tn(e[n],t,r);else for(i=Object.keys(e),n=0,a=i.length;n<a;n+=1)Tn(e[i[n]],t,r)}p(Tn,"inspectNode");function ly(e,t){t=t||{};var r=new ou(t);r.noRefs||Cu(e,r);var i=e;return r.replacer&&(i=r.replacer.call({"":i},"",i)),xe(r,0,i,!0,!0)?r.dump+`
`:""}p(ly,"dump$1");function cy(e,t){return function(){throw new Error("Function yaml."+e+" is removed in js-yaml 4. Use yaml."+t+" instead, which is now safe by default.")}}p(cy,"renamed");var hy=uh,uy=q0.load;/*! Bundled license information:

js-yaml/dist/js-yaml.mjs:
  (*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT *)
*/var ee={aggregation:18,extension:18,composition:18,dependency:6,lollipop:13.5,arrow_point:4};function ci(e,t){if(e===void 0||t===void 0)return{angle:0,deltaX:0,deltaY:0};e=yt(e),t=yt(t);const[r,i]=[e.x,e.y],[n,a]=[t.x,t.y],o=n-r,s=a-i;return{angle:Math.atan(s/o),deltaX:o,deltaY:s}}p(ci,"calculateDeltaAndAngle");var yt=p(e=>Array.isArray(e)?{x:e[0],y:e[1]}:e,"pointTransformer"),fy=p(e=>({x:p(function(t,r,i){let n=0;const a=yt(i[0]).x<yt(i[i.length-1]).x?"left":"right";if(r===0&&Object.hasOwn(ee,e.arrowTypeStart)){const{angle:d,deltaX:g}=ci(i[0],i[1]);n=ee[e.arrowTypeStart]*Math.cos(d)*(g>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(ee,e.arrowTypeEnd)){const{angle:d,deltaX:g}=ci(i[i.length-1],i[i.length-2]);n=ee[e.arrowTypeEnd]*Math.cos(d)*(g>=0?1:-1)}const o=Math.abs(yt(t).x-yt(i[i.length-1]).x),s=Math.abs(yt(t).y-yt(i[i.length-1]).y),c=Math.abs(yt(t).x-yt(i[0]).x),l=Math.abs(yt(t).y-yt(i[0]).y),h=ee[e.arrowTypeStart],u=ee[e.arrowTypeEnd],f=1;if(o<u&&o>0&&s<u){let d=u+f-o;d*=a==="right"?-1:1,n-=d}if(c<h&&c>0&&l<h){let d=h+f-c;d*=a==="right"?-1:1,n+=d}return yt(t).x+n},"x"),y:p(function(t,r,i){let n=0;const a=yt(i[0]).y<yt(i[i.length-1]).y?"down":"up";if(r===0&&Object.hasOwn(ee,e.arrowTypeStart)){const{angle:d,deltaY:g}=ci(i[0],i[1]);n=ee[e.arrowTypeStart]*Math.abs(Math.sin(d))*(g>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(ee,e.arrowTypeEnd)){const{angle:d,deltaY:g}=ci(i[i.length-1],i[i.length-2]);n=ee[e.arrowTypeEnd]*Math.abs(Math.sin(d))*(g>=0?1:-1)}const o=Math.abs(yt(t).y-yt(i[i.length-1]).y),s=Math.abs(yt(t).x-yt(i[i.length-1]).x),c=Math.abs(yt(t).y-yt(i[0]).y),l=Math.abs(yt(t).x-yt(i[0]).x),h=ee[e.arrowTypeStart],u=ee[e.arrowTypeEnd],f=1;if(o<u&&o>0&&s<u){let d=u+f-o;d*=a==="up"?-1:1,n-=d}if(c<h&&c>0&&l<h){let d=h+f-c;d*=a==="up"?-1:1,n+=d}return yt(t).y+n},"y")}),"getLineFunctionsWithOffset"),Zs=p(({flowchart:e})=>{var n,a;const t=((n=e==null?void 0:e.subGraphTitleMargin)==null?void 0:n.top)??0,r=((a=e==null?void 0:e.subGraphTitleMargin)==null?void 0:a.bottom)??0,i=t+r;return{subGraphTitleTopMargin:t,subGraphTitleBottomMargin:r,subGraphTitleTotalMargin:i}},"getSubGraphTitleMargins");const dy=Object.freeze({left:0,top:0,width:16,height:16}),Ln=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),wu=Object.freeze({...dy,...Ln}),py=Object.freeze({...wu,body:"",hidden:!1}),gy=Object.freeze({width:null,height:null}),my=Object.freeze({...gy,...Ln}),yy=(e,t,r,i="")=>{const n=e.split(":");if(e.slice(0,1)==="@"){if(n.length<2||n.length>3)return null;i=n.shift().slice(1)}if(n.length>3||!n.length)return null;if(n.length>1){const s=n.pop(),c=n.pop(),l={provider:n.length>0?n[0]:i,prefix:c,name:s};return Ea(l)?l:null}const a=n[0],o=a.split("-");if(o.length>1){const s={provider:i,prefix:o.shift(),name:o.join("-")};return Ea(s)?s:null}if(r&&i===""){const s={provider:i,prefix:"",name:a};return Ea(s,r)?s:null}return null},Ea=(e,t)=>e?!!((t&&e.prefix===""||e.prefix)&&e.name):!1;function xy(e,t){const r={};!e.hFlip!=!t.hFlip&&(r.hFlip=!0),!e.vFlip!=!t.vFlip&&(r.vFlip=!0);const i=((e.rotate||0)+(t.rotate||0))%4;return i&&(r.rotate=i),r}function yl(e,t){const r=xy(e,t);for(const i in py)i in Ln?i in e&&!(i in r)&&(r[i]=Ln[i]):i in t?r[i]=t[i]:i in e&&(r[i]=e[i]);return r}function by(e,t){const r=e.icons,i=e.aliases||Object.create(null),n=Object.create(null);function a(o){if(r[o])return n[o]=[];if(!(o in n)){n[o]=null;const s=i[o]&&i[o].parent,c=s&&a(s);c&&(n[o]=[s].concat(c))}return n[o]}return(t||Object.keys(r).concat(Object.keys(i))).forEach(a),n}function xl(e,t,r){const i=e.icons,n=e.aliases||Object.create(null);let a={};function o(s){a=yl(i[s]||n[s],a)}return o(t),r.forEach(o),yl(e,a)}function _y(e,t){if(e.icons[t])return xl(e,t,[]);const r=by(e,[t])[t];return r?xl(e,t,r):null}const Cy=/(-?[0-9.]*[0-9]+[0-9.]*)/g,wy=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function bl(e,t,r){if(t===1)return e;if(r=r||100,typeof e=="number")return Math.ceil(e*t*r)/r;if(typeof e!="string")return e;const i=e.split(Cy);if(i===null||!i.length)return e;const n=[];let a=i.shift(),o=wy.test(a);for(;;){if(o){const s=parseFloat(a);isNaN(s)?n.push(a):n.push(Math.ceil(s*t*r)/r)}else n.push(a);if(a=i.shift(),a===void 0)return n.join("");o=!o}}function ky(e,t="defs"){let r="";const i=e.indexOf("<"+t);for(;i>=0;){const n=e.indexOf(">",i),a=e.indexOf("</"+t);if(n===-1||a===-1)break;const o=e.indexOf(">",a);if(o===-1)break;r+=e.slice(n+1,a).trim(),e=e.slice(0,i).trim()+e.slice(o+1)}return{defs:r,content:e}}function vy(e,t){return e?"<defs>"+e+"</defs>"+t:t}function Sy(e,t,r){const i=ky(e);return vy(i.defs,t+i.content+r)}const Ty=e=>e==="unset"||e==="undefined"||e==="none";function Ly(e,t){const r={...wu,...e},i={...my,...t},n={left:r.left,top:r.top,width:r.width,height:r.height};let a=r.body;[r,i].forEach(m=>{const y=[],x=m.hFlip,b=m.vFlip;let C=m.rotate;x?b?C+=2:(y.push("translate("+(n.width+n.left).toString()+" "+(0-n.top).toString()+")"),y.push("scale(-1 1)"),n.top=n.left=0):b&&(y.push("translate("+(0-n.left).toString()+" "+(n.height+n.top).toString()+")"),y.push("scale(1 -1)"),n.top=n.left=0);let v;switch(C<0&&(C-=Math.floor(C/4)*4),C=C%4,C){case 1:v=n.height/2+n.top,y.unshift("rotate(90 "+v.toString()+" "+v.toString()+")");break;case 2:y.unshift("rotate(180 "+(n.width/2+n.left).toString()+" "+(n.height/2+n.top).toString()+")");break;case 3:v=n.width/2+n.left,y.unshift("rotate(-90 "+v.toString()+" "+v.toString()+")");break}C%2===1&&(n.left!==n.top&&(v=n.left,n.left=n.top,n.top=v),n.width!==n.height&&(v=n.width,n.width=n.height,n.height=v)),y.length&&(a=Sy(a,'<g transform="'+y.join(" ")+'">',"</g>"))});const o=i.width,s=i.height,c=n.width,l=n.height;let h,u;o===null?(u=s===null?"1em":s==="auto"?l:s,h=bl(u,c/l)):(h=o==="auto"?c:o,u=s===null?bl(h,l/c):s==="auto"?l:s);const f={},d=(m,y)=>{Ty(y)||(f[m]=y.toString())};d("width",h),d("height",u);const g=[n.left,n.top,c,l];return f.viewBox=g.join(" "),{attributes:f,viewBox:g,body:a}}const By=/\sid="(\S+)"/g,Ay="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let My=0;function Ey(e,t=Ay){const r=[];let i;for(;i=By.exec(e);)r.push(i[1]);if(!r.length)return e;const n="suffix"+(Math.random()*16777216|Date.now()).toString(16);return r.forEach(a=>{const o=typeof t=="function"?t(a):t+(My++).toString(),s=a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");e=e.replace(new RegExp('([#;"])('+s+')([")]|\\.[a-z])',"g"),"$1"+o+n+"$3")}),e=e.replace(new RegExp(n,"g"),""),e}function Fy(e,t){let r=e.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const i in t)r+=" "+i+'="'+t[i]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+r+">"+e+"</svg>"}var $y={body:'<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/><text transform="translate(21.16 64.67)" style="fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;"><tspan x="0" y="0">?</tspan></text></g>',height:80,width:80},us=new Map,ku=new Map,Dy=p(e=>{for(const t of e){if(!t.name)throw new Error('Invalid icon loader. Must have a "name" property with non-empty string value.');if(D.debug("Registering icon pack:",t.name),"loader"in t)ku.set(t.name,t.loader);else if("icons"in t)us.set(t.name,t.icons);else throw D.error("Invalid icon loader:",t),new Error('Invalid icon loader. Must have either "icons" or "loader" property.')}},"registerIconPacks"),Ry=p(async(e,t)=>{const r=yy(e,!0,t!==void 0);if(!r)throw new Error(`Invalid icon name: ${e}`);const i=r.prefix||t;if(!i)throw new Error(`Icon name must contain a prefix: ${e}`);let n=us.get(i);if(!n){const o=ku.get(i);if(!o)throw new Error(`Icon set not found: ${r.prefix}`);try{n={...await o(),prefix:i},us.set(i,n)}catch(s){throw D.error(s),new Error(`Failed to load icon set: ${r.prefix}`)}}const a=_y(n,r.name);if(!a)throw new Error(`Icon not found: ${e}`);return a},"getRegisteredIconData"),ta=p(async(e,t)=>{let r;try{r=await Ry(e,t==null?void 0:t.fallbackPrefix)}catch(a){D.error(a),r=$y}const i=Ly(r,t);return Fy(Ey(i.body),i.attributes)},"getIconSVG"),Ks={},Tt={};Object.defineProperty(Tt,"__esModule",{value:!0});Tt.BLANK_URL=Tt.relativeFirstCharacters=Tt.whitespaceEscapeCharsRegex=Tt.urlSchemeRegex=Tt.ctrlCharactersRegex=Tt.htmlCtrlEntityRegex=Tt.htmlEntitiesRegex=Tt.invalidProtocolRegex=void 0;Tt.invalidProtocolRegex=/^([^\w]*)(javascript|data|vbscript)/im;Tt.htmlEntitiesRegex=/&#(\w+)(^\w|;)?/g;Tt.htmlCtrlEntityRegex=/&(newline|tab);/gi;Tt.ctrlCharactersRegex=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim;Tt.urlSchemeRegex=/^.+(:|&colon;)/gim;Tt.whitespaceEscapeCharsRegex=/(\\|%5[cC])((%(6[eE]|72|74))|[nrt])/g;Tt.relativeFirstCharacters=[".","/"];Tt.BLANK_URL="about:blank";Object.defineProperty(Ks,"__esModule",{value:!0});var vu=Ks.sanitizeUrl=void 0,Dt=Tt;function Oy(e){return Dt.relativeFirstCharacters.indexOf(e[0])>-1}function Iy(e){var t=e.replace(Dt.ctrlCharactersRegex,"");return t.replace(Dt.htmlEntitiesRegex,function(r,i){return String.fromCharCode(i)})}function Py(e){return URL.canParse(e)}function _l(e){try{return decodeURIComponent(e)}catch{return e}}function Ny(e){if(!e)return Dt.BLANK_URL;var t,r=_l(e.trim());do r=Iy(r).replace(Dt.htmlCtrlEntityRegex,"").replace(Dt.ctrlCharactersRegex,"").replace(Dt.whitespaceEscapeCharsRegex,"").trim(),r=_l(r),t=r.match(Dt.ctrlCharactersRegex)||r.match(Dt.htmlEntitiesRegex)||r.match(Dt.htmlCtrlEntityRegex)||r.match(Dt.whitespaceEscapeCharsRegex);while(t&&t.length>0);var i=r;if(!i)return Dt.BLANK_URL;if(Oy(i))return i;var n=i.trimStart(),a=n.match(Dt.urlSchemeRegex);if(!a)return i;var o=a[0].toLowerCase().trim();if(Dt.invalidProtocolRegex.test(o))return Dt.BLANK_URL;var s=n.replace(/\\/g,"/");if(o==="mailto:"||o.includes("://"))return s;if(o==="http:"||o==="https:"){if(!Py(s))return Dt.BLANK_URL;var c=new URL(s);return c.protocol=c.protocol.toLowerCase(),c.hostname=c.hostname.toLowerCase(),c.toString()}return s}vu=Ks.sanitizeUrl=Ny;var zy={value:()=>{}};function Su(){for(var e=0,t=arguments.length,r={},i;e<t;++e){if(!(i=arguments[e]+"")||i in r||/[\s.]/.test(i))throw new Error("illegal type: "+i);r[i]=[]}return new on(r)}function on(e){this._=e}function Wy(e,t){return e.trim().split(/^|\s+/).map(function(r){var i="",n=r.indexOf(".");if(n>=0&&(i=r.slice(n+1),r=r.slice(0,n)),r&&!t.hasOwnProperty(r))throw new Error("unknown type: "+r);return{type:r,name:i}})}on.prototype=Su.prototype={constructor:on,on:function(e,t){var r=this._,i=Wy(e+"",r),n,a=-1,o=i.length;if(arguments.length<2){for(;++a<o;)if((n=(e=i[a]).type)&&(n=qy(r[n],e.name)))return n;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++a<o;)if(n=(e=i[a]).type)r[n]=Cl(r[n],e.name,t);else if(t==null)for(n in r)r[n]=Cl(r[n],e.name,null);return this},copy:function(){var e={},t=this._;for(var r in t)e[r]=t[r].slice();return new on(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var r=new Array(n),i=0,n,a;i<n;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(a=this._[e],i=0,n=a.length;i<n;++i)a[i].value.apply(t,r)},apply:function(e,t,r){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var i=this._[e],n=0,a=i.length;n<a;++n)i[n].value.apply(t,r)}};function qy(e,t){for(var r=0,i=e.length,n;r<i;++r)if((n=e[r]).name===t)return n.value}function Cl(e,t,r){for(var i=0,n=e.length;i<n;++i)if(e[i].name===t){e[i]=zy,e=e.slice(0,i).concat(e.slice(i+1));break}return r!=null&&e.push({name:t,value:r}),e}var fs="http://www.w3.org/1999/xhtml";const wl={svg:"http://www.w3.org/2000/svg",xhtml:fs,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function ea(e){var t=e+="",r=t.indexOf(":");return r>=0&&(t=e.slice(0,r))!=="xmlns"&&(e=e.slice(r+1)),wl.hasOwnProperty(t)?{space:wl[t],local:e}:e}function Hy(e){return function(){var t=this.ownerDocument,r=this.namespaceURI;return r===fs&&t.documentElement.namespaceURI===fs?t.createElement(e):t.createElementNS(r,e)}}function Yy(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Tu(e){var t=ea(e);return(t.local?Yy:Hy)(t)}function jy(){}function Qs(e){return e==null?jy:function(){return this.querySelector(e)}}function Uy(e){typeof e!="function"&&(e=Qs(e));for(var t=this._groups,r=t.length,i=new Array(r),n=0;n<r;++n)for(var a=t[n],o=a.length,s=i[n]=new Array(o),c,l,h=0;h<o;++h)(c=a[h])&&(l=e.call(c,c.__data__,h,a))&&("__data__"in c&&(l.__data__=c.__data__),s[h]=l);return new Qt(i,this._parents)}function Gy(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function Vy(){return[]}function Lu(e){return e==null?Vy:function(){return this.querySelectorAll(e)}}function Xy(e){return function(){return Gy(e.apply(this,arguments))}}function Zy(e){typeof e=="function"?e=Xy(e):e=Lu(e);for(var t=this._groups,r=t.length,i=[],n=[],a=0;a<r;++a)for(var o=t[a],s=o.length,c,l=0;l<s;++l)(c=o[l])&&(i.push(e.call(c,c.__data__,l,o)),n.push(c));return new Qt(i,n)}function Bu(e){return function(){return this.matches(e)}}function Au(e){return function(t){return t.matches(e)}}var Ky=Array.prototype.find;function Qy(e){return function(){return Ky.call(this.children,e)}}function Jy(){return this.firstElementChild}function tx(e){return this.select(e==null?Jy:Qy(typeof e=="function"?e:Au(e)))}var ex=Array.prototype.filter;function rx(){return Array.from(this.children)}function ix(e){return function(){return ex.call(this.children,e)}}function nx(e){return this.selectAll(e==null?rx:ix(typeof e=="function"?e:Au(e)))}function ax(e){typeof e!="function"&&(e=Bu(e));for(var t=this._groups,r=t.length,i=new Array(r),n=0;n<r;++n)for(var a=t[n],o=a.length,s=i[n]=[],c,l=0;l<o;++l)(c=a[l])&&e.call(c,c.__data__,l,a)&&s.push(c);return new Qt(i,this._parents)}function Mu(e){return new Array(e.length)}function sx(){return new Qt(this._enter||this._groups.map(Mu),this._parents)}function Bn(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}Bn.prototype={constructor:Bn,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function ox(e){return function(){return e}}function lx(e,t,r,i,n,a){for(var o=0,s,c=t.length,l=a.length;o<l;++o)(s=t[o])?(s.__data__=a[o],i[o]=s):r[o]=new Bn(e,a[o]);for(;o<c;++o)(s=t[o])&&(n[o]=s)}function cx(e,t,r,i,n,a,o){var s,c,l=new Map,h=t.length,u=a.length,f=new Array(h),d;for(s=0;s<h;++s)(c=t[s])&&(f[s]=d=o.call(c,c.__data__,s,t)+"",l.has(d)?n[s]=c:l.set(d,c));for(s=0;s<u;++s)d=o.call(e,a[s],s,a)+"",(c=l.get(d))?(i[s]=c,c.__data__=a[s],l.delete(d)):r[s]=new Bn(e,a[s]);for(s=0;s<h;++s)(c=t[s])&&l.get(f[s])===c&&(n[s]=c)}function hx(e){return e.__data__}function ux(e,t){if(!arguments.length)return Array.from(this,hx);var r=t?cx:lx,i=this._parents,n=this._groups;typeof e!="function"&&(e=ox(e));for(var a=n.length,o=new Array(a),s=new Array(a),c=new Array(a),l=0;l<a;++l){var h=i[l],u=n[l],f=u.length,d=fx(e.call(h,h&&h.__data__,l,i)),g=d.length,m=s[l]=new Array(g),y=o[l]=new Array(g),x=c[l]=new Array(f);r(h,u,m,y,x,d,t);for(var b=0,C=0,v,S;b<g;++b)if(v=m[b]){for(b>=C&&(C=b+1);!(S=y[C])&&++C<g;);v._next=S||null}}return o=new Qt(o,i),o._enter=s,o._exit=c,o}function fx(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function dx(){return new Qt(this._exit||this._groups.map(Mu),this._parents)}function px(e,t,r){var i=this.enter(),n=this,a=this.exit();return typeof e=="function"?(i=e(i),i&&(i=i.selection())):i=i.append(e+""),t!=null&&(n=t(n),n&&(n=n.selection())),r==null?a.remove():r(a),i&&n?i.merge(n).order():n}function gx(e){for(var t=e.selection?e.selection():e,r=this._groups,i=t._groups,n=r.length,a=i.length,o=Math.min(n,a),s=new Array(n),c=0;c<o;++c)for(var l=r[c],h=i[c],u=l.length,f=s[c]=new Array(u),d,g=0;g<u;++g)(d=l[g]||h[g])&&(f[g]=d);for(;c<n;++c)s[c]=r[c];return new Qt(s,this._parents)}function mx(){for(var e=this._groups,t=-1,r=e.length;++t<r;)for(var i=e[t],n=i.length-1,a=i[n],o;--n>=0;)(o=i[n])&&(a&&o.compareDocumentPosition(a)^4&&a.parentNode.insertBefore(o,a),a=o);return this}function yx(e){e||(e=xx);function t(u,f){return u&&f?e(u.__data__,f.__data__):!u-!f}for(var r=this._groups,i=r.length,n=new Array(i),a=0;a<i;++a){for(var o=r[a],s=o.length,c=n[a]=new Array(s),l,h=0;h<s;++h)(l=o[h])&&(c[h]=l);c.sort(t)}return new Qt(n,this._parents).order()}function xx(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function bx(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function _x(){return Array.from(this)}function Cx(){for(var e=this._groups,t=0,r=e.length;t<r;++t)for(var i=e[t],n=0,a=i.length;n<a;++n){var o=i[n];if(o)return o}return null}function wx(){let e=0;for(const t of this)++e;return e}function kx(){return!this.node()}function vx(e){for(var t=this._groups,r=0,i=t.length;r<i;++r)for(var n=t[r],a=0,o=n.length,s;a<o;++a)(s=n[a])&&e.call(s,s.__data__,a,n);return this}function Sx(e){return function(){this.removeAttribute(e)}}function Tx(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Lx(e,t){return function(){this.setAttribute(e,t)}}function Bx(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function Ax(e,t){return function(){var r=t.apply(this,arguments);r==null?this.removeAttribute(e):this.setAttribute(e,r)}}function Mx(e,t){return function(){var r=t.apply(this,arguments);r==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,r)}}function Ex(e,t){var r=ea(e);if(arguments.length<2){var i=this.node();return r.local?i.getAttributeNS(r.space,r.local):i.getAttribute(r)}return this.each((t==null?r.local?Tx:Sx:typeof t=="function"?r.local?Mx:Ax:r.local?Bx:Lx)(r,t))}function Eu(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function Fx(e){return function(){this.style.removeProperty(e)}}function $x(e,t,r){return function(){this.style.setProperty(e,t,r)}}function Dx(e,t,r){return function(){var i=t.apply(this,arguments);i==null?this.style.removeProperty(e):this.style.setProperty(e,i,r)}}function Rx(e,t,r){return arguments.length>1?this.each((t==null?Fx:typeof t=="function"?Dx:$x)(e,t,r??"")):zr(this.node(),e)}function zr(e,t){return e.style.getPropertyValue(t)||Eu(e).getComputedStyle(e,null).getPropertyValue(t)}function Ox(e){return function(){delete this[e]}}function Ix(e,t){return function(){this[e]=t}}function Px(e,t){return function(){var r=t.apply(this,arguments);r==null?delete this[e]:this[e]=r}}function Nx(e,t){return arguments.length>1?this.each((t==null?Ox:typeof t=="function"?Px:Ix)(e,t)):this.node()[e]}function Fu(e){return e.trim().split(/^|\s+/)}function Js(e){return e.classList||new $u(e)}function $u(e){this._node=e,this._names=Fu(e.getAttribute("class")||"")}$u.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function Du(e,t){for(var r=Js(e),i=-1,n=t.length;++i<n;)r.add(t[i])}function Ru(e,t){for(var r=Js(e),i=-1,n=t.length;++i<n;)r.remove(t[i])}function zx(e){return function(){Du(this,e)}}function Wx(e){return function(){Ru(this,e)}}function qx(e,t){return function(){(t.apply(this,arguments)?Du:Ru)(this,e)}}function Hx(e,t){var r=Fu(e+"");if(arguments.length<2){for(var i=Js(this.node()),n=-1,a=r.length;++n<a;)if(!i.contains(r[n]))return!1;return!0}return this.each((typeof t=="function"?qx:t?zx:Wx)(r,t))}function Yx(){this.textContent=""}function jx(e){return function(){this.textContent=e}}function Ux(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function Gx(e){return arguments.length?this.each(e==null?Yx:(typeof e=="function"?Ux:jx)(e)):this.node().textContent}function Vx(){this.innerHTML=""}function Xx(e){return function(){this.innerHTML=e}}function Zx(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function Kx(e){return arguments.length?this.each(e==null?Vx:(typeof e=="function"?Zx:Xx)(e)):this.node().innerHTML}function Qx(){this.nextSibling&&this.parentNode.appendChild(this)}function Jx(){return this.each(Qx)}function tb(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function eb(){return this.each(tb)}function rb(e){var t=typeof e=="function"?e:Tu(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function ib(){return null}function nb(e,t){var r=typeof e=="function"?e:Tu(e),i=t==null?ib:typeof t=="function"?t:Qs(t);return this.select(function(){return this.insertBefore(r.apply(this,arguments),i.apply(this,arguments)||null)})}function ab(){var e=this.parentNode;e&&e.removeChild(this)}function sb(){return this.each(ab)}function ob(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function lb(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function cb(e){return this.select(e?lb:ob)}function hb(e){return arguments.length?this.property("__data__",e):this.node().__data__}function ub(e){return function(t){e.call(this,t,this.__data__)}}function fb(e){return e.trim().split(/^|\s+/).map(function(t){var r="",i=t.indexOf(".");return i>=0&&(r=t.slice(i+1),t=t.slice(0,i)),{type:t,name:r}})}function db(e){return function(){var t=this.__on;if(t){for(var r=0,i=-1,n=t.length,a;r<n;++r)a=t[r],(!e.type||a.type===e.type)&&a.name===e.name?this.removeEventListener(a.type,a.listener,a.options):t[++i]=a;++i?t.length=i:delete this.__on}}}function pb(e,t,r){return function(){var i=this.__on,n,a=ub(t);if(i){for(var o=0,s=i.length;o<s;++o)if((n=i[o]).type===e.type&&n.name===e.name){this.removeEventListener(n.type,n.listener,n.options),this.addEventListener(n.type,n.listener=a,n.options=r),n.value=t;return}}this.addEventListener(e.type,a,r),n={type:e.type,name:e.name,value:t,listener:a,options:r},i?i.push(n):this.__on=[n]}}function gb(e,t,r){var i=fb(e+""),n,a=i.length,o;if(arguments.length<2){var s=this.node().__on;if(s){for(var c=0,l=s.length,h;c<l;++c)for(n=0,h=s[c];n<a;++n)if((o=i[n]).type===h.type&&o.name===h.name)return h.value}return}for(s=t?pb:db,n=0;n<a;++n)this.each(s(i[n],t,r));return this}function Ou(e,t,r){var i=Eu(e),n=i.CustomEvent;typeof n=="function"?n=new n(t,r):(n=i.document.createEvent("Event"),r?(n.initEvent(t,r.bubbles,r.cancelable),n.detail=r.detail):n.initEvent(t,!1,!1)),e.dispatchEvent(n)}function mb(e,t){return function(){return Ou(this,e,t)}}function yb(e,t){return function(){return Ou(this,e,t.apply(this,arguments))}}function xb(e,t){return this.each((typeof t=="function"?yb:mb)(e,t))}function*bb(){for(var e=this._groups,t=0,r=e.length;t<r;++t)for(var i=e[t],n=0,a=i.length,o;n<a;++n)(o=i[n])&&(yield o)}var Iu=[null];function Qt(e,t){this._groups=e,this._parents=t}function $i(){return new Qt([[document.documentElement]],Iu)}function _b(){return this}Qt.prototype=$i.prototype={constructor:Qt,select:Uy,selectAll:Zy,selectChild:tx,selectChildren:nx,filter:ax,data:ux,enter:sx,exit:dx,join:px,merge:gx,selection:_b,order:mx,sort:yx,call:bx,nodes:_x,node:Cx,size:wx,empty:kx,each:vx,attr:Ex,style:Rx,property:Nx,classed:Hx,text:Gx,html:Kx,raise:Jx,lower:eb,append:rb,insert:nb,remove:sb,clone:cb,datum:hb,on:gb,dispatch:xb,[Symbol.iterator]:bb};function lt(e){return typeof e=="string"?new Qt([[document.querySelector(e)]],[document.documentElement]):new Qt([[e]],Iu)}function to(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function Pu(e,t){var r=Object.create(e.prototype);for(var i in t)r[i]=t[i];return r}function Di(){}var vi=.7,An=1/vi,kr="\\s*([+-]?\\d+)\\s*",Si="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",me="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Cb=/^#([0-9a-f]{3,8})$/,wb=new RegExp(`^rgb\\(${kr},${kr},${kr}\\)$`),kb=new RegExp(`^rgb\\(${me},${me},${me}\\)$`),vb=new RegExp(`^rgba\\(${kr},${kr},${kr},${Si}\\)$`),Sb=new RegExp(`^rgba\\(${me},${me},${me},${Si}\\)$`),Tb=new RegExp(`^hsl\\(${Si},${me},${me}\\)$`),Lb=new RegExp(`^hsla\\(${Si},${me},${me},${Si}\\)$`),kl={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};to(Di,Ti,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:vl,formatHex:vl,formatHex8:Bb,formatHsl:Ab,formatRgb:Sl,toString:Sl});function vl(){return this.rgb().formatHex()}function Bb(){return this.rgb().formatHex8()}function Ab(){return Nu(this).formatHsl()}function Sl(){return this.rgb().formatRgb()}function Ti(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=Cb.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?Tl(t):r===3?new Vt(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?Ui(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?Ui(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=wb.exec(e))?new Vt(t[1],t[2],t[3],1):(t=kb.exec(e))?new Vt(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=vb.exec(e))?Ui(t[1],t[2],t[3],t[4]):(t=Sb.exec(e))?Ui(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=Tb.exec(e))?Al(t[1],t[2]/100,t[3]/100,1):(t=Lb.exec(e))?Al(t[1],t[2]/100,t[3]/100,t[4]):kl.hasOwnProperty(e)?Tl(kl[e]):e==="transparent"?new Vt(NaN,NaN,NaN,0):null}function Tl(e){return new Vt(e>>16&255,e>>8&255,e&255,1)}function Ui(e,t,r,i){return i<=0&&(e=t=r=NaN),new Vt(e,t,r,i)}function Mb(e){return e instanceof Di||(e=Ti(e)),e?(e=e.rgb(),new Vt(e.r,e.g,e.b,e.opacity)):new Vt}function ds(e,t,r,i){return arguments.length===1?Mb(e):new Vt(e,t,r,i??1)}function Vt(e,t,r,i){this.r=+e,this.g=+t,this.b=+r,this.opacity=+i}to(Vt,ds,Pu(Di,{brighter(e){return e=e==null?An:Math.pow(An,e),new Vt(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?vi:Math.pow(vi,e),new Vt(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Vt(rr(this.r),rr(this.g),rr(this.b),Mn(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Ll,formatHex:Ll,formatHex8:Eb,formatRgb:Bl,toString:Bl}));function Ll(){return`#${tr(this.r)}${tr(this.g)}${tr(this.b)}`}function Eb(){return`#${tr(this.r)}${tr(this.g)}${tr(this.b)}${tr((isNaN(this.opacity)?1:this.opacity)*255)}`}function Bl(){const e=Mn(this.opacity);return`${e===1?"rgb(":"rgba("}${rr(this.r)}, ${rr(this.g)}, ${rr(this.b)}${e===1?")":`, ${e})`}`}function Mn(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function rr(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function tr(e){return e=rr(e),(e<16?"0":"")+e.toString(16)}function Al(e,t,r,i){return i<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new oe(e,t,r,i)}function Nu(e){if(e instanceof oe)return new oe(e.h,e.s,e.l,e.opacity);if(e instanceof Di||(e=Ti(e)),!e)return new oe;if(e instanceof oe)return e;e=e.rgb();var t=e.r/255,r=e.g/255,i=e.b/255,n=Math.min(t,r,i),a=Math.max(t,r,i),o=NaN,s=a-n,c=(a+n)/2;return s?(t===a?o=(r-i)/s+(r<i)*6:r===a?o=(i-t)/s+2:o=(t-r)/s+4,s/=c<.5?a+n:2-a-n,o*=60):s=c>0&&c<1?0:o,new oe(o,s,c,e.opacity)}function Fb(e,t,r,i){return arguments.length===1?Nu(e):new oe(e,t,r,i??1)}function oe(e,t,r,i){this.h=+e,this.s=+t,this.l=+r,this.opacity=+i}to(oe,Fb,Pu(Di,{brighter(e){return e=e==null?An:Math.pow(An,e),new oe(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?vi:Math.pow(vi,e),new oe(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,i=r+(r<.5?r:1-r)*t,n=2*r-i;return new Vt(Fa(e>=240?e-240:e+120,n,i),Fa(e,n,i),Fa(e<120?e+240:e-120,n,i),this.opacity)},clamp(){return new oe(Ml(this.h),Gi(this.s),Gi(this.l),Mn(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Mn(this.opacity);return`${e===1?"hsl(":"hsla("}${Ml(this.h)}, ${Gi(this.s)*100}%, ${Gi(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Ml(e){return e=(e||0)%360,e<0?e+360:e}function Gi(e){return Math.max(0,Math.min(1,e||0))}function Fa(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const eo=e=>()=>e;function zu(e,t){return function(r){return e+r*t}}function $b(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(i){return Math.pow(e+i*t,r)}}function zv(e,t){var r=t-e;return r?zu(e,r>180||r<-180?r-360*Math.round(r/360):r):eo(isNaN(e)?t:e)}function Db(e){return(e=+e)==1?Wu:function(t,r){return r-t?$b(t,r,e):eo(isNaN(t)?r:t)}}function Wu(e,t){var r=t-e;return r?zu(e,r):eo(isNaN(e)?t:e)}const El=function e(t){var r=Db(t);function i(n,a){var o=r((n=ds(n)).r,(a=ds(a)).r),s=r(n.g,a.g),c=r(n.b,a.b),l=Wu(n.opacity,a.opacity);return function(h){return n.r=o(h),n.g=s(h),n.b=c(h),n.opacity=l(h),n+""}}return i.gamma=e,i}(1);function Ne(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}var ps=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,$a=new RegExp(ps.source,"g");function Rb(e){return function(){return e}}function Ob(e){return function(t){return e(t)+""}}function Ib(e,t){var r=ps.lastIndex=$a.lastIndex=0,i,n,a,o=-1,s=[],c=[];for(e=e+"",t=t+"";(i=ps.exec(e))&&(n=$a.exec(t));)(a=n.index)>r&&(a=t.slice(r,a),s[o]?s[o]+=a:s[++o]=a),(i=i[0])===(n=n[0])?s[o]?s[o]+=n:s[++o]=n:(s[++o]=null,c.push({i:o,x:Ne(i,n)})),r=$a.lastIndex;return r<t.length&&(a=t.slice(r),s[o]?s[o]+=a:s[++o]=a),s.length<2?c[0]?Ob(c[0].x):Rb(t):(t=c.length,function(l){for(var h=0,u;h<t;++h)s[(u=c[h]).i]=u.x(l);return s.join("")})}var Fl=180/Math.PI,gs={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function qu(e,t,r,i,n,a){var o,s,c;return(o=Math.sqrt(e*e+t*t))&&(e/=o,t/=o),(c=e*r+t*i)&&(r-=e*c,i-=t*c),(s=Math.sqrt(r*r+i*i))&&(r/=s,i/=s,c/=s),e*i<t*r&&(e=-e,t=-t,c=-c,o=-o),{translateX:n,translateY:a,rotate:Math.atan2(t,e)*Fl,skewX:Math.atan(c)*Fl,scaleX:o,scaleY:s}}var Vi;function Pb(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?gs:qu(t.a,t.b,t.c,t.d,t.e,t.f)}function Nb(e){return e==null||(Vi||(Vi=document.createElementNS("http://www.w3.org/2000/svg","g")),Vi.setAttribute("transform",e),!(e=Vi.transform.baseVal.consolidate()))?gs:(e=e.matrix,qu(e.a,e.b,e.c,e.d,e.e,e.f))}function Hu(e,t,r,i){function n(l){return l.length?l.pop()+" ":""}function a(l,h,u,f,d,g){if(l!==u||h!==f){var m=d.push("translate(",null,t,null,r);g.push({i:m-4,x:Ne(l,u)},{i:m-2,x:Ne(h,f)})}else(u||f)&&d.push("translate("+u+t+f+r)}function o(l,h,u,f){l!==h?(l-h>180?h+=360:h-l>180&&(l+=360),f.push({i:u.push(n(u)+"rotate(",null,i)-2,x:Ne(l,h)})):h&&u.push(n(u)+"rotate("+h+i)}function s(l,h,u,f){l!==h?f.push({i:u.push(n(u)+"skewX(",null,i)-2,x:Ne(l,h)}):h&&u.push(n(u)+"skewX("+h+i)}function c(l,h,u,f,d,g){if(l!==u||h!==f){var m=d.push(n(d)+"scale(",null,",",null,")");g.push({i:m-4,x:Ne(l,u)},{i:m-2,x:Ne(h,f)})}else(u!==1||f!==1)&&d.push(n(d)+"scale("+u+","+f+")")}return function(l,h){var u=[],f=[];return l=e(l),h=e(h),a(l.translateX,l.translateY,h.translateX,h.translateY,u,f),o(l.rotate,h.rotate,u,f),s(l.skewX,h.skewX,u,f),c(l.scaleX,l.scaleY,h.scaleX,h.scaleY,u,f),l=h=null,function(d){for(var g=-1,m=f.length,y;++g<m;)u[(y=f[g]).i]=y.x(d);return u.join("")}}}var zb=Hu(Pb,"px, ","px)","deg)"),Wb=Hu(Nb,", ",")",")"),Wr=0,hi=0,ri=0,Yu=1e3,En,ui,Fn=0,sr=0,ra=0,Li=typeof performance=="object"&&performance.now?performance:Date,ju=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function ro(){return sr||(ju(qb),sr=Li.now()+ra)}function qb(){sr=0}function $n(){this._call=this._time=this._next=null}$n.prototype=Uu.prototype={constructor:$n,restart:function(e,t,r){if(typeof e!="function")throw new TypeError("callback is not a function");r=(r==null?ro():+r)+(t==null?0:+t),!this._next&&ui!==this&&(ui?ui._next=this:En=this,ui=this),this._call=e,this._time=r,ms()},stop:function(){this._call&&(this._call=null,this._time=1/0,ms())}};function Uu(e,t,r){var i=new $n;return i.restart(e,t,r),i}function Hb(){ro(),++Wr;for(var e=En,t;e;)(t=sr-e._time)>=0&&e._call.call(void 0,t),e=e._next;--Wr}function $l(){sr=(Fn=Li.now())+ra,Wr=hi=0;try{Hb()}finally{Wr=0,jb(),sr=0}}function Yb(){var e=Li.now(),t=e-Fn;t>Yu&&(ra-=t,Fn=e)}function jb(){for(var e,t=En,r,i=1/0;t;)t._call?(i>t._time&&(i=t._time),e=t,t=t._next):(r=t._next,t._next=null,t=e?e._next=r:En=r);ui=e,ms(i)}function ms(e){if(!Wr){hi&&(hi=clearTimeout(hi));var t=e-sr;t>24?(e<1/0&&(hi=setTimeout($l,e-Li.now()-ra)),ri&&(ri=clearInterval(ri))):(ri||(Fn=Li.now(),ri=setInterval(Yb,Yu)),Wr=1,ju($l))}}function Dl(e,t,r){var i=new $n;return t=t==null?0:+t,i.restart(n=>{i.stop(),e(n+t)},t,r),i}var Ub=Su("start","end","cancel","interrupt"),Gb=[],Gu=0,Rl=1,ys=2,ln=3,Ol=4,xs=5,cn=6;function ia(e,t,r,i,n,a){var o=e.__transition;if(!o)e.__transition={};else if(r in o)return;Vb(e,r,{name:t,index:i,group:n,on:Ub,tween:Gb,time:a.time,delay:a.delay,duration:a.duration,ease:a.ease,timer:null,state:Gu})}function io(e,t){var r=he(e,t);if(r.state>Gu)throw new Error("too late; already scheduled");return r}function be(e,t){var r=he(e,t);if(r.state>ln)throw new Error("too late; already running");return r}function he(e,t){var r=e.__transition;if(!r||!(r=r[t]))throw new Error("transition not found");return r}function Vb(e,t,r){var i=e.__transition,n;i[t]=r,r.timer=Uu(a,0,r.time);function a(l){r.state=Rl,r.timer.restart(o,r.delay,r.time),r.delay<=l&&o(l-r.delay)}function o(l){var h,u,f,d;if(r.state!==Rl)return c();for(h in i)if(d=i[h],d.name===r.name){if(d.state===ln)return Dl(o);d.state===Ol?(d.state=cn,d.timer.stop(),d.on.call("interrupt",e,e.__data__,d.index,d.group),delete i[h]):+h<t&&(d.state=cn,d.timer.stop(),d.on.call("cancel",e,e.__data__,d.index,d.group),delete i[h])}if(Dl(function(){r.state===ln&&(r.state=Ol,r.timer.restart(s,r.delay,r.time),s(l))}),r.state=ys,r.on.call("start",e,e.__data__,r.index,r.group),r.state===ys){for(r.state=ln,n=new Array(f=r.tween.length),h=0,u=-1;h<f;++h)(d=r.tween[h].value.call(e,e.__data__,r.index,r.group))&&(n[++u]=d);n.length=u+1}}function s(l){for(var h=l<r.duration?r.ease.call(null,l/r.duration):(r.timer.restart(c),r.state=xs,1),u=-1,f=n.length;++u<f;)n[u].call(e,h);r.state===xs&&(r.on.call("end",e,e.__data__,r.index,r.group),c())}function c(){r.state=cn,r.timer.stop(),delete i[t];for(var l in i)return;delete e.__transition}}function Xb(e,t){var r=e.__transition,i,n,a=!0,o;if(r){t=t==null?null:t+"";for(o in r){if((i=r[o]).name!==t){a=!1;continue}n=i.state>ys&&i.state<xs,i.state=cn,i.timer.stop(),i.on.call(n?"interrupt":"cancel",e,e.__data__,i.index,i.group),delete r[o]}a&&delete e.__transition}}function Zb(e){return this.each(function(){Xb(this,e)})}function Kb(e,t){var r,i;return function(){var n=be(this,e),a=n.tween;if(a!==r){i=r=a;for(var o=0,s=i.length;o<s;++o)if(i[o].name===t){i=i.slice(),i.splice(o,1);break}}n.tween=i}}function Qb(e,t,r){var i,n;if(typeof r!="function")throw new Error;return function(){var a=be(this,e),o=a.tween;if(o!==i){n=(i=o).slice();for(var s={name:t,value:r},c=0,l=n.length;c<l;++c)if(n[c].name===t){n[c]=s;break}c===l&&n.push(s)}a.tween=n}}function Jb(e,t){var r=this._id;if(e+="",arguments.length<2){for(var i=he(this.node(),r).tween,n=0,a=i.length,o;n<a;++n)if((o=i[n]).name===e)return o.value;return null}return this.each((t==null?Kb:Qb)(r,e,t))}function no(e,t,r){var i=e._id;return e.each(function(){var n=be(this,i);(n.value||(n.value={}))[t]=r.apply(this,arguments)}),function(n){return he(n,i).value[t]}}function Vu(e,t){var r;return(typeof t=="number"?Ne:t instanceof Ti?El:(r=Ti(t))?(t=r,El):Ib)(e,t)}function t1(e){return function(){this.removeAttribute(e)}}function e1(e){return function(){this.removeAttributeNS(e.space,e.local)}}function r1(e,t,r){var i,n=r+"",a;return function(){var o=this.getAttribute(e);return o===n?null:o===i?a:a=t(i=o,r)}}function i1(e,t,r){var i,n=r+"",a;return function(){var o=this.getAttributeNS(e.space,e.local);return o===n?null:o===i?a:a=t(i=o,r)}}function n1(e,t,r){var i,n,a;return function(){var o,s=r(this),c;return s==null?void this.removeAttribute(e):(o=this.getAttribute(e),c=s+"",o===c?null:o===i&&c===n?a:(n=c,a=t(i=o,s)))}}function a1(e,t,r){var i,n,a;return function(){var o,s=r(this),c;return s==null?void this.removeAttributeNS(e.space,e.local):(o=this.getAttributeNS(e.space,e.local),c=s+"",o===c?null:o===i&&c===n?a:(n=c,a=t(i=o,s)))}}function s1(e,t){var r=ea(e),i=r==="transform"?Wb:Vu;return this.attrTween(e,typeof t=="function"?(r.local?a1:n1)(r,i,no(this,"attr."+e,t)):t==null?(r.local?e1:t1)(r):(r.local?i1:r1)(r,i,t))}function o1(e,t){return function(r){this.setAttribute(e,t.call(this,r))}}function l1(e,t){return function(r){this.setAttributeNS(e.space,e.local,t.call(this,r))}}function c1(e,t){var r,i;function n(){var a=t.apply(this,arguments);return a!==i&&(r=(i=a)&&l1(e,a)),r}return n._value=t,n}function h1(e,t){var r,i;function n(){var a=t.apply(this,arguments);return a!==i&&(r=(i=a)&&o1(e,a)),r}return n._value=t,n}function u1(e,t){var r="attr."+e;if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;var i=ea(e);return this.tween(r,(i.local?c1:h1)(i,t))}function f1(e,t){return function(){io(this,e).delay=+t.apply(this,arguments)}}function d1(e,t){return t=+t,function(){io(this,e).delay=t}}function p1(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?f1:d1)(t,e)):he(this.node(),t).delay}function g1(e,t){return function(){be(this,e).duration=+t.apply(this,arguments)}}function m1(e,t){return t=+t,function(){be(this,e).duration=t}}function y1(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?g1:m1)(t,e)):he(this.node(),t).duration}function x1(e,t){if(typeof t!="function")throw new Error;return function(){be(this,e).ease=t}}function b1(e){var t=this._id;return arguments.length?this.each(x1(t,e)):he(this.node(),t).ease}function _1(e,t){return function(){var r=t.apply(this,arguments);if(typeof r!="function")throw new Error;be(this,e).ease=r}}function C1(e){if(typeof e!="function")throw new Error;return this.each(_1(this._id,e))}function w1(e){typeof e!="function"&&(e=Bu(e));for(var t=this._groups,r=t.length,i=new Array(r),n=0;n<r;++n)for(var a=t[n],o=a.length,s=i[n]=[],c,l=0;l<o;++l)(c=a[l])&&e.call(c,c.__data__,l,a)&&s.push(c);return new De(i,this._parents,this._name,this._id)}function k1(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,r=e._groups,i=t.length,n=r.length,a=Math.min(i,n),o=new Array(i),s=0;s<a;++s)for(var c=t[s],l=r[s],h=c.length,u=o[s]=new Array(h),f,d=0;d<h;++d)(f=c[d]||l[d])&&(u[d]=f);for(;s<i;++s)o[s]=t[s];return new De(o,this._parents,this._name,this._id)}function v1(e){return(e+"").trim().split(/^|\s+/).every(function(t){var r=t.indexOf(".");return r>=0&&(t=t.slice(0,r)),!t||t==="start"})}function S1(e,t,r){var i,n,a=v1(t)?io:be;return function(){var o=a(this,e),s=o.on;s!==i&&(n=(i=s).copy()).on(t,r),o.on=n}}function T1(e,t){var r=this._id;return arguments.length<2?he(this.node(),r).on.on(e):this.each(S1(r,e,t))}function L1(e){return function(){var t=this.parentNode;for(var r in this.__transition)if(+r!==e)return;t&&t.removeChild(this)}}function B1(){return this.on("end.remove",L1(this._id))}function A1(e){var t=this._name,r=this._id;typeof e!="function"&&(e=Qs(e));for(var i=this._groups,n=i.length,a=new Array(n),o=0;o<n;++o)for(var s=i[o],c=s.length,l=a[o]=new Array(c),h,u,f=0;f<c;++f)(h=s[f])&&(u=e.call(h,h.__data__,f,s))&&("__data__"in h&&(u.__data__=h.__data__),l[f]=u,ia(l[f],t,r,f,l,he(h,r)));return new De(a,this._parents,t,r)}function M1(e){var t=this._name,r=this._id;typeof e!="function"&&(e=Lu(e));for(var i=this._groups,n=i.length,a=[],o=[],s=0;s<n;++s)for(var c=i[s],l=c.length,h,u=0;u<l;++u)if(h=c[u]){for(var f=e.call(h,h.__data__,u,c),d,g=he(h,r),m=0,y=f.length;m<y;++m)(d=f[m])&&ia(d,t,r,m,f,g);a.push(f),o.push(h)}return new De(a,o,t,r)}var E1=$i.prototype.constructor;function F1(){return new E1(this._groups,this._parents)}function $1(e,t){var r,i,n;return function(){var a=zr(this,e),o=(this.style.removeProperty(e),zr(this,e));return a===o?null:a===r&&o===i?n:n=t(r=a,i=o)}}function Xu(e){return function(){this.style.removeProperty(e)}}function D1(e,t,r){var i,n=r+"",a;return function(){var o=zr(this,e);return o===n?null:o===i?a:a=t(i=o,r)}}function R1(e,t,r){var i,n,a;return function(){var o=zr(this,e),s=r(this),c=s+"";return s==null&&(c=s=(this.style.removeProperty(e),zr(this,e))),o===c?null:o===i&&c===n?a:(n=c,a=t(i=o,s))}}function O1(e,t){var r,i,n,a="style."+t,o="end."+a,s;return function(){var c=be(this,e),l=c.on,h=c.value[a]==null?s||(s=Xu(t)):void 0;(l!==r||n!==h)&&(i=(r=l).copy()).on(o,n=h),c.on=i}}function I1(e,t,r){var i=(e+="")=="transform"?zb:Vu;return t==null?this.styleTween(e,$1(e,i)).on("end.style."+e,Xu(e)):typeof t=="function"?this.styleTween(e,R1(e,i,no(this,"style."+e,t))).each(O1(this._id,e)):this.styleTween(e,D1(e,i,t),r).on("end.style."+e,null)}function P1(e,t,r){return function(i){this.style.setProperty(e,t.call(this,i),r)}}function N1(e,t,r){var i,n;function a(){var o=t.apply(this,arguments);return o!==n&&(i=(n=o)&&P1(e,o,r)),i}return a._value=t,a}function z1(e,t,r){var i="style."+(e+="");if(arguments.length<2)return(i=this.tween(i))&&i._value;if(t==null)return this.tween(i,null);if(typeof t!="function")throw new Error;return this.tween(i,N1(e,t,r??""))}function W1(e){return function(){this.textContent=e}}function q1(e){return function(){var t=e(this);this.textContent=t??""}}function H1(e){return this.tween("text",typeof e=="function"?q1(no(this,"text",e)):W1(e==null?"":e+""))}function Y1(e){return function(t){this.textContent=e.call(this,t)}}function j1(e){var t,r;function i(){var n=e.apply(this,arguments);return n!==r&&(t=(r=n)&&Y1(n)),t}return i._value=e,i}function U1(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,j1(e))}function G1(){for(var e=this._name,t=this._id,r=Zu(),i=this._groups,n=i.length,a=0;a<n;++a)for(var o=i[a],s=o.length,c,l=0;l<s;++l)if(c=o[l]){var h=he(c,t);ia(c,e,r,l,o,{time:h.time+h.delay+h.duration,delay:0,duration:h.duration,ease:h.ease})}return new De(i,this._parents,e,r)}function V1(){var e,t,r=this,i=r._id,n=r.size();return new Promise(function(a,o){var s={value:o},c={value:function(){--n===0&&a()}};r.each(function(){var l=be(this,i),h=l.on;h!==e&&(t=(e=h).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(c)),l.on=t}),n===0&&a()})}var X1=0;function De(e,t,r,i){this._groups=e,this._parents=t,this._name=r,this._id=i}function Zu(){return++X1}var Te=$i.prototype;De.prototype={constructor:De,select:A1,selectAll:M1,selectChild:Te.selectChild,selectChildren:Te.selectChildren,filter:w1,merge:k1,selection:F1,transition:G1,call:Te.call,nodes:Te.nodes,node:Te.node,size:Te.size,empty:Te.empty,each:Te.each,on:T1,attr:s1,attrTween:u1,style:I1,styleTween:z1,text:H1,textTween:U1,remove:B1,tween:Jb,delay:p1,duration:y1,ease:b1,easeVarying:C1,end:V1,[Symbol.iterator]:Te[Symbol.iterator]};function Z1(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var K1={time:null,delay:0,duration:250,ease:Z1};function Q1(e,t){for(var r;!(r=e.__transition)||!(r=r[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return r}function J1(e){var t,r;e instanceof De?(t=e._id,e=e._name):(t=Zu(),(r=K1).time=ro(),e=e==null?null:e+"");for(var i=this._groups,n=i.length,a=0;a<n;++a)for(var o=i[a],s=o.length,c,l=0;l<s;++l)(c=o[l])&&ia(c,e,t,l,o,r||Q1(c,t));return new De(i,this._parents,e,t)}$i.prototype.interrupt=Zb;$i.prototype.transition=J1;const bs=Math.PI,_s=2*bs,Xe=1e-6,t2=_s-Xe;function Ku(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function e2(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Ku;const r=10**t;return function(i){this._+=i[0];for(let n=1,a=i.length;n<a;++n)this._+=Math.round(arguments[n]*r)/r+i[n]}}class r2{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Ku:e2(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,i,n){this._append`Q${+t},${+r},${this._x1=+i},${this._y1=+n}`}bezierCurveTo(t,r,i,n,a,o){this._append`C${+t},${+r},${+i},${+n},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,i,n,a){if(t=+t,r=+r,i=+i,n=+n,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,s=this._y1,c=i-t,l=n-r,h=o-t,u=s-r,f=h*h+u*u;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(f>Xe)if(!(Math.abs(u*c-l*h)>Xe)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let d=i-o,g=n-s,m=c*c+l*l,y=d*d+g*g,x=Math.sqrt(m),b=Math.sqrt(f),C=a*Math.tan((bs-Math.acos((m+f-y)/(2*x*b)))/2),v=C/b,S=C/x;Math.abs(v-1)>Xe&&this._append`L${t+v*h},${r+v*u}`,this._append`A${a},${a},0,0,${+(u*d>h*g)},${this._x1=t+S*c},${this._y1=r+S*l}`}}arc(t,r,i,n,a,o){if(t=+t,r=+r,i=+i,o=!!o,i<0)throw new Error(`negative radius: ${i}`);let s=i*Math.cos(n),c=i*Math.sin(n),l=t+s,h=r+c,u=1^o,f=o?n-a:a-n;this._x1===null?this._append`M${l},${h}`:(Math.abs(this._x1-l)>Xe||Math.abs(this._y1-h)>Xe)&&this._append`L${l},${h}`,i&&(f<0&&(f=f%_s+_s),f>t2?this._append`A${i},${i},0,1,${u},${t-s},${r-c}A${i},${i},0,1,${u},${this._x1=l},${this._y1=h}`:f>Xe&&this._append`A${i},${i},0,${+(f>=bs)},${u},${this._x1=t+i*Math.cos(a)},${this._y1=r+i*Math.sin(a)}`)}rect(t,r,i,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${i=+i}v${+n}h${-i}Z`}toString(){return this._}}function yr(e){return function(){return e}}const Wv=Math.abs,qv=Math.atan2,Hv=Math.cos,Yv=Math.max,jv=Math.min,Uv=Math.sin,Gv=Math.sqrt,Il=1e-12,ao=Math.PI,Pl=ao/2,Vv=2*ao;function Xv(e){return e>1?0:e<-1?ao:Math.acos(e)}function Zv(e){return e>=1?Pl:e<=-1?-Pl:Math.asin(e)}function i2(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const i=Math.floor(r);if(!(i>=0))throw new RangeError(`invalid digits: ${r}`);t=i}return e},()=>new r2(t)}function n2(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Qu(e){this._context=e}Qu.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function Dn(e){return new Qu(e)}function a2(e){return e[0]}function s2(e){return e[1]}function o2(e,t){var r=yr(!0),i=null,n=Dn,a=null,o=i2(s);e=typeof e=="function"?e:e===void 0?a2:yr(e),t=typeof t=="function"?t:t===void 0?s2:yr(t);function s(c){var l,h=(c=n2(c)).length,u,f=!1,d;for(i==null&&(a=n(d=o())),l=0;l<=h;++l)!(l<h&&r(u=c[l],l,c))===f&&((f=!f)?a.lineStart():a.lineEnd()),f&&a.point(+e(u,l,c),+t(u,l,c));if(d)return a=null,d+""||null}return s.x=function(c){return arguments.length?(e=typeof c=="function"?c:yr(+c),s):e},s.y=function(c){return arguments.length?(t=typeof c=="function"?c:yr(+c),s):t},s.defined=function(c){return arguments.length?(r=typeof c=="function"?c:yr(!!c),s):r},s.curve=function(c){return arguments.length?(n=c,i!=null&&(a=n(i)),s):n},s.context=function(c){return arguments.length?(c==null?i=a=null:a=n(i=c),s):i},s}class Ju{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function tf(e){return new Ju(e,!0)}function ef(e){return new Ju(e,!1)}function He(){}function Rn(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function na(e){this._context=e}na.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Rn(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Rn(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function hn(e){return new na(e)}function rf(e){this._context=e}rf.prototype={areaStart:He,areaEnd:He,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Rn(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function l2(e){return new rf(e)}function nf(e){this._context=e}nf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,i=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,i):this._context.moveTo(r,i);break;case 3:this._point=4;default:Rn(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function c2(e){return new nf(e)}function af(e,t){this._basis=new na(e),this._beta=t}af.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var e=this._x,t=this._y,r=e.length-1;if(r>0)for(var i=e[0],n=t[0],a=e[r]-i,o=t[r]-n,s=-1,c;++s<=r;)c=s/r,this._basis.point(this._beta*e[s]+(1-this._beta)*(i+c*a),this._beta*t[s]+(1-this._beta)*(n+c*o));this._x=this._y=null,this._basis.lineEnd()},point:function(e,t){this._x.push(+e),this._y.push(+t)}};const h2=function e(t){function r(i){return t===1?new na(i):new af(i,t)}return r.beta=function(i){return e(+i)},r}(.85);function On(e,t,r){e._context.bezierCurveTo(e._x1+e._k*(e._x2-e._x0),e._y1+e._k*(e._y2-e._y0),e._x2+e._k*(e._x1-t),e._y2+e._k*(e._y1-r),e._x2,e._y2)}function so(e,t){this._context=e,this._k=(1-t)/6}so.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:On(this,this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2,this._x1=e,this._y1=t;break;case 2:this._point=3;default:On(this,e,t);break}this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const sf=function e(t){function r(i){return new so(i,t)}return r.tension=function(i){return e(+i)},r}(0);function oo(e,t){this._context=e,this._k=(1-t)/6}oo.prototype={areaStart:He,areaEnd:He,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x3=e,this._y3=t;break;case 1:this._point=2,this._context.moveTo(this._x4=e,this._y4=t);break;case 2:this._point=3,this._x5=e,this._y5=t;break;default:On(this,e,t);break}this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const u2=function e(t){function r(i){return new oo(i,t)}return r.tension=function(i){return e(+i)},r}(0);function lo(e,t){this._context=e,this._k=(1-t)/6}lo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:On(this,e,t);break}this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const f2=function e(t){function r(i){return new lo(i,t)}return r.tension=function(i){return e(+i)},r}(0);function co(e,t,r){var i=e._x1,n=e._y1,a=e._x2,o=e._y2;if(e._l01_a>Il){var s=2*e._l01_2a+3*e._l01_a*e._l12_a+e._l12_2a,c=3*e._l01_a*(e._l01_a+e._l12_a);i=(i*s-e._x0*e._l12_2a+e._x2*e._l01_2a)/c,n=(n*s-e._y0*e._l12_2a+e._y2*e._l01_2a)/c}if(e._l23_a>Il){var l=2*e._l23_2a+3*e._l23_a*e._l12_a+e._l12_2a,h=3*e._l23_a*(e._l23_a+e._l12_a);a=(a*l+e._x1*e._l23_2a-t*e._l12_2a)/h,o=(o*l+e._y1*e._l23_2a-r*e._l12_2a)/h}e._context.bezierCurveTo(i,n,a,o,e._x2,e._y2)}function of(e,t){this._context=e,this._alpha=t}of.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){if(e=+e,t=+t,this._point){var r=this._x2-e,i=this._y2-t;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(r*r+i*i,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3;default:co(this,e,t);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const lf=function e(t){function r(i){return t?new of(i,t):new so(i,0)}return r.alpha=function(i){return e(+i)},r}(.5);function cf(e,t){this._context=e,this._alpha=t}cf.prototype={areaStart:He,areaEnd:He,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(e,t){if(e=+e,t=+t,this._point){var r=this._x2-e,i=this._y2-t;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(r*r+i*i,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=e,this._y3=t;break;case 1:this._point=2,this._context.moveTo(this._x4=e,this._y4=t);break;case 2:this._point=3,this._x5=e,this._y5=t;break;default:co(this,e,t);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const d2=function e(t){function r(i){return t?new cf(i,t):new oo(i,0)}return r.alpha=function(i){return e(+i)},r}(.5);function hf(e,t){this._context=e,this._alpha=t}hf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){if(e=+e,t=+t,this._point){var r=this._x2-e,i=this._y2-t;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(r*r+i*i,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:co(this,e,t);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const p2=function e(t){function r(i){return t?new hf(i,t):new lo(i,0)}return r.alpha=function(i){return e(+i)},r}(.5);function uf(e){this._context=e}uf.prototype={areaStart:He,areaEnd:He,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function g2(e){return new uf(e)}function Nl(e){return e<0?-1:1}function zl(e,t,r){var i=e._x1-e._x0,n=t-e._x1,a=(e._y1-e._y0)/(i||n<0&&-0),o=(r-e._y1)/(n||i<0&&-0),s=(a*n+o*i)/(i+n);return(Nl(a)+Nl(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(s))||0}function Wl(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Da(e,t,r){var i=e._x0,n=e._y0,a=e._x1,o=e._y1,s=(a-i)/3;e._context.bezierCurveTo(i+s,n+s*t,a-s,o-s*r,a,o)}function In(e){this._context=e}In.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Da(this,this._t0,Wl(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Da(this,Wl(this,r=zl(this,e,t)),r);break;default:Da(this,this._t0,r=zl(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function ff(e){this._context=new df(e)}(ff.prototype=Object.create(In.prototype)).point=function(e,t){In.prototype.point.call(this,t,e)};function df(e){this._context=e}df.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,i,n,a){this._context.bezierCurveTo(t,e,i,r,a,n)}};function pf(e){return new In(e)}function gf(e){return new ff(e)}function mf(e){this._context=e}mf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var i=ql(e),n=ql(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(i[0][a],n[0][a],i[1][a],n[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function ql(e){var t,r=e.length-1,i,n=new Array(r),a=new Array(r),o=new Array(r);for(n[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)n[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(n[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)i=n[t]/a[t-1],a[t]-=i,o[t]-=i*o[t-1];for(n[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)n[t]=(o[t]-n[t+1])/a[t];for(a[r-1]=(e[r]+n[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-n[t+1];return[n,a]}function yf(e){return new mf(e)}function aa(e,t){this._context=e,this._t=t}aa.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function xf(e){return new aa(e,.5)}function bf(e){return new aa(e,0)}function _f(e){return new aa(e,1)}function fi(e,t,r){this.k=e,this.x=t,this.y=r}fi.prototype={constructor:fi,scale:function(e){return e===1?this:new fi(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new fi(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};fi.prototype;var m2="​",y2={curveBasis:hn,curveBasisClosed:l2,curveBasisOpen:c2,curveBumpX:tf,curveBumpY:ef,curveBundle:h2,curveCardinalClosed:u2,curveCardinalOpen:f2,curveCardinal:sf,curveCatmullRomClosed:d2,curveCatmullRomOpen:p2,curveCatmullRom:lf,curveLinear:Dn,curveLinearClosed:g2,curveMonotoneX:pf,curveMonotoneY:gf,curveNatural:yf,curveStep:xf,curveStepAfter:_f,curveStepBefore:bf},x2=/\s*(?:(\w+)(?=:):|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi,b2=p(function(e,t){const r=Cf(e,/(?:init\b)|(?:initialize\b)/);let i={};if(Array.isArray(r)){const o=r.map(s=>s.args);yn(o),i=Mt(i,[...o])}else i=r.args;if(!i)return;let n=Os(e,t);const a="config";return i[a]!==void 0&&(n==="flowchart-v2"&&(n="flowchart"),i[n]=i[a],delete i[a]),i},"detectInit"),Cf=p(function(e,t=null){var r,i;try{const n=new RegExp(`[%]{2}(?![{]${x2.source})(?=[}][%]{2}).*
`,"ig");e=e.trim().replace(n,"").replace(/'/gm,'"'),D.debug(`Detecting diagram directive${t!==null?" type:"+t:""} based on the text:${e}`);let a;const o=[];for(;(a=yi.exec(e))!==null;)if(a.index===yi.lastIndex&&yi.lastIndex++,a&&!t||t&&((r=a[1])!=null&&r.match(t))||t&&((i=a[2])!=null&&i.match(t))){const s=a[1]?a[1]:a[2],c=a[3]?a[3].trim():a[4]?JSON.parse(a[4].trim()):null;o.push({type:s,args:c})}return o.length===0?{type:e,args:null}:o.length===1?o[0]:o}catch(n){return D.error(`ERROR: ${n.message} - Unable to parse directive type: '${t}' based on the text: '${e}'`),{type:void 0,args:null}}},"detectDirective"),_2=p(function(e){return e.replace(yi,"")},"removeDirectives"),C2=p(function(e,t){for(const[r,i]of t.entries())if(i.match(e))return r;return-1},"isSubstringInArray");function ho(e,t){if(!e)return t;const r=`curve${e.charAt(0).toUpperCase()+e.slice(1)}`;return y2[r]??t}p(ho,"interpolateToCurve");function wf(e,t){const r=e.trim();if(r)return t.securityLevel!=="loose"?vu(r):r}p(wf,"formatUrl");var w2=p((e,...t)=>{const r=e.split("."),i=r.length-1,n=r[i];let a=window;for(let o=0;o<i;o++)if(a=a[r[o]],!a){D.error(`Function name: ${e} not found in window`);return}a[n](...t)},"runFunc");function uo(e,t){return!e||!t?0:Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}p(uo,"distance");function kf(e){let t,r=0;e.forEach(n=>{r+=uo(n,t),t=n});const i=r/2;return fo(e,i)}p(kf,"traverseEdge");function vf(e){return e.length===1?e[0]:kf(e)}p(vf,"calcLabelPosition");var Hl=p((e,t=2)=>{const r=Math.pow(10,t);return Math.round(e*r)/r},"roundNumber"),fo=p((e,t)=>{let r,i=t;for(const n of e){if(r){const a=uo(n,r);if(a===0)return r;if(a<i)i-=a;else{const o=i/a;if(o<=0)return r;if(o>=1)return{x:n.x,y:n.y};if(o>0&&o<1)return{x:Hl((1-o)*r.x+o*n.x,5),y:Hl((1-o)*r.y+o*n.y,5)}}}r=n}throw new Error("Could not find a suitable point for the given distance")},"calculatePoint"),k2=p((e,t,r)=>{D.info(`our points ${JSON.stringify(t)}`),t[0]!==r&&(t=t.reverse());const n=fo(t,25),a=e?10:5,o=Math.atan2(t[0].y-n.y,t[0].x-n.x),s={x:0,y:0};return s.x=Math.sin(o)*a+(t[0].x+n.x)/2,s.y=-Math.cos(o)*a+(t[0].y+n.y)/2,s},"calcCardinalityPosition");function Sf(e,t,r){const i=structuredClone(r);D.info("our points",i),t!=="start_left"&&t!=="start_right"&&i.reverse();const n=25+e,a=fo(i,n),o=10+e*.5,s=Math.atan2(i[0].y-a.y,i[0].x-a.x),c={x:0,y:0};return t==="start_left"?(c.x=Math.sin(s+Math.PI)*o+(i[0].x+a.x)/2,c.y=-Math.cos(s+Math.PI)*o+(i[0].y+a.y)/2):t==="end_right"?(c.x=Math.sin(s-Math.PI)*o+(i[0].x+a.x)/2-5,c.y=-Math.cos(s-Math.PI)*o+(i[0].y+a.y)/2-5):t==="end_left"?(c.x=Math.sin(s)*o+(i[0].x+a.x)/2-5,c.y=-Math.cos(s)*o+(i[0].y+a.y)/2-5):(c.x=Math.sin(s)*o+(i[0].x+a.x)/2,c.y=-Math.cos(s)*o+(i[0].y+a.y)/2),c}p(Sf,"calcTerminalLabelPosition");function Tf(e){let t="",r="";for(const i of e)i!==void 0&&(i.startsWith("color:")||i.startsWith("text-align:")?r=r+i+";":t=t+i+";");return{style:t,labelStyle:r}}p(Tf,"getStylesFromArray");var Yl=0,v2=p(()=>(Yl++,"id-"+Math.random().toString(36).substr(2,12)+"-"+Yl),"generateId");function Lf(e){let t="";const r="0123456789abcdef",i=r.length;for(let n=0;n<e;n++)t+=r.charAt(Math.floor(Math.random()*i));return t}p(Lf,"makeRandomHex");var S2=p(e=>Lf(e.length),"random"),T2=p(function(){return{x:0,y:0,fill:void 0,anchor:"start",style:"#666",width:100,height:100,textMargin:0,rx:0,ry:0,valign:void 0,text:""}},"getTextObj"),L2=p(function(e,t){const r=t.text.replace(Yr.lineBreakRegex," "),[,i]=sa(t.fontSize),n=e.append("text");n.attr("x",t.x),n.attr("y",t.y),n.style("text-anchor",t.anchor),n.style("font-family",t.fontFamily),n.style("font-size",i),n.style("font-weight",t.fontWeight),n.attr("fill",t.fill),t.class!==void 0&&n.attr("class",t.class);const a=n.append("tspan");return a.attr("x",t.x+t.textMargin*2),a.attr("fill",t.fill),a.text(r),n},"drawSimpleText"),B2=Ds((e,t,r)=>{if(!e||(r=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",joinWith:"<br/>"},r),Yr.lineBreakRegex.test(e)))return e;const i=e.split(" ").filter(Boolean),n=[];let a="";return i.forEach((o,s)=>{const c=Re(`${o} `,r),l=Re(a,r);if(c>t){const{hyphenatedStrings:f,remainingWord:d}=A2(o,t,"-",r);n.push(a,...f),a=d}else l+c>=t?(n.push(a),a=o):a=[a,o].filter(Boolean).join(" ");s+1===i.length&&n.push(a)}),n.filter(o=>o!=="").join(r.joinWith)},(e,t,r)=>`${e}${t}${r.fontSize}${r.fontWeight}${r.fontFamily}${r.joinWith}`),A2=Ds((e,t,r="-",i)=>{i=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",margin:0},i);const n=[...e],a=[];let o="";return n.forEach((s,c)=>{const l=`${o}${s}`;if(Re(l,i)>=t){const u=c+1,f=n.length===u,d=`${l}${r}`;a.push(f?l:d),o=""}else o=l}),{hyphenatedStrings:a,remainingWord:o}},(e,t,r="-",i)=>`${e}${t}${r}${i.fontSize}${i.fontWeight}${i.fontFamily}`);function Bf(e,t){return po(e,t).height}p(Bf,"calculateTextHeight");function Re(e,t){return po(e,t).width}p(Re,"calculateTextWidth");var po=Ds((e,t)=>{const{fontSize:r=12,fontFamily:i="Arial",fontWeight:n=400}=t;if(!e)return{width:0,height:0};const[,a]=sa(r),o=["sans-serif",i],s=e.split(Yr.lineBreakRegex),c=[],l=lt("body");if(!l.remove)return{width:0,height:0,lineHeight:0};const h=l.append("svg");for(const f of o){let d=0;const g={width:0,height:0,lineHeight:0};for(const m of s){const y=T2();y.text=m||m2;const x=L2(h,y).style("font-size",a).style("font-weight",n).style("font-family",f),b=(x._groups||x)[0][0].getBBox();if(b.width===0&&b.height===0)throw new Error("svg element not in render tree");g.width=Math.round(Math.max(g.width,b.width)),d=Math.round(b.height),g.height+=d,g.lineHeight=Math.round(Math.max(g.lineHeight,d))}c.push(g)}h.remove();const u=isNaN(c[1].height)||isNaN(c[1].width)||isNaN(c[1].lineHeight)||c[0].height>c[1].height&&c[0].width>c[1].width&&c[0].lineHeight>c[1].lineHeight?0:1;return c[u]},(e,t)=>`${e}${t.fontSize}${t.fontWeight}${t.fontFamily}`),Fr,M2=(Fr=class{constructor(t=!1,r){this.count=0,this.count=r?r.length:0,this.next=t?()=>this.count++:()=>Date.now()}},p(Fr,"InitIDGenerator"),Fr),Xi,E2=p(function(e){return Xi=Xi||document.createElement("div"),e=escape(e).replace(/%26/g,"&").replace(/%23/g,"#").replace(/%3B/g,";"),Xi.innerHTML=e,unescape(Xi.textContent)},"entityDecode");function go(e){return"str"in e}p(go,"isDetailedError");var F2=p((e,t,r,i)=>{var a;if(!i)return;const n=(a=e.node())==null?void 0:a.getBBox();n&&e.append("text").text(i).attr("text-anchor","middle").attr("x",n.x+n.width/2).attr("y",-r).attr("class",t)},"insertTitle"),sa=p(e=>{if(typeof e=="number")return[e,e+"px"];const t=parseInt(e??"",10);return Number.isNaN(t)?[void 0,void 0]:e===String(t)?[t,e+"px"]:[t,e]},"parseFontSize");function mo(e,t){return Fg({},e,t)}p(mo,"cleanAndMerge");var ge={assignWithDepth:Mt,wrapLabel:B2,calculateTextHeight:Bf,calculateTextWidth:Re,calculateTextDimensions:po,cleanAndMerge:mo,detectInit:b2,detectDirective:Cf,isSubstringInArray:C2,interpolateToCurve:ho,calcLabelPosition:vf,calcCardinalityPosition:k2,calcTerminalLabelPosition:Sf,formatUrl:wf,getStylesFromArray:Tf,generateId:v2,random:S2,runFunc:w2,entityDecode:E2,insertTitle:F2,parseFontSize:sa,InitIDGenerator:M2},$2=p(function(e){let t=e;return t=t.replace(/style.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/classDef.*:\S*#.*;/g,function(r){return r.substring(0,r.length-1)}),t=t.replace(/#\w+;/g,function(r){const i=r.substring(1,r.length-1);return/^\+?\d+$/.test(i)?"ﬂ°°"+i+"¶ß":"ﬂ°"+i+"¶ß"}),t},"encodeEntities"),cr=p(function(e){return e.replace(/ﬂ°°/g,"&#").replace(/ﬂ°/g,"&").replace(/¶ß/g,";")},"decodeEntities"),Kv=p((e,t,{counter:r=0,prefix:i,suffix:n},a)=>a||`${i?`${i}_`:""}${e}_${t}_${r}${n?`_${n}`:""}`,"getEdgeId");function Wt(e){return e??null}p(Wt,"handleUndefinedAttr");function yo(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var hr=yo();function Af(e){hr=e}var bi={exec:()=>null};function dt(e,t=""){let r=typeof e=="string"?e:e.source;const i={replace:(n,a)=>{let o=typeof a=="string"?a:a.source;return o=o.replace(zt.caret,"$1"),r=r.replace(n,o),i},getRegex:()=>new RegExp(r,t)};return i}var zt={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},D2=/^(?:[ \t]*(?:\n|$))+/,R2=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,O2=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,Ri=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,I2=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,xo=/(?:[*+-]|\d{1,9}[.)])/,Mf=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Ef=dt(Mf).replace(/bull/g,xo).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),P2=dt(Mf).replace(/bull/g,xo).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),bo=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,N2=/^[^\n]+/,_o=/(?!\s*\])(?:\\.|[^\[\]\\])+/,z2=dt(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",_o).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),W2=dt(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,xo).getRegex(),oa="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Co=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,q2=dt("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",Co).replace("tag",oa).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Ff=dt(bo).replace("hr",Ri).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",oa).getRegex(),H2=dt(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Ff).getRegex(),wo={blockquote:H2,code:R2,def:z2,fences:O2,heading:I2,hr:Ri,html:q2,lheading:Ef,list:W2,newline:D2,paragraph:Ff,table:bi,text:N2},jl=dt("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Ri).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",oa).getRegex(),Y2={...wo,lheading:P2,table:jl,paragraph:dt(bo).replace("hr",Ri).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",jl).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",oa).getRegex()},j2={...wo,html:dt(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Co).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:bi,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:dt(bo).replace("hr",Ri).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Ef).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},U2=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,G2=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,$f=/^( {2,}|\\)\n(?!\s*$)/,V2=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,la=/[\p{P}\p{S}]/u,ko=/[\s\p{P}\p{S}]/u,Df=/[^\s\p{P}\p{S}]/u,X2=dt(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,ko).getRegex(),Rf=/(?!~)[\p{P}\p{S}]/u,Z2=/(?!~)[\s\p{P}\p{S}]/u,K2=/(?:[^\s\p{P}\p{S}]|~)/u,Q2=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Of=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,J2=dt(Of,"u").replace(/punct/g,la).getRegex(),t_=dt(Of,"u").replace(/punct/g,Rf).getRegex(),If="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",e_=dt(If,"gu").replace(/notPunctSpace/g,Df).replace(/punctSpace/g,ko).replace(/punct/g,la).getRegex(),r_=dt(If,"gu").replace(/notPunctSpace/g,K2).replace(/punctSpace/g,Z2).replace(/punct/g,Rf).getRegex(),i_=dt("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Df).replace(/punctSpace/g,ko).replace(/punct/g,la).getRegex(),n_=dt(/\\(punct)/,"gu").replace(/punct/g,la).getRegex(),a_=dt(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),s_=dt(Co).replace("(?:-->|$)","-->").getRegex(),o_=dt("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",s_).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Pn=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,l_=dt(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",Pn).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Pf=dt(/^!?\[(label)\]\[(ref)\]/).replace("label",Pn).replace("ref",_o).getRegex(),Nf=dt(/^!?\[(ref)\](?:\[\])?/).replace("ref",_o).getRegex(),c_=dt("reflink|nolink(?!\\()","g").replace("reflink",Pf).replace("nolink",Nf).getRegex(),vo={_backpedal:bi,anyPunctuation:n_,autolink:a_,blockSkip:Q2,br:$f,code:G2,del:bi,emStrongLDelim:J2,emStrongRDelimAst:e_,emStrongRDelimUnd:i_,escape:U2,link:l_,nolink:Nf,punctuation:X2,reflink:Pf,reflinkSearch:c_,tag:o_,text:V2,url:bi},h_={...vo,link:dt(/^!?\[(label)\]\((.*?)\)/).replace("label",Pn).getRegex(),reflink:dt(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Pn).getRegex()},Cs={...vo,emStrongRDelimAst:r_,emStrongLDelim:t_,url:dt(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},u_={...Cs,br:dt($f).replace("{2,}","*").getRegex(),text:dt(Cs.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Zi={normal:wo,gfm:Y2,pedantic:j2},ii={normal:vo,gfm:Cs,breaks:u_,pedantic:h_},f_={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Ul=e=>f_[e];function fe(e,t){if(t){if(zt.escapeTest.test(e))return e.replace(zt.escapeReplace,Ul)}else if(zt.escapeTestNoEncode.test(e))return e.replace(zt.escapeReplaceNoEncode,Ul);return e}function Gl(e){try{e=encodeURI(e).replace(zt.percentDecode,"%")}catch{return null}return e}function Vl(e,t){var a;const r=e.replace(zt.findPipe,(o,s,c)=>{let l=!1,h=s;for(;--h>=0&&c[h]==="\\";)l=!l;return l?"|":" |"}),i=r.split(zt.splitPipe);let n=0;if(i[0].trim()||i.shift(),i.length>0&&!((a=i.at(-1))!=null&&a.trim())&&i.pop(),t)if(i.length>t)i.splice(t);else for(;i.length<t;)i.push("");for(;n<i.length;n++)i[n]=i[n].trim().replace(zt.slashPipe,"|");return i}function ni(e,t,r){const i=e.length;if(i===0)return"";let n=0;for(;n<i;){const a=e.charAt(i-n-1);if(a===t&&!r)n++;else if(a!==t&&r)n++;else break}return e.slice(0,i-n)}function d_(e,t){if(e.indexOf(t[1])===-1)return-1;let r=0;for(let i=0;i<e.length;i++)if(e[i]==="\\")i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&(r--,r<0))return i;return r>0?-2:-1}function Xl(e,t,r,i,n){const a=t.href,o=t.title||null,s=e[1].replace(n.other.outputLinkReplace,"$1");i.state.inLink=!0;const c={type:e[0].charAt(0)==="!"?"image":"link",raw:r,href:a,title:o,text:s,tokens:i.inlineTokens(s)};return i.state.inLink=!1,c}function p_(e,t,r){const i=e.match(r.other.indentCodeCompensation);if(i===null)return t;const n=i[1];return t.split(`
`).map(a=>{const o=a.match(r.other.beginningSpace);if(o===null)return a;const[s]=o;return s.length>=n.length?a.slice(n.length):a}).join(`
`)}var Nn=class{constructor(e){gt(this,"options");gt(this,"rules");gt(this,"lexer");this.options=e||hr}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const r=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?r:ni(r,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const r=t[0],i=p_(r,t[3]||"",this.rules);return{type:"code",raw:r,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:i}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let r=t[2].trim();if(this.rules.other.endingHash.test(r)){const i=ni(r,"#");(this.options.pedantic||!i||this.rules.other.endingSpaceChar.test(i))&&(r=i.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:ni(t[0],`
`)}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){let r=ni(t[0],`
`).split(`
`),i="",n="";const a=[];for(;r.length>0;){let o=!1;const s=[];let c;for(c=0;c<r.length;c++)if(this.rules.other.blockquoteStart.test(r[c]))s.push(r[c]),o=!0;else if(!o)s.push(r[c]);else break;r=r.slice(c);const l=s.join(`
`),h=l.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");i=i?`${i}
${l}`:l,n=n?`${n}
${h}`:h;const u=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(h,a,!0),this.lexer.state.top=u,r.length===0)break;const f=a.at(-1);if((f==null?void 0:f.type)==="code")break;if((f==null?void 0:f.type)==="blockquote"){const d=f,g=d.raw+`
`+r.join(`
`),m=this.blockquote(g);a[a.length-1]=m,i=i.substring(0,i.length-d.raw.length)+m.raw,n=n.substring(0,n.length-d.text.length)+m.text;break}else if((f==null?void 0:f.type)==="list"){const d=f,g=d.raw+`
`+r.join(`
`),m=this.list(g);a[a.length-1]=m,i=i.substring(0,i.length-f.raw.length)+m.raw,n=n.substring(0,n.length-d.raw.length)+m.raw,r=g.substring(a.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:i,tokens:a,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let r=t[1].trim();const i=r.length>1,n={type:"list",raw:"",ordered:i,start:i?+r.slice(0,-1):"",loose:!1,items:[]};r=i?`\\d{1,9}\\${r.slice(-1)}`:`\\${r}`,this.options.pedantic&&(r=i?r:"[*+-]");const a=this.rules.other.listItemRegex(r);let o=!1;for(;e;){let c=!1,l="",h="";if(!(t=a.exec(e))||this.rules.block.hr.test(e))break;l=t[0],e=e.substring(l.length);let u=t[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,x=>" ".repeat(3*x.length)),f=e.split(`
`,1)[0],d=!u.trim(),g=0;if(this.options.pedantic?(g=2,h=u.trimStart()):d?g=t[1].length+1:(g=t[2].search(this.rules.other.nonSpaceChar),g=g>4?1:g,h=u.slice(g),g+=t[1].length),d&&this.rules.other.blankLine.test(f)&&(l+=f+`
`,e=e.substring(f.length+1),c=!0),!c){const x=this.rules.other.nextBulletRegex(g),b=this.rules.other.hrRegex(g),C=this.rules.other.fencesBeginRegex(g),v=this.rules.other.headingBeginRegex(g),S=this.rules.other.htmlBeginRegex(g);for(;e;){const _=e.split(`
`,1)[0];let k;if(f=_,this.options.pedantic?(f=f.replace(this.rules.other.listReplaceNesting,"  "),k=f):k=f.replace(this.rules.other.tabCharGlobal,"    "),C.test(f)||v.test(f)||S.test(f)||x.test(f)||b.test(f))break;if(k.search(this.rules.other.nonSpaceChar)>=g||!f.trim())h+=`
`+k.slice(g);else{if(d||u.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||C.test(u)||v.test(u)||b.test(u))break;h+=`
`+f}!d&&!f.trim()&&(d=!0),l+=_+`
`,e=e.substring(_.length+1),u=k.slice(g)}}n.loose||(o?n.loose=!0:this.rules.other.doubleBlankLine.test(l)&&(o=!0));let m=null,y;this.options.gfm&&(m=this.rules.other.listIsTask.exec(h),m&&(y=m[0]!=="[ ] ",h=h.replace(this.rules.other.listReplaceTask,""))),n.items.push({type:"list_item",raw:l,task:!!m,checked:y,loose:!1,text:h,tokens:[]}),n.raw+=l}const s=n.items.at(-1);if(s)s.raw=s.raw.trimEnd(),s.text=s.text.trimEnd();else return;n.raw=n.raw.trimEnd();for(let c=0;c<n.items.length;c++)if(this.lexer.state.top=!1,n.items[c].tokens=this.lexer.blockTokens(n.items[c].text,[]),!n.loose){const l=n.items[c].tokens.filter(u=>u.type==="space"),h=l.length>0&&l.some(u=>this.rules.other.anyLine.test(u.raw));n.loose=h}if(n.loose)for(let c=0;c<n.items.length;c++)n.items[c].loose=!0;return n}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const r=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),i=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",n=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:r,raw:t[0],href:i,title:n}}}table(e){var o;const t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;const r=Vl(t[1]),i=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),n=(o=t[3])!=null&&o.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],a={type:"table",raw:t[0],header:[],align:[],rows:[]};if(r.length===i.length){for(const s of i)this.rules.other.tableAlignRight.test(s)?a.align.push("right"):this.rules.other.tableAlignCenter.test(s)?a.align.push("center"):this.rules.other.tableAlignLeft.test(s)?a.align.push("left"):a.align.push(null);for(let s=0;s<r.length;s++)a.header.push({text:r[s],tokens:this.lexer.inline(r[s]),header:!0,align:a.align[s]});for(const s of n)a.rows.push(Vl(s,a.header.length).map((c,l)=>({text:c,tokens:this.lexer.inline(c),header:!1,align:a.align[l]})));return a}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const r=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:r,tokens:this.lexer.inline(r)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const r=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(r)){if(!this.rules.other.endAngleBracket.test(r))return;const a=ni(r.slice(0,-1),"\\");if((r.length-a.length)%2===0)return}else{const a=d_(t[2],"()");if(a===-2)return;if(a>-1){const s=(t[0].indexOf("!")===0?5:4)+t[1].length+a;t[2]=t[2].substring(0,a),t[0]=t[0].substring(0,s).trim(),t[3]=""}}let i=t[2],n="";if(this.options.pedantic){const a=this.rules.other.pedanticHrefTitle.exec(i);a&&(i=a[1],n=a[3])}else n=t[3]?t[3].slice(1,-1):"";return i=i.trim(),this.rules.other.startAngleBracket.test(i)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(r)?i=i.slice(1):i=i.slice(1,-1)),Xl(t,{href:i&&i.replace(this.rules.inline.anyPunctuation,"$1"),title:n&&n.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer,this.rules)}}reflink(e,t){let r;if((r=this.rules.inline.reflink.exec(e))||(r=this.rules.inline.nolink.exec(e))){const i=(r[2]||r[1]).replace(this.rules.other.multipleSpaceGlobal," "),n=t[i.toLowerCase()];if(!n){const a=r[0].charAt(0);return{type:"text",raw:a,text:a}}return Xl(r,n,r[0],this.lexer,this.rules)}}emStrong(e,t,r=""){let i=this.rules.inline.emStrongLDelim.exec(e);if(!i||i[3]&&r.match(this.rules.other.unicodeAlphaNumeric))return;if(!(i[1]||i[2]||"")||!r||this.rules.inline.punctuation.exec(r)){const a=[...i[0]].length-1;let o,s,c=a,l=0;const h=i[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(h.lastIndex=0,t=t.slice(-1*e.length+a);(i=h.exec(t))!=null;){if(o=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!o)continue;if(s=[...o].length,i[3]||i[4]){c+=s;continue}else if((i[5]||i[6])&&a%3&&!((a+s)%3)){l+=s;continue}if(c-=s,c>0)continue;s=Math.min(s,s+c+l);const u=[...i[0]][0].length,f=e.slice(0,a+i.index+u+s);if(Math.min(a,s)%2){const g=f.slice(1,-1);return{type:"em",raw:f,text:g,tokens:this.lexer.inlineTokens(g)}}const d=f.slice(2,-2);return{type:"strong",raw:f,text:d,tokens:this.lexer.inlineTokens(d)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let r=t[2].replace(this.rules.other.newLineCharGlobal," ");const i=this.rules.other.nonSpaceChar.test(r),n=this.rules.other.startingSpaceChar.test(r)&&this.rules.other.endingSpaceChar.test(r);return i&&n&&(r=r.substring(1,r.length-1)),{type:"codespan",raw:t[0],text:r}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let r,i;return t[2]==="@"?(r=t[1],i="mailto:"+r):(r=t[1],i=r),{type:"link",raw:t[0],text:r,href:i,tokens:[{type:"text",raw:r,text:r}]}}}url(e){var r;let t;if(t=this.rules.inline.url.exec(e)){let i,n;if(t[2]==="@")i=t[0],n="mailto:"+i;else{let a;do a=t[0],t[0]=((r=this.rules.inline._backpedal.exec(t[0]))==null?void 0:r[0])??"";while(a!==t[0]);i=t[0],t[1]==="www."?n="http://"+t[0]:n=t[0]}return{type:"link",raw:t[0],text:i,href:n,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){const r=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:r}}}},Me=class ws{constructor(t){gt(this,"tokens");gt(this,"options");gt(this,"state");gt(this,"tokenizer");gt(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||hr,this.options.tokenizer=this.options.tokenizer||new Nn,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const r={other:zt,block:Zi.normal,inline:ii.normal};this.options.pedantic?(r.block=Zi.pedantic,r.inline=ii.pedantic):this.options.gfm&&(r.block=Zi.gfm,this.options.breaks?r.inline=ii.breaks:r.inline=ii.gfm),this.tokenizer.rules=r}static get rules(){return{block:Zi,inline:ii}}static lex(t,r){return new ws(r).lex(t)}static lexInline(t,r){return new ws(r).inlineTokens(t)}lex(t){t=t.replace(zt.carriageReturn,`
`),this.blockTokens(t,this.tokens);for(let r=0;r<this.inlineQueue.length;r++){const i=this.inlineQueue[r];this.inlineTokens(i.src,i.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,r=[],i=!1){var n,a,o;for(this.options.pedantic&&(t=t.replace(zt.tabCharGlobal,"    ").replace(zt.spaceLine,""));t;){let s;if((a=(n=this.options.extensions)==null?void 0:n.block)!=null&&a.some(l=>(s=l.call({lexer:this},t,r))?(t=t.substring(s.raw.length),r.push(s),!0):!1))continue;if(s=this.tokenizer.space(t)){t=t.substring(s.raw.length);const l=r.at(-1);s.raw.length===1&&l!==void 0?l.raw+=`
`:r.push(s);continue}if(s=this.tokenizer.code(t)){t=t.substring(s.raw.length);const l=r.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.at(-1).src=l.text):r.push(s);continue}if(s=this.tokenizer.fences(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.heading(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.hr(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.blockquote(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.list(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.html(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.def(t)){t=t.substring(s.raw.length);const l=r.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.raw,this.inlineQueue.at(-1).src=l.text):this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title});continue}if(s=this.tokenizer.table(t)){t=t.substring(s.raw.length),r.push(s);continue}if(s=this.tokenizer.lheading(t)){t=t.substring(s.raw.length),r.push(s);continue}let c=t;if((o=this.options.extensions)!=null&&o.startBlock){let l=1/0;const h=t.slice(1);let u;this.options.extensions.startBlock.forEach(f=>{u=f.call({lexer:this},h),typeof u=="number"&&u>=0&&(l=Math.min(l,u))}),l<1/0&&l>=0&&(c=t.substring(0,l+1))}if(this.state.top&&(s=this.tokenizer.paragraph(c))){const l=r.at(-1);i&&(l==null?void 0:l.type)==="paragraph"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):r.push(s),i=c.length!==t.length,t=t.substring(s.raw.length);continue}if(s=this.tokenizer.text(t)){t=t.substring(s.raw.length);const l=r.at(-1);(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):r.push(s);continue}if(t){const l="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent)break;throw new Error(l)}}return this.state.top=!0,r}inline(t,r=[]){return this.inlineQueue.push({src:t,tokens:r}),r}inlineTokens(t,r=[]){var s,c,l;let i=t,n=null;if(this.tokens.links){const h=Object.keys(this.tokens.links);if(h.length>0)for(;(n=this.tokenizer.rules.inline.reflinkSearch.exec(i))!=null;)h.includes(n[0].slice(n[0].lastIndexOf("[")+1,-1))&&(i=i.slice(0,n.index)+"["+"a".repeat(n[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(n=this.tokenizer.rules.inline.anyPunctuation.exec(i))!=null;)i=i.slice(0,n.index)+"++"+i.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(n=this.tokenizer.rules.inline.blockSkip.exec(i))!=null;)i=i.slice(0,n.index)+"["+"a".repeat(n[0].length-2)+"]"+i.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let a=!1,o="";for(;t;){a||(o=""),a=!1;let h;if((c=(s=this.options.extensions)==null?void 0:s.inline)!=null&&c.some(f=>(h=f.call({lexer:this},t,r))?(t=t.substring(h.raw.length),r.push(h),!0):!1))continue;if(h=this.tokenizer.escape(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.tag(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.link(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(h.raw.length);const f=r.at(-1);h.type==="text"&&(f==null?void 0:f.type)==="text"?(f.raw+=h.raw,f.text+=h.text):r.push(h);continue}if(h=this.tokenizer.emStrong(t,i,o)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.codespan(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.br(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.del(t)){t=t.substring(h.raw.length),r.push(h);continue}if(h=this.tokenizer.autolink(t)){t=t.substring(h.raw.length),r.push(h);continue}if(!this.state.inLink&&(h=this.tokenizer.url(t))){t=t.substring(h.raw.length),r.push(h);continue}let u=t;if((l=this.options.extensions)!=null&&l.startInline){let f=1/0;const d=t.slice(1);let g;this.options.extensions.startInline.forEach(m=>{g=m.call({lexer:this},d),typeof g=="number"&&g>=0&&(f=Math.min(f,g))}),f<1/0&&f>=0&&(u=t.substring(0,f+1))}if(h=this.tokenizer.inlineText(u)){t=t.substring(h.raw.length),h.raw.slice(-1)!=="_"&&(o=h.raw.slice(-1)),a=!0;const f=r.at(-1);(f==null?void 0:f.type)==="text"?(f.raw+=h.raw,f.text+=h.text):r.push(h);continue}if(t){const f="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent)break;throw new Error(f)}}return r}},zn=class{constructor(e){gt(this,"options");gt(this,"parser");this.options=e||hr}space(e){return""}code({text:e,lang:t,escaped:r}){var a;const i=(a=(t||"").match(zt.notSpaceStart))==null?void 0:a[0],n=e.replace(zt.endingNewline,"")+`
`;return i?'<pre><code class="language-'+fe(i)+'">'+(r?n:fe(n,!0))+`</code></pre>
`:"<pre><code>"+(r?n:fe(n,!0))+`</code></pre>
`}blockquote({tokens:e}){return`<blockquote>
${this.parser.parse(e)}</blockquote>
`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>
`}hr(e){return`<hr>
`}list(e){const t=e.ordered,r=e.start;let i="";for(let o=0;o<e.items.length;o++){const s=e.items[o];i+=this.listitem(s)}const n=t?"ol":"ul",a=t&&r!==1?' start="'+r+'"':"";return"<"+n+a+`>
`+i+"</"+n+`>
`}listitem(e){var r;let t="";if(e.task){const i=this.checkbox({checked:!!e.checked});e.loose?((r=e.tokens[0])==null?void 0:r.type)==="paragraph"?(e.tokens[0].text=i+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&e.tokens[0].tokens[0].type==="text"&&(e.tokens[0].tokens[0].text=i+" "+fe(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:i+" ",text:i+" ",escaped:!0}):t+=i+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>
`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>
`}table(e){let t="",r="";for(let n=0;n<e.header.length;n++)r+=this.tablecell(e.header[n]);t+=this.tablerow({text:r});let i="";for(let n=0;n<e.rows.length;n++){const a=e.rows[n];r="";for(let o=0;o<a.length;o++)r+=this.tablecell(a[o]);i+=this.tablerow({text:r})}return i&&(i=`<tbody>${i}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+i+`</table>
`}tablerow({text:e}){return`<tr>
${e}</tr>
`}tablecell(e){const t=this.parser.parseInline(e.tokens),r=e.header?"th":"td";return(e.align?`<${r} align="${e.align}">`:`<${r}>`)+t+`</${r}>
`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${fe(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:r}){const i=this.parser.parseInline(r),n=Gl(e);if(n===null)return i;e=n;let a='<a href="'+e+'"';return t&&(a+=' title="'+fe(t)+'"'),a+=">"+i+"</a>",a}image({href:e,title:t,text:r,tokens:i}){i&&(r=this.parser.parseInline(i,this.parser.textRenderer));const n=Gl(e);if(n===null)return fe(r);e=n;let a=`<img src="${e}" alt="${r}"`;return t&&(a+=` title="${fe(t)}"`),a+=">",a}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:fe(e.text)}},So=class{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}},Ee=class ks{constructor(t){gt(this,"options");gt(this,"renderer");gt(this,"textRenderer");this.options=t||hr,this.options.renderer=this.options.renderer||new zn,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new So}static parse(t,r){return new ks(r).parse(t)}static parseInline(t,r){return new ks(r).parseInline(t)}parse(t,r=!0){var n,a;let i="";for(let o=0;o<t.length;o++){const s=t[o];if((a=(n=this.options.extensions)==null?void 0:n.renderers)!=null&&a[s.type]){const l=s,h=this.options.extensions.renderers[l.type].call({parser:this},l);if(h!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(l.type)){i+=h||"";continue}}const c=s;switch(c.type){case"space":{i+=this.renderer.space(c);continue}case"hr":{i+=this.renderer.hr(c);continue}case"heading":{i+=this.renderer.heading(c);continue}case"code":{i+=this.renderer.code(c);continue}case"table":{i+=this.renderer.table(c);continue}case"blockquote":{i+=this.renderer.blockquote(c);continue}case"list":{i+=this.renderer.list(c);continue}case"html":{i+=this.renderer.html(c);continue}case"paragraph":{i+=this.renderer.paragraph(c);continue}case"text":{let l=c,h=this.renderer.text(l);for(;o+1<t.length&&t[o+1].type==="text";)l=t[++o],h+=`
`+this.renderer.text(l);r?i+=this.renderer.paragraph({type:"paragraph",raw:h,text:h,tokens:[{type:"text",raw:h,text:h,escaped:!0}]}):i+=h;continue}default:{const l='Token with "'+c.type+'" type was not found.';if(this.options.silent)return"";throw new Error(l)}}}return i}parseInline(t,r=this.renderer){var n,a;let i="";for(let o=0;o<t.length;o++){const s=t[o];if((a=(n=this.options.extensions)==null?void 0:n.renderers)!=null&&a[s.type]){const l=this.options.extensions.renderers[s.type].call({parser:this},s);if(l!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){i+=l||"";continue}}const c=s;switch(c.type){case"escape":{i+=r.text(c);break}case"html":{i+=r.html(c);break}case"link":{i+=r.link(c);break}case"image":{i+=r.image(c);break}case"strong":{i+=r.strong(c);break}case"em":{i+=r.em(c);break}case"codespan":{i+=r.codespan(c);break}case"br":{i+=r.br(c);break}case"del":{i+=r.del(c);break}case"text":{i+=r.text(c);break}default:{const l='Token with "'+c.type+'" type was not found.';if(this.options.silent)return"";throw new Error(l)}}}return i}},Ha,un=(Ha=class{constructor(e){gt(this,"options");gt(this,"block");this.options=e||hr}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?Me.lex:Me.lexInline}provideParser(){return this.block?Ee.parse:Ee.parseInline}},gt(Ha,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),Ha),g_=class{constructor(...e){gt(this,"defaults",yo());gt(this,"options",this.setOptions);gt(this,"parse",this.parseMarkdown(!0));gt(this,"parseInline",this.parseMarkdown(!1));gt(this,"Parser",Ee);gt(this,"Renderer",zn);gt(this,"TextRenderer",So);gt(this,"Lexer",Me);gt(this,"Tokenizer",Nn);gt(this,"Hooks",un);this.use(...e)}walkTokens(e,t){var i,n;let r=[];for(const a of e)switch(r=r.concat(t.call(this,a)),a.type){case"table":{const o=a;for(const s of o.header)r=r.concat(this.walkTokens(s.tokens,t));for(const s of o.rows)for(const c of s)r=r.concat(this.walkTokens(c.tokens,t));break}case"list":{const o=a;r=r.concat(this.walkTokens(o.items,t));break}default:{const o=a;(n=(i=this.defaults.extensions)==null?void 0:i.childTokens)!=null&&n[o.type]?this.defaults.extensions.childTokens[o.type].forEach(s=>{const c=o[s].flat(1/0);r=r.concat(this.walkTokens(c,t))}):o.tokens&&(r=r.concat(this.walkTokens(o.tokens,t)))}}return r}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(r=>{const i={...r};if(i.async=this.defaults.async||i.async||!1,r.extensions&&(r.extensions.forEach(n=>{if(!n.name)throw new Error("extension name required");if("renderer"in n){const a=t.renderers[n.name];a?t.renderers[n.name]=function(...o){let s=n.renderer.apply(this,o);return s===!1&&(s=a.apply(this,o)),s}:t.renderers[n.name]=n.renderer}if("tokenizer"in n){if(!n.level||n.level!=="block"&&n.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const a=t[n.level];a?a.unshift(n.tokenizer):t[n.level]=[n.tokenizer],n.start&&(n.level==="block"?t.startBlock?t.startBlock.push(n.start):t.startBlock=[n.start]:n.level==="inline"&&(t.startInline?t.startInline.push(n.start):t.startInline=[n.start]))}"childTokens"in n&&n.childTokens&&(t.childTokens[n.name]=n.childTokens)}),i.extensions=t),r.renderer){const n=this.defaults.renderer||new zn(this.defaults);for(const a in r.renderer){if(!(a in n))throw new Error(`renderer '${a}' does not exist`);if(["options","parser"].includes(a))continue;const o=a,s=r.renderer[o],c=n[o];n[o]=(...l)=>{let h=s.apply(n,l);return h===!1&&(h=c.apply(n,l)),h||""}}i.renderer=n}if(r.tokenizer){const n=this.defaults.tokenizer||new Nn(this.defaults);for(const a in r.tokenizer){if(!(a in n))throw new Error(`tokenizer '${a}' does not exist`);if(["options","rules","lexer"].includes(a))continue;const o=a,s=r.tokenizer[o],c=n[o];n[o]=(...l)=>{let h=s.apply(n,l);return h===!1&&(h=c.apply(n,l)),h}}i.tokenizer=n}if(r.hooks){const n=this.defaults.hooks||new un;for(const a in r.hooks){if(!(a in n))throw new Error(`hook '${a}' does not exist`);if(["options","block"].includes(a))continue;const o=a,s=r.hooks[o],c=n[o];un.passThroughHooks.has(a)?n[o]=l=>{if(this.defaults.async)return Promise.resolve(s.call(n,l)).then(u=>c.call(n,u));const h=s.call(n,l);return c.call(n,h)}:n[o]=(...l)=>{let h=s.apply(n,l);return h===!1&&(h=c.apply(n,l)),h}}i.hooks=n}if(r.walkTokens){const n=this.defaults.walkTokens,a=r.walkTokens;i.walkTokens=function(o){let s=[];return s.push(a.call(this,o)),n&&(s=s.concat(n.call(this,o))),s}}this.defaults={...this.defaults,...i}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return Me.lex(e,t??this.defaults)}parser(e,t){return Ee.parse(e,t??this.defaults)}parseMarkdown(e){return(r,i)=>{const n={...i},a={...this.defaults,...n},o=this.onError(!!a.silent,!!a.async);if(this.defaults.async===!0&&n.async===!1)return o(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof r>"u"||r===null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof r!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(r)+", string expected"));a.hooks&&(a.hooks.options=a,a.hooks.block=e);const s=a.hooks?a.hooks.provideLexer():e?Me.lex:Me.lexInline,c=a.hooks?a.hooks.provideParser():e?Ee.parse:Ee.parseInline;if(a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(r):r).then(l=>s(l,a)).then(l=>a.hooks?a.hooks.processAllTokens(l):l).then(l=>a.walkTokens?Promise.all(this.walkTokens(l,a.walkTokens)).then(()=>l):l).then(l=>c(l,a)).then(l=>a.hooks?a.hooks.postprocess(l):l).catch(o);try{a.hooks&&(r=a.hooks.preprocess(r));let l=s(r,a);a.hooks&&(l=a.hooks.processAllTokens(l)),a.walkTokens&&this.walkTokens(l,a.walkTokens);let h=c(l,a);return a.hooks&&(h=a.hooks.postprocess(h)),h}catch(l){return o(l)}}}onError(e,t){return r=>{if(r.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const i="<p>An error occurred:</p><pre>"+fe(r.message+"",!0)+"</pre>";return t?Promise.resolve(i):i}if(t)return Promise.reject(r);throw r}}},or=new g_;function ft(e,t){return or.parse(e,t)}ft.options=ft.setOptions=function(e){return or.setOptions(e),ft.defaults=or.defaults,Af(ft.defaults),ft};ft.getDefaults=yo;ft.defaults=hr;ft.use=function(...e){return or.use(...e),ft.defaults=or.defaults,Af(ft.defaults),ft};ft.walkTokens=function(e,t){return or.walkTokens(e,t)};ft.parseInline=or.parseInline;ft.Parser=Ee;ft.parser=Ee.parse;ft.Renderer=zn;ft.TextRenderer=So;ft.Lexer=Me;ft.lexer=Me.lex;ft.Tokenizer=Nn;ft.Hooks=un;ft.parse=ft;ft.options;ft.setOptions;ft.use;ft.walkTokens;ft.parseInline;Ee.parse;Me.lex;function zf(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var i=Array.from(typeof e=="string"?[e]:e);i[i.length-1]=i[i.length-1].replace(/\r?\n([\t ]*)$/,"");var n=i.reduce(function(s,c){var l=c.match(/\n([\t ]+|(?!\s).)/g);return l?s.concat(l.map(function(h){var u,f;return(f=(u=h.match(/[\t ]/g))===null||u===void 0?void 0:u.length)!==null&&f!==void 0?f:0})):s},[]);if(n.length){var a=new RegExp(`
[	 ]{`+Math.min.apply(Math,n)+"}","g");i=i.map(function(s){return s.replace(a,`
`)})}i[0]=i[0].replace(/^\r?\n/,"");var o=i[0];return t.forEach(function(s,c){var l=o.match(/(?:^|\n)( *)$/),h=l?l[1]:"",u=s;typeof s=="string"&&s.includes(`
`)&&(u=String(s).split(`
`).map(function(f,d){return d===0?f:""+h+f}).join(`
`)),o+=u+i[c+1]}),o}function Wf(e,{markdownAutoWrap:t}){const i=e.replace(/<br\/>/g,`
`).replace(/\n{2,}/g,`
`),n=zf(i);return t===!1?n.replace(/ /g,"&nbsp;"):n}p(Wf,"preprocessMarkdown");function qf(e,t={}){const r=Wf(e,t),i=ft.lexer(r),n=[[]];let a=0;function o(s,c="normal"){s.type==="text"?s.text.split(`
`).forEach((h,u)=>{u!==0&&(a++,n.push([])),h.split(" ").forEach(f=>{f=f.replace(/&#39;/g,"'"),f&&n[a].push({content:f,type:c})})}):s.type==="strong"||s.type==="em"?s.tokens.forEach(l=>{o(l,s.type)}):s.type==="html"&&n[a].push({content:s.text,type:"normal"})}return p(o,"processNode"),i.forEach(s=>{var c;s.type==="paragraph"?(c=s.tokens)==null||c.forEach(l=>{o(l)}):s.type==="html"&&n[a].push({content:s.text,type:"normal"})}),n}p(qf,"markdownToLines");function Hf(e,{markdownAutoWrap:t}={}){const r=ft.lexer(e);function i(n){var a,o,s;return n.type==="text"?t===!1?n.text.replace(/\n */g,"<br/>").replace(/ /g,"&nbsp;"):n.text.replace(/\n */g,"<br/>"):n.type==="strong"?`<strong>${(a=n.tokens)==null?void 0:a.map(i).join("")}</strong>`:n.type==="em"?`<em>${(o=n.tokens)==null?void 0:o.map(i).join("")}</em>`:n.type==="paragraph"?`<p>${(s=n.tokens)==null?void 0:s.map(i).join("")}</p>`:n.type==="space"?"":n.type==="html"?`${n.text}`:n.type==="escape"?n.text:`Unsupported markdown: ${n.type}`}return p(i,"output"),r.map(i).join("")}p(Hf,"markdownToHTML");function Yf(e){return Intl.Segmenter?[...new Intl.Segmenter().segment(e)].map(t=>t.segment):[...e]}p(Yf,"splitTextToChars");function jf(e,t){const r=Yf(t.content);return To(e,[],r,t.type)}p(jf,"splitWordToFitWidth");function To(e,t,r,i){if(r.length===0)return[{content:t.join(""),type:i},{content:"",type:i}];const[n,...a]=r,o=[...t,n];return e([{content:o.join(""),type:i}])?To(e,o,a,i):(t.length===0&&n&&(t.push(n),r.shift()),[{content:t.join(""),type:i},{content:r.join(""),type:i}])}p(To,"splitWordToFitWidthRecursion");function Uf(e,t){if(e.some(({content:r})=>r.includes(`
`)))throw new Error("splitLineToFitWidth does not support newlines in the line");return Wn(e,t)}p(Uf,"splitLineToFitWidth");function Wn(e,t,r=[],i=[]){if(e.length===0)return i.length>0&&r.push(i),r.length>0?r:[];let n="";e[0].content===" "&&(n=" ",e.shift());const a=e.shift()??{content:" ",type:"normal"},o=[...i];if(n!==""&&o.push({content:n,type:"normal"}),o.push(a),t(o))return Wn(e,t,r,o);if(i.length>0)r.push(i),e.unshift(a);else if(a.content){const[s,c]=jf(t,a);r.push([s]),c.content&&e.unshift(c)}return Wn(e,t,r)}p(Wn,"splitLineToFitWidthRecursion");function vs(e,t){t&&e.attr("style",t)}p(vs,"applyStyle");async function Gf(e,t,r,i,n=!1){const a=e.append("foreignObject");a.attr("width",`${10*r}px`),a.attr("height",`${10*r}px`);const o=a.append("xhtml:div");let s=t.label;t.label&&Ir(t.label)&&(s=await Is(t.label.replace(Yr.lineBreakRegex,`
`),ut()));const c=t.isNode?"nodeLabel":"edgeLabel",l=o.append("span");l.html(s),vs(l,t.labelStyle),l.attr("class",`${c} ${i}`),vs(o,t.labelStyle),o.style("display","table-cell"),o.style("white-space","nowrap"),o.style("line-height","1.5"),o.style("max-width",r+"px"),o.style("text-align","center"),o.attr("xmlns","http://www.w3.org/1999/xhtml"),n&&o.attr("class","labelBkg");let h=o.node().getBoundingClientRect();return h.width===r&&(o.style("display","table"),o.style("white-space","break-spaces"),o.style("width",r+"px"),h=o.node().getBoundingClientRect()),a.node()}p(Gf,"addHtmlSpan");function ca(e,t,r){return e.append("tspan").attr("class","text-outer-tspan").attr("x",0).attr("y",t*r-.1+"em").attr("dy",r+"em")}p(ca,"createTspan");function Vf(e,t,r){const i=e.append("text"),n=ca(i,1,t);ha(n,r);const a=n.node().getComputedTextLength();return i.remove(),a}p(Vf,"computeWidthOfText");function m_(e,t,r){var o;const i=e.append("text"),n=ca(i,1,t);ha(n,[{content:r,type:"normal"}]);const a=(o=n.node())==null?void 0:o.getBoundingClientRect();return a&&i.remove(),a}p(m_,"computeDimensionOfText");function Xf(e,t,r,i=!1){const a=t.append("g"),o=a.insert("rect").attr("class","background").attr("style","stroke: none"),s=a.append("text").attr("y","-10.1");let c=0;for(const l of r){const h=p(f=>Vf(a,1.1,f)<=e,"checkWidth"),u=h(l)?[l]:Uf(l,h);for(const f of u){const d=ca(s,c,1.1);ha(d,f),c++}}if(i){const l=s.node().getBBox(),h=2;return o.attr("x",l.x-h).attr("y",l.y-h).attr("width",l.width+2*h).attr("height",l.height+2*h),a.node()}else return s.node()}p(Xf,"createFormattedText");function ha(e,t){e.text(""),t.forEach((r,i)=>{const n=e.append("tspan").attr("font-style",r.type==="em"?"italic":"normal").attr("class","text-inner-tspan").attr("font-weight",r.type==="strong"?"bold":"normal");i===0?n.text(r.content):n.text(" "+r.content)})}p(ha,"updateTextContentAndStyles");function Zf(e){return e.replace(/fa[bklrs]?:fa-[\w-]+/g,t=>`<i class='${t.replace(":"," ")}'></i>`)}p(Zf,"replaceIconSubstring");var Ue=p(async(e,t="",{style:r="",isTitle:i=!1,classes:n="",useHtmlLabels:a=!0,isNode:o=!0,width:s=200,addSvgBackground:c=!1}={},l)=>{if(D.debug("XYZ createText",t,r,i,n,a,o,"addSvgBackground: ",c),a){const h=Hf(t,l),u=Zf(cr(h)),f=t.replace(/\\\\/g,"\\"),d={isNode:o,label:Ir(t)?f:u,labelStyle:r.replace("fill:","color:")};return await Gf(e,d,s,n,c)}else{const h=t.replace(/<br\s*\/?>/g,"<br/>"),u=qf(h.replace("<br>","<br/>"),l),f=Xf(s,e,u,t?c:!1);if(o){/stroke:/.exec(r)&&(r=r.replace("stroke:","lineColor:"));const d=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");lt(f).attr("style",d)}else{const d=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/background:/g,"fill:");lt(f).select("rect").attr("style",d.replace(/background:/g,"fill:"));const g=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");lt(f).select("text").attr("style",g)}return f}},"createText");function Ra(e,t,r){if(e&&e.length){const[i,n]=t,a=Math.PI/180*r,o=Math.cos(a),s=Math.sin(a);for(const c of e){const[l,h]=c;c[0]=(l-i)*o-(h-n)*s+i,c[1]=(l-i)*s+(h-n)*o+n}}}function y_(e,t){return e[0]===t[0]&&e[1]===t[1]}function x_(e,t,r,i=1){const n=r,a=Math.max(t,.1),o=e[0]&&e[0][0]&&typeof e[0][0]=="number"?[e]:e,s=[0,0];if(n)for(const l of o)Ra(l,s,n);const c=function(l,h,u){const f=[];for(const b of l){const C=[...b];y_(C[0],C[C.length-1])||C.push([C[0][0],C[0][1]]),C.length>2&&f.push(C)}const d=[];h=Math.max(h,.1);const g=[];for(const b of f)for(let C=0;C<b.length-1;C++){const v=b[C],S=b[C+1];if(v[1]!==S[1]){const _=Math.min(v[1],S[1]);g.push({ymin:_,ymax:Math.max(v[1],S[1]),x:_===v[1]?v[0]:S[0],islope:(S[0]-v[0])/(S[1]-v[1])})}}if(g.sort((b,C)=>b.ymin<C.ymin?-1:b.ymin>C.ymin?1:b.x<C.x?-1:b.x>C.x?1:b.ymax===C.ymax?0:(b.ymax-C.ymax)/Math.abs(b.ymax-C.ymax)),!g.length)return d;let m=[],y=g[0].ymin,x=0;for(;m.length||g.length;){if(g.length){let b=-1;for(let C=0;C<g.length&&!(g[C].ymin>y);C++)b=C;g.splice(0,b+1).forEach(C=>{m.push({s:y,edge:C})})}if(m=m.filter(b=>!(b.edge.ymax<=y)),m.sort((b,C)=>b.edge.x===C.edge.x?0:(b.edge.x-C.edge.x)/Math.abs(b.edge.x-C.edge.x)),(u!==1||x%h==0)&&m.length>1)for(let b=0;b<m.length;b+=2){const C=b+1;if(C>=m.length)break;const v=m[b].edge,S=m[C].edge;d.push([[Math.round(v.x),y],[Math.round(S.x),y]])}y+=u,m.forEach(b=>{b.edge.x=b.edge.x+u*b.edge.islope}),x++}return d}(o,a,i);if(n){for(const l of o)Ra(l,s,-n);(function(l,h,u){const f=[];l.forEach(d=>f.push(...d)),Ra(f,h,u)})(c,s,-n)}return c}function Oi(e,t){var r;const i=t.hachureAngle+90;let n=t.hachureGap;n<0&&(n=4*t.strokeWidth),n=Math.round(Math.max(n,.1));let a=1;return t.roughness>=1&&(((r=t.randomizer)===null||r===void 0?void 0:r.next())||Math.random())>.7&&(a=n),x_(e,n,i,a||1)}class Lo{constructor(t){this.helper=t}fillPolygons(t,r){return this._fillPolygons(t,r)}_fillPolygons(t,r){const i=Oi(t,r);return{type:"fillSketch",ops:this.renderLines(i,r)}}renderLines(t,r){const i=[];for(const n of t)i.push(...this.helper.doubleLineOps(n[0][0],n[0][1],n[1][0],n[1][1],r));return i}}function ua(e){const t=e[0],r=e[1];return Math.sqrt(Math.pow(t[0]-r[0],2)+Math.pow(t[1]-r[1],2))}class b_ extends Lo{fillPolygons(t,r){let i=r.hachureGap;i<0&&(i=4*r.strokeWidth),i=Math.max(i,.1);const n=Oi(t,Object.assign({},r,{hachureGap:i})),a=Math.PI/180*r.hachureAngle,o=[],s=.5*i*Math.cos(a),c=.5*i*Math.sin(a);for(const[l,h]of n)ua([l,h])&&o.push([[l[0]-s,l[1]+c],[...h]],[[l[0]+s,l[1]-c],[...h]]);return{type:"fillSketch",ops:this.renderLines(o,r)}}}class __ extends Lo{fillPolygons(t,r){const i=this._fillPolygons(t,r),n=Object.assign({},r,{hachureAngle:r.hachureAngle+90}),a=this._fillPolygons(t,n);return i.ops=i.ops.concat(a.ops),i}}class C_{constructor(t){this.helper=t}fillPolygons(t,r){const i=Oi(t,r=Object.assign({},r,{hachureAngle:0}));return this.dotsOnLines(i,r)}dotsOnLines(t,r){const i=[];let n=r.hachureGap;n<0&&(n=4*r.strokeWidth),n=Math.max(n,.1);let a=r.fillWeight;a<0&&(a=r.strokeWidth/2);const o=n/4;for(const s of t){const c=ua(s),l=c/n,h=Math.ceil(l)-1,u=c-h*n,f=(s[0][0]+s[1][0])/2-n/4,d=Math.min(s[0][1],s[1][1]);for(let g=0;g<h;g++){const m=d+u+g*n,y=f-o+2*Math.random()*o,x=m-o+2*Math.random()*o,b=this.helper.ellipse(y,x,a,a,r);i.push(...b.ops)}}return{type:"fillSketch",ops:i}}}class w_{constructor(t){this.helper=t}fillPolygons(t,r){const i=Oi(t,r);return{type:"fillSketch",ops:this.dashedLine(i,r)}}dashedLine(t,r){const i=r.dashOffset<0?r.hachureGap<0?4*r.strokeWidth:r.hachureGap:r.dashOffset,n=r.dashGap<0?r.hachureGap<0?4*r.strokeWidth:r.hachureGap:r.dashGap,a=[];return t.forEach(o=>{const s=ua(o),c=Math.floor(s/(i+n)),l=(s+n-c*(i+n))/2;let h=o[0],u=o[1];h[0]>u[0]&&(h=o[1],u=o[0]);const f=Math.atan((u[1]-h[1])/(u[0]-h[0]));for(let d=0;d<c;d++){const g=d*(i+n),m=g+i,y=[h[0]+g*Math.cos(f)+l*Math.cos(f),h[1]+g*Math.sin(f)+l*Math.sin(f)],x=[h[0]+m*Math.cos(f)+l*Math.cos(f),h[1]+m*Math.sin(f)+l*Math.sin(f)];a.push(...this.helper.doubleLineOps(y[0],y[1],x[0],x[1],r))}}),a}}class k_{constructor(t){this.helper=t}fillPolygons(t,r){const i=r.hachureGap<0?4*r.strokeWidth:r.hachureGap,n=r.zigzagOffset<0?i:r.zigzagOffset,a=Oi(t,r=Object.assign({},r,{hachureGap:i+n}));return{type:"fillSketch",ops:this.zigzagLines(a,n,r)}}zigzagLines(t,r,i){const n=[];return t.forEach(a=>{const o=ua(a),s=Math.round(o/(2*r));let c=a[0],l=a[1];c[0]>l[0]&&(c=a[1],l=a[0]);const h=Math.atan((l[1]-c[1])/(l[0]-c[0]));for(let u=0;u<s;u++){const f=2*u*r,d=2*(u+1)*r,g=Math.sqrt(2*Math.pow(r,2)),m=[c[0]+f*Math.cos(h),c[1]+f*Math.sin(h)],y=[c[0]+d*Math.cos(h),c[1]+d*Math.sin(h)],x=[m[0]+g*Math.cos(h+Math.PI/4),m[1]+g*Math.sin(h+Math.PI/4)];n.push(...this.helper.doubleLineOps(m[0],m[1],x[0],x[1],i),...this.helper.doubleLineOps(x[0],x[1],y[0],y[1],i))}}),n}}const jt={};class v_{constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}}const S_=0,Oa=1,Zl=2,Ki={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function Ia(e,t){return e.type===t}function Bo(e){const t=[],r=function(o){const s=new Array;for(;o!=="";)if(o.match(/^([ \t\r\n,]+)/))o=o.substr(RegExp.$1.length);else if(o.match(/^([aAcChHlLmMqQsStTvVzZ])/))s[s.length]={type:S_,text:RegExp.$1},o=o.substr(RegExp.$1.length);else{if(!o.match(/^(([-+]?[0-9]+(\.[0-9]*)?|[-+]?\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];s[s.length]={type:Oa,text:`${parseFloat(RegExp.$1)}`},o=o.substr(RegExp.$1.length)}return s[s.length]={type:Zl,text:""},s}(e);let i="BOD",n=0,a=r[n];for(;!Ia(a,Zl);){let o=0;const s=[];if(i==="BOD"){if(a.text!=="M"&&a.text!=="m")return Bo("M0,0"+e);n++,o=Ki[a.text],i=a.text}else Ia(a,Oa)?o=Ki[i]:(n++,o=Ki[a.text],i=a.text);if(!(n+o<r.length))throw new Error("Path data ended short");for(let c=n;c<n+o;c++){const l=r[c];if(!Ia(l,Oa))throw new Error("Param not a number: "+i+","+l.text);s[s.length]=+l.text}if(typeof Ki[i]!="number")throw new Error("Bad segment: "+i);{const c={key:i,data:s};t.push(c),n+=o,a=r[n],i==="M"&&(i="L"),i==="m"&&(i="l")}}return t}function Kf(e){let t=0,r=0,i=0,n=0;const a=[];for(const{key:o,data:s}of e)switch(o){case"M":a.push({key:"M",data:[...s]}),[t,r]=s,[i,n]=s;break;case"m":t+=s[0],r+=s[1],a.push({key:"M",data:[t,r]}),i=t,n=r;break;case"L":a.push({key:"L",data:[...s]}),[t,r]=s;break;case"l":t+=s[0],r+=s[1],a.push({key:"L",data:[t,r]});break;case"C":a.push({key:"C",data:[...s]}),t=s[4],r=s[5];break;case"c":{const c=s.map((l,h)=>h%2?l+r:l+t);a.push({key:"C",data:c}),t=c[4],r=c[5];break}case"Q":a.push({key:"Q",data:[...s]}),t=s[2],r=s[3];break;case"q":{const c=s.map((l,h)=>h%2?l+r:l+t);a.push({key:"Q",data:c}),t=c[2],r=c[3];break}case"A":a.push({key:"A",data:[...s]}),t=s[5],r=s[6];break;case"a":t+=s[5],r+=s[6],a.push({key:"A",data:[s[0],s[1],s[2],s[3],s[4],t,r]});break;case"H":a.push({key:"H",data:[...s]}),t=s[0];break;case"h":t+=s[0],a.push({key:"H",data:[t]});break;case"V":a.push({key:"V",data:[...s]}),r=s[0];break;case"v":r+=s[0],a.push({key:"V",data:[r]});break;case"S":a.push({key:"S",data:[...s]}),t=s[2],r=s[3];break;case"s":{const c=s.map((l,h)=>h%2?l+r:l+t);a.push({key:"S",data:c}),t=c[2],r=c[3];break}case"T":a.push({key:"T",data:[...s]}),t=s[0],r=s[1];break;case"t":t+=s[0],r+=s[1],a.push({key:"T",data:[t,r]});break;case"Z":case"z":a.push({key:"Z",data:[]}),t=i,r=n}return a}function Qf(e){const t=[];let r="",i=0,n=0,a=0,o=0,s=0,c=0;for(const{key:l,data:h}of e){switch(l){case"M":t.push({key:"M",data:[...h]}),[i,n]=h,[a,o]=h;break;case"C":t.push({key:"C",data:[...h]}),i=h[4],n=h[5],s=h[2],c=h[3];break;case"L":t.push({key:"L",data:[...h]}),[i,n]=h;break;case"H":i=h[0],t.push({key:"L",data:[i,n]});break;case"V":n=h[0],t.push({key:"L",data:[i,n]});break;case"S":{let u=0,f=0;r==="C"||r==="S"?(u=i+(i-s),f=n+(n-c)):(u=i,f=n),t.push({key:"C",data:[u,f,...h]}),s=h[0],c=h[1],i=h[2],n=h[3];break}case"T":{const[u,f]=h;let d=0,g=0;r==="Q"||r==="T"?(d=i+(i-s),g=n+(n-c)):(d=i,g=n);const m=i+2*(d-i)/3,y=n+2*(g-n)/3,x=u+2*(d-u)/3,b=f+2*(g-f)/3;t.push({key:"C",data:[m,y,x,b,u,f]}),s=d,c=g,i=u,n=f;break}case"Q":{const[u,f,d,g]=h,m=i+2*(u-i)/3,y=n+2*(f-n)/3,x=d+2*(u-d)/3,b=g+2*(f-g)/3;t.push({key:"C",data:[m,y,x,b,d,g]}),s=u,c=f,i=d,n=g;break}case"A":{const u=Math.abs(h[0]),f=Math.abs(h[1]),d=h[2],g=h[3],m=h[4],y=h[5],x=h[6];u===0||f===0?(t.push({key:"C",data:[i,n,y,x,y,x]}),i=y,n=x):(i!==y||n!==x)&&(Jf(i,n,y,x,u,f,d,g,m).forEach(function(b){t.push({key:"C",data:b})}),i=y,n=x);break}case"Z":t.push({key:"Z",data:[]}),i=a,n=o}r=l}return t}function ai(e,t,r){return[e*Math.cos(r)-t*Math.sin(r),e*Math.sin(r)+t*Math.cos(r)]}function Jf(e,t,r,i,n,a,o,s,c,l){const h=(u=o,Math.PI*u/180);var u;let f=[],d=0,g=0,m=0,y=0;if(l)[d,g,m,y]=l;else{[e,t]=ai(e,t,-h),[r,i]=ai(r,i,-h);const O=(e-r)/2,E=(t-i)/2;let M=O*O/(n*n)+E*E/(a*a);M>1&&(M=Math.sqrt(M),n*=M,a*=M);const L=n*n,F=a*a,B=L*F-L*E*E-F*O*O,W=L*E*E+F*O*O,j=(s===c?-1:1)*Math.sqrt(Math.abs(B/W));m=j*n*E/a+(e+r)/2,y=j*-a*O/n+(t+i)/2,d=Math.asin(parseFloat(((t-y)/a).toFixed(9))),g=Math.asin(parseFloat(((i-y)/a).toFixed(9))),e<m&&(d=Math.PI-d),r<m&&(g=Math.PI-g),d<0&&(d=2*Math.PI+d),g<0&&(g=2*Math.PI+g),c&&d>g&&(d-=2*Math.PI),!c&&g>d&&(g-=2*Math.PI)}let x=g-d;if(Math.abs(x)>120*Math.PI/180){const O=g,E=r,M=i;g=c&&g>d?d+120*Math.PI/180*1:d+120*Math.PI/180*-1,f=Jf(r=m+n*Math.cos(g),i=y+a*Math.sin(g),E,M,n,a,o,0,c,[g,O,m,y])}x=g-d;const b=Math.cos(d),C=Math.sin(d),v=Math.cos(g),S=Math.sin(g),_=Math.tan(x/4),k=4/3*n*_,R=4/3*a*_,P=[e,t],$=[e+k*C,t-R*b],A=[r+k*S,i-R*v],z=[r,i];if($[0]=2*P[0]-$[0],$[1]=2*P[1]-$[1],l)return[$,A,z].concat(f);{f=[$,A,z].concat(f);const O=[];for(let E=0;E<f.length;E+=3){const M=ai(f[E][0],f[E][1],h),L=ai(f[E+1][0],f[E+1][1],h),F=ai(f[E+2][0],f[E+2][1],h);O.push([M[0],M[1],L[0],L[1],F[0],F[1]])}return O}}const T_={randOffset:function(e,t){return rt(e,t)},randOffsetWithRange:function(e,t,r){return qn(e,t,r)},ellipse:function(e,t,r,i,n){const a=ed(r,i,n);return Ss(e,t,n,a).opset},doubleLineOps:function(e,t,r,i,n){return Ye(e,t,r,i,n,!0)}};function td(e,t,r,i,n){return{type:"path",ops:Ye(e,t,r,i,n)}}function fn(e,t,r){const i=(e||[]).length;if(i>2){const n=[];for(let a=0;a<i-1;a++)n.push(...Ye(e[a][0],e[a][1],e[a+1][0],e[a+1][1],r));return t&&n.push(...Ye(e[i-1][0],e[i-1][1],e[0][0],e[0][1],r)),{type:"path",ops:n}}return i===2?td(e[0][0],e[0][1],e[1][0],e[1][1],r):{type:"path",ops:[]}}function L_(e,t,r,i,n){return function(a,o){return fn(a,!0,o)}([[e,t],[e+r,t],[e+r,t+i],[e,t+i]],n)}function Kl(e,t){if(e.length){const r=typeof e[0][0]=="number"?[e]:e,i=Qi(r[0],1*(1+.2*t.roughness),t),n=t.disableMultiStroke?[]:Qi(r[0],1.5*(1+.22*t.roughness),tc(t));for(let a=1;a<r.length;a++){const o=r[a];if(o.length){const s=Qi(o,1*(1+.2*t.roughness),t),c=t.disableMultiStroke?[]:Qi(o,1.5*(1+.22*t.roughness),tc(t));for(const l of s)l.op!=="move"&&i.push(l);for(const l of c)l.op!=="move"&&n.push(l)}}return{type:"path",ops:i.concat(n)}}return{type:"path",ops:[]}}function ed(e,t,r){const i=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(e/2,2)+Math.pow(t/2,2))/2)),n=Math.ceil(Math.max(r.curveStepCount,r.curveStepCount/Math.sqrt(200)*i)),a=2*Math.PI/n;let o=Math.abs(e/2),s=Math.abs(t/2);const c=1-r.curveFitting;return o+=rt(o*c,r),s+=rt(s*c,r),{increment:a,rx:o,ry:s}}function Ss(e,t,r,i){const[n,a]=ec(i.increment,e,t,i.rx,i.ry,1,i.increment*qn(.1,qn(.4,1,r),r),r);let o=Hn(n,null,r);if(!r.disableMultiStroke&&r.roughness!==0){const[s]=ec(i.increment,e,t,i.rx,i.ry,1.5,0,r),c=Hn(s,null,r);o=o.concat(c)}return{estimatedPoints:a,opset:{type:"path",ops:o}}}function Ql(e,t,r,i,n,a,o,s,c){const l=e,h=t;let u=Math.abs(r/2),f=Math.abs(i/2);u+=rt(.01*u,c),f+=rt(.01*f,c);let d=n,g=a;for(;d<0;)d+=2*Math.PI,g+=2*Math.PI;g-d>2*Math.PI&&(d=0,g=2*Math.PI);const m=2*Math.PI/c.curveStepCount,y=Math.min(m/2,(g-d)/2),x=rc(y,l,h,u,f,d,g,1,c);if(!c.disableMultiStroke){const b=rc(y,l,h,u,f,d,g,1.5,c);x.push(...b)}return o&&(s?x.push(...Ye(l,h,l+u*Math.cos(d),h+f*Math.sin(d),c),...Ye(l,h,l+u*Math.cos(g),h+f*Math.sin(g),c)):x.push({op:"lineTo",data:[l,h]},{op:"lineTo",data:[l+u*Math.cos(d),h+f*Math.sin(d)]})),{type:"path",ops:x}}function Jl(e,t){const r=Qf(Kf(Bo(e))),i=[];let n=[0,0],a=[0,0];for(const{key:o,data:s}of r)switch(o){case"M":a=[s[0],s[1]],n=[s[0],s[1]];break;case"L":i.push(...Ye(a[0],a[1],s[0],s[1],t)),a=[s[0],s[1]];break;case"C":{const[c,l,h,u,f,d]=s;i.push(...B_(c,l,h,u,f,d,a,t)),a=[f,d];break}case"Z":i.push(...Ye(a[0],a[1],n[0],n[1],t)),a=[n[0],n[1]]}return{type:"path",ops:i}}function Pa(e,t){const r=[];for(const i of e)if(i.length){const n=t.maxRandomnessOffset||0,a=i.length;if(a>2){r.push({op:"move",data:[i[0][0]+rt(n,t),i[0][1]+rt(n,t)]});for(let o=1;o<a;o++)r.push({op:"lineTo",data:[i[o][0]+rt(n,t),i[o][1]+rt(n,t)]})}}return{type:"fillPath",ops:r}}function xr(e,t){return function(r,i){let n=r.fillStyle||"hachure";if(!jt[n])switch(n){case"zigzag":jt[n]||(jt[n]=new b_(i));break;case"cross-hatch":jt[n]||(jt[n]=new __(i));break;case"dots":jt[n]||(jt[n]=new C_(i));break;case"dashed":jt[n]||(jt[n]=new w_(i));break;case"zigzag-line":jt[n]||(jt[n]=new k_(i));break;default:n="hachure",jt[n]||(jt[n]=new Lo(i))}return jt[n]}(t,T_).fillPolygons(e,t)}function tc(e){const t=Object.assign({},e);return t.randomizer=void 0,e.seed&&(t.seed=e.seed+1),t}function rd(e){return e.randomizer||(e.randomizer=new v_(e.seed||0)),e.randomizer.next()}function qn(e,t,r,i=1){return r.roughness*i*(rd(r)*(t-e)+e)}function rt(e,t,r=1){return qn(-e,e,t,r)}function Ye(e,t,r,i,n,a=!1){const o=a?n.disableMultiStrokeFill:n.disableMultiStroke,s=Ts(e,t,r,i,n,!0,!1);if(o)return s;const c=Ts(e,t,r,i,n,!0,!0);return s.concat(c)}function Ts(e,t,r,i,n,a,o){const s=Math.pow(e-r,2)+Math.pow(t-i,2),c=Math.sqrt(s);let l=1;l=c<200?1:c>500?.4:-.0016668*c+1.233334;let h=n.maxRandomnessOffset||0;h*h*100>s&&(h=c/10);const u=h/2,f=.2+.2*rd(n);let d=n.bowing*n.maxRandomnessOffset*(i-t)/200,g=n.bowing*n.maxRandomnessOffset*(e-r)/200;d=rt(d,n,l),g=rt(g,n,l);const m=[],y=()=>rt(u,n,l),x=()=>rt(h,n,l),b=n.preserveVertices;return o?m.push({op:"move",data:[e+(b?0:y()),t+(b?0:y())]}):m.push({op:"move",data:[e+(b?0:rt(h,n,l)),t+(b?0:rt(h,n,l))]}),o?m.push({op:"bcurveTo",data:[d+e+(r-e)*f+y(),g+t+(i-t)*f+y(),d+e+2*(r-e)*f+y(),g+t+2*(i-t)*f+y(),r+(b?0:y()),i+(b?0:y())]}):m.push({op:"bcurveTo",data:[d+e+(r-e)*f+x(),g+t+(i-t)*f+x(),d+e+2*(r-e)*f+x(),g+t+2*(i-t)*f+x(),r+(b?0:x()),i+(b?0:x())]}),m}function Qi(e,t,r){if(!e.length)return[];const i=[];i.push([e[0][0]+rt(t,r),e[0][1]+rt(t,r)]),i.push([e[0][0]+rt(t,r),e[0][1]+rt(t,r)]);for(let n=1;n<e.length;n++)i.push([e[n][0]+rt(t,r),e[n][1]+rt(t,r)]),n===e.length-1&&i.push([e[n][0]+rt(t,r),e[n][1]+rt(t,r)]);return Hn(i,null,r)}function Hn(e,t,r){const i=e.length,n=[];if(i>3){const a=[],o=1-r.curveTightness;n.push({op:"move",data:[e[1][0],e[1][1]]});for(let s=1;s+2<i;s++){const c=e[s];a[0]=[c[0],c[1]],a[1]=[c[0]+(o*e[s+1][0]-o*e[s-1][0])/6,c[1]+(o*e[s+1][1]-o*e[s-1][1])/6],a[2]=[e[s+1][0]+(o*e[s][0]-o*e[s+2][0])/6,e[s+1][1]+(o*e[s][1]-o*e[s+2][1])/6],a[3]=[e[s+1][0],e[s+1][1]],n.push({op:"bcurveTo",data:[a[1][0],a[1][1],a[2][0],a[2][1],a[3][0],a[3][1]]})}}else i===3?(n.push({op:"move",data:[e[1][0],e[1][1]]}),n.push({op:"bcurveTo",data:[e[1][0],e[1][1],e[2][0],e[2][1],e[2][0],e[2][1]]})):i===2&&n.push(...Ts(e[0][0],e[0][1],e[1][0],e[1][1],r,!0,!0));return n}function ec(e,t,r,i,n,a,o,s){const c=[],l=[];if(s.roughness===0){e/=4,l.push([t+i*Math.cos(-e),r+n*Math.sin(-e)]);for(let h=0;h<=2*Math.PI;h+=e){const u=[t+i*Math.cos(h),r+n*Math.sin(h)];c.push(u),l.push(u)}l.push([t+i*Math.cos(0),r+n*Math.sin(0)]),l.push([t+i*Math.cos(e),r+n*Math.sin(e)])}else{const h=rt(.5,s)-Math.PI/2;l.push([rt(a,s)+t+.9*i*Math.cos(h-e),rt(a,s)+r+.9*n*Math.sin(h-e)]);const u=2*Math.PI+h-.01;for(let f=h;f<u;f+=e){const d=[rt(a,s)+t+i*Math.cos(f),rt(a,s)+r+n*Math.sin(f)];c.push(d),l.push(d)}l.push([rt(a,s)+t+i*Math.cos(h+2*Math.PI+.5*o),rt(a,s)+r+n*Math.sin(h+2*Math.PI+.5*o)]),l.push([rt(a,s)+t+.98*i*Math.cos(h+o),rt(a,s)+r+.98*n*Math.sin(h+o)]),l.push([rt(a,s)+t+.9*i*Math.cos(h+.5*o),rt(a,s)+r+.9*n*Math.sin(h+.5*o)])}return[l,c]}function rc(e,t,r,i,n,a,o,s,c){const l=a+rt(.1,c),h=[];h.push([rt(s,c)+t+.9*i*Math.cos(l-e),rt(s,c)+r+.9*n*Math.sin(l-e)]);for(let u=l;u<=o;u+=e)h.push([rt(s,c)+t+i*Math.cos(u),rt(s,c)+r+n*Math.sin(u)]);return h.push([t+i*Math.cos(o),r+n*Math.sin(o)]),h.push([t+i*Math.cos(o),r+n*Math.sin(o)]),Hn(h,null,c)}function B_(e,t,r,i,n,a,o,s){const c=[],l=[s.maxRandomnessOffset||1,(s.maxRandomnessOffset||1)+.3];let h=[0,0];const u=s.disableMultiStroke?1:2,f=s.preserveVertices;for(let d=0;d<u;d++)d===0?c.push({op:"move",data:[o[0],o[1]]}):c.push({op:"move",data:[o[0]+(f?0:rt(l[0],s)),o[1]+(f?0:rt(l[0],s))]}),h=f?[n,a]:[n+rt(l[d],s),a+rt(l[d],s)],c.push({op:"bcurveTo",data:[e+rt(l[d],s),t+rt(l[d],s),r+rt(l[d],s),i+rt(l[d],s),h[0],h[1]]});return c}function si(e){return[...e]}function ic(e,t=0){const r=e.length;if(r<3)throw new Error("A curve must have at least three points.");const i=[];if(r===3)i.push(si(e[0]),si(e[1]),si(e[2]),si(e[2]));else{const n=[];n.push(e[0],e[0]);for(let s=1;s<e.length;s++)n.push(e[s]),s===e.length-1&&n.push(e[s]);const a=[],o=1-t;i.push(si(n[0]));for(let s=1;s+2<n.length;s++){const c=n[s];a[0]=[c[0],c[1]],a[1]=[c[0]+(o*n[s+1][0]-o*n[s-1][0])/6,c[1]+(o*n[s+1][1]-o*n[s-1][1])/6],a[2]=[n[s+1][0]+(o*n[s][0]-o*n[s+2][0])/6,n[s+1][1]+(o*n[s][1]-o*n[s+2][1])/6],a[3]=[n[s+1][0],n[s+1][1]],i.push(a[1],a[2],a[3])}}return i}function dn(e,t){return Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2)}function A_(e,t,r){const i=dn(t,r);if(i===0)return dn(e,t);let n=((e[0]-t[0])*(r[0]-t[0])+(e[1]-t[1])*(r[1]-t[1]))/i;return n=Math.max(0,Math.min(1,n)),dn(e,Ze(t,r,n))}function Ze(e,t,r){return[e[0]+(t[0]-e[0])*r,e[1]+(t[1]-e[1])*r]}function Ls(e,t,r,i){const n=i||[];if(function(s,c){const l=s[c+0],h=s[c+1],u=s[c+2],f=s[c+3];let d=3*h[0]-2*l[0]-f[0];d*=d;let g=3*h[1]-2*l[1]-f[1];g*=g;let m=3*u[0]-2*f[0]-l[0];m*=m;let y=3*u[1]-2*f[1]-l[1];return y*=y,d<m&&(d=m),g<y&&(g=y),d+g}(e,t)<r){const s=e[t+0];n.length?(a=n[n.length-1],o=s,Math.sqrt(dn(a,o))>1&&n.push(s)):n.push(s),n.push(e[t+3])}else{const c=e[t+0],l=e[t+1],h=e[t+2],u=e[t+3],f=Ze(c,l,.5),d=Ze(l,h,.5),g=Ze(h,u,.5),m=Ze(f,d,.5),y=Ze(d,g,.5),x=Ze(m,y,.5);Ls([c,f,m,x],0,r,n),Ls([x,y,g,u],0,r,n)}var a,o;return n}function M_(e,t){return Yn(e,0,e.length,t)}function Yn(e,t,r,i,n){const a=n||[],o=e[t],s=e[r-1];let c=0,l=1;for(let h=t+1;h<r-1;++h){const u=A_(e[h],o,s);u>c&&(c=u,l=h)}return Math.sqrt(c)>i?(Yn(e,t,l+1,i,a),Yn(e,l,r,i,a)):(a.length||a.push(o),a.push(s)),a}function Na(e,t=.15,r){const i=[],n=(e.length-1)/3;for(let a=0;a<n;a++)Ls(e,3*a,t,i);return r&&r>0?Yn(i,0,i.length,r):i}const Zt="none";class jn{constructor(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:"#000",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:"hachure",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}static newSeed(){return Math.floor(Math.random()*2**31)}_o(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions}_d(t,r,i){return{shape:t,sets:r||[],options:i||this.defaultOptions}}line(t,r,i,n,a){const o=this._o(a);return this._d("line",[td(t,r,i,n,o)],o)}rectangle(t,r,i,n,a){const o=this._o(a),s=[],c=L_(t,r,i,n,o);if(o.fill){const l=[[t,r],[t+i,r],[t+i,r+n],[t,r+n]];o.fillStyle==="solid"?s.push(Pa([l],o)):s.push(xr([l],o))}return o.stroke!==Zt&&s.push(c),this._d("rectangle",s,o)}ellipse(t,r,i,n,a){const o=this._o(a),s=[],c=ed(i,n,o),l=Ss(t,r,o,c);if(o.fill)if(o.fillStyle==="solid"){const h=Ss(t,r,o,c).opset;h.type="fillPath",s.push(h)}else s.push(xr([l.estimatedPoints],o));return o.stroke!==Zt&&s.push(l.opset),this._d("ellipse",s,o)}circle(t,r,i,n){const a=this.ellipse(t,r,i,i,n);return a.shape="circle",a}linearPath(t,r){const i=this._o(r);return this._d("linearPath",[fn(t,!1,i)],i)}arc(t,r,i,n,a,o,s=!1,c){const l=this._o(c),h=[],u=Ql(t,r,i,n,a,o,s,!0,l);if(s&&l.fill)if(l.fillStyle==="solid"){const f=Object.assign({},l);f.disableMultiStroke=!0;const d=Ql(t,r,i,n,a,o,!0,!1,f);d.type="fillPath",h.push(d)}else h.push(function(f,d,g,m,y,x,b){const C=f,v=d;let S=Math.abs(g/2),_=Math.abs(m/2);S+=rt(.01*S,b),_+=rt(.01*_,b);let k=y,R=x;for(;k<0;)k+=2*Math.PI,R+=2*Math.PI;R-k>2*Math.PI&&(k=0,R=2*Math.PI);const P=(R-k)/b.curveStepCount,$=[];for(let A=k;A<=R;A+=P)$.push([C+S*Math.cos(A),v+_*Math.sin(A)]);return $.push([C+S*Math.cos(R),v+_*Math.sin(R)]),$.push([C,v]),xr([$],b)}(t,r,i,n,a,o,l));return l.stroke!==Zt&&h.push(u),this._d("arc",h,l)}curve(t,r){const i=this._o(r),n=[],a=Kl(t,i);if(i.fill&&i.fill!==Zt)if(i.fillStyle==="solid"){const o=Kl(t,Object.assign(Object.assign({},i),{disableMultiStroke:!0,roughness:i.roughness?i.roughness+i.fillShapeRoughnessGain:0}));n.push({type:"fillPath",ops:this._mergedShape(o.ops)})}else{const o=[],s=t;if(s.length){const c=typeof s[0][0]=="number"?[s]:s;for(const l of c)l.length<3?o.push(...l):l.length===3?o.push(...Na(ic([l[0],l[0],l[1],l[2]]),10,(1+i.roughness)/2)):o.push(...Na(ic(l),10,(1+i.roughness)/2))}o.length&&n.push(xr([o],i))}return i.stroke!==Zt&&n.push(a),this._d("curve",n,i)}polygon(t,r){const i=this._o(r),n=[],a=fn(t,!0,i);return i.fill&&(i.fillStyle==="solid"?n.push(Pa([t],i)):n.push(xr([t],i))),i.stroke!==Zt&&n.push(a),this._d("polygon",n,i)}path(t,r){const i=this._o(r),n=[];if(!t)return this._d("path",n,i);t=(t||"").replace(/\n/g," ").replace(/(-\s)/g,"-").replace("/(ss)/g"," ");const a=i.fill&&i.fill!=="transparent"&&i.fill!==Zt,o=i.stroke!==Zt,s=!!(i.simplification&&i.simplification<1),c=function(h,u,f){const d=Qf(Kf(Bo(h))),g=[];let m=[],y=[0,0],x=[];const b=()=>{x.length>=4&&m.push(...Na(x,u)),x=[]},C=()=>{b(),m.length&&(g.push(m),m=[])};for(const{key:S,data:_}of d)switch(S){case"M":C(),y=[_[0],_[1]],m.push(y);break;case"L":b(),m.push([_[0],_[1]]);break;case"C":if(!x.length){const k=m.length?m[m.length-1]:y;x.push([k[0],k[1]])}x.push([_[0],_[1]]),x.push([_[2],_[3]]),x.push([_[4],_[5]]);break;case"Z":b(),m.push([y[0],y[1]])}if(C(),!f)return g;const v=[];for(const S of g){const _=M_(S,f);_.length&&v.push(_)}return v}(t,1,s?4-4*(i.simplification||1):(1+i.roughness)/2),l=Jl(t,i);if(a)if(i.fillStyle==="solid")if(c.length===1){const h=Jl(t,Object.assign(Object.assign({},i),{disableMultiStroke:!0,roughness:i.roughness?i.roughness+i.fillShapeRoughnessGain:0}));n.push({type:"fillPath",ops:this._mergedShape(h.ops)})}else n.push(Pa(c,i));else n.push(xr(c,i));return o&&(s?c.forEach(h=>{n.push(fn(h,!1,i))}):n.push(l)),this._d("path",n,i)}opsToPath(t,r){let i="";for(const n of t.ops){const a=typeof r=="number"&&r>=0?n.data.map(o=>+o.toFixed(r)):n.data;switch(n.op){case"move":i+=`M${a[0]} ${a[1]} `;break;case"bcurveTo":i+=`C${a[0]} ${a[1]}, ${a[2]} ${a[3]}, ${a[4]} ${a[5]} `;break;case"lineTo":i+=`L${a[0]} ${a[1]} `}}return i.trim()}toPaths(t){const r=t.sets||[],i=t.options||this.defaultOptions,n=[];for(const a of r){let o=null;switch(a.type){case"path":o={d:this.opsToPath(a),stroke:i.stroke,strokeWidth:i.strokeWidth,fill:Zt};break;case"fillPath":o={d:this.opsToPath(a),stroke:Zt,strokeWidth:0,fill:i.fill||Zt};break;case"fillSketch":o=this.fillSketch(a,i)}o&&n.push(o)}return n}fillSketch(t,r){let i=r.fillWeight;return i<0&&(i=r.strokeWidth/2),{d:this.opsToPath(t),stroke:r.fill||Zt,strokeWidth:i,fill:Zt}}_mergedShape(t){return t.filter((r,i)=>i===0||r.op!=="move")}}class E_{constructor(t,r){this.canvas=t,this.ctx=this.canvas.getContext("2d"),this.gen=new jn(r)}draw(t){const r=t.sets||[],i=t.options||this.getDefaultOptions(),n=this.ctx,a=t.options.fixedDecimalPlaceDigits;for(const o of r)switch(o.type){case"path":n.save(),n.strokeStyle=i.stroke==="none"?"transparent":i.stroke,n.lineWidth=i.strokeWidth,i.strokeLineDash&&n.setLineDash(i.strokeLineDash),i.strokeLineDashOffset&&(n.lineDashOffset=i.strokeLineDashOffset),this._drawToContext(n,o,a),n.restore();break;case"fillPath":{n.save(),n.fillStyle=i.fill||"";const s=t.shape==="curve"||t.shape==="polygon"||t.shape==="path"?"evenodd":"nonzero";this._drawToContext(n,o,a,s),n.restore();break}case"fillSketch":this.fillSketch(n,o,i)}}fillSketch(t,r,i){let n=i.fillWeight;n<0&&(n=i.strokeWidth/2),t.save(),i.fillLineDash&&t.setLineDash(i.fillLineDash),i.fillLineDashOffset&&(t.lineDashOffset=i.fillLineDashOffset),t.strokeStyle=i.fill||"",t.lineWidth=n,this._drawToContext(t,r,i.fixedDecimalPlaceDigits),t.restore()}_drawToContext(t,r,i,n="nonzero"){t.beginPath();for(const a of r.ops){const o=typeof i=="number"&&i>=0?a.data.map(s=>+s.toFixed(i)):a.data;switch(a.op){case"move":t.moveTo(o[0],o[1]);break;case"bcurveTo":t.bezierCurveTo(o[0],o[1],o[2],o[3],o[4],o[5]);break;case"lineTo":t.lineTo(o[0],o[1])}}r.type==="fillPath"?t.fill(n):t.stroke()}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}line(t,r,i,n,a){const o=this.gen.line(t,r,i,n,a);return this.draw(o),o}rectangle(t,r,i,n,a){const o=this.gen.rectangle(t,r,i,n,a);return this.draw(o),o}ellipse(t,r,i,n,a){const o=this.gen.ellipse(t,r,i,n,a);return this.draw(o),o}circle(t,r,i,n){const a=this.gen.circle(t,r,i,n);return this.draw(a),a}linearPath(t,r){const i=this.gen.linearPath(t,r);return this.draw(i),i}polygon(t,r){const i=this.gen.polygon(t,r);return this.draw(i),i}arc(t,r,i,n,a,o,s=!1,c){const l=this.gen.arc(t,r,i,n,a,o,s,c);return this.draw(l),l}curve(t,r){const i=this.gen.curve(t,r);return this.draw(i),i}path(t,r){const i=this.gen.path(t,r);return this.draw(i),i}}const Ji="http://www.w3.org/2000/svg";class F_{constructor(t,r){this.svg=t,this.gen=new jn(r)}draw(t){const r=t.sets||[],i=t.options||this.getDefaultOptions(),n=this.svg.ownerDocument||window.document,a=n.createElementNS(Ji,"g"),o=t.options.fixedDecimalPlaceDigits;for(const s of r){let c=null;switch(s.type){case"path":c=n.createElementNS(Ji,"path"),c.setAttribute("d",this.opsToPath(s,o)),c.setAttribute("stroke",i.stroke),c.setAttribute("stroke-width",i.strokeWidth+""),c.setAttribute("fill","none"),i.strokeLineDash&&c.setAttribute("stroke-dasharray",i.strokeLineDash.join(" ").trim()),i.strokeLineDashOffset&&c.setAttribute("stroke-dashoffset",`${i.strokeLineDashOffset}`);break;case"fillPath":c=n.createElementNS(Ji,"path"),c.setAttribute("d",this.opsToPath(s,o)),c.setAttribute("stroke","none"),c.setAttribute("stroke-width","0"),c.setAttribute("fill",i.fill||""),t.shape!=="curve"&&t.shape!=="polygon"||c.setAttribute("fill-rule","evenodd");break;case"fillSketch":c=this.fillSketch(n,s,i)}c&&a.appendChild(c)}return a}fillSketch(t,r,i){let n=i.fillWeight;n<0&&(n=i.strokeWidth/2);const a=t.createElementNS(Ji,"path");return a.setAttribute("d",this.opsToPath(r,i.fixedDecimalPlaceDigits)),a.setAttribute("stroke",i.fill||""),a.setAttribute("stroke-width",n+""),a.setAttribute("fill","none"),i.fillLineDash&&a.setAttribute("stroke-dasharray",i.fillLineDash.join(" ").trim()),i.fillLineDashOffset&&a.setAttribute("stroke-dashoffset",`${i.fillLineDashOffset}`),a}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}opsToPath(t,r){return this.gen.opsToPath(t,r)}line(t,r,i,n,a){const o=this.gen.line(t,r,i,n,a);return this.draw(o)}rectangle(t,r,i,n,a){const o=this.gen.rectangle(t,r,i,n,a);return this.draw(o)}ellipse(t,r,i,n,a){const o=this.gen.ellipse(t,r,i,n,a);return this.draw(o)}circle(t,r,i,n){const a=this.gen.circle(t,r,i,n);return this.draw(a)}linearPath(t,r){const i=this.gen.linearPath(t,r);return this.draw(i)}polygon(t,r){const i=this.gen.polygon(t,r);return this.draw(i)}arc(t,r,i,n,a,o,s=!1,c){const l=this.gen.arc(t,r,i,n,a,o,s,c);return this.draw(l)}curve(t,r){const i=this.gen.curve(t,r);return this.draw(i)}path(t,r){const i=this.gen.path(t,r);return this.draw(i)}}var Y={canvas:(e,t)=>new E_(e,t),svg:(e,t)=>new F_(e,t),generator:e=>new jn(e),newSeed:()=>jn.newSeed()},st=p(async(e,t,r)=>{var u,f;let i;const n=t.useHtmlLabels||kt((u=ut())==null?void 0:u.htmlLabels);r?i=r:i="node default";const a=e.insert("g").attr("class",i).attr("id",t.domId||t.id),o=a.insert("g").attr("class","label").attr("style",Wt(t.labelStyle));let s;t.label===void 0?s="":s=typeof t.label=="string"?t.label:t.label[0];const c=await Ue(o,nr(cr(s),ut()),{useHtmlLabels:n,width:t.width||((f=ut().flowchart)==null?void 0:f.wrappingWidth),cssClasses:"markdown-node-label",style:t.labelStyle,addSvgBackground:!!t.icon||!!t.img});let l=c.getBBox();const h=((t==null?void 0:t.padding)??0)/2;if(n){const d=c.children[0],g=lt(c),m=d.getElementsByTagName("img");if(m){const y=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...m].map(x=>new Promise(b=>{function C(){if(x.style.display="flex",x.style.flexDirection="column",y){const v=ut().fontSize?ut().fontSize:window.getComputedStyle(document.body).fontSize,S=5,[_=Ac.fontSize]=sa(v),k=_*S+"px";x.style.minWidth=k,x.style.maxWidth=k}else x.style.width="100%";b(x)}p(C,"setupImage"),setTimeout(()=>{x.complete&&C()}),x.addEventListener("error",C),x.addEventListener("load",C)})))}l=d.getBoundingClientRect(),g.attr("width",l.width),g.attr("height",l.height)}return n?o.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"):o.attr("transform","translate(0, "+-l.height/2+")"),t.centerLabel&&o.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"),o.insert("rect",":first-child"),{shapeSvg:a,bbox:l,halfPadding:h,label:o}},"labelHelper"),za=p(async(e,t,r)=>{var c,l,h,u,f,d;const i=r.useHtmlLabels||kt((l=(c=ut())==null?void 0:c.flowchart)==null?void 0:l.htmlLabels),n=e.insert("g").attr("class","label").attr("style",r.labelStyle||""),a=await Ue(n,nr(cr(t),ut()),{useHtmlLabels:i,width:r.width||((u=(h=ut())==null?void 0:h.flowchart)==null?void 0:u.wrappingWidth),style:r.labelStyle,addSvgBackground:!!r.icon||!!r.img});let o=a.getBBox();const s=r.padding/2;if(kt((d=(f=ut())==null?void 0:f.flowchart)==null?void 0:d.htmlLabels)){const g=a.children[0],m=lt(a);o=g.getBoundingClientRect(),m.attr("width",o.width),m.attr("height",o.height)}return i?n.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"):n.attr("transform","translate(0, "+-o.height/2+")"),r.centerLabel&&n.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),n.insert("rect",":first-child"),{shapeSvg:e,bbox:o,halfPadding:s,label:n}},"insertLabel"),V=p((e,t)=>{const r=t.node().getBBox();e.width=r.width,e.height=r.height},"updateNodeBounds"),it=p((e,t)=>(e.look==="handDrawn"?"rough-node":"node")+" "+e.cssClasses+" "+(t||""),"getNodeClasses");function ct(e){const t=e.map((r,i)=>`${i===0?"M":"L"}${r.x},${r.y}`);return t.push("Z"),t.join(" ")}p(ct,"createPathFromPoints");function je(e,t,r,i,n,a){const o=[],c=r-e,l=i-t,h=c/a,u=2*Math.PI/h,f=t+l/2;for(let d=0;d<=50;d++){const g=d/50,m=e+g*c,y=f+n*Math.sin(u*(m-e));o.push({x:m,y})}return o}p(je,"generateFullSineWavePoints");function Ao(e,t,r,i,n,a){const o=[],s=n*Math.PI/180,h=(a*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const f=s+u*h,d=e+r*Math.cos(f),g=t+r*Math.sin(f);o.push({x:-d,y:-g})}return o}p(Ao,"generateCirclePoints");var $_=p((e,t)=>{var r=e.x,i=e.y,n=t.x-r,a=t.y-i,o=e.width/2,s=e.height/2,c,l;return Math.abs(a)*o>Math.abs(n)*s?(a<0&&(s=-s),c=a===0?0:s*n/a,l=s):(n<0&&(o=-o),c=o,l=n===0?0:o*a/n),{x:r+c,y:i+l}},"intersectRect"),jr=$_;function id(e,t){t&&e.attr("style",t)}p(id,"applyStyle");async function nd(e){const t=lt(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),r=t.append("xhtml:div");let i=e.label;e.label&&Ir(e.label)&&(i=await Is(e.label.replace(Yr.lineBreakRegex,`
`),ut()));const n=e.isNode?"nodeLabel":"edgeLabel";return r.html('<span class="'+n+'" '+(e.labelStyle?'style="'+e.labelStyle+'"':"")+">"+i+"</span>"),id(r,e.labelStyle),r.style("display","inline-block"),r.style("padding-right","1px"),r.style("white-space","nowrap"),r.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}p(nd,"addHtmlLabel");var D_=p(async(e,t,r,i)=>{let n=e||"";if(typeof n=="object"&&(n=n[0]),kt(ut().flowchart.htmlLabels)){n=n.replace(/\\n|\n/g,"<br />"),D.info("vertexText"+n);const a={isNode:i,label:cr(n).replace(/fa[blrs]?:fa-[\w-]+/g,s=>`<i class='${s.replace(":"," ")}'></i>`),labelStyle:t&&t.replace("fill:","color:")};return await nd(a)}else{const a=document.createElementNS("http://www.w3.org/2000/svg","text");a.setAttribute("style",t.replace("color:","fill:"));let o=[];typeof n=="string"?o=n.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(n)?o=n:o=[];for(const s of o){const c=document.createElementNS("http://www.w3.org/2000/svg","tspan");c.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),c.setAttribute("dy","1em"),c.setAttribute("x","0"),r?c.setAttribute("class","title-row"):c.setAttribute("class","row"),c.textContent=s.trim(),a.appendChild(c)}return a}},"createLabel"),er=D_,Oe=p((e,t,r,i,n)=>["M",e+n,t,"H",e+r-n,"A",n,n,0,0,1,e+r,t+n,"V",t+i-n,"A",n,n,0,0,1,e+r-n,t+i,"H",e+n,"A",n,n,0,0,1,e,t+i-n,"V",t+n,"A",n,n,0,0,1,e+n,t,"Z"].join(" "),"createRoundedRectPathD"),R_=p(e=>{const{handDrawnSeed:t}=ut();return{fill:e,hachureAngle:120,hachureGap:4,fillWeight:2,roughness:.7,stroke:e,seed:t}},"solidStateFill"),Ur=p(e=>{const t=O_([...e.cssCompiledStyles||[],...e.cssStyles||[]]);return{stylesMap:t,stylesArray:[...t]}},"compileStyles"),O_=p(e=>{const t=new Map;return e.forEach(r=>{const[i,n]=r.split(":");t.set(i.trim(),n==null?void 0:n.trim())}),t},"styles2Map"),ad=p(e=>e==="color"||e==="font-size"||e==="font-family"||e==="font-weight"||e==="font-style"||e==="text-decoration"||e==="text-align"||e==="text-transform"||e==="line-height"||e==="letter-spacing"||e==="word-spacing"||e==="text-shadow"||e==="text-overflow"||e==="white-space"||e==="word-wrap"||e==="word-break"||e==="overflow-wrap"||e==="hyphens","isLabelStyle"),X=p(e=>{const{stylesArray:t}=Ur(e),r=[],i=[],n=[],a=[];return t.forEach(o=>{const s=o[0];ad(s)?r.push(o.join(":")+" !important"):(i.push(o.join(":")+" !important"),s.includes("stroke")&&n.push(o.join(":")+" !important"),s==="fill"&&a.push(o.join(":")+" !important"))}),{labelStyles:r.join(";"),nodeStyles:i.join(";"),stylesArray:t,borderStyles:n,backgroundStyles:a}},"styles2String"),G=p((e,t)=>{var c;const{themeVariables:r,handDrawnSeed:i}=ut(),{nodeBorder:n,mainBkg:a}=r,{stylesMap:o}=Ur(e);return Object.assign({roughness:.7,fill:o.get("fill")||a,fillStyle:"hachure",fillWeight:4,hachureGap:5.2,stroke:o.get("stroke")||n,seed:i,strokeWidth:((c=o.get("stroke-width"))==null?void 0:c.replace("px",""))||1.3,fillLineDash:[0,0]},t)},"userNodeOverrides"),sd=p(async(e,t)=>{D.info("Creating subgraph rect for ",t.id,t);const r=ut(),{themeVariables:i,handDrawnSeed:n}=r,{clusterBkg:a,clusterBorder:o}=i,{labelStyles:s,nodeStyles:c,borderStyles:l,backgroundStyles:h}=X(t),u=e.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),f=kt(r.flowchart.htmlLabels),d=u.insert("g").attr("class","cluster-label "),g=await Ue(d,t.label,{style:t.labelStyle,useHtmlLabels:f,isNode:!0});let m=g.getBBox();if(kt(r.flowchart.htmlLabels)){const k=g.children[0],R=lt(g);m=k.getBoundingClientRect(),R.attr("width",m.width),R.attr("height",m.height)}const y=t.width<=m.width+t.padding?m.width+t.padding:t.width;t.width<=m.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;const x=t.height,b=t.x-y/2,C=t.y-x/2;D.trace("Data ",t,JSON.stringify(t));let v;if(t.look==="handDrawn"){const k=Y.svg(u),R=G(t,{roughness:.7,fill:a,stroke:o,fillWeight:3,seed:n}),P=k.path(Oe(b,C,y,x,0),R);v=u.insert(()=>(D.debug("Rough node insert CXC",P),P),":first-child"),v.select("path:nth-child(2)").attr("style",l.join(";")),v.select("path").attr("style",h.join(";").replace("fill","stroke"))}else v=u.insert("rect",":first-child"),v.attr("style",c).attr("rx",t.rx).attr("ry",t.ry).attr("x",b).attr("y",C).attr("width",y).attr("height",x);const{subGraphTitleTopMargin:S}=Zs(r);if(d.attr("transform",`translate(${t.x-m.width/2}, ${t.y-t.height/2+S})`),s){const k=d.select("span");k&&k.attr("style",s)}const _=v.node().getBBox();return t.offsetX=0,t.width=_.width,t.height=_.height,t.offsetY=m.height-t.padding/2,t.intersect=function(k){return jr(t,k)},{cluster:u,labelBBox:m}},"rect"),I_=p((e,t)=>{const r=e.insert("g").attr("class","note-cluster").attr("id",t.id),i=r.insert("rect",":first-child"),n=0*t.padding,a=n/2;i.attr("rx",t.rx).attr("ry",t.ry).attr("x",t.x-t.width/2-a).attr("y",t.y-t.height/2-a).attr("width",t.width+n).attr("height",t.height+n).attr("fill","none");const o=i.node().getBBox();return t.width=o.width,t.height=o.height,t.intersect=function(s){return jr(t,s)},{cluster:r,labelBBox:{width:0,height:0}}},"noteGroup"),P_=p(async(e,t)=>{const r=ut(),{themeVariables:i,handDrawnSeed:n}=r,{altBackground:a,compositeBackground:o,compositeTitleBackground:s,nodeBorder:c}=i,l=e.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-id",t.id).attr("data-look",t.look),h=l.insert("g",":first-child"),u=l.insert("g").attr("class","cluster-label");let f=l.append("rect");const d=u.node().appendChild(await er(t.label,t.labelStyle,void 0,!0));let g=d.getBBox();if(kt(r.flowchart.htmlLabels)){const P=d.children[0],$=lt(d);g=P.getBoundingClientRect(),$.attr("width",g.width),$.attr("height",g.height)}const m=0*t.padding,y=m/2,x=(t.width<=g.width+t.padding?g.width+t.padding:t.width)+m;t.width<=g.width+t.padding?t.diff=(x-t.width)/2-t.padding:t.diff=-t.padding;const b=t.height+m,C=t.height+m-g.height-6,v=t.x-x/2,S=t.y-b/2;t.width=x;const _=t.y-t.height/2-y+g.height+2;let k;if(t.look==="handDrawn"){const P=t.cssClasses.includes("statediagram-cluster-alt"),$=Y.svg(l),A=t.rx||t.ry?$.path(Oe(v,S,x,b,10),{roughness:.7,fill:s,fillStyle:"solid",stroke:c,seed:n}):$.rectangle(v,S,x,b,{seed:n});k=l.insert(()=>A,":first-child");const z=$.rectangle(v,_,x,C,{fill:P?a:o,fillStyle:P?"hachure":"solid",stroke:c,seed:n});k=l.insert(()=>A,":first-child"),f=l.insert(()=>z)}else k=h.insert("rect",":first-child"),k.attr("class","outer").attr("x",v).attr("y",S).attr("width",x).attr("height",b).attr("data-look",t.look),f.attr("class","inner").attr("x",v).attr("y",_).attr("width",x).attr("height",C);u.attr("transform",`translate(${t.x-g.width/2}, ${S+1-(kt(r.flowchart.htmlLabels)?0:3)})`);const R=k.node().getBBox();return t.height=R.height,t.offsetX=0,t.offsetY=g.height-t.padding/2,t.labelBBox=g,t.intersect=function(P){return jr(t,P)},{cluster:l,labelBBox:g}},"roundedWithTitle"),N_=p(async(e,t)=>{D.info("Creating subgraph rect for ",t.id,t);const r=ut(),{themeVariables:i,handDrawnSeed:n}=r,{clusterBkg:a,clusterBorder:o}=i,{labelStyles:s,nodeStyles:c,borderStyles:l,backgroundStyles:h}=X(t),u=e.insert("g").attr("class","cluster "+t.cssClasses).attr("id",t.id).attr("data-look",t.look),f=kt(r.flowchart.htmlLabels),d=u.insert("g").attr("class","cluster-label "),g=await Ue(d,t.label,{style:t.labelStyle,useHtmlLabels:f,isNode:!0,width:t.width});let m=g.getBBox();if(kt(r.flowchart.htmlLabels)){const k=g.children[0],R=lt(g);m=k.getBoundingClientRect(),R.attr("width",m.width),R.attr("height",m.height)}const y=t.width<=m.width+t.padding?m.width+t.padding:t.width;t.width<=m.width+t.padding?t.diff=(y-t.width)/2-t.padding:t.diff=-t.padding;const x=t.height,b=t.x-y/2,C=t.y-x/2;D.trace("Data ",t,JSON.stringify(t));let v;if(t.look==="handDrawn"){const k=Y.svg(u),R=G(t,{roughness:.7,fill:a,stroke:o,fillWeight:4,seed:n}),P=k.path(Oe(b,C,y,x,t.rx),R);v=u.insert(()=>(D.debug("Rough node insert CXC",P),P),":first-child"),v.select("path:nth-child(2)").attr("style",l.join(";")),v.select("path").attr("style",h.join(";").replace("fill","stroke"))}else v=u.insert("rect",":first-child"),v.attr("style",c).attr("rx",t.rx).attr("ry",t.ry).attr("x",b).attr("y",C).attr("width",y).attr("height",x);const{subGraphTitleTopMargin:S}=Zs(r);if(d.attr("transform",`translate(${t.x-m.width/2}, ${t.y-t.height/2+S})`),s){const k=d.select("span");k&&k.attr("style",s)}const _=v.node().getBBox();return t.offsetX=0,t.width=_.width,t.height=_.height,t.offsetY=m.height-t.padding/2,t.intersect=function(k){return jr(t,k)},{cluster:u,labelBBox:m}},"kanbanSection"),z_=p((e,t)=>{const r=ut(),{themeVariables:i,handDrawnSeed:n}=r,{nodeBorder:a}=i,o=e.insert("g").attr("class",t.cssClasses).attr("id",t.id).attr("data-look",t.look),s=o.insert("g",":first-child"),c=0*t.padding,l=t.width+c;t.diff=-t.padding;const h=t.height+c,u=t.x-l/2,f=t.y-h/2;t.width=l;let d;if(t.look==="handDrawn"){const y=Y.svg(o).rectangle(u,f,l,h,{fill:"lightgrey",roughness:.5,strokeLineDash:[5],stroke:a,seed:n});d=o.insert(()=>y,":first-child")}else d=s.insert("rect",":first-child"),d.attr("class","divider").attr("x",u).attr("y",f).attr("width",l).attr("height",h).attr("data-look",t.look);const g=d.node().getBBox();return t.height=g.height,t.offsetX=0,t.offsetY=0,t.intersect=function(m){return jr(t,m)},{cluster:o,labelBBox:{}}},"divider"),W_=sd,q_={rect:sd,squareRect:W_,roundedWithTitle:P_,noteGroup:I_,divider:z_,kanbanSection:N_},od=new Map,H_=p(async(e,t)=>{const r=t.shape||"rect",i=await q_[r](e,t);return od.set(t.id,i),i},"insertCluster"),Qv=p(()=>{od=new Map},"clear");function ld(e,t){return e.intersect(t)}p(ld,"intersectNode");var Y_=ld;function cd(e,t,r,i){var n=e.x,a=e.y,o=n-i.x,s=a-i.y,c=Math.sqrt(t*t*s*s+r*r*o*o),l=Math.abs(t*r*o/c);i.x<n&&(l=-l);var h=Math.abs(t*r*s/c);return i.y<a&&(h=-h),{x:n+l,y:a+h}}p(cd,"intersectEllipse");var hd=cd;function ud(e,t,r){return hd(e,t,t,r)}p(ud,"intersectCircle");var j_=ud;function fd(e,t,r,i){var n,a,o,s,c,l,h,u,f,d,g,m,y,x,b;if(n=t.y-e.y,o=e.x-t.x,c=t.x*e.y-e.x*t.y,f=n*r.x+o*r.y+c,d=n*i.x+o*i.y+c,!(f!==0&&d!==0&&Bs(f,d))&&(a=i.y-r.y,s=r.x-i.x,l=i.x*r.y-r.x*i.y,h=a*e.x+s*e.y+l,u=a*t.x+s*t.y+l,!(h!==0&&u!==0&&Bs(h,u))&&(g=n*s-a*o,g!==0)))return m=Math.abs(g/2),y=o*l-s*c,x=y<0?(y-m)/g:(y+m)/g,y=a*c-n*l,b=y<0?(y-m)/g:(y+m)/g,{x,y:b}}p(fd,"intersectLine");function Bs(e,t){return e*t>0}p(Bs,"sameSign");var U_=fd;function dd(e,t,r){let i=e.x,n=e.y,a=[],o=Number.POSITIVE_INFINITY,s=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(h){o=Math.min(o,h.x),s=Math.min(s,h.y)}):(o=Math.min(o,t.x),s=Math.min(s,t.y));let c=i-e.width/2-o,l=n-e.height/2-s;for(let h=0;h<t.length;h++){let u=t[h],f=t[h<t.length-1?h+1:0],d=U_(e,r,{x:c+u.x,y:l+u.y},{x:c+f.x,y:l+f.y});d&&a.push(d)}return a.length?(a.length>1&&a.sort(function(h,u){let f=h.x-r.x,d=h.y-r.y,g=Math.sqrt(f*f+d*d),m=u.x-r.x,y=u.y-r.y,x=Math.sqrt(m*m+y*y);return g<x?-1:g===x?0:1}),a[0]):e}p(dd,"intersectPolygon");var G_=dd,H={node:Y_,circle:j_,ellipse:hd,polygon:G_,rect:jr};function pd(e,t){const{labelStyles:r}=X(t);t.labelStyle=r;const i=it(t);let n=i;i||(n="anchor");const a=e.insert("g").attr("class",n).attr("id",t.domId||t.id),o=1,{cssStyles:s}=t,c=Y.svg(a),l=G(t,{fill:"black",stroke:"none",fillStyle:"solid"});t.look!=="handDrawn"&&(l.roughness=0);const h=c.circle(0,0,o*2,l),u=a.insert(()=>h,":first-child");return u.attr("class","anchor").attr("style",Wt(s)),V(t,u),t.intersect=function(f){return D.info("Circle intersect",t,o,f),H.circle(t,o,f)},a}p(pd,"anchor");function As(e,t,r,i,n,a,o){const c=(e+r)/2,l=(t+i)/2,h=Math.atan2(i-t,r-e),u=(r-e)/2,f=(i-t)/2,d=u/n,g=f/a,m=Math.sqrt(d**2+g**2);if(m>1)throw new Error("The given radii are too small to create an arc between the points.");const y=Math.sqrt(1-m**2),x=c+y*a*Math.sin(h)*(o?-1:1),b=l-y*n*Math.cos(h)*(o?-1:1),C=Math.atan2((t-b)/a,(e-x)/n);let S=Math.atan2((i-b)/a,(r-x)/n)-C;o&&S<0&&(S+=2*Math.PI),!o&&S>0&&(S-=2*Math.PI);const _=[];for(let k=0;k<20;k++){const R=k/19,P=C+R*S,$=x+n*Math.cos(P),A=b+a*Math.sin(P);_.push({x:$,y:A})}return _}p(As,"generateArcPoints");async function gd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=a.width+t.padding+20,s=a.height+t.padding,c=s/2,l=c/(2.5+s/50),{cssStyles:h}=t,u=[{x:o/2,y:-s/2},{x:-o/2,y:-s/2},...As(-o/2,-s/2,-o/2,s/2,l,c,!1),{x:o/2,y:s/2},...As(o/2,s/2,o/2,-s/2,l,c,!0)],f=Y.svg(n),d=G(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=ct(u),m=f.path(g,d),y=n.insert(()=>m,":first-child");return y.attr("class","basic label-container"),h&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",i),y.attr("transform",`translate(${l/2}, 0)`),V(t,y),t.intersect=function(x){return H.polygon(t,u,x)},n}p(gd,"bowTieRect");function Ie(e,t,r,i){return e.insert("polygon",":first-child").attr("points",i.map(function(n){return n.x+","+n.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+r/2+")")}p(Ie,"insertPolygonShape");async function md(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=a.height+t.padding,s=12,c=a.width+t.padding+s,l=0,h=c,u=-o,f=0,d=[{x:l+s,y:u},{x:h,y:u},{x:h,y:f},{x:l,y:f},{x:l,y:u+s},{x:l+s,y:u}];let g;const{cssStyles:m}=t;if(t.look==="handDrawn"){const y=Y.svg(n),x=G(t,{}),b=ct(d),C=y.path(b,x);g=n.insert(()=>C,":first-child").attr("transform",`translate(${-c/2}, ${o/2})`),m&&g.attr("style",m)}else g=Ie(n,c,o,d);return i&&g.attr("style",i),V(t,g),t.intersect=function(y){return H.polygon(t,d,y)},n}p(md,"card");function yd(e,t){const{nodeStyles:r}=X(t);t.label="";const i=e.insert("g").attr("class",it(t)).attr("id",t.domId??t.id),{cssStyles:n}=t,a=Math.max(28,t.width??0),o=[{x:0,y:a/2},{x:a/2,y:0},{x:0,y:-a/2},{x:-a/2,y:0}],s=Y.svg(i),c=G(t,{});t.look!=="handDrawn"&&(c.roughness=0,c.fillStyle="solid");const l=ct(o),h=s.path(l,c),u=i.insert(()=>h,":first-child");return n&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",n),r&&t.look!=="handDrawn"&&u.selectAll("path").attr("style",r),t.width=28,t.height=28,t.intersect=function(f){return H.polygon(t,o,f)},i}p(yd,"choice");async function xd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,halfPadding:o}=await st(e,t,it(t)),s=a.width/2+o;let c;const{cssStyles:l}=t;if(t.look==="handDrawn"){const h=Y.svg(n),u=G(t,{}),f=h.circle(0,0,s*2,u);c=n.insert(()=>f,":first-child"),c.attr("class","basic label-container").attr("style",Wt(l))}else c=n.insert("circle",":first-child").attr("class","basic label-container").attr("style",i).attr("r",s).attr("cx",0).attr("cy",0);return V(t,c),t.intersect=function(h){return D.info("Circle intersect",t,s,h),H.circle(t,s,h)},n}p(xd,"circle");function bd(e){const t=Math.cos(Math.PI/4),r=Math.sin(Math.PI/4),i=e*2,n={x:i/2*t,y:i/2*r},a={x:-(i/2)*t,y:i/2*r},o={x:-(i/2)*t,y:-(i/2)*r},s={x:i/2*t,y:-(i/2)*r};return`M ${a.x},${a.y} L ${s.x},${s.y}
                   M ${n.x},${n.y} L ${o.x},${o.y}`}p(bd,"createLine");function _d(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r,t.label="";const n=e.insert("g").attr("class",it(t)).attr("id",t.domId??t.id),a=Math.max(30,(t==null?void 0:t.width)??0),{cssStyles:o}=t,s=Y.svg(n),c=G(t,{});t.look!=="handDrawn"&&(c.roughness=0,c.fillStyle="solid");const l=s.circle(0,0,a*2,c),h=bd(a),u=s.path(h,c),f=n.insert(()=>l,":first-child");return f.insert(()=>u),o&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",o),i&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",i),V(t,f),t.intersect=function(d){return D.info("crossedCircle intersect",t,{radius:a,point:d}),H.circle(t,a,d)},n}p(_d,"crossedCircle");function Be(e,t,r,i=100,n=0,a=180){const o=[],s=n*Math.PI/180,h=(a*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const f=s+u*h,d=e+r*Math.cos(f),g=t+r*Math.sin(f);o.push({x:-d,y:-g})}return o}p(Be,"generateCirclePoints");async function Cd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=a.width+(t.padding??0),c=a.height+(t.padding??0),l=Math.max(5,c*.1),{cssStyles:h}=t,u=[...Be(s/2,-c/2,l,30,-90,0),{x:-s/2-l,y:l},...Be(s/2+l*2,-l,l,20,-180,-270),...Be(s/2+l*2,l,l,20,-90,-180),{x:-s/2-l,y:-c/2},...Be(s/2,c/2,l,20,0,90)],f=[{x:s/2,y:-c/2-l},{x:-s/2,y:-c/2-l},...Be(s/2,-c/2,l,20,-90,0),{x:-s/2-l,y:-l},...Be(s/2+s*.1,-l,l,20,-180,-270),...Be(s/2+s*.1,l,l,20,-90,-180),{x:-s/2-l,y:c/2},...Be(s/2,c/2,l,20,0,90),{x:-s/2,y:c/2+l},{x:s/2,y:c/2+l}],d=Y.svg(n),g=G(t,{fill:"none"});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const y=ct(u).replace("Z",""),x=d.path(y,g),b=ct(f),C=d.path(b,{...g}),v=n.insert("g",":first-child");return v.insert(()=>C,":first-child").attr("stroke-opacity",0),v.insert(()=>x,":first-child"),v.attr("class","text"),h&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),v.attr("transform",`translate(${l}, 0)`),o.attr("transform",`translate(${-s/2+l-(a.x-(a.left??0))},${-c/2+(t.padding??0)/2-(a.y-(a.top??0))})`),V(t,v),t.intersect=function(S){return H.polygon(t,f,S)},n}p(Cd,"curlyBraceLeft");function Ae(e,t,r,i=100,n=0,a=180){const o=[],s=n*Math.PI/180,h=(a*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const f=s+u*h,d=e+r*Math.cos(f),g=t+r*Math.sin(f);o.push({x:d,y:g})}return o}p(Ae,"generateCirclePoints");async function wd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=a.width+(t.padding??0),c=a.height+(t.padding??0),l=Math.max(5,c*.1),{cssStyles:h}=t,u=[...Ae(s/2,-c/2,l,20,-90,0),{x:s/2+l,y:-l},...Ae(s/2+l*2,-l,l,20,-180,-270),...Ae(s/2+l*2,l,l,20,-90,-180),{x:s/2+l,y:c/2},...Ae(s/2,c/2,l,20,0,90)],f=[{x:-s/2,y:-c/2-l},{x:s/2,y:-c/2-l},...Ae(s/2,-c/2,l,20,-90,0),{x:s/2+l,y:-l},...Ae(s/2+l*2,-l,l,20,-180,-270),...Ae(s/2+l*2,l,l,20,-90,-180),{x:s/2+l,y:c/2},...Ae(s/2,c/2,l,20,0,90),{x:s/2,y:c/2+l},{x:-s/2,y:c/2+l}],d=Y.svg(n),g=G(t,{fill:"none"});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const y=ct(u).replace("Z",""),x=d.path(y,g),b=ct(f),C=d.path(b,{...g}),v=n.insert("g",":first-child");return v.insert(()=>C,":first-child").attr("stroke-opacity",0),v.insert(()=>x,":first-child"),v.attr("class","text"),h&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),v.attr("transform",`translate(${-l}, 0)`),o.attr("transform",`translate(${-s/2+(t.padding??0)/2-(a.x-(a.left??0))},${-c/2+(t.padding??0)/2-(a.y-(a.top??0))})`),V(t,v),t.intersect=function(S){return H.polygon(t,f,S)},n}p(wd,"curlyBraceRight");function Bt(e,t,r,i=100,n=0,a=180){const o=[],s=n*Math.PI/180,h=(a*Math.PI/180-s)/(i-1);for(let u=0;u<i;u++){const f=s+u*h,d=e+r*Math.cos(f),g=t+r*Math.sin(f);o.push({x:-d,y:-g})}return o}p(Bt,"generateCirclePoints");async function kd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=a.width+(t.padding??0),c=a.height+(t.padding??0),l=Math.max(5,c*.1),{cssStyles:h}=t,u=[...Bt(s/2,-c/2,l,30,-90,0),{x:-s/2-l,y:l},...Bt(s/2+l*2,-l,l,20,-180,-270),...Bt(s/2+l*2,l,l,20,-90,-180),{x:-s/2-l,y:-c/2},...Bt(s/2,c/2,l,20,0,90)],f=[...Bt(-s/2+l+l/2,-c/2,l,20,-90,-180),{x:s/2-l/2,y:l},...Bt(-s/2-l/2,-l,l,20,0,90),...Bt(-s/2-l/2,l,l,20,-90,0),{x:s/2-l/2,y:-l},...Bt(-s/2+l+l/2,c/2,l,30,-180,-270)],d=[{x:s/2,y:-c/2-l},{x:-s/2,y:-c/2-l},...Bt(s/2,-c/2,l,20,-90,0),{x:-s/2-l,y:-l},...Bt(s/2+l*2,-l,l,20,-180,-270),...Bt(s/2+l*2,l,l,20,-90,-180),{x:-s/2-l,y:c/2},...Bt(s/2,c/2,l,20,0,90),{x:-s/2,y:c/2+l},{x:s/2-l-l/2,y:c/2+l},...Bt(-s/2+l+l/2,-c/2,l,20,-90,-180),{x:s/2-l/2,y:l},...Bt(-s/2-l/2,-l,l,20,0,90),...Bt(-s/2-l/2,l,l,20,-90,0),{x:s/2-l/2,y:-l},...Bt(-s/2+l+l/2,c/2,l,30,-180,-270)],g=Y.svg(n),m=G(t,{fill:"none"});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const x=ct(u).replace("Z",""),b=g.path(x,m),v=ct(f).replace("Z",""),S=g.path(v,m),_=ct(d),k=g.path(_,{...m}),R=n.insert("g",":first-child");return R.insert(()=>k,":first-child").attr("stroke-opacity",0),R.insert(()=>b,":first-child"),R.insert(()=>S,":first-child"),R.attr("class","text"),h&&t.look!=="handDrawn"&&R.selectAll("path").attr("style",h),i&&t.look!=="handDrawn"&&R.selectAll("path").attr("style",i),R.attr("transform",`translate(${l-l/4}, 0)`),o.attr("transform",`translate(${-s/2+(t.padding??0)/2-(a.x-(a.left??0))},${-c/2+(t.padding??0)/2-(a.y-(a.top??0))})`),V(t,R),t.intersect=function(P){return H.polygon(t,d,P)},n}p(kd,"curlyBraces");async function vd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=80,s=20,c=Math.max(o,(a.width+(t.padding??0)*2)*1.25,(t==null?void 0:t.width)??0),l=Math.max(s,a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=l/2,{cssStyles:u}=t,f=Y.svg(n),d=G(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=c,m=l,y=g-h,x=m/4,b=[{x:y,y:0},{x,y:0},{x:0,y:m/2},{x,y:m},{x:y,y:m},...Ao(-y,-m/2,h,50,270,90)],C=ct(b),v=f.path(C,d),S=n.insert(()=>v,":first-child");return S.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&S.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&S.selectChildren("path").attr("style",i),S.attr("transform",`translate(${-c/2}, ${-l/2})`),V(t,S),t.intersect=function(_){return H.polygon(t,b,_)},n}p(vd,"curvedTrapezoid");var V_=p((e,t,r,i,n,a)=>[`M${e},${t+a}`,`a${n},${a} 0,0,0 ${r},0`,`a${n},${a} 0,0,0 ${-r},0`,`l0,${i}`,`a${n},${a} 0,0,0 ${r},0`,`l0,${-i}`].join(" "),"createCylinderPathD"),X_=p((e,t,r,i,n,a)=>[`M${e},${t+a}`,`M${e+r},${t+a}`,`a${n},${a} 0,0,0 ${-r},0`,`l0,${i}`,`a${n},${a} 0,0,0 ${r},0`,`l0,${-i}`].join(" "),"createOuterCylinderPathD"),Z_=p((e,t,r,i,n,a)=>[`M${e-r/2},${-i/2}`,`a${n},${a} 0,0,0 ${r},0`].join(" "),"createInnerCylinderPathD");async function Sd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=Math.max(a.width+t.padding,t.width??0),c=s/2,l=c/(2.5+s/50),h=Math.max(a.height+l+t.padding,t.height??0);let u;const{cssStyles:f}=t;if(t.look==="handDrawn"){const d=Y.svg(n),g=X_(0,0,s,h,c,l),m=Z_(0,l,s,h,c,l),y=d.path(g,G(t,{})),x=d.path(m,G(t,{fill:"none"}));u=n.insert(()=>x,":first-child"),u=n.insert(()=>y,":first-child"),u.attr("class","basic label-container"),f&&u.attr("style",f)}else{const d=V_(0,0,s,h,c,l);u=n.insert("path",":first-child").attr("d",d).attr("class","basic label-container").attr("style",Wt(f)).attr("style",i)}return u.attr("label-offset-y",l),u.attr("transform",`translate(${-s/2}, ${-(h/2+l)})`),V(t,u),o.attr("transform",`translate(${-(a.width/2)-(a.x-(a.left??0))}, ${-(a.height/2)+(t.padding??0)/1.5-(a.y-(a.top??0))})`),t.intersect=function(d){const g=H.rect(t,d),m=g.x-(t.x??0);if(c!=0&&(Math.abs(m)<(t.width??0)/2||Math.abs(m)==(t.width??0)/2&&Math.abs(g.y-(t.y??0))>(t.height??0)/2-l)){let y=l*l*(1-m*m/(c*c));y>0&&(y=Math.sqrt(y)),y=l-y,d.y-(t.y??0)>0&&(y=-y),g.y+=y}return g},n}p(Sd,"cylinder");async function Td(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=a.width+t.padding,c=a.height+t.padding,l=c*.2,h=-s/2,u=-c/2-l/2,{cssStyles:f}=t,d=Y.svg(n),g=G(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=[{x:h,y:u+l},{x:-h,y:u+l},{x:-h,y:-u},{x:h,y:-u},{x:h,y:u},{x:-h,y:u},{x:-h,y:u+l}],y=d.polygon(m.map(b=>[b.x,b.y]),g),x=n.insert(()=>y,":first-child");return x.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",f),i&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),o.attr("transform",`translate(${h+(t.padding??0)/2-(a.x-(a.left??0))}, ${u+l+(t.padding??0)/2-(a.y-(a.top??0))})`),V(t,x),t.intersect=function(b){return H.rect(t,b)},n}p(Td,"dividedRectangle");async function Ld(e,t){var f,d;const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,halfPadding:o}=await st(e,t,it(t)),c=a.width/2+o+5,l=a.width/2+o;let h;const{cssStyles:u}=t;if(t.look==="handDrawn"){const g=Y.svg(n),m=G(t,{roughness:.2,strokeWidth:2.5}),y=G(t,{roughness:.2,strokeWidth:1.5}),x=g.circle(0,0,c*2,m),b=g.circle(0,0,l*2,y);h=n.insert("g",":first-child"),h.attr("class",Wt(t.cssClasses)).attr("style",Wt(u)),(f=h.node())==null||f.appendChild(x),(d=h.node())==null||d.appendChild(b)}else{h=n.insert("g",":first-child");const g=h.insert("circle",":first-child"),m=h.insert("circle");h.attr("class","basic label-container").attr("style",i),g.attr("class","outer-circle").attr("style",i).attr("r",c).attr("cx",0).attr("cy",0),m.attr("class","inner-circle").attr("style",i).attr("r",l).attr("cx",0).attr("cy",0)}return V(t,h),t.intersect=function(g){return D.info("DoubleCircle intersect",t,c,g),H.circle(t,c,g)},n}p(Ld,"doublecircle");function Bd(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:n}=X(t);t.label="",t.labelStyle=i;const a=e.insert("g").attr("class",it(t)).attr("id",t.domId??t.id),o=7,{cssStyles:s}=t,c=Y.svg(a),{nodeBorder:l}=r,h=G(t,{fillStyle:"solid"});t.look!=="handDrawn"&&(h.roughness=0);const u=c.circle(0,0,o*2,h),f=a.insert(()=>u,":first-child");return f.selectAll("path").attr("style",`fill: ${l} !important;`),s&&s.length>0&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",s),n&&t.look!=="handDrawn"&&f.selectAll("path").attr("style",n),V(t,f),t.intersect=function(d){return D.info("filledCircle intersect",t,{radius:o,point:d}),H.circle(t,o,d)},a}p(Bd,"filledCircle");async function Ad(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=a.width+(t.padding??0),c=s+a.height,l=s+a.height,h=[{x:0,y:-c},{x:l,y:-c},{x:l/2,y:0}],{cssStyles:u}=t,f=Y.svg(n),d=G(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=ct(h),m=f.path(g,d),y=n.insert(()=>m,":first-child").attr("transform",`translate(${-c/2}, ${c/2})`);return u&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",i),t.width=s,t.height=c,V(t,y),o.attr("transform",`translate(${-a.width/2-(a.x-(a.left??0))}, ${-c/2+(t.padding??0)/2+(a.y-(a.top??0))})`),t.intersect=function(x){return D.info("Triangle intersect",t,h,x),H.polygon(t,h,x)},n}p(Ad,"flippedTriangle");function Md(e,t,{dir:r,config:{state:i,themeVariables:n}}){const{nodeStyles:a}=X(t);t.label="";const o=e.insert("g").attr("class",it(t)).attr("id",t.domId??t.id),{cssStyles:s}=t;let c=Math.max(70,(t==null?void 0:t.width)??0),l=Math.max(10,(t==null?void 0:t.height)??0);r==="LR"&&(c=Math.max(10,(t==null?void 0:t.width)??0),l=Math.max(70,(t==null?void 0:t.height)??0));const h=-1*c/2,u=-1*l/2,f=Y.svg(o),d=G(t,{stroke:n.lineColor,fill:n.lineColor});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=f.rectangle(h,u,c,l,d),m=o.insert(()=>g,":first-child");s&&t.look!=="handDrawn"&&m.selectAll("path").attr("style",s),a&&t.look!=="handDrawn"&&m.selectAll("path").attr("style",a),V(t,m);const y=(i==null?void 0:i.padding)??0;return t.width&&t.height&&(t.width+=y/2||0,t.height+=y/2||0),t.intersect=function(x){return H.rect(t,x)},o}p(Md,"forkJoin");async function Ed(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const n=80,a=50,{shapeSvg:o,bbox:s}=await st(e,t,it(t)),c=Math.max(n,s.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(a,s.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=l/2,{cssStyles:u}=t,f=Y.svg(o),d=G(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=[{x:-c/2,y:-l/2},{x:c/2-h,y:-l/2},...Ao(-c/2+h,0,h,50,90,270),{x:c/2-h,y:l/2},{x:-c/2,y:l/2}],m=ct(g),y=f.path(m,d),x=o.insert(()=>y,":first-child");return x.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),V(t,x),t.intersect=function(b){return D.info("Pill intersect",t,{radius:h,point:b}),H.polygon(t,g,b)},o}p(Ed,"halfRoundedRectangle");var K_=p((e,t,r,i,n)=>[`M${e+n},${t}`,`L${e+r-n},${t}`,`L${e+r},${t-i/2}`,`L${e+r-n},${t-i}`,`L${e+n},${t-i}`,`L${e},${t-i/2}`,"Z"].join(" "),"createHexagonPathD");async function Fd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=4,s=a.height+t.padding,c=s/o,l=a.width+2*c+t.padding,h=[{x:c,y:0},{x:l-c,y:0},{x:l,y:-s/2},{x:l-c,y:-s},{x:c,y:-s},{x:0,y:-s/2}];let u;const{cssStyles:f}=t;if(t.look==="handDrawn"){const d=Y.svg(n),g=G(t,{}),m=K_(0,0,l,s,c),y=d.path(m,g);u=n.insert(()=>y,":first-child").attr("transform",`translate(${-l/2}, ${s/2})`),f&&u.attr("style",f)}else u=Ie(n,l,s,h);return i&&u.attr("style",i),t.width=l,t.height=s,V(t,u),t.intersect=function(d){return H.polygon(t,h,d)},n}p(Fd,"hexagon");async function $d(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.label="",t.labelStyle=r;const{shapeSvg:n}=await st(e,t,it(t)),a=Math.max(30,(t==null?void 0:t.width)??0),o=Math.max(30,(t==null?void 0:t.height)??0),{cssStyles:s}=t,c=Y.svg(n),l=G(t,{});t.look!=="handDrawn"&&(l.roughness=0,l.fillStyle="solid");const h=[{x:0,y:0},{x:a,y:0},{x:0,y:o},{x:a,y:o}],u=ct(h),f=c.path(u,l),d=n.insert(()=>f,":first-child");return d.attr("class","basic label-container"),s&&t.look!=="handDrawn"&&d.selectChildren("path").attr("style",s),i&&t.look!=="handDrawn"&&d.selectChildren("path").attr("style",i),d.attr("transform",`translate(${-a/2}, ${-o/2})`),V(t,d),t.intersect=function(g){return D.info("Pill intersect",t,{points:h}),H.polygon(t,h,g)},n}p($d,"hourglass");async function Dd(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:n}=X(t);t.labelStyle=n;const a=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(a,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,label:u}=await st(e,t,"icon-shape default"),f=t.pos==="t",d=s,g=s,{nodeBorder:m}=r,{stylesMap:y}=Ur(t),x=-g/2,b=-d/2,C=t.label?8:0,v=Y.svg(l),S=G(t,{stroke:"none",fill:"none"});t.look!=="handDrawn"&&(S.roughness=0,S.fillStyle="solid");const _=v.rectangle(x,b,g,d,S),k=Math.max(g,h.width),R=d+h.height+C,P=v.rectangle(-k/2,-R/2,k,R,{...S,fill:"transparent",stroke:"none"}),$=l.insert(()=>_,":first-child"),A=l.insert(()=>P);if(t.icon){const z=l.append("g");z.html(`<g>${await ta(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const O=z.node().getBBox(),E=O.width,M=O.height,L=O.x,F=O.y;z.attr("transform",`translate(${-E/2-L},${f?h.height/2+C/2-M/2-F:-h.height/2-C/2-M/2-F})`),z.attr("style",`color: ${y.get("stroke")??m};`)}return u.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${f?-R/2:R/2-h.height})`),$.attr("transform",`translate(0,${f?h.height/2+C/2:-h.height/2-C/2})`),V(t,A),t.intersect=function(z){if(D.info("iconSquare intersect",t,z),!t.label)return H.rect(t,z);const O=t.x??0,E=t.y??0,M=t.height??0;let L=[];return f?L=[{x:O-h.width/2,y:E-M/2},{x:O+h.width/2,y:E-M/2},{x:O+h.width/2,y:E-M/2+h.height+C},{x:O+g/2,y:E-M/2+h.height+C},{x:O+g/2,y:E+M/2},{x:O-g/2,y:E+M/2},{x:O-g/2,y:E-M/2+h.height+C},{x:O-h.width/2,y:E-M/2+h.height+C}]:L=[{x:O-g/2,y:E-M/2},{x:O+g/2,y:E-M/2},{x:O+g/2,y:E-M/2+d},{x:O+h.width/2,y:E-M/2+d},{x:O+h.width/2/2,y:E+M/2},{x:O-h.width/2,y:E+M/2},{x:O-h.width/2,y:E-M/2+d},{x:O-g/2,y:E-M/2+d}],H.polygon(t,L,z)},l}p(Dd,"icon");async function Rd(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:n}=X(t);t.labelStyle=n;const a=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(a,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,label:u}=await st(e,t,"icon-shape default"),f=20,d=t.label?8:0,g=t.pos==="t",{nodeBorder:m,mainBkg:y}=r,{stylesMap:x}=Ur(t),b=Y.svg(l),C=G(t,{});t.look!=="handDrawn"&&(C.roughness=0,C.fillStyle="solid");const v=x.get("fill");C.stroke=v??y;const S=l.append("g");t.icon&&S.html(`<g>${await ta(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const _=S.node().getBBox(),k=_.width,R=_.height,P=_.x,$=_.y,A=Math.max(k,R)*Math.SQRT2+f*2,z=b.circle(0,0,A,C),O=Math.max(A,h.width),E=A+h.height+d,M=b.rectangle(-O/2,-E/2,O,E,{...C,fill:"transparent",stroke:"none"}),L=l.insert(()=>z,":first-child"),F=l.insert(()=>M);return S.attr("transform",`translate(${-k/2-P},${g?h.height/2+d/2-R/2-$:-h.height/2-d/2-R/2-$})`),S.attr("style",`color: ${x.get("stroke")??m};`),u.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${g?-E/2:E/2-h.height})`),L.attr("transform",`translate(0,${g?h.height/2+d/2:-h.height/2-d/2})`),V(t,F),t.intersect=function(B){return D.info("iconSquare intersect",t,B),H.rect(t,B)},l}p(Rd,"iconCircle");async function Od(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:n}=X(t);t.labelStyle=n;const a=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(a,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,halfPadding:u,label:f}=await st(e,t,"icon-shape default"),d=t.pos==="t",g=s+u*2,m=s+u*2,{nodeBorder:y,mainBkg:x}=r,{stylesMap:b}=Ur(t),C=-m/2,v=-g/2,S=t.label?8:0,_=Y.svg(l),k=G(t,{});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const R=b.get("fill");k.stroke=R??x;const P=_.path(Oe(C,v,m,g,5),k),$=Math.max(m,h.width),A=g+h.height+S,z=_.rectangle(-$/2,-A/2,$,A,{...k,fill:"transparent",stroke:"none"}),O=l.insert(()=>P,":first-child").attr("class","icon-shape2"),E=l.insert(()=>z);if(t.icon){const M=l.append("g");M.html(`<g>${await ta(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const L=M.node().getBBox(),F=L.width,B=L.height,W=L.x,j=L.y;M.attr("transform",`translate(${-F/2-W},${d?h.height/2+S/2-B/2-j:-h.height/2-S/2-B/2-j})`),M.attr("style",`color: ${b.get("stroke")??y};`)}return f.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${d?-A/2:A/2-h.height})`),O.attr("transform",`translate(0,${d?h.height/2+S/2:-h.height/2-S/2})`),V(t,E),t.intersect=function(M){if(D.info("iconSquare intersect",t,M),!t.label)return H.rect(t,M);const L=t.x??0,F=t.y??0,B=t.height??0;let W=[];return d?W=[{x:L-h.width/2,y:F-B/2},{x:L+h.width/2,y:F-B/2},{x:L+h.width/2,y:F-B/2+h.height+S},{x:L+m/2,y:F-B/2+h.height+S},{x:L+m/2,y:F+B/2},{x:L-m/2,y:F+B/2},{x:L-m/2,y:F-B/2+h.height+S},{x:L-h.width/2,y:F-B/2+h.height+S}]:W=[{x:L-m/2,y:F-B/2},{x:L+m/2,y:F-B/2},{x:L+m/2,y:F-B/2+g},{x:L+h.width/2,y:F-B/2+g},{x:L+h.width/2/2,y:F+B/2},{x:L-h.width/2,y:F+B/2},{x:L-h.width/2,y:F-B/2+g},{x:L-m/2,y:F-B/2+g}],H.polygon(t,W,M)},l}p(Od,"iconRounded");async function Id(e,t,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:n}=X(t);t.labelStyle=n;const a=t.assetHeight??48,o=t.assetWidth??48,s=Math.max(a,o),c=i==null?void 0:i.wrappingWidth;t.width=Math.max(s,c??0);const{shapeSvg:l,bbox:h,halfPadding:u,label:f}=await st(e,t,"icon-shape default"),d=t.pos==="t",g=s+u*2,m=s+u*2,{nodeBorder:y,mainBkg:x}=r,{stylesMap:b}=Ur(t),C=-m/2,v=-g/2,S=t.label?8:0,_=Y.svg(l),k=G(t,{});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const R=b.get("fill");k.stroke=R??x;const P=_.path(Oe(C,v,m,g,.1),k),$=Math.max(m,h.width),A=g+h.height+S,z=_.rectangle(-$/2,-A/2,$,A,{...k,fill:"transparent",stroke:"none"}),O=l.insert(()=>P,":first-child"),E=l.insert(()=>z);if(t.icon){const M=l.append("g");M.html(`<g>${await ta(t.icon,{height:s,width:s,fallbackPrefix:""})}</g>`);const L=M.node().getBBox(),F=L.width,B=L.height,W=L.x,j=L.y;M.attr("transform",`translate(${-F/2-W},${d?h.height/2+S/2-B/2-j:-h.height/2-S/2-B/2-j})`),M.attr("style",`color: ${b.get("stroke")??y};`)}return f.attr("transform",`translate(${-h.width/2-(h.x-(h.left??0))},${d?-A/2:A/2-h.height})`),O.attr("transform",`translate(0,${d?h.height/2+S/2:-h.height/2-S/2})`),V(t,E),t.intersect=function(M){if(D.info("iconSquare intersect",t,M),!t.label)return H.rect(t,M);const L=t.x??0,F=t.y??0,B=t.height??0;let W=[];return d?W=[{x:L-h.width/2,y:F-B/2},{x:L+h.width/2,y:F-B/2},{x:L+h.width/2,y:F-B/2+h.height+S},{x:L+m/2,y:F-B/2+h.height+S},{x:L+m/2,y:F+B/2},{x:L-m/2,y:F+B/2},{x:L-m/2,y:F-B/2+h.height+S},{x:L-h.width/2,y:F-B/2+h.height+S}]:W=[{x:L-m/2,y:F-B/2},{x:L+m/2,y:F-B/2},{x:L+m/2,y:F-B/2+g},{x:L+h.width/2,y:F-B/2+g},{x:L+h.width/2/2,y:F+B/2},{x:L-h.width/2,y:F+B/2},{x:L-h.width/2,y:F-B/2+g},{x:L-m/2,y:F-B/2+g}],H.polygon(t,W,M)},l}p(Id,"iconSquare");async function Pd(e,t,{config:{flowchart:r}}){const i=new Image;i.src=(t==null?void 0:t.img)??"",await i.decode();const n=Number(i.naturalWidth.toString().replace("px","")),a=Number(i.naturalHeight.toString().replace("px",""));t.imageAspectRatio=n/a;const{labelStyles:o}=X(t);t.labelStyle=o;const s=r==null?void 0:r.wrappingWidth;t.defaultWidth=r==null?void 0:r.wrappingWidth;const c=Math.max(t.label?s??0:0,(t==null?void 0:t.assetWidth)??n),l=t.constraint==="on"&&t!=null&&t.assetHeight?t.assetHeight*t.imageAspectRatio:c,h=t.constraint==="on"?l/t.imageAspectRatio:(t==null?void 0:t.assetHeight)??a;t.width=Math.max(l,s??0);const{shapeSvg:u,bbox:f,label:d}=await st(e,t,"image-shape default"),g=t.pos==="t",m=-l/2,y=-h/2,x=t.label?8:0,b=Y.svg(u),C=G(t,{});t.look!=="handDrawn"&&(C.roughness=0,C.fillStyle="solid");const v=b.rectangle(m,y,l,h,C),S=Math.max(l,f.width),_=h+f.height+x,k=b.rectangle(-S/2,-_/2,S,_,{...C,fill:"none",stroke:"none"}),R=u.insert(()=>v,":first-child"),P=u.insert(()=>k);if(t.img){const $=u.append("image");$.attr("href",t.img),$.attr("width",l),$.attr("height",h),$.attr("preserveAspectRatio","none"),$.attr("transform",`translate(${-l/2},${g?_/2-h:-_/2})`)}return d.attr("transform",`translate(${-f.width/2-(f.x-(f.left??0))},${g?-h/2-f.height/2-x/2:h/2-f.height/2+x/2})`),R.attr("transform",`translate(0,${g?f.height/2+x/2:-f.height/2-x/2})`),V(t,P),t.intersect=function($){if(D.info("iconSquare intersect",t,$),!t.label)return H.rect(t,$);const A=t.x??0,z=t.y??0,O=t.height??0;let E=[];return g?E=[{x:A-f.width/2,y:z-O/2},{x:A+f.width/2,y:z-O/2},{x:A+f.width/2,y:z-O/2+f.height+x},{x:A+l/2,y:z-O/2+f.height+x},{x:A+l/2,y:z+O/2},{x:A-l/2,y:z+O/2},{x:A-l/2,y:z-O/2+f.height+x},{x:A-f.width/2,y:z-O/2+f.height+x}]:E=[{x:A-l/2,y:z-O/2},{x:A+l/2,y:z-O/2},{x:A+l/2,y:z-O/2+h},{x:A+f.width/2,y:z-O/2+h},{x:A+f.width/2/2,y:z+O/2},{x:A-f.width/2,y:z+O/2},{x:A-f.width/2,y:z-O/2+h},{x:A-l/2,y:z-O/2+h}],H.polygon(t,E,$)},u}p(Pd,"imageSquare");async function Nd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),s=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=[{x:0,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:-3*s/6,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=Y.svg(n),f=G(t,{}),d=ct(c),g=u.path(d,f);l=n.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=Ie(n,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,V(t,l),t.intersect=function(u){return H.polygon(t,c,u)},n}p(Nd,"inv_trapezoid");async function Ii(e,t,r){const{labelStyles:i,nodeStyles:n}=X(t);t.labelStyle=i;const{shapeSvg:a,bbox:o}=await st(e,t,it(t)),s=Math.max(o.width+r.labelPaddingX*2,(t==null?void 0:t.width)||0),c=Math.max(o.height+r.labelPaddingY*2,(t==null?void 0:t.height)||0),l=-s/2,h=-c/2;let u,{rx:f,ry:d}=t;const{cssStyles:g}=t;if(r!=null&&r.rx&&r.ry&&(f=r.rx,d=r.ry),t.look==="handDrawn"){const m=Y.svg(a),y=G(t,{}),x=f||d?m.path(Oe(l,h,s,c,f||0),y):m.rectangle(l,h,s,c,y);u=a.insert(()=>x,":first-child"),u.attr("class","basic label-container").attr("style",Wt(g))}else u=a.insert("rect",":first-child"),u.attr("class","basic label-container").attr("style",n).attr("rx",Wt(f)).attr("ry",Wt(d)).attr("x",l).attr("y",h).attr("width",s).attr("height",c);return V(t,u),t.intersect=function(m){return H.rect(t,m)},a}p(Ii,"drawRect");async function zd(e,t){const{shapeSvg:r,bbox:i,label:n}=await st(e,t,"label"),a=r.insert("rect",":first-child");return a.attr("width",.1).attr("height",.1),r.attr("class","label edgeLabel"),n.attr("transform",`translate(${-(i.width/2)-(i.x-(i.left??0))}, ${-(i.height/2)-(i.y-(i.top??0))})`),V(t,a),t.intersect=function(c){return H.rect(t,c)},r}p(zd,"labelRect");async function Wd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=Math.max(a.width+(t.padding??0),(t==null?void 0:t.width)??0),s=Math.max(a.height+(t.padding??0),(t==null?void 0:t.height)??0),c=[{x:0,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:-(3*s)/6,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=Y.svg(n),f=G(t,{}),d=ct(c),g=u.path(d,f);l=n.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=Ie(n,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,V(t,l),t.intersect=function(u){return H.polygon(t,c,u)},n}p(Wd,"lean_left");async function qd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=Math.max(a.width+(t.padding??0),(t==null?void 0:t.width)??0),s=Math.max(a.height+(t.padding??0),(t==null?void 0:t.height)??0),c=[{x:-3*s/6,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:0,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=Y.svg(n),f=G(t,{}),d=ct(c),g=u.path(d,f);l=n.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=Ie(n,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,V(t,l),t.intersect=function(u){return H.polygon(t,c,u)},n}p(qd,"lean_right");function Hd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.label="",t.labelStyle=r;const n=e.insert("g").attr("class",it(t)).attr("id",t.domId??t.id),{cssStyles:a}=t,o=Math.max(35,(t==null?void 0:t.width)??0),s=Math.max(35,(t==null?void 0:t.height)??0),c=7,l=[{x:o,y:0},{x:0,y:s+c/2},{x:o-2*c,y:s+c/2},{x:0,y:2*s},{x:o,y:s-c/2},{x:2*c,y:s-c/2}],h=Y.svg(n),u=G(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const f=ct(l),d=h.path(f,u),g=n.insert(()=>d,":first-child");return a&&t.look!=="handDrawn"&&g.selectAll("path").attr("style",a),i&&t.look!=="handDrawn"&&g.selectAll("path").attr("style",i),g.attr("transform",`translate(-${o/2},${-s})`),V(t,g),t.intersect=function(m){return D.info("lightningBolt intersect",t,m),H.polygon(t,l,m)},n}p(Hd,"lightningBolt");var Q_=p((e,t,r,i,n,a,o)=>[`M${e},${t+a}`,`a${n},${a} 0,0,0 ${r},0`,`a${n},${a} 0,0,0 ${-r},0`,`l0,${i}`,`a${n},${a} 0,0,0 ${r},0`,`l0,${-i}`,`M${e},${t+a+o}`,`a${n},${a} 0,0,0 ${r},0`].join(" "),"createCylinderPathD"),J_=p((e,t,r,i,n,a,o)=>[`M${e},${t+a}`,`M${e+r},${t+a}`,`a${n},${a} 0,0,0 ${-r},0`,`l0,${i}`,`a${n},${a} 0,0,0 ${r},0`,`l0,${-i}`,`M${e},${t+a+o}`,`a${n},${a} 0,0,0 ${r},0`].join(" "),"createOuterCylinderPathD"),tC=p((e,t,r,i,n,a)=>[`M${e-r/2},${-i/2}`,`a${n},${a} 0,0,0 ${r},0`].join(" "),"createInnerCylinderPathD");async function Yd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=Math.max(a.width+(t.padding??0),t.width??0),c=s/2,l=c/(2.5+s/50),h=Math.max(a.height+l+(t.padding??0),t.height??0),u=h*.1;let f;const{cssStyles:d}=t;if(t.look==="handDrawn"){const g=Y.svg(n),m=J_(0,0,s,h,c,l,u),y=tC(0,l,s,h,c,l),x=G(t,{}),b=g.path(m,x),C=g.path(y,x);n.insert(()=>C,":first-child").attr("class","line"),f=n.insert(()=>b,":first-child"),f.attr("class","basic label-container"),d&&f.attr("style",d)}else{const g=Q_(0,0,s,h,c,l,u);f=n.insert("path",":first-child").attr("d",g).attr("class","basic label-container").attr("style",Wt(d)).attr("style",i)}return f.attr("label-offset-y",l),f.attr("transform",`translate(${-s/2}, ${-(h/2+l)})`),V(t,f),o.attr("transform",`translate(${-(a.width/2)-(a.x-(a.left??0))}, ${-(a.height/2)+l-(a.y-(a.top??0))})`),t.intersect=function(g){const m=H.rect(t,g),y=m.x-(t.x??0);if(c!=0&&(Math.abs(y)<(t.width??0)/2||Math.abs(y)==(t.width??0)/2&&Math.abs(m.y-(t.y??0))>(t.height??0)/2-l)){let x=l*l*(1-y*y/(c*c));x>0&&(x=Math.sqrt(x)),x=l-x,g.y-(t.y??0)>0&&(x=-x),m.y+=x}return m},n}p(Yd,"linedCylinder");async function jd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/4,h=c+l,{cssStyles:u}=t,f=Y.svg(n),d=G(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=[{x:-s/2-s/2*.1,y:-h/2},{x:-s/2-s/2*.1,y:h/2},...je(-s/2-s/2*.1,h/2,s/2+s/2*.1,h/2,l,.8),{x:s/2+s/2*.1,y:-h/2},{x:-s/2-s/2*.1,y:-h/2},{x:-s/2,y:-h/2},{x:-s/2,y:h/2*1.1},{x:-s/2,y:-h/2}],m=f.polygon(g.map(x=>[x.x,x.y]),d),y=n.insert(()=>m,":first-child");return y.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",u),i&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",i),y.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)+s/2*.1/2-(a.x-(a.left??0))},${-c/2+(t.padding??0)-l/2-(a.y-(a.top??0))})`),V(t,y),t.intersect=function(x){return H.polygon(t,g,x)},n}p(jd,"linedWaveEdgedRect");async function Ud(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=5,h=-s/2,u=-c/2,{cssStyles:f}=t,d=Y.svg(n),g=G(t,{}),m=[{x:h-l,y:u+l},{x:h-l,y:u+c+l},{x:h+s-l,y:u+c+l},{x:h+s-l,y:u+c},{x:h+s,y:u+c},{x:h+s,y:u+c-l},{x:h+s+l,y:u+c-l},{x:h+s+l,y:u-l},{x:h+l,y:u-l},{x:h+l,y:u},{x:h,y:u},{x:h,y:u+l}],y=[{x:h,y:u+l},{x:h+s-l,y:u+l},{x:h+s-l,y:u+c},{x:h+s,y:u+c},{x:h+s,y:u},{x:h,y:u}];t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const x=ct(m),b=d.path(x,g),C=ct(y),v=d.path(C,{...g,fill:"none"}),S=n.insert(()=>v,":first-child");return S.insert(()=>b,":first-child"),S.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",f),i&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",i),o.attr("transform",`translate(${-(a.width/2)-l-(a.x-(a.left??0))}, ${-(a.height/2)+l-(a.y-(a.top??0))})`),V(t,S),t.intersect=function(_){return H.polygon(t,m,_)},n}p(Ud,"multiRect");async function Gd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/4,h=c+l,u=-s/2,f=-h/2,d=5,{cssStyles:g}=t,m=je(u-d,f+h+d,u+s-d,f+h+d,l,.8),y=m==null?void 0:m[m.length-1],x=[{x:u-d,y:f+d},{x:u-d,y:f+h+d},...m,{x:u+s-d,y:y.y-d},{x:u+s,y:y.y-d},{x:u+s,y:y.y-2*d},{x:u+s+d,y:y.y-2*d},{x:u+s+d,y:f-d},{x:u+d,y:f-d},{x:u+d,y:f},{x:u,y:f},{x:u,y:f+d}],b=[{x:u,y:f+d},{x:u+s-d,y:f+d},{x:u+s-d,y:y.y-d},{x:u+s,y:y.y-d},{x:u+s,y:f},{x:u,y:f}],C=Y.svg(n),v=G(t,{});t.look!=="handDrawn"&&(v.roughness=0,v.fillStyle="solid");const S=ct(x),_=C.path(S,v),k=ct(b),R=C.path(k,v),P=n.insert(()=>_,":first-child");return P.insert(()=>R),P.attr("class","basic label-container"),g&&t.look!=="handDrawn"&&P.selectAll("path").attr("style",g),i&&t.look!=="handDrawn"&&P.selectAll("path").attr("style",i),P.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-(a.width/2)-d-(a.x-(a.left??0))}, ${-(a.height/2)+d-l/2-(a.y-(a.top??0))})`),V(t,P),t.intersect=function($){return H.polygon(t,x,$)},n}p(Gd,"multiWaveEdgedRectangle");async function Vd(e,t,{config:{themeVariables:r}}){var x;const{labelStyles:i,nodeStyles:n}=X(t);t.labelStyle=i,t.useHtmlLabels||((x=Xt().flowchart)==null?void 0:x.htmlLabels)!==!1||(t.centerLabel=!0);const{shapeSvg:o,bbox:s}=await st(e,t,it(t)),c=Math.max(s.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(s.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=-c/2,u=-l/2,{cssStyles:f}=t,d=Y.svg(o),g=G(t,{fill:r.noteBkgColor,stroke:r.noteBorderColor});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=d.rectangle(h,u,c,l,g),y=o.insert(()=>m,":first-child");return y.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",f),n&&t.look!=="handDrawn"&&y.selectAll("path").attr("style",n),V(t,y),t.intersect=function(b){return H.rect(t,b)},o}p(Vd,"note");var eC=p((e,t,r)=>[`M${e+r/2},${t}`,`L${e+r},${t-r/2}`,`L${e+r/2},${t-r}`,`L${e},${t-r/2}`,"Z"].join(" "),"createDecisionBoxPathD");async function Xd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=a.width+t.padding,s=a.height+t.padding,c=o+s,l=[{x:c/2,y:0},{x:c,y:-c/2},{x:c/2,y:-c},{x:0,y:-c/2}];let h;const{cssStyles:u}=t;if(t.look==="handDrawn"){const f=Y.svg(n),d=G(t,{}),g=eC(0,0,c),m=f.path(g,d);h=n.insert(()=>m,":first-child").attr("transform",`translate(${-c/2}, ${c/2})`),u&&h.attr("style",u)}else h=Ie(n,c,c,l);return i&&h.attr("style",i),V(t,h),t.intersect=function(f){return D.debug(`APA12 Intersect called SPLIT
point:`,f,`
node:
`,t,`
res:`,H.polygon(t,l,f)),H.polygon(t,l,f)},n}p(Xd,"question");async function Zd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=Math.max(a.width+(t.padding??0),(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0),(t==null?void 0:t.height)??0),l=-s/2,h=-c/2,u=h/2,f=[{x:l+u,y:h},{x:l,y:0},{x:l+u,y:-h},{x:-l,y:-h},{x:-l,y:h}],{cssStyles:d}=t,g=Y.svg(n),m=G(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=ct(f),x=g.path(y,m),b=n.insert(()=>x,":first-child");return b.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",d),i&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),b.attr("transform",`translate(${-u/2},0)`),o.attr("transform",`translate(${-u/2-a.width/2-(a.x-(a.left??0))}, ${-(a.height/2)-(a.y-(a.top??0))})`),V(t,b),t.intersect=function(C){return H.polygon(t,f,C)},n}p(Zd,"rect_left_inv_arrow");async function Kd(e,t){var R,P;const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;let n;t.cssClasses?n="node "+t.cssClasses:n="node default";const a=e.insert("g").attr("class",n).attr("id",t.domId||t.id),o=a.insert("g"),s=a.insert("g").attr("class","label").attr("style",i),c=t.description,l=t.label,h=s.node().appendChild(await er(l,t.labelStyle,!0,!0));let u={width:0,height:0};if(kt((P=(R=ut())==null?void 0:R.flowchart)==null?void 0:P.htmlLabels)){const $=h.children[0],A=lt(h);u=$.getBoundingClientRect(),A.attr("width",u.width),A.attr("height",u.height)}D.info("Text 2",c);const f=c||[],d=h.getBBox(),g=s.node().appendChild(await er(f.join?f.join("<br/>"):f,t.labelStyle,!0,!0)),m=g.children[0],y=lt(g);u=m.getBoundingClientRect(),y.attr("width",u.width),y.attr("height",u.height);const x=(t.padding||0)/2;lt(g).attr("transform","translate( "+(u.width>d.width?0:(d.width-u.width)/2)+", "+(d.height+x+5)+")"),lt(h).attr("transform","translate( "+(u.width<d.width?0:-(d.width-u.width)/2)+", 0)"),u=s.node().getBBox(),s.attr("transform","translate("+-u.width/2+", "+(-u.height/2-x+3)+")");const b=u.width+(t.padding||0),C=u.height+(t.padding||0),v=-u.width/2-x,S=-u.height/2-x;let _,k;if(t.look==="handDrawn"){const $=Y.svg(a),A=G(t,{}),z=$.path(Oe(v,S,b,C,t.rx||0),A),O=$.line(-u.width/2-x,-u.height/2-x+d.height+x,u.width/2+x,-u.height/2-x+d.height+x,A);k=a.insert(()=>(D.debug("Rough node insert CXC",z),O),":first-child"),_=a.insert(()=>(D.debug("Rough node insert CXC",z),z),":first-child")}else _=o.insert("rect",":first-child"),k=o.insert("line"),_.attr("class","outer title-state").attr("style",i).attr("x",-u.width/2-x).attr("y",-u.height/2-x).attr("width",u.width+(t.padding||0)).attr("height",u.height+(t.padding||0)),k.attr("class","divider").attr("x1",-u.width/2-x).attr("x2",u.width/2+x).attr("y1",-u.height/2-x+d.height+x).attr("y2",-u.height/2-x+d.height+x);return V(t,_),t.intersect=function($){return H.rect(t,$)},a}p(Kd,"rectWithTitle");async function Qd(e,t){const r={rx:5,ry:5,classes:"",labelPaddingX:((t==null?void 0:t.padding)||0)*1,labelPaddingY:((t==null?void 0:t.padding)||0)*1};return Ii(e,t,r)}p(Qd,"roundedRect");async function Jd(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=(t==null?void 0:t.padding)??0,c=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=-a.width/2-s,u=-a.height/2-s,{cssStyles:f}=t,d=Y.svg(n),g=G(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=[{x:h,y:u},{x:h+c+8,y:u},{x:h+c+8,y:u+l},{x:h-8,y:u+l},{x:h-8,y:u},{x:h,y:u},{x:h,y:u+l}],y=d.polygon(m.map(b=>[b.x,b.y]),g),x=n.insert(()=>y,":first-child");return x.attr("class","basic label-container").attr("style",Wt(f)),i&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),f&&t.look!=="handDrawn"&&x.selectAll("path").attr("style",i),o.attr("transform",`translate(${-c/2+4+(t.padding??0)-(a.x-(a.left??0))},${-l/2+(t.padding??0)-(a.y-(a.top??0))})`),V(t,x),t.intersect=function(b){return H.rect(t,b)},n}p(Jd,"shadedProcess");async function tp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=-s/2,h=-c/2,{cssStyles:u}=t,f=Y.svg(n),d=G(t,{});t.look!=="handDrawn"&&(d.roughness=0,d.fillStyle="solid");const g=[{x:l,y:h},{x:l,y:h+c},{x:l+s,y:h+c},{x:l+s,y:h-c/2}],m=ct(g),y=f.path(m,d),x=n.insert(()=>y,":first-child");return x.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",u),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),x.attr("transform",`translate(0, ${c/4})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(a.x-(a.left??0))}, ${-c/4+(t.padding??0)-(a.y-(a.top??0))})`),V(t,x),t.intersect=function(b){return H.polygon(t,g,b)},n}p(tp,"slopedRect");async function ep(e,t){const r={rx:0,ry:0,classes:"",labelPaddingX:((t==null?void 0:t.padding)||0)*2,labelPaddingY:((t==null?void 0:t.padding)||0)*1};return Ii(e,t,r)}p(ep,"squareRect");async function rp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=a.height+t.padding,s=a.width+o/4+t.padding;let c;const{cssStyles:l}=t;if(t.look==="handDrawn"){const h=Y.svg(n),u=G(t,{}),f=Oe(-s/2,-o/2,s,o,o/2),d=h.path(f,u);c=n.insert(()=>d,":first-child"),c.attr("class","basic label-container").attr("style",Wt(l))}else c=n.insert("rect",":first-child"),c.attr("class","basic label-container").attr("style",i).attr("rx",o/2).attr("ry",o/2).attr("x",-s/2).attr("y",-o/2).attr("width",s).attr("height",o);return V(t,c),t.intersect=function(h){return H.rect(t,h)},n}p(rp,"stadium");async function ip(e,t){return Ii(e,t,{rx:5,ry:5,classes:"flowchart-node"})}p(ip,"state");function np(e,t,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:n}=X(t);t.labelStyle=i;const{cssStyles:a}=t,{lineColor:o,stateBorder:s,nodeBorder:c}=r,l=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),h=Y.svg(l),u=G(t,{});t.look!=="handDrawn"&&(u.roughness=0,u.fillStyle="solid");const f=h.circle(0,0,14,{...u,stroke:o,strokeWidth:2}),d=s??c,g=h.circle(0,0,5,{...u,fill:d,stroke:d,strokeWidth:2,fillStyle:"solid"}),m=l.insert(()=>f,":first-child");return m.insert(()=>g),a&&m.selectAll("path").attr("style",a),n&&m.selectAll("path").attr("style",n),V(t,m),t.intersect=function(y){return H.circle(t,7,y)},l}p(np,"stateEnd");function ap(e,t,{config:{themeVariables:r}}){const{lineColor:i}=r,n=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let a;if(t.look==="handDrawn"){const s=Y.svg(n).circle(0,0,14,R_(i));a=n.insert(()=>s),a.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14)}else a=n.insert("circle",":first-child"),a.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14);return V(t,a),t.intersect=function(o){return H.circle(t,7,o)},n}p(ap,"stateStart");async function sp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=((t==null?void 0:t.padding)||0)/2,s=a.width+t.padding,c=a.height+t.padding,l=-a.width/2-o,h=-a.height/2-o,u=[{x:0,y:0},{x:s,y:0},{x:s,y:-c},{x:0,y:-c},{x:0,y:0},{x:-8,y:0},{x:s+8,y:0},{x:s+8,y:-c},{x:-8,y:-c},{x:-8,y:0}];if(t.look==="handDrawn"){const f=Y.svg(n),d=G(t,{}),g=f.rectangle(l-8,h,s+16,c,d),m=f.line(l,h,l,h+c,d),y=f.line(l+s,h,l+s,h+c,d);n.insert(()=>m,":first-child"),n.insert(()=>y,":first-child");const x=n.insert(()=>g,":first-child"),{cssStyles:b}=t;x.attr("class","basic label-container").attr("style",Wt(b)),V(t,x)}else{const f=Ie(n,s,c,u);i&&f.attr("style",i),V(t,f)}return t.intersect=function(f){return H.polygon(t,u,f)},n}p(sp,"subroutine");async function op(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),s=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),c=-o/2,l=-s/2,h=.2*s,u=.2*s,{cssStyles:f}=t,d=Y.svg(n),g=G(t,{}),m=[{x:c-h/2,y:l},{x:c+o+h/2,y:l},{x:c+o+h/2,y:l+s},{x:c-h/2,y:l+s}],y=[{x:c+o-h/2,y:l+s},{x:c+o+h/2,y:l+s},{x:c+o+h/2,y:l+s-u}];t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const x=ct(m),b=d.path(x,g),C=ct(y),v=d.path(C,{...g,fillStyle:"solid"}),S=n.insert(()=>v,":first-child");return S.insert(()=>b,":first-child"),S.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",f),i&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",i),V(t,S),t.intersect=function(_){return H.polygon(t,m,_)},n}p(op,"taggedRect");async function lp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/4,h=.2*s,u=.2*c,f=c+l,{cssStyles:d}=t,g=Y.svg(n),m=G(t,{});t.look!=="handDrawn"&&(m.roughness=0,m.fillStyle="solid");const y=[{x:-s/2-s/2*.1,y:f/2},...je(-s/2-s/2*.1,f/2,s/2+s/2*.1,f/2,l,.8),{x:s/2+s/2*.1,y:-f/2},{x:-s/2-s/2*.1,y:-f/2}],x=-s/2+s/2*.1,b=-f/2-u*.4,C=[{x:x+s-h,y:(b+c)*1.4},{x:x+s,y:b+c-u},{x:x+s,y:(b+c)*.9},...je(x+s,(b+c)*1.3,x+s-h,(b+c)*1.5,-c*.03,.5)],v=ct(y),S=g.path(v,m),_=ct(C),k=g.path(_,{...m,fillStyle:"solid"}),R=n.insert(()=>k,":first-child");return R.insert(()=>S,":first-child"),R.attr("class","basic label-container"),d&&t.look!=="handDrawn"&&R.selectAll("path").attr("style",d),i&&t.look!=="handDrawn"&&R.selectAll("path").attr("style",i),R.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(a.x-(a.left??0))},${-c/2+(t.padding??0)-l/2-(a.y-(a.top??0))})`),V(t,R),t.intersect=function(P){return H.polygon(t,y,P)},n}p(lp,"taggedWaveEdgedRectangle");async function cp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=Math.max(a.width+t.padding,(t==null?void 0:t.width)||0),s=Math.max(a.height+t.padding,(t==null?void 0:t.height)||0),c=-o/2,l=-s/2,h=n.insert("rect",":first-child");return h.attr("class","text").attr("style",i).attr("rx",0).attr("ry",0).attr("x",c).attr("y",l).attr("width",o).attr("height",s),V(t,h),t.intersect=function(u){return H.rect(t,u)},n}p(cp,"text");var rC=p((e,t,r,i,n,a)=>`M${e},${t}
    a${n},${a} 0,0,1 0,${-i}
    l${r},0
    a${n},${a} 0,0,1 0,${i}
    M${r},${-i}
    a${n},${a} 0,0,0 0,${i}
    l${-r},0`,"createCylinderPathD"),iC=p((e,t,r,i,n,a)=>[`M${e},${t}`,`M${e+r},${t}`,`a${n},${a} 0,0,0 0,${-i}`,`l${-r},0`,`a${n},${a} 0,0,0 0,${i}`,`l${r},0`].join(" "),"createOuterCylinderPathD"),nC=p((e,t,r,i,n,a)=>[`M${e+r/2},${-i/2}`,`a${n},${a} 0,0,0 0,${i}`].join(" "),"createInnerCylinderPathD");async function hp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o,halfPadding:s}=await st(e,t,it(t)),c=t.look==="neo"?s*2:s,l=a.height+c,h=l/2,u=h/(2.5+l/50),f=a.width+u+c,{cssStyles:d}=t;let g;if(t.look==="handDrawn"){const m=Y.svg(n),y=iC(0,0,f,l,u,h),x=nC(0,0,f,l,u,h),b=m.path(y,G(t,{})),C=m.path(x,G(t,{fill:"none"}));g=n.insert(()=>C,":first-child"),g=n.insert(()=>b,":first-child"),g.attr("class","basic label-container"),d&&g.attr("style",d)}else{const m=rC(0,0,f,l,u,h);g=n.insert("path",":first-child").attr("d",m).attr("class","basic label-container").attr("style",Wt(d)).attr("style",i),g.attr("class","basic label-container"),d&&g.selectAll("path").attr("style",d),i&&g.selectAll("path").attr("style",i)}return g.attr("label-offset-x",u),g.attr("transform",`translate(${-f/2}, ${l/2} )`),o.attr("transform",`translate(${-(a.width/2)-u-(a.x-(a.left??0))}, ${-(a.height/2)-(a.y-(a.top??0))})`),V(t,g),t.intersect=function(m){const y=H.rect(t,m),x=y.y-(t.y??0);if(h!=0&&(Math.abs(x)<(t.height??0)/2||Math.abs(x)==(t.height??0)/2&&Math.abs(y.x-(t.x??0))>(t.width??0)/2-u)){let b=u*u*(1-x*x/(h*h));b!=0&&(b=Math.sqrt(Math.abs(b))),b=u-b,m.x-(t.x??0)>0&&(b=-b),y.x+=b}return y},n}p(hp,"tiltedCylinder");async function up(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=a.width+t.padding,s=a.height+t.padding,c=[{x:-3*s/6,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:0,y:-s}];let l;const{cssStyles:h}=t;if(t.look==="handDrawn"){const u=Y.svg(n),f=G(t,{}),d=ct(c),g=u.path(d,f);l=n.insert(()=>g,":first-child").attr("transform",`translate(${-o/2}, ${s/2})`),h&&l.attr("style",h)}else l=Ie(n,o,s,c);return i&&l.attr("style",i),t.width=o,t.height=s,V(t,l),t.intersect=function(u){return H.polygon(t,c,u)},n}p(up,"trapezoid");async function fp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=60,s=20,c=Math.max(o,a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(s,a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),{cssStyles:h}=t,u=Y.svg(n),f=G(t,{});t.look!=="handDrawn"&&(f.roughness=0,f.fillStyle="solid");const d=[{x:-c/2*.8,y:-l/2},{x:c/2*.8,y:-l/2},{x:c/2,y:-l/2*.6},{x:c/2,y:l/2},{x:-c/2,y:l/2},{x:-c/2,y:-l/2*.6}],g=ct(d),m=u.path(g,f),y=n.insert(()=>m,":first-child");return y.attr("class","basic label-container"),h&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",h),i&&t.look!=="handDrawn"&&y.selectChildren("path").attr("style",i),V(t,y),t.intersect=function(x){return H.polygon(t,d,x)},n}p(fp,"trapezoidalPentagon");async function dp(e,t){var b;const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=kt((b=ut().flowchart)==null?void 0:b.htmlLabels),c=a.width+(t.padding??0),l=c+a.height,h=c+a.height,u=[{x:0,y:0},{x:h,y:0},{x:h/2,y:-l}],{cssStyles:f}=t,d=Y.svg(n),g=G(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=ct(u),y=d.path(m,g),x=n.insert(()=>y,":first-child").attr("transform",`translate(${-l/2}, ${l/2})`);return f&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",f),i&&t.look!=="handDrawn"&&x.selectChildren("path").attr("style",i),t.width=c,t.height=l,V(t,x),o.attr("transform",`translate(${-a.width/2-(a.x-(a.left??0))}, ${l/2-(a.height+(t.padding??0)/(s?2:1)-(a.y-(a.top??0)))})`),t.intersect=function(C){return D.info("Triangle intersect",t,u,C),H.polygon(t,u,C)},n}p(dp,"triangle");async function pp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=c/8,h=c+l,{cssStyles:u}=t,d=70-s,g=d>0?d/2:0,m=Y.svg(n),y=G(t,{});t.look!=="handDrawn"&&(y.roughness=0,y.fillStyle="solid");const x=[{x:-s/2-g,y:h/2},...je(-s/2-g,h/2,s/2+g,h/2,l,.8),{x:s/2+g,y:-h/2},{x:-s/2-g,y:-h/2}],b=ct(x),C=m.path(b,y),v=n.insert(()=>C,":first-child");return v.attr("class","basic label-container"),u&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",u),i&&t.look!=="handDrawn"&&v.selectAll("path").attr("style",i),v.attr("transform",`translate(0,${-l/2})`),o.attr("transform",`translate(${-s/2+(t.padding??0)-(a.x-(a.left??0))},${-c/2+(t.padding??0)-l-(a.y-(a.top??0))})`),V(t,v),t.intersect=function(S){return H.polygon(t,x,S)},n}p(pp,"waveEdgedRectangle");async function gp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a}=await st(e,t,it(t)),o=100,s=50,c=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),l=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),h=c/l;let u=c,f=l;u>f*h?f=u/h:u=f*h,u=Math.max(u,o),f=Math.max(f,s);const d=Math.min(f*.2,f/4),g=f+d*2,{cssStyles:m}=t,y=Y.svg(n),x=G(t,{});t.look!=="handDrawn"&&(x.roughness=0,x.fillStyle="solid");const b=[{x:-u/2,y:g/2},...je(-u/2,g/2,u/2,g/2,d,1),{x:u/2,y:-g/2},...je(u/2,-g/2,-u/2,-g/2,d,-1)],C=ct(b),v=y.path(C,x),S=n.insert(()=>v,":first-child");return S.attr("class","basic label-container"),m&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",m),i&&t.look!=="handDrawn"&&S.selectAll("path").attr("style",i),V(t,S),t.intersect=function(_){return H.polygon(t,b,_)},n}p(gp,"waveRectangle");async function mp(e,t){const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const{shapeSvg:n,bbox:a,label:o}=await st(e,t,it(t)),s=Math.max(a.width+(t.padding??0)*2,(t==null?void 0:t.width)??0),c=Math.max(a.height+(t.padding??0)*2,(t==null?void 0:t.height)??0),l=5,h=-s/2,u=-c/2,{cssStyles:f}=t,d=Y.svg(n),g=G(t,{}),m=[{x:h-l,y:u-l},{x:h-l,y:u+c},{x:h+s,y:u+c},{x:h+s,y:u-l}],y=`M${h-l},${u-l} L${h+s},${u-l} L${h+s},${u+c} L${h-l},${u+c} L${h-l},${u-l}
                M${h-l},${u} L${h+s},${u}
                M${h},${u-l} L${h},${u+c}`;t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const x=d.path(y,g),b=n.insert(()=>x,":first-child");return b.attr("transform",`translate(${l/2}, ${l/2})`),b.attr("class","basic label-container"),f&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",f),i&&t.look!=="handDrawn"&&b.selectAll("path").attr("style",i),o.attr("transform",`translate(${-(a.width/2)+l/2-(a.x-(a.left??0))}, ${-(a.height/2)+l/2-(a.y-(a.top??0))})`),V(t,b),t.intersect=function(C){return H.polygon(t,m,C)},n}p(mp,"windowPane");async function Mo(e,t){var W,j,J;const r=t;if(r.alias&&(t.label=r.alias),t.look==="handDrawn"){const{themeVariables:tt}=Xt(),{background:et}=tt,ht={...t,id:t.id+"-background",look:"default",cssStyles:["stroke: none",`fill: ${et}`]};await Mo(e,ht)}const i=Xt();t.useHtmlLabels=i.htmlLabels;let n=((W=i.er)==null?void 0:W.diagramPadding)??10,a=((j=i.er)==null?void 0:j.entityPadding)??6;const{cssStyles:o}=t,{labelStyles:s}=X(t);if(r.attributes.length===0&&t.label){const tt={rx:0,ry:0,labelPaddingX:n,labelPaddingY:n*1.5,classes:""};Re(t.label,i)+tt.labelPaddingX*2<i.er.minEntityWidth&&(t.width=i.er.minEntityWidth);const et=await Ii(e,t,tt);if(!kt(i.htmlLabels)){const ht=et.select("text"),nt=(J=ht.node())==null?void 0:J.getBBox();ht.attr("transform",`translate(${-nt.width/2}, 0)`)}return et}i.htmlLabels||(n*=1.25,a*=1.25);let c=it(t);c||(c="node default");const l=e.insert("g").attr("class",c).attr("id",t.domId||t.id),h=await _r(l,t.label??"",i,0,0,["name"],s);h.height+=a;let u=0;const f=[];let d=0,g=0,m=0,y=0,x=!0,b=!0;for(const tt of r.attributes){const et=await _r(l,tt.type,i,0,u,["attribute-type"],s);d=Math.max(d,et.width+n);const ht=await _r(l,tt.name,i,0,u,["attribute-name"],s);g=Math.max(g,ht.width+n);const nt=await _r(l,tt.keys.join(),i,0,u,["attribute-keys"],s);m=Math.max(m,nt.width+n);const vt=await _r(l,tt.comment,i,0,u,["attribute-comment"],s);y=Math.max(y,vt.width+n),u+=Math.max(et.height,ht.height,nt.height,vt.height)+a,f.push(u)}f.pop();let C=4;m<=n&&(x=!1,m=0,C--),y<=n&&(b=!1,y=0,C--);const v=l.node().getBBox();if(h.width+n*2-(d+g+m+y)>0){const tt=h.width+n*2-(d+g+m+y);d+=tt/C,g+=tt/C,m>0&&(m+=tt/C),y>0&&(y+=tt/C)}const S=d+g+m+y,_=Y.svg(l),k=G(t,{});t.look!=="handDrawn"&&(k.roughness=0,k.fillStyle="solid");const R=Math.max(v.width+n*2,(t==null?void 0:t.width)||0,S),P=Math.max(v.height+(f[0]||u)+a,(t==null?void 0:t.height)||0),$=-R/2,A=-P/2;l.selectAll("g:not(:first-child)").each((tt,et,ht)=>{const nt=lt(ht[et]),vt=nt.attr("transform");let Lt=0,ne=0;if(vt){const xt=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(vt);xt&&(Lt=parseFloat(xt[1]),ne=parseFloat(xt[2]),nt.attr("class").includes("attribute-name")?Lt+=d:nt.attr("class").includes("attribute-keys")?Lt+=d+g:nt.attr("class").includes("attribute-comment")&&(Lt+=d+g+m))}nt.attr("transform",`translate(${$+n/2+Lt}, ${ne+A+h.height+a/2})`)}),l.select(".name").attr("transform","translate("+-h.width/2+", "+(A+a/2)+")");const z=_.rectangle($,A,R,P,k),O=l.insert(()=>z,":first-child").attr("style",o.join("")),{themeVariables:E}=Xt(),{rowEven:M,rowOdd:L,nodeBorder:F}=E;f.push(0);for(const[tt,et]of f.entries()){if(tt===0&&f.length>1)continue;const ht=tt%2===0&&et!==0,nt=_.rectangle($,h.height+A+et,R,h.height,{...k,fill:ht?M:L,stroke:F});l.insert(()=>nt,"g.label").attr("style",o.join("")).attr("class",`row-rect-${tt%2===0?"even":"odd"}`)}let B=_.line($,h.height+A,R+$,h.height+A,k);l.insert(()=>B).attr("class","divider"),B=_.line(d+$,h.height+A,d+$,P+A,k),l.insert(()=>B).attr("class","divider"),x&&(B=_.line(d+g+$,h.height+A,d+g+$,P+A,k),l.insert(()=>B).attr("class","divider")),b&&(B=_.line(d+g+m+$,h.height+A,d+g+m+$,P+A,k),l.insert(()=>B).attr("class","divider"));for(const tt of f)B=_.line($,h.height+A+tt,R+$,h.height+A+tt,k),l.insert(()=>B).attr("class","divider");return V(t,O),t.intersect=function(tt){return H.rect(t,tt)},l}p(Mo,"erBox");async function _r(e,t,r,i=0,n=0,a=[],o=""){const s=e.insert("g").attr("class",`label ${a.join(" ")}`).attr("transform",`translate(${i}, ${n})`).attr("style",o);t!==fl(t)&&(t=fl(t),t=t.replaceAll("<","&lt;").replaceAll(">","&gt;"));const c=s.node().appendChild(await Ue(s,t,{width:Re(t,r)+100,style:o,useHtmlLabels:r.htmlLabels},r));if(t.includes("&lt;")||t.includes("&gt;")){let h=c.children[0];for(h.textContent=h.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">");h.childNodes[0];)h=h.childNodes[0],h.textContent=h.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">")}let l=c.getBBox();if(kt(r.htmlLabels)){const h=c.children[0];h.style.textAlign="start";const u=lt(c);l=h.getBoundingClientRect(),u.attr("width",l.width),u.attr("height",l.height)}return l}p(_r,"addText");async function yp(e,t,r,i,n=r.class.padding??12){const a=i?0:3,o=e.insert("g").attr("class",it(t)).attr("id",t.domId||t.id);let s=null,c=null,l=null,h=null,u=0,f=0,d=0;if(s=o.insert("g").attr("class","annotation-group text"),t.annotations.length>0){const b=t.annotations[0];await di(s,{text:`«${b}»`},0),u=s.node().getBBox().height}c=o.insert("g").attr("class","label-group text"),await di(c,t,0,["font-weight: bolder"]);const g=c.node().getBBox();f=g.height,l=o.insert("g").attr("class","members-group text");let m=0;for(const b of t.members){const C=await di(l,b,m,[b.parseClassifier()]);m+=C+a}d=l.node().getBBox().height,d<=0&&(d=n/2),h=o.insert("g").attr("class","methods-group text");let y=0;for(const b of t.methods){const C=await di(h,b,y,[b.parseClassifier()]);y+=C+a}let x=o.node().getBBox();if(s!==null){const b=s.node().getBBox();s.attr("transform",`translate(${-b.width/2})`)}return c.attr("transform",`translate(${-g.width/2}, ${u})`),x=o.node().getBBox(),l.attr("transform",`translate(0, ${u+f+n*2})`),x=o.node().getBBox(),h.attr("transform",`translate(0, ${u+f+(d?d+n*4:n*2)})`),x=o.node().getBBox(),{shapeSvg:o,bbox:x}}p(yp,"textHelper");async function di(e,t,r,i=[]){const n=e.insert("g").attr("class","label").attr("style",i.join("; ")),a=Xt();let o="useHtmlLabels"in t?t.useHtmlLabels:kt(a.htmlLabels)??!0,s="";"text"in t?s=t.text:s=t.label,!o&&s.startsWith("\\")&&(s=s.substring(1)),Ir(s)&&(o=!0);const c=await Ue(n,qs(cr(s)),{width:Re(s,a)+50,classes:"markdown-node-label",useHtmlLabels:o},a);let l,h=1;if(o){const u=c.children[0],f=lt(c);h=u.innerHTML.split("<br>").length,u.innerHTML.includes("</math>")&&(h+=u.innerHTML.split("<mrow>").length-1);const d=u.getElementsByTagName("img");if(d){const g=s.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...d].map(m=>new Promise(y=>{function x(){var b;if(m.style.display="flex",m.style.flexDirection="column",g){const C=((b=a.fontSize)==null?void 0:b.toString())??window.getComputedStyle(document.body).fontSize,S=parseInt(C,10)*5+"px";m.style.minWidth=S,m.style.maxWidth=S}else m.style.width="100%";y(m)}p(x,"setupImage"),setTimeout(()=>{m.complete&&x()}),m.addEventListener("error",x),m.addEventListener("load",x)})))}l=u.getBoundingClientRect(),f.attr("width",l.width),f.attr("height",l.height)}else{i.includes("font-weight: bolder")&&lt(c).selectAll("tspan").attr("font-weight",""),h=c.children.length;const u=c.children[0];(c.textContent===""||c.textContent.includes("&gt"))&&(u.textContent=s[0]+s.substring(1).replaceAll("&gt;",">").replaceAll("&lt;","<").trim(),s[1]===" "&&(u.textContent=u.textContent[0]+" "+u.textContent.substring(1))),u.textContent==="undefined"&&(u.textContent=""),l=c.getBBox()}return n.attr("transform","translate(0,"+(-l.height/(2*h)+r)+")"),l.height}p(di,"addText");async function xp(e,t){var P,$;const r=ut(),i=r.class.padding??12,n=i,a=t.useHtmlLabels??kt(r.htmlLabels)??!0,o=t;o.annotations=o.annotations??[],o.members=o.members??[],o.methods=o.methods??[];const{shapeSvg:s,bbox:c}=await yp(e,t,r,a,n),{labelStyles:l,nodeStyles:h}=X(t);t.labelStyle=l,t.cssStyles=o.styles||"";const u=((P=o.styles)==null?void 0:P.join(";"))||h||"";t.cssStyles||(t.cssStyles=u.replaceAll("!important","").split(";"));const f=o.members.length===0&&o.methods.length===0&&!(($=r.class)!=null&&$.hideEmptyMembersBox),d=Y.svg(s),g=G(t,{});t.look!=="handDrawn"&&(g.roughness=0,g.fillStyle="solid");const m=c.width;let y=c.height;o.members.length===0&&o.methods.length===0?y+=n:o.members.length>0&&o.methods.length===0&&(y+=n*2);const x=-m/2,b=-y/2,C=d.rectangle(x-i,b-i-(f?i:o.members.length===0&&o.methods.length===0?-i/2:0),m+2*i,y+2*i+(f?i*2:o.members.length===0&&o.methods.length===0?-i:0),g),v=s.insert(()=>C,":first-child");v.attr("class","basic label-container");const S=v.node().getBBox();s.selectAll(".text").each((A,z,O)=>{var W;const E=lt(O[z]),M=E.attr("transform");let L=0;if(M){const J=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(M);J&&(L=parseFloat(J[2]))}let F=L+b+i-(f?i:o.members.length===0&&o.methods.length===0?-i/2:0);a||(F-=4);let B=x;(E.attr("class").includes("label-group")||E.attr("class").includes("annotation-group"))&&(B=-((W=E.node())==null?void 0:W.getBBox().width)/2||0,s.selectAll("text").each(function(j,J,tt){window.getComputedStyle(tt[J]).textAnchor==="middle"&&(B=0)})),E.attr("transform",`translate(${B}, ${F})`)});const _=s.select(".annotation-group").node().getBBox().height-(f?i/2:0)||0,k=s.select(".label-group").node().getBBox().height-(f?i/2:0)||0,R=s.select(".members-group").node().getBBox().height-(f?i/2:0)||0;if(o.members.length>0||o.methods.length>0||f){const A=d.line(S.x,_+k+b+i,S.x+S.width,_+k+b+i,g);s.insert(()=>A).attr("class","divider").attr("style",u)}if(f||o.members.length>0||o.methods.length>0){const A=d.line(S.x,_+k+R+b+n*2+i,S.x+S.width,_+k+R+b+i+n*2,g);s.insert(()=>A).attr("class","divider").attr("style",u)}if(o.look!=="handDrawn"&&s.selectAll("path").attr("style",u),v.select(":nth-child(2)").attr("style",u),s.selectAll(".divider").select("path").attr("style",u),t.labelStyle?s.selectAll("span").attr("style",t.labelStyle):s.selectAll("span").attr("style",u),!a){const A=RegExp(/color\s*:\s*([^;]*)/),z=A.exec(u);if(z){const O=z[0].replace("color","fill");s.selectAll("tspan").attr("style",O)}else if(l){const O=A.exec(l);if(O){const E=O[0].replace("color","fill");s.selectAll("tspan").attr("style",E)}}}return V(t,v),t.intersect=function(A){return H.rect(t,A)},s}p(xp,"classBox");async function bp(e,t){var _,k;const{labelStyles:r,nodeStyles:i}=X(t);t.labelStyle=r;const n=t,a=t,o=20,s=20,c="verifyMethod"in t,l=it(t),h=e.insert("g").attr("class",l).attr("id",t.domId??t.id);let u;c?u=await de(h,`&lt;&lt;${n.type}&gt;&gt;`,0,t.labelStyle):u=await de(h,"&lt;&lt;Element&gt;&gt;",0,t.labelStyle);let f=u;const d=await de(h,n.name,f,t.labelStyle+"; font-weight: bold;");if(f+=d+s,c){const R=await de(h,`${n.requirementId?`Id: ${n.requirementId}`:""}`,f,t.labelStyle);f+=R;const P=await de(h,`${n.text?`Text: ${n.text}`:""}`,f,t.labelStyle);f+=P;const $=await de(h,`${n.risk?`Risk: ${n.risk}`:""}`,f,t.labelStyle);f+=$,await de(h,`${n.verifyMethod?`Verification: ${n.verifyMethod}`:""}`,f,t.labelStyle)}else{const R=await de(h,`${a.type?`Type: ${a.type}`:""}`,f,t.labelStyle);f+=R,await de(h,`${a.docRef?`Doc Ref: ${a.docRef}`:""}`,f,t.labelStyle)}const g=(((_=h.node())==null?void 0:_.getBBox().width)??200)+o,m=(((k=h.node())==null?void 0:k.getBBox().height)??200)+o,y=-g/2,x=-m/2,b=Y.svg(h),C=G(t,{});t.look!=="handDrawn"&&(C.roughness=0,C.fillStyle="solid");const v=b.rectangle(y,x,g,m,C),S=h.insert(()=>v,":first-child");if(S.attr("class","basic label-container").attr("style",i),h.selectAll(".label").each((R,P,$)=>{const A=lt($[P]),z=A.attr("transform");let O=0,E=0;if(z){const B=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(z);B&&(O=parseFloat(B[1]),E=parseFloat(B[2]))}const M=E-m/2;let L=y+o/2;(P===0||P===1)&&(L=O),A.attr("transform",`translate(${L}, ${M+o})`)}),f>u+d+s){const R=b.line(y,x+u+d+s,y+g,x+u+d+s,C);h.insert(()=>R).attr("style",i)}return V(t,S),t.intersect=function(R){return H.rect(t,R)},h}p(bp,"requirementBox");async function de(e,t,r,i=""){if(t==="")return 0;const n=e.insert("g").attr("class","label").attr("style",i),a=ut(),o=a.htmlLabels??!0,s=await Ue(n,qs(cr(t)),{width:Re(t,a)+50,classes:"markdown-node-label",useHtmlLabels:o,style:i},a);let c;if(o){const l=s.children[0],h=lt(s);c=l.getBoundingClientRect(),h.attr("width",c.width),h.attr("height",c.height)}else{const l=s.children[0];for(const h of l.children)h.textContent=h.textContent.replaceAll("&gt;",">").replaceAll("&lt;","<"),i&&h.setAttribute("style",i);c=s.getBBox(),c.height+=6}return n.attr("transform",`translate(${-c.width/2},${-c.height/2+r})`),c.height}p(de,"addText");var aC=p(e=>{switch(e){case"Very High":return"red";case"High":return"orange";case"Medium":return null;case"Low":return"blue";case"Very Low":return"lightblue"}},"colorFromPriority");async function _p(e,t,{config:r}){var z,O;const{labelStyles:i,nodeStyles:n}=X(t);t.labelStyle=i||"";const a=10,o=t.width;t.width=(t.width??200)-10;const{shapeSvg:s,bbox:c,label:l}=await st(e,t,it(t)),h=t.padding||10;let u="",f;"ticket"in t&&t.ticket&&((z=r==null?void 0:r.kanban)!=null&&z.ticketBaseUrl)&&(u=(O=r==null?void 0:r.kanban)==null?void 0:O.ticketBaseUrl.replace("#TICKET#",t.ticket),f=s.insert("svg:a",":first-child").attr("class","kanban-ticket-link").attr("xlink:href",u).attr("target","_blank"));const d={useHtmlLabels:t.useHtmlLabels,labelStyle:t.labelStyle||"",width:t.width,img:t.img,padding:t.padding||8,centerLabel:!1};let g,m;f?{label:g,bbox:m}=await za(f,"ticket"in t&&t.ticket||"",d):{label:g,bbox:m}=await za(s,"ticket"in t&&t.ticket||"",d);const{label:y,bbox:x}=await za(s,"assigned"in t&&t.assigned||"",d);t.width=o;const b=10,C=(t==null?void 0:t.width)||0,v=Math.max(m.height,x.height)/2,S=Math.max(c.height+b*2,(t==null?void 0:t.height)||0)+v,_=-C/2,k=-S/2;l.attr("transform","translate("+(h-C/2)+", "+(-v-c.height/2)+")"),g.attr("transform","translate("+(h-C/2)+", "+(-v+c.height/2)+")"),y.attr("transform","translate("+(h+C/2-x.width-2*a)+", "+(-v+c.height/2)+")");let R;const{rx:P,ry:$}=t,{cssStyles:A}=t;if(t.look==="handDrawn"){const E=Y.svg(s),M=G(t,{}),L=P||$?E.path(Oe(_,k,C,S,P||0),M):E.rectangle(_,k,C,S,M);R=s.insert(()=>L,":first-child"),R.attr("class","basic label-container").attr("style",A||null)}else{R=s.insert("rect",":first-child"),R.attr("class","basic label-container __APA__").attr("style",n).attr("rx",P??5).attr("ry",$??5).attr("x",_).attr("y",k).attr("width",C).attr("height",S);const E="priority"in t&&t.priority;if(E){const M=s.append("line"),L=_+2,F=k+Math.floor((P??0)/2),B=k+S-Math.floor((P??0)/2);M.attr("x1",L).attr("y1",F).attr("x2",L).attr("y2",B).attr("stroke-width","4").attr("stroke",aC(E))}}return V(t,R),t.height=S,t.intersect=function(E){return H.rect(t,E)},s}p(_p,"kanbanItem");var sC=[{semanticName:"Process",name:"Rectangle",shortName:"rect",description:"Standard process shape",aliases:["proc","process","rectangle"],internalAliases:["squareRect"],handler:ep},{semanticName:"Event",name:"Rounded Rectangle",shortName:"rounded",description:"Represents an event",aliases:["event"],internalAliases:["roundedRect"],handler:Qd},{semanticName:"Terminal Point",name:"Stadium",shortName:"stadium",description:"Terminal point",aliases:["terminal","pill"],handler:rp},{semanticName:"Subprocess",name:"Framed Rectangle",shortName:"fr-rect",description:"Subprocess",aliases:["subprocess","subproc","framed-rectangle","subroutine"],handler:sp},{semanticName:"Database",name:"Cylinder",shortName:"cyl",description:"Database storage",aliases:["db","database","cylinder"],handler:Sd},{semanticName:"Start",name:"Circle",shortName:"circle",description:"Starting point",aliases:["circ"],handler:xd},{semanticName:"Decision",name:"Diamond",shortName:"diam",description:"Decision-making step",aliases:["decision","diamond","question"],handler:Xd},{semanticName:"Prepare Conditional",name:"Hexagon",shortName:"hex",description:"Preparation or condition step",aliases:["hexagon","prepare"],handler:Fd},{semanticName:"Data Input/Output",name:"Lean Right",shortName:"lean-r",description:"Represents input or output",aliases:["lean-right","in-out"],internalAliases:["lean_right"],handler:qd},{semanticName:"Data Input/Output",name:"Lean Left",shortName:"lean-l",description:"Represents output or input",aliases:["lean-left","out-in"],internalAliases:["lean_left"],handler:Wd},{semanticName:"Priority Action",name:"Trapezoid Base Bottom",shortName:"trap-b",description:"Priority action",aliases:["priority","trapezoid-bottom","trapezoid"],handler:up},{semanticName:"Manual Operation",name:"Trapezoid Base Top",shortName:"trap-t",description:"Represents a manual task",aliases:["manual","trapezoid-top","inv-trapezoid"],internalAliases:["inv_trapezoid"],handler:Nd},{semanticName:"Stop",name:"Double Circle",shortName:"dbl-circ",description:"Represents a stop point",aliases:["double-circle"],internalAliases:["doublecircle"],handler:Ld},{semanticName:"Text Block",name:"Text Block",shortName:"text",description:"Text block",handler:cp},{semanticName:"Card",name:"Notched Rectangle",shortName:"notch-rect",description:"Represents a card",aliases:["card","notched-rectangle"],handler:md},{semanticName:"Lined/Shaded Process",name:"Lined Rectangle",shortName:"lin-rect",description:"Lined process shape",aliases:["lined-rectangle","lined-process","lin-proc","shaded-process"],handler:Jd},{semanticName:"Start",name:"Small Circle",shortName:"sm-circ",description:"Small starting point",aliases:["start","small-circle"],internalAliases:["stateStart"],handler:ap},{semanticName:"Stop",name:"Framed Circle",shortName:"fr-circ",description:"Stop point",aliases:["stop","framed-circle"],internalAliases:["stateEnd"],handler:np},{semanticName:"Fork/Join",name:"Filled Rectangle",shortName:"fork",description:"Fork or join in process flow",aliases:["join"],internalAliases:["forkJoin"],handler:Md},{semanticName:"Collate",name:"Hourglass",shortName:"hourglass",description:"Represents a collate operation",aliases:["hourglass","collate"],handler:$d},{semanticName:"Comment",name:"Curly Brace",shortName:"brace",description:"Adds a comment",aliases:["comment","brace-l"],handler:Cd},{semanticName:"Comment Right",name:"Curly Brace",shortName:"brace-r",description:"Adds a comment",handler:wd},{semanticName:"Comment with braces on both sides",name:"Curly Braces",shortName:"braces",description:"Adds a comment",handler:kd},{semanticName:"Com Link",name:"Lightning Bolt",shortName:"bolt",description:"Communication link",aliases:["com-link","lightning-bolt"],handler:Hd},{semanticName:"Document",name:"Document",shortName:"doc",description:"Represents a document",aliases:["doc","document"],handler:pp},{semanticName:"Delay",name:"Half-Rounded Rectangle",shortName:"delay",description:"Represents a delay",aliases:["half-rounded-rectangle"],handler:Ed},{semanticName:"Direct Access Storage",name:"Horizontal Cylinder",shortName:"h-cyl",description:"Direct access storage",aliases:["das","horizontal-cylinder"],handler:hp},{semanticName:"Disk Storage",name:"Lined Cylinder",shortName:"lin-cyl",description:"Disk storage",aliases:["disk","lined-cylinder"],handler:Yd},{semanticName:"Display",name:"Curved Trapezoid",shortName:"curv-trap",description:"Represents a display",aliases:["curved-trapezoid","display"],handler:vd},{semanticName:"Divided Process",name:"Divided Rectangle",shortName:"div-rect",description:"Divided process shape",aliases:["div-proc","divided-rectangle","divided-process"],handler:Td},{semanticName:"Extract",name:"Triangle",shortName:"tri",description:"Extraction process",aliases:["extract","triangle"],handler:dp},{semanticName:"Internal Storage",name:"Window Pane",shortName:"win-pane",description:"Internal storage",aliases:["internal-storage","window-pane"],handler:mp},{semanticName:"Junction",name:"Filled Circle",shortName:"f-circ",description:"Junction point",aliases:["junction","filled-circle"],handler:Bd},{semanticName:"Loop Limit",name:"Trapezoidal Pentagon",shortName:"notch-pent",description:"Loop limit step",aliases:["loop-limit","notched-pentagon"],handler:fp},{semanticName:"Manual File",name:"Flipped Triangle",shortName:"flip-tri",description:"Manual file operation",aliases:["manual-file","flipped-triangle"],handler:Ad},{semanticName:"Manual Input",name:"Sloped Rectangle",shortName:"sl-rect",description:"Manual input step",aliases:["manual-input","sloped-rectangle"],handler:tp},{semanticName:"Multi-Document",name:"Stacked Document",shortName:"docs",description:"Multiple documents",aliases:["documents","st-doc","stacked-document"],handler:Gd},{semanticName:"Multi-Process",name:"Stacked Rectangle",shortName:"st-rect",description:"Multiple processes",aliases:["procs","processes","stacked-rectangle"],handler:Ud},{semanticName:"Stored Data",name:"Bow Tie Rectangle",shortName:"bow-rect",description:"Stored data",aliases:["stored-data","bow-tie-rectangle"],handler:gd},{semanticName:"Summary",name:"Crossed Circle",shortName:"cross-circ",description:"Summary",aliases:["summary","crossed-circle"],handler:_d},{semanticName:"Tagged Document",name:"Tagged Document",shortName:"tag-doc",description:"Tagged document",aliases:["tag-doc","tagged-document"],handler:lp},{semanticName:"Tagged Process",name:"Tagged Rectangle",shortName:"tag-rect",description:"Tagged process",aliases:["tagged-rectangle","tag-proc","tagged-process"],handler:op},{semanticName:"Paper Tape",name:"Flag",shortName:"flag",description:"Paper tape",aliases:["paper-tape"],handler:gp},{semanticName:"Odd",name:"Odd",shortName:"odd",description:"Odd shape",internalAliases:["rect_left_inv_arrow"],handler:Zd},{semanticName:"Lined Document",name:"Lined Document",shortName:"lin-doc",description:"Lined document",aliases:["lined-document"],handler:jd}],oC=p(()=>{const t=[...Object.entries({state:ip,choice:yd,note:Vd,rectWithTitle:Kd,labelRect:zd,iconSquare:Id,iconCircle:Rd,icon:Dd,iconRounded:Od,imageSquare:Pd,anchor:pd,kanbanItem:_p,classBox:xp,erBox:Mo,requirementBox:bp}),...sC.flatMap(r=>[r.shortName,..."aliases"in r?r.aliases:[],..."internalAliases"in r?r.internalAliases:[]].map(n=>[n,r.handler]))];return Object.fromEntries(t)},"generateShapeMap"),Cp=oC();function lC(e){return e in Cp}p(lC,"isValidShape");var fa=new Map;async function wp(e,t,r){let i,n;t.shape==="rect"&&(t.rx&&t.ry?t.shape="roundedRect":t.shape="squareRect");const a=t.shape?Cp[t.shape]:void 0;if(!a)throw new Error(`No such shape: ${t.shape}. Please check your syntax.`);if(t.link){let o;r.config.securityLevel==="sandbox"?o="_top":t.linkTarget&&(o=t.linkTarget||"_blank"),i=e.insert("svg:a").attr("xlink:href",t.link).attr("target",o??null),n=await a(i,t,r)}else n=await a(e,t,r),i=n;return t.tooltip&&n.attr("title",t.tooltip),fa.set(t.id,i),t.haveCallback&&i.attr("class",i.attr("class")+" clickable"),i}p(wp,"insertNode");var Jv=p((e,t)=>{fa.set(t.id,e)},"setNodeElem"),tS=p(()=>{fa.clear()},"clear"),eS=p(e=>{const t=fa.get(e.id);D.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const r=8,i=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+i-e.width/2)+", "+(e.y-e.height/2-r)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),i},"positionNode"),cC=p((e,t,r,i,n,a)=>{t.arrowTypeStart&&nc(e,"start",t.arrowTypeStart,r,i,n,a),t.arrowTypeEnd&&nc(e,"end",t.arrowTypeEnd,r,i,n,a)},"addEdgeMarkers"),hC={arrow_cross:{type:"cross",fill:!1},arrow_point:{type:"point",fill:!0},arrow_barb:{type:"barb",fill:!0},arrow_circle:{type:"circle",fill:!1},aggregation:{type:"aggregation",fill:!1},extension:{type:"extension",fill:!1},composition:{type:"composition",fill:!0},dependency:{type:"dependency",fill:!0},lollipop:{type:"lollipop",fill:!1},only_one:{type:"onlyOne",fill:!1},zero_or_one:{type:"zeroOrOne",fill:!1},one_or_more:{type:"oneOrMore",fill:!1},zero_or_more:{type:"zeroOrMore",fill:!1},requirement_arrow:{type:"requirement_arrow",fill:!1},requirement_contains:{type:"requirement_contains",fill:!1}},nc=p((e,t,r,i,n,a,o)=>{var u;const s=hC[r];if(!s){D.warn(`Unknown arrow type: ${r}`);return}const c=s.type,h=`${n}_${a}-${c}${t==="start"?"Start":"End"}`;if(o&&o.trim()!==""){const f=o.replace(/[^\dA-Za-z]/g,"_"),d=`${h}_${f}`;if(!document.getElementById(d)){const g=document.getElementById(h);if(g){const m=g.cloneNode(!0);m.id=d,m.querySelectorAll("path, circle, line").forEach(x=>{x.setAttribute("stroke",o),s.fill&&x.setAttribute("fill",o)}),(u=g.parentNode)==null||u.appendChild(m)}}e.attr(`marker-${t}`,`url(${i}#${d})`)}else e.attr(`marker-${t}`,`url(${i}#${h})`)},"addEdgeMarker"),Un=new Map,At=new Map,rS=p(()=>{Un.clear(),At.clear()},"clear"),oi=p(e=>e?e.reduce((r,i)=>r+";"+i,""):"","getLabelStyles"),uC=p(async(e,t)=>{let r=kt(ut().flowchart.htmlLabels);const i=await Ue(e,t.label,{style:oi(t.labelStyle),useHtmlLabels:r,addSvgBackground:!0,isNode:!1});D.info("abc82",t,t.labelType);const n=e.insert("g").attr("class","edgeLabel"),a=n.insert("g").attr("class","label");a.node().appendChild(i);let o=i.getBBox();if(r){const c=i.children[0],l=lt(i);o=c.getBoundingClientRect(),l.attr("width",o.width),l.attr("height",o.height)}a.attr("transform","translate("+-o.width/2+", "+-o.height/2+")"),Un.set(t.id,n),t.width=o.width,t.height=o.height;let s;if(t.startLabelLeft){const c=await er(t.startLabelLeft,oi(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),At.get(t.id)||At.set(t.id,{}),At.get(t.id).startLeft=l,pi(s,t.startLabelLeft)}if(t.startLabelRight){const c=await er(t.startLabelRight,oi(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=l.node().appendChild(c),h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),At.get(t.id)||At.set(t.id,{}),At.get(t.id).startRight=l,pi(s,t.startLabelRight)}if(t.endLabelLeft){const c=await er(t.endLabelLeft,oi(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),l.node().appendChild(c),At.get(t.id)||At.set(t.id,{}),At.get(t.id).endLeft=l,pi(s,t.endLabelLeft)}if(t.endLabelRight){const c=await er(t.endLabelRight,oi(t.labelStyle)),l=e.insert("g").attr("class","edgeTerminals"),h=l.insert("g").attr("class","inner");s=h.node().appendChild(c);const u=c.getBBox();h.attr("transform","translate("+-u.width/2+", "+-u.height/2+")"),l.node().appendChild(c),At.get(t.id)||At.set(t.id,{}),At.get(t.id).endRight=l,pi(s,t.endLabelRight)}return i},"insertEdgeLabel");function pi(e,t){ut().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}p(pi,"setTerminalWidth");var fC=p((e,t)=>{D.debug("Moving label abc88 ",e.id,e.label,Un.get(e.id),t);let r=t.updatedPath?t.updatedPath:t.originalPath;const i=ut(),{subGraphTitleTotalMargin:n}=Zs(i);if(e.label){const a=Un.get(e.id);let o=e.x,s=e.y;if(r){const c=ge.calcLabelPosition(r);D.debug("Moving label "+e.label+" from (",o,",",s,") to (",c.x,",",c.y,") abc88"),t.updatedPath&&(o=c.x,s=c.y)}a.attr("transform",`translate(${o}, ${s+n/2})`)}if(e.startLabelLeft){const a=At.get(e.id).startLeft;let o=e.x,s=e.y;if(r){const c=ge.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",r);o=c.x,s=c.y}a.attr("transform",`translate(${o}, ${s})`)}if(e.startLabelRight){const a=At.get(e.id).startRight;let o=e.x,s=e.y;if(r){const c=ge.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",r);o=c.x,s=c.y}a.attr("transform",`translate(${o}, ${s})`)}if(e.endLabelLeft){const a=At.get(e.id).endLeft;let o=e.x,s=e.y;if(r){const c=ge.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",r);o=c.x,s=c.y}a.attr("transform",`translate(${o}, ${s})`)}if(e.endLabelRight){const a=At.get(e.id).endRight;let o=e.x,s=e.y;if(r){const c=ge.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",r);o=c.x,s=c.y}a.attr("transform",`translate(${o}, ${s})`)}},"positionEdgeLabel"),dC=p((e,t)=>{const r=e.x,i=e.y,n=Math.abs(t.x-r),a=Math.abs(t.y-i),o=e.width/2,s=e.height/2;return n>=o||a>=s},"outsideNode"),pC=p((e,t,r)=>{D.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(r)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const i=e.x,n=e.y,a=Math.abs(i-r.x),o=e.width/2;let s=r.x<t.x?o-a:o+a;const c=e.height/2,l=Math.abs(t.y-r.y),h=Math.abs(t.x-r.x);if(Math.abs(n-t.y)*o>Math.abs(i-t.x)*c){let u=r.y<t.y?t.y-c-n:n-c-t.y;s=h*u/l;const f={x:r.x<t.x?r.x+s:r.x-h+s,y:r.y<t.y?r.y+l-u:r.y-l+u};return s===0&&(f.x=t.x,f.y=t.y),h===0&&(f.x=t.x),l===0&&(f.y=t.y),D.debug(`abc89 top/bottom calc, Q ${l}, q ${u}, R ${h}, r ${s}`,f),f}else{r.x<t.x?s=t.x-o-i:s=i-o-t.x;let u=l*s/h,f=r.x<t.x?r.x+h-s:r.x-h+s,d=r.y<t.y?r.y+u:r.y-u;return D.debug(`sides calc abc89, Q ${l}, q ${u}, R ${h}, r ${s}`,{_x:f,_y:d}),s===0&&(f=t.x,d=t.y),h===0&&(f=t.x),l===0&&(d=t.y),{x:f,y:d}}},"intersection"),ac=p((e,t)=>{D.warn("abc88 cutPathAtIntersect",e,t);let r=[],i=e[0],n=!1;return e.forEach(a=>{if(D.info("abc88 checking point",a,t),!dC(t,a)&&!n){const o=pC(t,i,a);D.debug("abc88 inside",a,i,o),D.debug("abc88 intersection",o,t);let s=!1;r.forEach(c=>{s=s||c.x===o.x&&c.y===o.y}),r.some(c=>c.x===o.x&&c.y===o.y)?D.warn("abc88 no intersect",o,r):r.push(o),n=!0}else D.warn("abc88 outside",a,i),i=a,n||r.push(a)}),D.debug("returning points",r),r},"cutPathAtIntersect");function kp(e){const t=[],r=[];for(let i=1;i<e.length-1;i++){const n=e[i-1],a=e[i],o=e[i+1];(n.x===a.x&&a.y===o.y&&Math.abs(a.x-o.x)>5&&Math.abs(a.y-n.y)>5||n.y===a.y&&a.x===o.x&&Math.abs(a.x-n.x)>5&&Math.abs(a.y-o.y)>5)&&(t.push(a),r.push(i))}return{cornerPoints:t,cornerPointPositions:r}}p(kp,"extractCornerPoints");var sc=p(function(e,t,r){const i=t.x-e.x,n=t.y-e.y,a=Math.sqrt(i*i+n*n),o=r/a;return{x:t.x-o*i,y:t.y-o*n}},"findAdjacentPoint"),gC=p(function(e){const{cornerPointPositions:t}=kp(e),r=[];for(let i=0;i<e.length;i++)if(t.includes(i)){const n=e[i-1],a=e[i+1],o=e[i],s=sc(n,o,5),c=sc(a,o,5),l=c.x-s.x,h=c.y-s.y;r.push(s);const u=Math.sqrt(2)*2;let f={x:o.x,y:o.y};if(Math.abs(a.x-n.x)>10&&Math.abs(a.y-n.y)>=10){D.debug("Corner point fixing",Math.abs(a.x-n.x),Math.abs(a.y-n.y));const d=5;o.x===s.x?f={x:l<0?s.x-d+u:s.x+d-u,y:h<0?s.y-u:s.y+u}:f={x:l<0?s.x-u:s.x+u,y:h<0?s.y-d+u:s.y+d-u}}else D.debug("Corner point skipping fixing",Math.abs(a.x-n.x),Math.abs(a.y-n.y));r.push(f,c)}else r.push(e[i]);return r},"fixCorners"),mC=p(function(e,t,r,i,n,a,o){var P;const{handDrawnSeed:s}=ut();let c=t.points,l=!1;const h=n;var u=a;const f=[];for(const $ in t.cssCompiledStyles)ad($)||f.push(t.cssCompiledStyles[$]);u.intersect&&h.intersect&&(c=c.slice(1,t.points.length-1),c.unshift(h.intersect(c[0])),D.debug("Last point APA12",t.start,"-->",t.end,c[c.length-1],u,u.intersect(c[c.length-1])),c.push(u.intersect(c[c.length-1]))),t.toCluster&&(D.info("to cluster abc88",r.get(t.toCluster)),c=ac(t.points,r.get(t.toCluster).node),l=!0),t.fromCluster&&(D.debug("from cluster abc88",r.get(t.fromCluster),JSON.stringify(c,null,2)),c=ac(c.reverse(),r.get(t.fromCluster).node).reverse(),l=!0);let d=c.filter($=>!Number.isNaN($.y));d=gC(d);let g=hn;switch(g=Dn,t.curve){case"linear":g=Dn;break;case"basis":g=hn;break;case"cardinal":g=sf;break;case"bumpX":g=tf;break;case"bumpY":g=ef;break;case"catmullRom":g=lf;break;case"monotoneX":g=pf;break;case"monotoneY":g=gf;break;case"natural":g=yf;break;case"step":g=xf;break;case"stepAfter":g=_f;break;case"stepBefore":g=bf;break;default:g=hn}const{x:m,y}=fy(t),x=o2().x(m).y(y).curve(g);let b;switch(t.thickness){case"normal":b="edge-thickness-normal";break;case"thick":b="edge-thickness-thick";break;case"invisible":b="edge-thickness-invisible";break;default:b="edge-thickness-normal"}switch(t.pattern){case"solid":b+=" edge-pattern-solid";break;case"dotted":b+=" edge-pattern-dotted";break;case"dashed":b+=" edge-pattern-dashed";break;default:b+=" edge-pattern-solid"}let C,v=x(d);const S=Array.isArray(t.style)?t.style:[t.style];let _=S.find($=>$==null?void 0:$.startsWith("stroke:"));if(t.look==="handDrawn"){const $=Y.svg(e);Object.assign([],d);const A=$.path(v,{roughness:.3,seed:s});b+=" transition",C=lt(A).select("path").attr("id",t.id).attr("class"," "+b+(t.classes?" "+t.classes:"")).attr("style",S?S.reduce((O,E)=>O+";"+E,""):"");let z=C.attr("d");C.attr("d",z),e.node().appendChild(C.node())}else{const $=f.join(";"),A=S?S.reduce((E,M)=>E+M+";",""):"";let z="";t.animate&&(z=" edge-animation-fast"),t.animation&&(z=" edge-animation-"+t.animation);const O=$?$+";"+A+";":A;C=e.append("path").attr("d",v).attr("id",t.id).attr("class"," "+b+(t.classes?" "+t.classes:"")+(z??"")).attr("style",O),_=(P=O.match(/stroke:([^;]+)/))==null?void 0:P[1]}let k="";(ut().flowchart.arrowMarkerAbsolute||ut().state.arrowMarkerAbsolute)&&(k=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,k=k.replace(/\(/g,"\\(").replace(/\)/g,"\\)")),D.info("arrowTypeStart",t.arrowTypeStart),D.info("arrowTypeEnd",t.arrowTypeEnd),cC(C,t,k,o,i,_);let R={};return l&&(R.updatedPath=c),R.originalPath=t.points,R},"insertEdge"),yC=p((e,t,r,i)=>{t.forEach(n=>{$C[n](e,r,i)})},"insertMarkers"),xC=p((e,t,r)=>{D.trace("Making markers for ",r),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),bC=p((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),_C=p((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),CC=p((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),wC=p((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),kC=p((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),vC=p((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),SC=p((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),TC=p((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","userSpaceOnUse").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),LC=p((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-onlyOneStart").attr("class","marker onlyOne "+t).attr("refX",0).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M9,0 L9,18 M15,0 L15,18"),e.append("defs").append("marker").attr("id",r+"_"+t+"-onlyOneEnd").attr("class","marker onlyOne "+t).attr("refX",18).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M3,0 L3,18 M9,0 L9,18")},"only_one"),BC=p((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrOneStart").attr("class","marker zeroOrOne "+t).attr("refX",0).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",21).attr("cy",9).attr("r",6),i.append("path").attr("d","M9,0 L9,18");const n=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrOneEnd").attr("class","marker zeroOrOne "+t).attr("refX",30).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");n.append("circle").attr("fill","white").attr("cx",9).attr("cy",9).attr("r",6),n.append("path").attr("d","M21,0 L21,18")},"zero_or_one"),AC=p((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-oneOrMoreStart").attr("class","marker oneOrMore "+t).attr("refX",18).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"),e.append("defs").append("marker").attr("id",r+"_"+t+"-oneOrMoreEnd").attr("class","marker oneOrMore "+t).attr("refX",27).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18")},"one_or_more"),MC=p((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrMoreStart").attr("class","marker zeroOrMore "+t).attr("refX",18).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",48).attr("cy",18).attr("r",6),i.append("path").attr("d","M0,18 Q18,0 36,18 Q18,36 0,18");const n=e.append("defs").append("marker").attr("id",r+"_"+t+"-zeroOrMoreEnd").attr("class","marker zeroOrMore "+t).attr("refX",39).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");n.append("circle").attr("fill","white").attr("cx",9).attr("cy",18).attr("r",6),n.append("path").attr("d","M21,18 Q39,0 57,18 Q39,36 21,18")},"zero_or_more"),EC=p((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-requirement_arrowEnd").attr("refX",20).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("path").attr("d",`M0,0
      L20,10
      M20,10
      L0,20`)},"requirement_arrow"),FC=p((e,t,r)=>{const i=e.append("defs").append("marker").attr("id",r+"_"+t+"-requirement_containsStart").attr("refX",0).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("g");i.append("circle").attr("cx",10).attr("cy",10).attr("r",9).attr("fill","none"),i.append("line").attr("x1",1).attr("x2",19).attr("y1",10).attr("y2",10),i.append("line").attr("y1",1).attr("y2",19).attr("x1",10).attr("x2",10)},"requirement_contains"),$C={extension:xC,composition:bC,aggregation:_C,dependency:CC,lollipop:wC,point:kC,circle:vC,cross:SC,barb:TC,only_one:LC,zero_or_one:BC,one_or_more:AC,zero_or_more:MC,requirement_arrow:EC,requirement_contains:FC},DC=yC,RC={common:Yr,getConfig:Xt,insertCluster:H_,insertEdge:mC,insertEdgeLabel:uC,insertMarkers:DC,insertNode:wp,interpolateToCurve:ho,labelHelper:st,log:D,positionEdgeLabel:fC},Bi={},vp=p(e=>{for(const t of e)Bi[t.name]=t},"registerLayoutLoaders"),OC=p(()=>{vp([{name:"dagre",loader:p(async()=>await pt(()=>import("./dagre-OKDRZEBW-BaeKrtYa.js"),__vite__mapDeps([3,4,1,5,6,0,2,7,8,9,10,11,12])),"loader")}])},"registerDefaultLayoutLoaders");OC();var iS=p(async(e,t)=>{if(!(e.layoutAlgorithm in Bi))throw new Error(`Unknown layout algorithm: ${e.layoutAlgorithm}`);const r=Bi[e.layoutAlgorithm];return(await r.loader()).render(e,t,RC,{algorithm:r.algorithm})},"render"),nS=p((e="",{fallback:t="dagre"}={})=>{if(e in Bi)return e;if(t in Bi)return D.warn(`Layout algorithm ${e} is not registered. Using ${t} as fallback.`),t;throw new Error(`Both layout algorithms ${e} and ${t} are not registered.`)},"getRegisteredLayoutAlgorithm"),oc={name:"mermaid",version:"11.6.0",description:"Markdown-ish syntax for generating flowcharts, mindmaps, sequence diagrams, class diagrams, gantt charts, git graphs and more.",type:"module",module:"./dist/mermaid.core.mjs",types:"./dist/mermaid.d.ts",exports:{".":{types:"./dist/mermaid.d.ts",import:"./dist/mermaid.core.mjs",default:"./dist/mermaid.core.mjs"},"./*":"./*"},keywords:["diagram","markdown","flowchart","sequence diagram","gantt","class diagram","git graph","mindmap","packet diagram","c4 diagram","er diagram","pie chart","pie diagram","quadrant chart","requirement diagram","graph"],scripts:{clean:"rimraf dist",dev:"pnpm -w dev","docs:code":"typedoc src/defaultConfig.ts src/config.ts src/mermaid.ts && prettier --write ./src/docs/config/setup","docs:build":"rimraf ../../docs && pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts","docs:verify":"pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts --verify","docs:pre:vitepress":"pnpm --filter ./src/docs prefetch && rimraf src/vitepress && pnpm docs:code && tsx scripts/docs.cli.mts --vitepress && pnpm --filter ./src/vitepress install --no-frozen-lockfile --ignore-scripts","docs:build:vitepress":"pnpm docs:pre:vitepress && (cd src/vitepress && pnpm run build) && cpy --flat src/docs/landing/ ./src/vitepress/.vitepress/dist/landing","docs:dev":'pnpm docs:pre:vitepress && concurrently "pnpm --filter ./src/vitepress dev" "tsx scripts/docs.cli.mts --watch --vitepress"',"docs:dev:docker":'pnpm docs:pre:vitepress && concurrently "pnpm --filter ./src/vitepress dev:docker" "tsx scripts/docs.cli.mts --watch --vitepress"',"docs:serve":"pnpm docs:build:vitepress && vitepress serve src/vitepress","docs:spellcheck":'cspell "src/docs/**/*.md"',"docs:release-version":"tsx scripts/update-release-version.mts","docs:verify-version":"tsx scripts/update-release-version.mts --verify","types:build-config":"tsx scripts/create-types-from-json-schema.mts","types:verify-config":"tsx scripts/create-types-from-json-schema.mts --verify",checkCircle:"npx madge --circular ./src",prepublishOnly:"pnpm docs:verify-version"},repository:{type:"git",url:"https://github.com/mermaid-js/mermaid"},author:"Knut Sveidqvist",license:"MIT",standard:{ignore:["**/parser/*.js","dist/**/*.js","cypress/**/*.js"],globals:["page"]},dependencies:{"@braintree/sanitize-url":"^7.0.4","@iconify/utils":"^2.1.33","@mermaid-js/parser":"workspace:^","@types/d3":"^7.4.3",cytoscape:"^3.29.3","cytoscape-cose-bilkent":"^4.1.0","cytoscape-fcose":"^2.2.0",d3:"^7.9.0","d3-sankey":"^0.12.3","dagre-d3-es":"7.0.11",dayjs:"^1.11.13",dompurify:"^3.2.4",katex:"^0.16.9",khroma:"^2.1.0","lodash-es":"^4.17.21",marked:"^15.0.7",roughjs:"^4.6.6",stylis:"^4.3.6","ts-dedent":"^2.2.0",uuid:"^11.1.0"},devDependencies:{"@adobe/jsonschema2md":"^8.0.2","@iconify/types":"^2.0.0","@types/cytoscape":"^3.21.9","@types/cytoscape-fcose":"^2.2.4","@types/d3-sankey":"^0.12.4","@types/d3-scale":"^4.0.9","@types/d3-scale-chromatic":"^3.1.0","@types/d3-selection":"^3.0.11","@types/d3-shape":"^3.1.7","@types/jsdom":"^21.1.7","@types/katex":"^0.16.7","@types/lodash-es":"^4.17.12","@types/micromatch":"^4.0.9","@types/stylis":"^4.2.7","@types/uuid":"^10.0.0",ajv:"^8.17.1",chokidar:"^4.0.3",concurrently:"^9.1.2","csstree-validator":"^4.0.1",globby:"^14.0.2",jison:"^0.4.18","js-base64":"^3.7.7",jsdom:"^26.0.0","json-schema-to-typescript":"^15.0.4",micromatch:"^4.0.8","path-browserify":"^1.0.1",prettier:"^3.5.2",remark:"^15.0.1","remark-frontmatter":"^5.0.0","remark-gfm":"^4.0.1",rimraf:"^6.0.1","start-server-and-test":"^2.0.10","type-fest":"^4.35.0",typedoc:"^0.27.8","typedoc-plugin-markdown":"^4.4.2",typescript:"~5.7.3","unist-util-flatmap":"^1.0.0","unist-util-visit":"^5.0.0",vitepress:"^1.0.2","vitepress-plugin-search":"1.0.4-alpha.22"},files:["dist/","README.md"],publishConfig:{access:"public"}},IC=p(e=>{var n;const{securityLevel:t}=ut();let r=lt("body");if(t==="sandbox"){const o=((n=lt(`#i${e}`).node())==null?void 0:n.contentDocument)??document;r=lt(o.body)}return r.select(`#${e}`)},"selectSvgElement"),Sp="comm",Tp="rule",Lp="decl",PC="@import",NC="@namespace",zC="@keyframes",WC="@layer",Bp=Math.abs,Eo=String.fromCharCode;function Ap(e){return e.trim()}function pn(e,t,r){return e.replace(t,r)}function qC(e,t,r){return e.indexOf(t,r)}function vr(e,t){return e.charCodeAt(t)|0}function qr(e,t,r){return e.slice(t,r)}function pe(e){return e.length}function HC(e){return e.length}function tn(e,t){return t.push(e),e}var da=1,Hr=1,Mp=0,ie=0,_t=0,Gr="";function Fo(e,t,r,i,n,a,o,s){return{value:e,root:t,parent:r,type:i,props:n,children:a,line:da,column:Hr,length:o,return:"",siblings:s}}function YC(){return _t}function jC(){return _t=ie>0?vr(Gr,--ie):0,Hr--,_t===10&&(Hr=1,da--),_t}function ce(){return _t=ie<Mp?vr(Gr,ie++):0,Hr++,_t===10&&(Hr=1,da++),_t}function ze(){return vr(Gr,ie)}function gn(){return ie}function pa(e,t){return qr(Gr,e,t)}function Ai(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function UC(e){return da=Hr=1,Mp=pe(Gr=e),ie=0,[]}function GC(e){return Gr="",e}function Wa(e){return Ap(pa(ie-1,Ms(e===91?e+2:e===40?e+1:e)))}function VC(e){for(;(_t=ze())&&_t<33;)ce();return Ai(e)>2||Ai(_t)>3?"":" "}function XC(e,t){for(;--t&&ce()&&!(_t<48||_t>102||_t>57&&_t<65||_t>70&&_t<97););return pa(e,gn()+(t<6&&ze()==32&&ce()==32))}function Ms(e){for(;ce();)switch(_t){case e:return ie;case 34:case 39:e!==34&&e!==39&&Ms(_t);break;case 40:e===41&&Ms(e);break;case 92:ce();break}return ie}function ZC(e,t){for(;ce()&&e+_t!==57;)if(e+_t===84&&ze()===47)break;return"/*"+pa(t,ie-1)+"*"+Eo(e===47?e:ce())}function KC(e){for(;!Ai(ze());)ce();return pa(e,ie)}function QC(e){return GC(mn("",null,null,null,[""],e=UC(e),0,[0],e))}function mn(e,t,r,i,n,a,o,s,c){for(var l=0,h=0,u=o,f=0,d=0,g=0,m=1,y=1,x=1,b=0,C="",v=n,S=a,_=i,k=C;y;)switch(g=b,b=ce()){case 40:if(g!=108&&vr(k,u-1)==58){qC(k+=pn(Wa(b),"&","&\f"),"&\f",Bp(l?s[l-1]:0))!=-1&&(x=-1);break}case 34:case 39:case 91:k+=Wa(b);break;case 9:case 10:case 13:case 32:k+=VC(g);break;case 92:k+=XC(gn()-1,7);continue;case 47:switch(ze()){case 42:case 47:tn(JC(ZC(ce(),gn()),t,r,c),c),(Ai(g||1)==5||Ai(ze()||1)==5)&&pe(k)&&qr(k,-1,void 0)!==" "&&(k+=" ");break;default:k+="/"}break;case 123*m:s[l++]=pe(k)*x;case 125*m:case 59:case 0:switch(b){case 0:case 125:y=0;case 59+h:x==-1&&(k=pn(k,/\f/g,"")),d>0&&(pe(k)-u||m===0&&g===47)&&tn(d>32?cc(k+";",i,r,u-1,c):cc(pn(k," ","")+";",i,r,u-2,c),c);break;case 59:k+=";";default:if(tn(_=lc(k,t,r,l,h,n,s,C,v=[],S=[],u,a),a),b===123)if(h===0)mn(k,t,_,_,v,a,u,s,S);else{switch(f){case 99:if(vr(k,3)===110)break;case 108:if(vr(k,2)===97)break;default:h=0;case 100:case 109:case 115:}h?mn(e,_,_,i&&tn(lc(e,_,_,0,0,n,s,C,n,v=[],u,S),S),n,S,u,s,i?v:S):mn(k,_,_,_,[""],S,0,s,S)}}l=h=d=0,m=x=1,C=k="",u=o;break;case 58:u=1+pe(k),d=g;default:if(m<1){if(b==123)--m;else if(b==125&&m++==0&&jC()==125)continue}switch(k+=Eo(b),b*m){case 38:x=h>0?1:(k+="\f",-1);break;case 44:s[l++]=(pe(k)-1)*x,x=1;break;case 64:ze()===45&&(k+=Wa(ce())),f=ze(),h=u=pe(C=k+=KC(gn())),b++;break;case 45:g===45&&pe(k)==2&&(m=0)}}return a}function lc(e,t,r,i,n,a,o,s,c,l,h,u){for(var f=n-1,d=n===0?a:[""],g=HC(d),m=0,y=0,x=0;m<i;++m)for(var b=0,C=qr(e,f+1,f=Bp(y=o[m])),v=e;b<g;++b)(v=Ap(y>0?d[b]+" "+C:pn(C,/&\f/g,d[b])))&&(c[x++]=v);return Fo(e,t,r,n===0?Tp:s,c,l,h,u)}function JC(e,t,r,i){return Fo(e,t,r,Sp,Eo(YC()),qr(e,2,-2),0,i)}function cc(e,t,r,i,n){return Fo(e,t,r,Lp,qr(e,0,i),qr(e,i+1,-1),i,n)}function Es(e,t){for(var r="",i=0;i<e.length;i++)r+=t(e[i],i,e,t)||"";return r}function tw(e,t,r,i){switch(e.type){case WC:if(e.children.length)break;case PC:case NC:case Lp:return e.return=e.return||e.value;case Sp:return"";case zC:return e.return=e.value+"{"+Es(e.children,i)+"}";case Tp:if(!pe(e.value=e.props.join(",")))return""}return pe(r=Es(e.children,i))?e.return=e.value+"{"+r+"}":""}var Ep="c4",ew=p(e=>/^\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(e),"detector"),rw=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./c4Diagram-VJAJSXHY-BpORWx-R.js");return{diagram:t}},__vite__mapDeps([13,14,0,1,2,11]));return{id:Ep,diagram:e}},"loader"),iw={id:Ep,detector:ew,loader:rw},nw=iw,Fp="flowchart",aw=p((e,t)=>{var r,i;return((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="dagre-wrapper"||((i=t==null?void 0:t.flowchart)==null?void 0:i.defaultRenderer)==="elk"?!1:/^\s*graph/.test(e)},"detector"),sw=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./flowDiagram-4HSFHLVR-BDtL0AhX.js");return{diagram:t}},__vite__mapDeps([15,16,17,0,1,2,11]));return{id:Fp,diagram:e}},"loader"),ow={id:Fp,detector:aw,loader:sw},lw=ow,$p="flowchart-v2",cw=p((e,t)=>{var r,i,n;return((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="dagre-d3"?!1:(((i=t==null?void 0:t.flowchart)==null?void 0:i.defaultRenderer)==="elk"&&(t.layout="elk"),/^\s*graph/.test(e)&&((n=t==null?void 0:t.flowchart)==null?void 0:n.defaultRenderer)==="dagre-wrapper"?!0:/^\s*flowchart/.test(e))},"detector"),hw=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./flowDiagram-4HSFHLVR-BDtL0AhX.js");return{diagram:t}},__vite__mapDeps([15,16,17,0,1,2,11]));return{id:$p,diagram:e}},"loader"),uw={id:$p,detector:cw,loader:hw},fw=uw,Dp="er",dw=p(e=>/^\s*erDiagram/.test(e),"detector"),pw=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./erDiagram-Q7BY3M3F-BOrTxCpr.js");return{diagram:t}},__vite__mapDeps([18,16,17,0,1,2,11]));return{id:Dp,diagram:e}},"loader"),gw={id:Dp,detector:dw,loader:pw},mw=gw,Rp="gitGraph",yw=p(e=>/^\s*gitGraph/.test(e),"detector"),xw=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./gitGraphDiagram-7IBYFJ6S-BlG8464A.js");return{diagram:t}},__vite__mapDeps([19,20,21,22,0,1,2,11,8,6,23,9,5,12]));return{id:Rp,diagram:e}},"loader"),bw={id:Rp,detector:yw,loader:xw},_w=bw,Op="gantt",Cw=p(e=>/^\s*gantt/.test(e),"detector"),ww=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./ganttDiagram-APWFNJXF-Byqdn0gf.js");return{diagram:t}},__vite__mapDeps([24,1,25,26,0,2,11]));return{id:Op,diagram:e}},"loader"),kw={id:Op,detector:Cw,loader:ww},vw=kw,Ip="info",Sw=p(e=>/^\s*info/.test(e),"detector"),Tw=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./infoDiagram-PH2N3AL5-7ojxUhJu.js");return{diagram:t}},__vite__mapDeps([27,22,0,1,2,11,8,6,23,9,5,12]));return{id:Ip,diagram:e}},"loader"),Lw={id:Ip,detector:Sw,loader:Tw},Pp="pie",Bw=p(e=>/^\s*pie/.test(e),"detector"),Aw=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./pieDiagram-IB7DONF6-BgEzCo4h.js");return{diagram:t}},__vite__mapDeps([28,20,22,0,1,2,11,8,6,23,9,5,12,29,30,26]));return{id:Pp,diagram:e}},"loader"),Mw={id:Pp,detector:Bw,loader:Aw},Np="quadrantChart",Ew=p(e=>/^\s*quadrantChart/.test(e),"detector"),Fw=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./quadrantDiagram-7GDLP6J5-DgcbtikI.js");return{diagram:t}},__vite__mapDeps([31,25,26,0,1,2,11]));return{id:Np,diagram:e}},"loader"),$w={id:Np,detector:Ew,loader:Fw},Dw=$w,zp="xychart",Rw=p(e=>/^\s*xychart-beta/.test(e),"detector"),Ow=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./xychartDiagram-VJFVF3MP-BnERav4O.js");return{diagram:t}},__vite__mapDeps([32,26,30,25,0,1,2,11]));return{id:zp,diagram:e}},"loader"),Iw={id:zp,detector:Rw,loader:Ow},Pw=Iw,Wp="requirement",Nw=p(e=>/^\s*requirement(Diagram)?/.test(e),"detector"),zw=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./requirementDiagram-KVF5MWMF-BYmmNXP7.js");return{diagram:t}},__vite__mapDeps([33,16,0,1,2,11]));return{id:Wp,diagram:e}},"loader"),Ww={id:Wp,detector:Nw,loader:zw},qw=Ww,qp="sequence",Hw=p(e=>/^\s*sequenceDiagram/.test(e),"detector"),Yw=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./sequenceDiagram-X6HHIX6F-BrsGcVKa.js");return{diagram:t}},__vite__mapDeps([34,14,21,0,1,2,11]));return{id:qp,diagram:e}},"loader"),jw={id:qp,detector:Hw,loader:Yw},Uw=jw,Hp="class",Gw=p((e,t)=>{var r;return((r=t==null?void 0:t.class)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!1:/^\s*classDiagram/.test(e)},"detector"),Vw=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./classDiagram-GIVACNV2-CaJ5MAn_.js");return{diagram:t}},__vite__mapDeps([35,36,16,0,1,2,11]));return{id:Hp,diagram:e}},"loader"),Xw={id:Hp,detector:Gw,loader:Vw},Zw=Xw,Yp="classDiagram",Kw=p((e,t)=>{var r;return/^\s*classDiagram/.test(e)&&((r=t==null?void 0:t.class)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!0:/^\s*classDiagram-v2/.test(e)},"detector"),Qw=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./classDiagram-v2-COTLJTTW-B2L3uNbr.js");return{diagram:t}},__vite__mapDeps([37,36,16,0,1,2,11]));return{id:Yp,diagram:e}},"loader"),Jw={id:Yp,detector:Kw,loader:Qw},tk=Jw,jp="state",ek=p((e,t)=>{var r;return((r=t==null?void 0:t.state)==null?void 0:r.defaultRenderer)==="dagre-wrapper"?!1:/^\s*stateDiagram/.test(e)},"detector"),rk=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./stateDiagram-DGXRK772-BbGcuAkE.js");return{diagram:t}},__vite__mapDeps([38,39,16,4,1,5,6,0,2,7,8,9,10,11]));return{id:jp,diagram:e}},"loader"),ik={id:jp,detector:ek,loader:rk},nk=ik,Up="stateDiagram",ak=p((e,t)=>{var r;return!!(/^\s*stateDiagram-v2/.test(e)||/^\s*stateDiagram/.test(e)&&((r=t==null?void 0:t.state)==null?void 0:r.defaultRenderer)==="dagre-wrapper")},"detector"),sk=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./stateDiagram-v2-YXO3MK2T-TmqRN8B9.js");return{diagram:t}},__vite__mapDeps([40,39,16,0,1,2,11]));return{id:Up,diagram:e}},"loader"),ok={id:Up,detector:ak,loader:sk},lk=ok,Gp="journey",ck=p(e=>/^\s*journey/.test(e),"detector"),hk=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./journeyDiagram-U35MCT3I-BFdVcXAy.js");return{diagram:t}},__vite__mapDeps([41,14,29,0,1,2,11]));return{id:Gp,diagram:e}},"loader"),uk={id:Gp,detector:ck,loader:hk},fk=uk,dk=p((e,t,r)=>{D.debug(`rendering svg for syntax error
`);const i=IC(t),n=i.append("g");i.attr("viewBox","0 0 2412 512"),Pc(i,100,512,!0),n.append("path").attr("class","error-icon").attr("d","m411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z"),n.append("path").attr("class","error-icon").attr("d","m459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z"),n.append("path").attr("class","error-icon").attr("d","m340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z"),n.append("path").attr("class","error-icon").attr("d","m400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z"),n.append("path").attr("class","error-icon").attr("d","m496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z"),n.append("path").attr("class","error-icon").attr("d","m436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z"),n.append("text").attr("class","error-text").attr("x",1440).attr("y",250).attr("font-size","150px").style("text-anchor","middle").text("Syntax error in text"),n.append("text").attr("class","error-text").attr("x",1250).attr("y",400).attr("font-size","100px").style("text-anchor","middle").text(`mermaid version ${r}`)},"draw"),Vp={draw:dk},pk=Vp,gk={db:{},renderer:Vp,parser:{parse:p(()=>{},"parse")}},mk=gk,Xp="flowchart-elk",yk=p((e,t={})=>{var r;return/^\s*flowchart-elk/.test(e)||/^\s*flowchart|graph/.test(e)&&((r=t==null?void 0:t.flowchart)==null?void 0:r.defaultRenderer)==="elk"?(t.layout="elk",!0):!1},"detector"),xk=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./flowDiagram-4HSFHLVR-BDtL0AhX.js");return{diagram:t}},__vite__mapDeps([15,16,17,0,1,2,11]));return{id:Xp,diagram:e}},"loader"),bk={id:Xp,detector:yk,loader:xk},_k=bk,Zp="timeline",Ck=p(e=>/^\s*timeline/.test(e),"detector"),wk=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./timeline-definition-BDJGKUSR-COIi1rQS.js");return{diagram:t}},__vite__mapDeps([42,29,0,1,2,11]));return{id:Zp,diagram:e}},"loader"),kk={id:Zp,detector:Ck,loader:wk},vk=kk,Kp="mindmap",Sk=p(e=>/^\s*mindmap/.test(e),"detector"),Tk=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./mindmap-definition-ALO5MXBD-DDnbJ6SA.js");return{diagram:t}},__vite__mapDeps([43,44,1,0,2,11]));return{id:Kp,diagram:e}},"loader"),Lk={id:Kp,detector:Sk,loader:Tk},Bk=Lk,Qp="kanban",Ak=p(e=>/^\s*kanban/.test(e),"detector"),Mk=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./kanban-definition-NDS4AKOZ-Cg5OAQPf.js");return{diagram:t}},__vite__mapDeps([45,0,1,2,11]));return{id:Qp,diagram:e}},"loader"),Ek={id:Qp,detector:Ak,loader:Mk},Fk=Ek,Jp="sankey",$k=p(e=>/^\s*sankey-beta/.test(e),"detector"),Dk=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./sankeyDiagram-QLVOVGJD-NpPsrDzZ.js");return{diagram:t}},__vite__mapDeps([46,30,26,0,1,2,11]));return{id:Jp,diagram:e}},"loader"),Rk={id:Jp,detector:$k,loader:Dk},Ok=Rk,tg="packet",Ik=p(e=>/^\s*packet-beta/.test(e),"detector"),Pk=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./diagram-VNBRO52H-Dj2Qe8wC.js");return{diagram:t}},__vite__mapDeps([47,20,22,0,1,2,11,8,6,23,9,5,12]));return{id:tg,diagram:e}},"loader"),Nk={id:tg,detector:Ik,loader:Pk},eg="radar",zk=p(e=>/^\s*radar-beta/.test(e),"detector"),Wk=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./diagram-SSKATNLV-CKsRMtof.js");return{diagram:t}},__vite__mapDeps([48,20,22,0,1,2,11,8,6,23,9,5,12]));return{id:eg,diagram:e}},"loader"),qk={id:eg,detector:zk,loader:Wk},rg="block",Hk=p(e=>/^\s*block-beta/.test(e),"detector"),Yk=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./blockDiagram-JOT3LUYC-Q10idWxD.js");return{diagram:t}},__vite__mapDeps([49,12,0,1,2,4,5,6,17,11]));return{id:rg,diagram:e}},"loader"),jk={id:rg,detector:Hk,loader:Yk},Uk=jk,ig="architecture",Gk=p(e=>/^\s*architecture/.test(e),"detector"),Vk=p(async()=>{const{diagram:e}=await pt(async()=>{const{diagram:t}=await import("./architectureDiagram-IEHRJDOE-DrTkLy5C.js");return{diagram:t}},__vite__mapDeps([50,20,21,22,0,1,2,11,8,6,23,9,5,12,44]));return{id:ig,diagram:e}},"loader"),Xk={id:ig,detector:Gk,loader:Vk},Zk=Xk,hc=!1,ga=p(()=>{hc||(hc=!0,_n("error",mk,e=>e.toLowerCase().trim()==="error"),_n("---",{db:{clear:p(()=>{},"clear")},styles:{},renderer:{draw:p(()=>{},"draw")},parser:{parse:p(()=>{throw new Error("Diagrams beginning with --- are not valid. If you were trying to use a YAML front-matter, please ensure that you've correctly opened and closed the YAML front-matter with un-indented `---` blocks")},"parse")},init:p(()=>null,"init")},e=>e.toLowerCase().trimStart().startsWith("---")),Sc(nw,Fk,tk,Zw,mw,vw,Lw,Mw,qw,Uw,_k,fw,lw,Bk,vk,_w,lk,nk,fk,Dw,Ok,Nk,Pw,Uk,Zk,qk))},"addDiagrams"),Kk=p(async()=>{D.debug("Loading registered diagrams");const t=(await Promise.allSettled(Object.entries(Dr).map(async([r,{detector:i,loader:n}])=>{if(n)try{Xa(r)}catch{try{const{diagram:a,id:o}=await n();_n(o,a,i)}catch(a){throw D.error(`Failed to load external diagram with key ${r}. Removing from detectors.`),delete Dr[r],a}}}))).filter(r=>r.status==="rejected");if(t.length>0){D.error(`Failed to load ${t.length} external diagrams`);for(const r of t)D.error(r);throw new Error(`Failed to load ${t.length} external diagrams`)}},"loadRegisteredDiagrams"),Qk="graphics-document document";function ng(e,t){e.attr("role",Qk),t!==""&&e.attr("aria-roledescription",t)}p(ng,"setA11yDiagramInfo");function ag(e,t,r,i){if(e.insert!==void 0){if(r){const n=`chart-desc-${i}`;e.attr("aria-describedby",n),e.insert("desc",":first-child").attr("id",n).text(r)}if(t){const n=`chart-title-${i}`;e.attr("aria-labelledby",n),e.insert("title",":first-child").attr("id",n).text(t)}}}p(ag,"addSVGa11yTitleDescription");var ir,Fs=(ir=class{constructor(t,r,i,n,a){this.type=t,this.text=r,this.db=i,this.parser=n,this.renderer=a}static async fromText(t,r={}){var l,h;const i=Xt(),n=Os(t,i);t=$2(t)+`
`;try{Xa(n)}catch{const u=fm(n);if(!u)throw new vc(`Diagram ${n} not found.`);const{id:f,diagram:d}=await u();_n(f,d)}const{db:a,parser:o,renderer:s,init:c}=Xa(n);return o.parser&&(o.parser.yy=a),(l=a.clear)==null||l.call(a),c==null||c(i),r.title&&((h=a.setDiagramTitle)==null||h.call(a,r.title)),await o.parse(t),new ir(n,t,a,o,s)}async render(t,r){await this.renderer.draw(this.text,t,r,this)}getParser(){return this.parser}getType(){return this.type}},p(ir,"Diagram"),ir),uc=[],Jk=p(()=>{uc.forEach(e=>{e()}),uc=[]},"attachFunctions"),tv=p(e=>e.replace(/^\s*%%(?!{)[^\n]+\n?/gm,"").trimStart(),"cleanupComments");function sg(e){const t=e.match(kc);if(!t)return{text:e,metadata:{}};let r=uy(t[1],{schema:hy})??{};r=typeof r=="object"&&!Array.isArray(r)?r:{};const i={};return r.displayMode&&(i.displayMode=r.displayMode.toString()),r.title&&(i.title=r.title.toString()),r.config&&(i.config=r.config),{text:e.slice(t[0].length),metadata:i}}p(sg,"extractFrontMatter");var ev=p(e=>e.replace(/\r\n?/g,`
`).replace(/<(\w+)([^>]*)>/g,(t,r,i)=>"<"+r+i.replace(/="([^"]*)"/g,"='$1'")+">"),"cleanupText"),rv=p(e=>{const{text:t,metadata:r}=sg(e),{displayMode:i,title:n,config:a={}}=r;return i&&(a.gantt||(a.gantt={}),a.gantt.displayMode=i),{title:n,config:a,text:t}},"processFrontmatter"),iv=p(e=>{const t=ge.detectInit(e)??{},r=ge.detectDirective(e,"wrap");return Array.isArray(r)?t.wrap=r.some(({type:i})=>i==="wrap"):(r==null?void 0:r.type)==="wrap"&&(t.wrap=!0),{text:_2(e),directive:t}},"processDirectives");function $o(e){const t=ev(e),r=rv(t),i=iv(r.text),n=mo(r.config,i.directive);return e=tv(i.text),{code:e,title:r.title,config:n}}p($o,"preprocessDiagram");function og(e){const t=new TextEncoder().encode(e),r=Array.from(t,i=>String.fromCodePoint(i)).join("");return btoa(r)}p(og,"toBase64");var nv=5e4,av="graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa",sv="sandbox",ov="loose",lv="http://www.w3.org/2000/svg",cv="http://www.w3.org/1999/xlink",hv="http://www.w3.org/1999/xhtml",uv="100%",fv="100%",dv="border:0;margin:0;",pv="margin:0",gv="allow-top-navigation-by-user-activation allow-popups",mv='The "iframe" tag is not supported by your browser.',yv=["foreignobject"],xv=["dominant-baseline"];function Do(e){const t=$o(e);return xn(),Bm(t.config??{}),t}p(Do,"processAndSetConfigs");async function lg(e,t){ga();try{const{code:r,config:i}=Do(e);return{diagramType:(await hg(r)).type,config:i}}catch(r){if(t!=null&&t.suppressErrors)return!1;throw r}}p(lg,"parse");var fc=p((e,t,r=[])=>`
.${e} ${t} { ${r.join(" !important; ")} !important; }`,"cssImportantStyles"),bv=p((e,t=new Map)=>{var i;let r="";if(e.themeCSS!==void 0&&(r+=`
${e.themeCSS}`),e.fontFamily!==void 0&&(r+=`
:root { --mermaid-font-family: ${e.fontFamily}}`),e.altFontFamily!==void 0&&(r+=`
:root { --mermaid-alt-font-family: ${e.altFontFamily}}`),t instanceof Map){const s=e.htmlLabels??((i=e.flowchart)==null?void 0:i.htmlLabels)?["> *","span"]:["rect","polygon","ellipse","circle","path"];t.forEach(c=>{Jo(c.styles)||s.forEach(l=>{r+=fc(c.id,l,c.styles)}),Jo(c.textStyles)||(r+=fc(c.id,"tspan",((c==null?void 0:c.textStyles)||[]).map(l=>l.replace("color","fill"))))})}return r},"createCssStyles"),_v=p((e,t,r,i)=>{const n=bv(e,r),a=Gm(t,n,e.themeVariables);return Es(QC(`${i}{${a}}`),tw)},"createUserStyles"),Cv=p((e="",t,r)=>{let i=e;return!r&&!t&&(i=i.replace(/marker-end="url\([\d+./:=?A-Za-z-]*?#/g,'marker-end="url(#')),i=cr(i),i=i.replace(/<br>/g,"<br/>"),i},"cleanUpSvgCode"),wv=p((e="",t)=>{var n,a;const r=(a=(n=t==null?void 0:t.viewBox)==null?void 0:n.baseVal)!=null&&a.height?t.viewBox.baseVal.height+"px":fv,i=og(`<body style="${pv}">${e}</body>`);return`<iframe style="width:${uv};height:${r};${dv}" src="data:text/html;charset=UTF-8;base64,${i}" sandbox="${gv}">
  ${mv}
</iframe>`},"putIntoIFrame"),dc=p((e,t,r,i,n)=>{const a=e.append("div");a.attr("id",r),i&&a.attr("style",i);const o=a.append("svg").attr("id",t).attr("width","100%").attr("xmlns",lv);return n&&o.attr("xmlns:xlink",n),o.append("g"),e},"appendDivSvgG");function $s(e,t){return e.append("iframe").attr("id",t).attr("style","width: 100%; height: 100%;").attr("sandbox","")}p($s,"sandboxedIframe");var kv=p((e,t,r,i)=>{var n,a,o;(n=e.getElementById(t))==null||n.remove(),(a=e.getElementById(r))==null||a.remove(),(o=e.getElementById(i))==null||o.remove()},"removeExistingElements"),vv=p(async function(e,t,r){var z,O,E,M,L,F;ga();const i=Do(t);t=i.code;const n=Xt();D.debug(n),t.length>((n==null?void 0:n.maxTextSize)??nv)&&(t=av);const a="#"+e,o="i"+e,s="#"+o,c="d"+e,l="#"+c,h=p(()=>{const W=lt(f?s:l).node();W&&"remove"in W&&W.remove()},"removeTempElements");let u=lt("body");const f=n.securityLevel===sv,d=n.securityLevel===ov,g=n.fontFamily;if(r!==void 0){if(r&&(r.innerHTML=""),f){const B=$s(lt(r),o);u=lt(B.nodes()[0].contentDocument.body),u.node().style.margin=0}else u=lt(r);dc(u,e,c,`font-family: ${g}`,cv)}else{if(kv(document,e,c,o),f){const B=$s(lt("body"),o);u=lt(B.nodes()[0].contentDocument.body),u.node().style.margin=0}else u=lt("body");dc(u,e,c)}let m,y;try{m=await Fs.fromText(t,{title:i.title})}catch(B){if(n.suppressErrorRendering)throw h(),B;m=await Fs.fromText("error"),y=B}const x=u.select(l).node(),b=m.type,C=x.firstChild,v=C.firstChild,S=(O=(z=m.renderer).getClasses)==null?void 0:O.call(z,t,m),_=_v(n,b,S,a),k=document.createElement("style");k.innerHTML=_,C.insertBefore(k,v);try{await m.renderer.draw(t,e,oc.version,m)}catch(B){throw n.suppressErrorRendering?h():pk.draw(t,e,oc.version),B}const R=u.select(`${l} svg`),P=(M=(E=m.db).getAccTitle)==null?void 0:M.call(E),$=(F=(L=m.db).getAccDescription)==null?void 0:F.call(L);ug(b,R,P,$),u.select(`[id="${e}"]`).selectAll("foreignobject > *").attr("xmlns",hv);let A=u.select(l).node().innerHTML;if(D.debug("config.arrowMarkerAbsolute",n.arrowMarkerAbsolute),A=Cv(A,f,kt(n.arrowMarkerAbsolute)),f){const B=u.select(l+" svg").node();A=wv(A,B)}else d||(A=$r.sanitize(A,{ADD_TAGS:yv,ADD_ATTR:xv,HTML_INTEGRATION_POINTS:{foreignobject:!0}}));if(Jk(),y)throw y;return h(),{diagramType:b,svg:A,bindFunctions:m.db.bindFunctions}},"render");function cg(e={}){var i;const t=Mt({},e);t!=null&&t.fontFamily&&!((i=t.themeVariables)!=null&&i.fontFamily)&&(t.themeVariables||(t.themeVariables={}),t.themeVariables.fontFamily=t.fontFamily),Tm(t),t!=null&&t.theme&&t.theme in Fe?t.themeVariables=Fe[t.theme].getThemeVariables(t.themeVariables):t&&(t.themeVariables=Fe.default.getThemeVariables(t.themeVariables));const r=typeof t=="object"?Sm(t):Ec();Rs(r.logLevel),ga()}p(cg,"initialize");var hg=p((e,t={})=>{const{code:r}=$o(e);return Fs.fromText(r,t)},"getDiagramFromText");function ug(e,t,r,i){ng(t,e),ag(t,r,i,t.attr("id"))}p(ug,"addA11yInfo");var lr=Object.freeze({render:vv,parse:lg,getDiagramFromText:hg,initialize:cg,getConfig:Xt,setConfig:Fc,getSiteConfig:Ec,updateSiteConfig:Lm,reset:p(()=>{xn()},"reset"),globalReset:p(()=>{xn(Rr)},"globalReset"),defaultConfig:Rr});Rs(Xt().logLevel);xn(Xt());var Sv=p((e,t,r)=>{D.warn(e),go(e)?(r&&r(e.str,e.hash),t.push({...e,message:e.str,error:e})):(r&&r(e),e instanceof Error&&t.push({str:e.message,message:e.message,hash:e.name,error:e}))},"handleError"),fg=p(async function(e={querySelector:".mermaid"}){try{await Tv(e)}catch(t){if(go(t)&&D.error(t.str),Kt.parseError&&Kt.parseError(t),!e.suppressErrors)throw D.error("Use the suppressErrors option to suppress these errors"),t}},"run"),Tv=p(async function({postRenderCallback:e,querySelector:t,nodes:r}={querySelector:".mermaid"}){const i=lr.getConfig();D.debug(`${e?"":"No "}Callback function found`);let n;if(r)n=r;else if(t)n=document.querySelectorAll(t);else throw new Error("Nodes and querySelector are both undefined");D.debug(`Found ${n.length} diagrams`),(i==null?void 0:i.startOnLoad)!==void 0&&(D.debug("Start On Load: "+(i==null?void 0:i.startOnLoad)),lr.updateSiteConfig({startOnLoad:i==null?void 0:i.startOnLoad}));const a=new ge.InitIDGenerator(i.deterministicIds,i.deterministicIDSeed);let o;const s=[];for(const c of Array.from(n)){if(D.info("Rendering diagram: "+c.id),c.getAttribute("data-processed"))continue;c.setAttribute("data-processed","true");const l=`mermaid-${a.next()}`;o=c.innerHTML,o=zf(ge.entityDecode(o)).trim().replace(/<br\s*\/?>/gi,"<br/>");const h=ge.detectInit(o);h&&D.debug("Detected early reinit: ",h);try{const{svg:u,bindFunctions:f}=await mg(l,o,c);c.innerHTML=u,e&&await e(l),f&&f(c)}catch(u){Sv(u,s,Kt.parseError)}}if(s.length>0)throw s[0]},"runThrowsErrors"),dg=p(function(e){lr.initialize(e)},"initialize"),Lv=p(async function(e,t,r){D.warn("mermaid.init is deprecated. Please use run instead."),e&&dg(e);const i={postRenderCallback:r,querySelector:".mermaid"};typeof t=="string"?i.querySelector=t:t&&(t instanceof HTMLElement?i.nodes=[t]:i.nodes=t),await fg(i)},"init"),Bv=p(async(e,{lazyLoad:t=!0}={})=>{ga(),Sc(...e),t===!1&&await Kk()},"registerExternalDiagrams"),pg=p(function(){if(Kt.startOnLoad){const{startOnLoad:e}=lr.getConfig();e&&Kt.run().catch(t=>D.error("Mermaid failed to initialize",t))}},"contentLoaded");typeof document<"u"&&window.addEventListener("load",pg,!1);var Av=p(function(e){Kt.parseError=e},"setParseErrorHandler"),Gn=[],qa=!1,gg=p(async()=>{if(!qa){for(qa=!0;Gn.length>0;){const e=Gn.shift();if(e)try{await e()}catch(t){D.error("Error executing queue",t)}}qa=!1}},"executeQueue"),Mv=p(async(e,t)=>new Promise((r,i)=>{const n=p(()=>new Promise((a,o)=>{lr.parse(e,t).then(s=>{a(s),r(s)},s=>{var c;D.error("Error parsing",s),(c=Kt.parseError)==null||c.call(Kt,s),o(s),i(s)})}),"performCall");Gn.push(n),gg().catch(i)}),"parse"),mg=p((e,t,r)=>new Promise((i,n)=>{const a=p(()=>new Promise((o,s)=>{lr.render(e,t,r).then(c=>{o(c),i(c)},c=>{var l;D.error("Error parsing",c),(l=Kt.parseError)==null||l.call(Kt,c),s(c),n(c)})}),"performCall");Gn.push(a),gg().catch(n)}),"render"),Kt={startOnLoad:!0,mermaidAPI:lr,parse:Mv,render:mg,init:Lv,run:fg,registerExternalDiagrams:Bv,registerLayoutLoaders:vp,initialize:dg,parseError:void 0,contentLoaded:pg,setParseErrorHandler:Av,detectType:Os,registerIconPacks:Dy},yg=Kt;/*! Check if previously processed *//*!
 * Wait for document loaded before starting the execution
 */globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};yg.initialize({startOnLoad:!1,theme:document.documentElement.className.includes("dark")?"dark":"default"});function Ev(e){const t=tl.useRef(null);return tl.useEffect(()=>{const r=Dg();(async()=>{if(t.current){const{svg:n,bindFunctions:a}=await yg.render(`mermaid-diagram-${r}`,e.code,t.current);t.current.innerHTML=n,a==null||a(t.current)}})().catch(console.error)},[e.code]),Bg.jsx("div",{ref:t})}const aS=Object.freeze(Object.defineProperty({__proto__:null,default:Ev},Symbol.toStringTag,{value:"Module"}));export{i2 as $,mi as A,hm as B,Ac as C,mo as D,Xt as E,S2 as F,r0 as G,IC as H,oc as I,hy as J,xm as K,Ir as L,Iv as M,Is as N,sa as O,fl as P,o2 as Q,hn as R,v2 as S,Ei as T,Nc as U,Ym as V,Mi as W,q as X,Q as Y,m2 as Z,p as _,Mt as a,Pl as a0,Il as a1,Vv as a2,Hv as a3,Uv as a4,jv as a5,Wv as a6,ao as a7,Gv as a8,qv as a9,n2 as aA,$y as aB,Dy as aC,ta as aD,to as aE,Pu as aF,Vt as aG,Di as aH,Mb as aI,Wu as aJ,zv as aK,Ov as aL,eo as aM,Ne as aN,Ti as aO,El as aP,Ib as aQ,Dg as aR,aS,yr as aa,Zv as ab,Xv as ac,Yv as ad,H_ as ae,wp as af,eS as ag,Tf as ah,kt as ai,Ue as aj,Zs as ak,fy as al,Zf as am,cr as an,at as ao,ye as ap,DC as aq,tS as ar,rS as as,Qv as at,V as au,Jv as av,mC as aw,fC as ax,uC as ay,m_ as az,Qm as b,Km as c,ut as d,Yr as e,Bf as f,Zm as g,Re as h,nr as i,lt as j,Pc as k,D as l,vu as m,Jm as n,t0 as o,uy as p,lC as q,Kv as r,Xm as s,Vm as t,ge as u,Nv as v,B2 as w,Pv as x,nS as y,iS as z};
//# sourceMappingURL=MermaidPreview-B7IzGWQB.js.map
