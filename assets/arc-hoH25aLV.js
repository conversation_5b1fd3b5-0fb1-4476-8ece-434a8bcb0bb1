import{$ as ln,a0 as an,a1 as y,a2 as tn,a3 as C,a4 as I,a5 as _,a6 as rn,a7 as un,a8 as H,a9 as o,aa as j,ab as sn,ac as on,ad as fn}from"./MermaidPreview-B7IzGWQB.js";(function(){try{var s=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},f=new s.Error().stack;f&&(s._sentryDebugIds=s._sentryDebugIds||{},s._sentryDebugIds[f]="aa0d0ba2-b66d-48c4-8519-ab8b8e196226",s._sentryDebugIdIdentifier="sentry-dbid-aa0d0ba2-b66d-48c4-8519-ab8b8e196226")}catch{}})();function cn(s){return s.innerRadius}function dn(s){return s.outerRadius}function yn(s){return s.startAngle}function gn(s){return s.endAngle}function pn(s){return s&&s.padAngle}function mn(s,f,D,P,v,b,k,a){var q=D-s,i=P-f,n=k-v,p=a-b,r=p*q-n*i;if(!(r*r<y))return r=(n*(f-b)-p*(s-v))/r,[s+r*q,f+r*i]}function Q(s,f,D,P,v,b,k){var a=s-D,q=f-P,i=(k?b:-b)/H(a*a+q*q),n=i*q,p=-i*a,r=s+n,l=f+p,c=D+n,d=P+p,z=(r+c)/2,t=(l+d)/2,m=c-r,g=d-l,A=m*m+g*g,R=v-b,T=r*d-c*l,O=(g<0?-1:1)*H(fn(0,R*R*A-T*T)),S=(T*g-m*O)/A,$=(-T*m-g*O)/A,w=(T*g+m*O)/A,h=(-T*m+g*O)/A,x=S-z,e=$-t,u=w-z,B=h-t;return x*x+e*e>u*u+B*B&&(S=w,$=h),{cx:S,cy:$,x01:-n,y01:-p,x11:S*(v/R-1),y11:$*(v/R-1)}}function xn(){var s=cn,f=dn,D=j(0),P=null,v=yn,b=gn,k=pn,a=null,q=ln(i);function i(){var n,p,r=+s.apply(this,arguments),l=+f.apply(this,arguments),c=v.apply(this,arguments)-an,d=b.apply(this,arguments)-an,z=rn(d-c),t=d>c;if(a||(a=n=q()),l<r&&(p=l,l=r,r=p),!(l>y))a.moveTo(0,0);else if(z>tn-y)a.moveTo(l*C(c),l*I(c)),a.arc(0,0,l,c,d,!t),r>y&&(a.moveTo(r*C(d),r*I(d)),a.arc(0,0,r,d,c,t));else{var m=c,g=d,A=c,R=d,T=z,O=z,S=k.apply(this,arguments)/2,$=S>y&&(P?+P.apply(this,arguments):H(r*r+l*l)),w=_(rn(l-r)/2,+D.apply(this,arguments)),h=w,x=w,e,u;if($>y){var B=sn($/r*I(S)),J=sn($/l*I(S));(T-=B*2)>y?(B*=t?1:-1,A+=B,R-=B):(T=0,A=R=(c+d)/2),(O-=J*2)>y?(J*=t?1:-1,m+=J,g-=J):(O=0,m=g=(c+d)/2)}var F=l*C(m),G=l*I(m),K=r*C(R),L=r*I(R);if(w>y){var M=l*C(g),N=l*I(g),U=r*C(A),V=r*I(A),E;if(z<un)if(E=mn(F,G,U,V,M,N,K,L)){var W=F-E[0],X=G-E[1],Y=M-E[0],Z=N-E[1],nn=1/I(on((W*Y+X*Z)/(H(W*W+X*X)*H(Y*Y+Z*Z)))/2),en=H(E[0]*E[0]+E[1]*E[1]);h=_(w,(r-en)/(nn-1)),x=_(w,(l-en)/(nn+1))}else h=x=0}O>y?x>y?(e=Q(U,V,F,G,l,x,t),u=Q(M,N,K,L,l,x,t),a.moveTo(e.cx+e.x01,e.cy+e.y01),x<w?a.arc(e.cx,e.cy,x,o(e.y01,e.x01),o(u.y01,u.x01),!t):(a.arc(e.cx,e.cy,x,o(e.y01,e.x01),o(e.y11,e.x11),!t),a.arc(0,0,l,o(e.cy+e.y11,e.cx+e.x11),o(u.cy+u.y11,u.cx+u.x11),!t),a.arc(u.cx,u.cy,x,o(u.y11,u.x11),o(u.y01,u.x01),!t))):(a.moveTo(F,G),a.arc(0,0,l,m,g,!t)):a.moveTo(F,G),!(r>y)||!(T>y)?a.lineTo(K,L):h>y?(e=Q(K,L,M,N,r,-h,t),u=Q(F,G,U,V,r,-h,t),a.lineTo(e.cx+e.x01,e.cy+e.y01),h<w?a.arc(e.cx,e.cy,h,o(e.y01,e.x01),o(u.y01,u.x01),!t):(a.arc(e.cx,e.cy,h,o(e.y01,e.x01),o(e.y11,e.x11),!t),a.arc(0,0,r,o(e.cy+e.y11,e.cx+e.x11),o(u.cy+u.y11,u.cx+u.x11),t),a.arc(u.cx,u.cy,h,o(u.y11,u.x11),o(u.y01,u.x01),!t))):a.arc(0,0,r,R,A,t)}if(a.closePath(),n)return a=null,n+""||null}return i.centroid=function(){var n=(+s.apply(this,arguments)+ +f.apply(this,arguments))/2,p=(+v.apply(this,arguments)+ +b.apply(this,arguments))/2-un/2;return[C(p)*n,I(p)*n]},i.innerRadius=function(n){return arguments.length?(s=typeof n=="function"?n:j(+n),i):s},i.outerRadius=function(n){return arguments.length?(f=typeof n=="function"?n:j(+n),i):f},i.cornerRadius=function(n){return arguments.length?(D=typeof n=="function"?n:j(+n),i):D},i.padRadius=function(n){return arguments.length?(P=n==null?null:typeof n=="function"?n:j(+n),i):P},i.startAngle=function(n){return arguments.length?(v=typeof n=="function"?n:j(+n),i):v},i.endAngle=function(n){return arguments.length?(b=typeof n=="function"?n:j(+n),i):b},i.padAngle=function(n){return arguments.length?(k=typeof n=="function"?n:j(+n),i):k},i.context=function(n){return arguments.length?(a=n??null,i):a},i}export{xn as d};
//# sourceMappingURL=arc-hoH25aLV.js.map
