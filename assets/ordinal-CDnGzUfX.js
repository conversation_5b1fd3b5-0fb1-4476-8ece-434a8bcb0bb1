import{i as a}from"./init-DBeb8MCR.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},n=new e.Error().stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="c3bcfdae-192b-4432-8006-eab86706c6b6",e._sentryDebugIdIdentifier="sentry-dbid-c3bcfdae-192b-4432-8006-eab86706c6b6")}catch{}})();class u extends Map{constructor(n,t=g){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),n!=null)for(const[r,s]of n)this.set(r,s)}get(n){return super.get(f(this,n))}has(n){return super.has(f(this,n))}set(n,t){return super.set(d(this,n),t)}delete(n){return super.delete(l(this,n))}}function f({_intern:e,_key:n},t){const r=n(t);return e.has(r)?e.get(r):t}function d({_intern:e,_key:n},t){const r=n(t);return e.has(r)?e.get(r):(e.set(r,t),t)}function l({_intern:e,_key:n},t){const r=n(t);return e.has(r)&&(t=e.get(r),e.delete(r)),t}function g(e){return e!==null&&typeof e=="object"?e.valueOf():e}const c=Symbol("implicit");function p(){var e=new u,n=[],t=[],r=c;function s(i){let o=e.get(i);if(o===void 0){if(r!==c)return r;e.set(i,o=n.push(i)-1)}return t[o%t.length]}return s.domain=function(i){if(!arguments.length)return n.slice();n=[],e=new u;for(const o of i)e.has(o)||e.set(o,n.push(o)-1);return s},s.range=function(i){return arguments.length?(t=Array.from(i),s):t.slice()},s.unknown=function(i){return arguments.length?(r=i,s):r},s.copy=function(){return p(n,t).unknown(r)},a.apply(s,arguments),s}export{p as o};
//# sourceMappingURL=ordinal-CDnGzUfX.js.map
