import{b as N,c,i as m,k as l}from"./user-config-BaX6AisS.js";import{f as g,r as v}from"./reduce-DteGjNFQ.js";import{b as j,a as w,f as d,v as p}from"./_baseUniq-CDtg80eS.js";import{a as D,i as _,c as y}from"./premium-DZfa0MBj.js";(function(){try{var h=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new h.Error().stack;e&&(h._sentryDebugIds=h._sentryDebugIds||{},h._sentryDebugIds[e]="7b038119-a36e-4cf6-a601-8ebc2bf036cc",h._sentryDebugIdIdentifier="sentry-dbid-7b038119-a36e-4cf6-a601-8ebc2bf036cc")}catch{}})();var P=N(function(h){return j(w(h,1,D,!0))}),F="\0",a="\0",E="";class L{constructor(e={}){this._isDirected=Object.prototype.hasOwnProperty.call(e,"directed")?e.directed:!0,this._isMultigraph=Object.prototype.hasOwnProperty.call(e,"multigraph")?e.multigraph:!1,this._isCompound=Object.prototype.hasOwnProperty.call(e,"compound")?e.compound:!1,this._label=void 0,this._defaultNodeLabelFn=c(void 0),this._defaultEdgeLabelFn=c(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children[a]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}isDirected(){return this._isDirected}isMultigraph(){return this._isMultigraph}isCompound(){return this._isCompound}setGraph(e){return this._label=e,this}graph(){return this._label}setDefaultNodeLabel(e){return m(e)||(e=c(e)),this._defaultNodeLabelFn=e,this}nodeCount(){return this._nodeCount}nodes(){return l(this._nodes)}sources(){var e=this;return g(this.nodes(),function(t){return y(e._in[t])})}sinks(){var e=this;return g(this.nodes(),function(t){return y(e._out[t])})}setNodes(e,t){var s=arguments,i=this;return d(e,function(r){s.length>1?i.setNode(r,t):i.setNode(r)}),this}setNode(e,t){return Object.prototype.hasOwnProperty.call(this._nodes,e)?(arguments.length>1&&(this._nodes[e]=t),this):(this._nodes[e]=arguments.length>1?t:this._defaultNodeLabelFn(e),this._isCompound&&(this._parent[e]=a,this._children[e]={},this._children[a][e]=!0),this._in[e]={},this._preds[e]={},this._out[e]={},this._sucs[e]={},++this._nodeCount,this)}node(e){return this._nodes[e]}hasNode(e){return Object.prototype.hasOwnProperty.call(this._nodes,e)}removeNode(e){if(Object.prototype.hasOwnProperty.call(this._nodes,e)){var t=s=>this.removeEdge(this._edgeObjs[s]);delete this._nodes[e],this._isCompound&&(this._removeFromParentsChildList(e),delete this._parent[e],d(this.children(e),s=>{this.setParent(s)}),delete this._children[e]),d(l(this._in[e]),t),delete this._in[e],delete this._preds[e],d(l(this._out[e]),t),delete this._out[e],delete this._sucs[e],--this._nodeCount}return this}setParent(e,t){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(_(t))t=a;else{t+="";for(var s=t;!_(s);s=this.parent(s))if(s===e)throw new Error("Setting "+t+" as parent of "+e+" would create a cycle");this.setNode(t)}return this.setNode(e),this._removeFromParentsChildList(e),this._parent[e]=t,this._children[t][e]=!0,this}_removeFromParentsChildList(e){delete this._children[this._parent[e]][e]}parent(e){if(this._isCompound){var t=this._parent[e];if(t!==a)return t}}children(e){if(_(e)&&(e=a),this._isCompound){var t=this._children[e];if(t)return l(t)}else{if(e===a)return this.nodes();if(this.hasNode(e))return[]}}predecessors(e){var t=this._preds[e];if(t)return l(t)}successors(e){var t=this._sucs[e];if(t)return l(t)}neighbors(e){var t=this.predecessors(e);if(t)return P(t,this.successors(e))}isLeaf(e){var t;return this.isDirected()?t=this.successors(e):t=this.neighbors(e),t.length===0}filterNodes(e){var t=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});t.setGraph(this.graph());var s=this;d(this._nodes,function(n,o){e(o)&&t.setNode(o,n)}),d(this._edgeObjs,function(n){t.hasNode(n.v)&&t.hasNode(n.w)&&t.setEdge(n,s.edge(n))});var i={};function r(n){var o=s.parent(n);return o===void 0||t.hasNode(o)?(i[n]=o,o):o in i?i[o]:r(o)}return this._isCompound&&d(t.nodes(),function(n){t.setParent(n,r(n))}),t}setDefaultEdgeLabel(e){return m(e)||(e=c(e)),this._defaultEdgeLabelFn=e,this}edgeCount(){return this._edgeCount}edges(){return p(this._edgeObjs)}setPath(e,t){var s=this,i=arguments;return v(e,function(r,n){return i.length>1?s.setEdge(r,n,t):s.setEdge(r,n),n}),this}setEdge(){var e,t,s,i,r=!1,n=arguments[0];typeof n=="object"&&n!==null&&"v"in n?(e=n.v,t=n.w,s=n.name,arguments.length===2&&(i=arguments[1],r=!0)):(e=n,t=arguments[1],s=arguments[3],arguments.length>2&&(i=arguments[2],r=!0)),e=""+e,t=""+t,_(s)||(s=""+s);var o=f(this._isDirected,e,t,s);if(Object.prototype.hasOwnProperty.call(this._edgeLabels,o))return r&&(this._edgeLabels[o]=i),this;if(!_(s)&&!this._isMultigraph)throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(e),this.setNode(t),this._edgeLabels[o]=r?i:this._defaultEdgeLabelFn(e,t,s);var u=I(this._isDirected,e,t,s);return e=u.v,t=u.w,Object.freeze(u),this._edgeObjs[o]=u,O(this._preds[t],e),O(this._sucs[e],t),this._in[t][o]=u,this._out[e][o]=u,this._edgeCount++,this}edge(e,t,s){var i=arguments.length===1?b(this._isDirected,arguments[0]):f(this._isDirected,e,t,s);return this._edgeLabels[i]}hasEdge(e,t,s){var i=arguments.length===1?b(this._isDirected,arguments[0]):f(this._isDirected,e,t,s);return Object.prototype.hasOwnProperty.call(this._edgeLabels,i)}removeEdge(e,t,s){var i=arguments.length===1?b(this._isDirected,arguments[0]):f(this._isDirected,e,t,s),r=this._edgeObjs[i];return r&&(e=r.v,t=r.w,delete this._edgeLabels[i],delete this._edgeObjs[i],C(this._preds[t],e),C(this._sucs[e],t),delete this._in[t][i],delete this._out[e][i],this._edgeCount--),this}inEdges(e,t){var s=this._in[e];if(s){var i=p(s);return t?g(i,function(r){return r.v===t}):i}}outEdges(e,t){var s=this._out[e];if(s){var i=p(s);return t?g(i,function(r){return r.w===t}):i}}nodeEdges(e,t){var s=this.inEdges(e,t);if(s)return s.concat(this.outEdges(e,t))}}L.prototype._nodeCount=0;L.prototype._edgeCount=0;function O(h,e){h[e]?h[e]++:h[e]=1}function C(h,e){--h[e]||delete h[e]}function f(h,e,t,s){var i=""+e,r=""+t;if(!h&&i>r){var n=i;i=r,r=n}return i+E+r+E+(_(s)?F:s)}function I(h,e,t,s){var i=""+e,r=""+t;if(!h&&i>r){var n=i;i=r,r=n}var o={v:i,w:r};return s&&(o.name=s),o}function b(h,e){return f(h,e.v,e.w,e.name)}export{L as G};
//# sourceMappingURL=graph-B0N4amsr.js.map
