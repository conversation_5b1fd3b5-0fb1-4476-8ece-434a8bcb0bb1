(function(){try{var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new r.Error().stack;e&&(r._sentryDebugIds=r._sentryDebugIds||{},r._sentryDebugIds[e]="07d529b7-5856-45c8-8680-32caf7a1cbaa",r._sentryDebugIdIdentifier="sentry-dbid-07d529b7-5856-45c8-8680-32caf7a1cbaa")}catch{}})();function Ue(r){"@babel/helpers - typeof";return Ue=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ue(r)}function ii(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function Hu(r,e){for(var t=0;t<e.length;t++){var a=e[t];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(r,a.key,a)}}function si(r,e,t){return e&&Hu(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}function Ys(r,e,t){return e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function Tr(r,e){return Gu(r)||Ku(r,e)||Xs(r,e)||Wu()}function Gu(r){if(Array.isArray(r))return r}function Ku(r,e){var t=r==null?null:typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(t!=null){var a=[],n=!0,i=!1,s,o;try{for(t=t.call(r);!(n=(s=t.next()).done)&&(a.push(s.value),!(e&&a.length===e));n=!0);}catch(l){i=!0,o=l}finally{try{!n&&t.return!=null&&t.return()}finally{if(i)throw o}}return a}}function Xs(r,e){if(r){if(typeof r=="string")return Ri(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);if(t==="Object"&&r.constructor&&(t=r.constructor.name),t==="Map"||t==="Set")return Array.from(r);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Ri(r,e)}}function Ri(r,e){(e==null||e>r.length)&&(e=r.length);for(var t=0,a=new Array(e);t<e;t++)a[t]=r[t];return a}function Wu(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Zs(r,e){var t=typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(!t){if(Array.isArray(r)||(t=Xs(r))||e){t&&(r=t);var a=0,n=function(){};return{s:n,n:function(){return a>=r.length?{done:!0}:{done:!1,value:r[a++]}},e:function(l){throw l},f:n}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i=!0,s=!1,o;return{s:function(){t=t.call(r)},n:function(){var l=t.next();return i=l.done,l},e:function(l){s=!0,o=l},f:function(){try{!i&&t.return!=null&&t.return()}finally{if(s)throw o}}}}var Ke=typeof window>"u"?null:window,Oi=Ke?Ke.navigator:null;Ke&&Ke.document;var Uu=Ue(""),Qs=Ue({}),Yu=Ue(function(){}),Xu=typeof HTMLElement>"u"?"undefined":Ue(HTMLElement),wa=function(e){return e&&e.instanceString&&qe(e.instanceString)?e.instanceString():null},ce=function(e){return e!=null&&Ue(e)==Uu},qe=function(e){return e!=null&&Ue(e)===Yu},Oe=function(e){return!yr(e)&&(Array.isArray?Array.isArray(e):e!=null&&e instanceof Array)},Te=function(e){return e!=null&&Ue(e)===Qs&&!Oe(e)&&e.constructor===Object},Zu=function(e){return e!=null&&Ue(e)===Qs},ne=function(e){return e!=null&&Ue(e)===Ue(1)&&!isNaN(e)},Qu=function(e){return ne(e)&&Math.floor(e)===e},_a=function(e){if(Xu!=="undefined")return e!=null&&e instanceof HTMLElement},yr=function(e){return xa(e)||Js(e)},xa=function(e){return wa(e)==="collection"&&e._private.single},Js=function(e){return wa(e)==="collection"&&!e._private.single},oi=function(e){return wa(e)==="core"},_s=function(e){return wa(e)==="stylesheet"},Ju=function(e){return wa(e)==="event"},jr=function(e){return e==null?!0:!!(e===""||e.match(/^\s+$/))},_u=function(e){return typeof HTMLElement>"u"?!1:e instanceof HTMLElement},ju=function(e){return Te(e)&&ne(e.x1)&&ne(e.x2)&&ne(e.y1)&&ne(e.y2)},el=function(e){return Zu(e)&&qe(e.then)},rl=function(){return Oi&&Oi.userAgent.match(/msie|trident|edge/i)},la=function(e,t){t||(t=function(){if(arguments.length===1)return arguments[0];if(arguments.length===0)return"undefined";for(var i=[],s=0;s<arguments.length;s++)i.push(arguments[s]);return i.join("$")});var a=function n(){var i=this,s=arguments,o,l=t.apply(i,s),u=n.cache;return(o=u[l])||(o=u[l]=e.apply(i,s)),o};return a.cache={},a},ui=la(function(r){return r.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()})}),vn=la(function(r){return r.replace(/(-\w)/g,function(e){return e[1].toUpperCase()})}),js=la(function(r,e){return r+e[0].toUpperCase()+e.substring(1)},function(r,e){return r+"$"+e}),Ii=function(e){return jr(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},We="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",tl="rgb[a]?\\(("+We+"[%]?)\\s*,\\s*("+We+"[%]?)\\s*,\\s*("+We+"[%]?)(?:\\s*,\\s*("+We+"))?\\)",al="rgb[a]?\\((?:"+We+"[%]?)\\s*,\\s*(?:"+We+"[%]?)\\s*,\\s*(?:"+We+"[%]?)(?:\\s*,\\s*(?:"+We+"))?\\)",nl="hsl[a]?\\(("+We+")\\s*,\\s*("+We+"[%])\\s*,\\s*("+We+"[%])(?:\\s*,\\s*("+We+"))?\\)",il="hsl[a]?\\((?:"+We+")\\s*,\\s*(?:"+We+"[%])\\s*,\\s*(?:"+We+"[%])(?:\\s*,\\s*(?:"+We+"))?\\)",sl="\\#[0-9a-fA-F]{3}",ol="\\#[0-9a-fA-F]{6}",eo=function(e,t){return e<t?-1:e>t?1:0},ul=function(e,t){return-1*eo(e,t)},pe=Object.assign!=null?Object.assign.bind(Object):function(r){for(var e=arguments,t=1;t<e.length;t++){var a=e[t];if(a!=null)for(var n=Object.keys(a),i=0;i<n.length;i++){var s=n[i];r[s]=a[s]}}return r},ll=function(e){if(!(!(e.length===4||e.length===7)||e[0]!=="#")){var t=e.length===4,a,n,i,s=16;return t?(a=parseInt(e[1]+e[1],s),n=parseInt(e[2]+e[2],s),i=parseInt(e[3]+e[3],s)):(a=parseInt(e[1]+e[2],s),n=parseInt(e[3]+e[4],s),i=parseInt(e[5]+e[6],s)),[a,n,i]}},vl=function(e){var t,a,n,i,s,o,l,u;function v(d,y,p){return p<0&&(p+=1),p>1&&(p-=1),p<1/6?d+(y-d)*6*p:p<1/2?y:p<2/3?d+(y-d)*(2/3-p)*6:d}var f=new RegExp("^"+nl+"$").exec(e);if(f){if(a=parseInt(f[1]),a<0?a=(360- -1*a%360)%360:a>360&&(a=a%360),a/=360,n=parseFloat(f[2]),n<0||n>100||(n=n/100,i=parseFloat(f[3]),i<0||i>100)||(i=i/100,s=f[4],s!==void 0&&(s=parseFloat(s),s<0||s>1)))return;if(n===0)o=l=u=Math.round(i*255);else{var c=i<.5?i*(1+n):i+n-i*n,h=2*i-c;o=Math.round(255*v(h,c,a+1/3)),l=Math.round(255*v(h,c,a)),u=Math.round(255*v(h,c,a-1/3))}t=[o,l,u,s]}return t},fl=function(e){var t,a=new RegExp("^"+tl+"$").exec(e);if(a){t=[];for(var n=[],i=1;i<=3;i++){var s=a[i];if(s[s.length-1]==="%"&&(n[i]=!0),s=parseFloat(s),n[i]&&(s=s/100*255),s<0||s>255)return;t.push(Math.floor(s))}var o=n[1]||n[2]||n[3],l=n[1]&&n[2]&&n[3];if(o&&!l)return;var u=a[4];if(u!==void 0){if(u=parseFloat(u),u<0||u>1)return;t.push(u)}}return t},cl=function(e){return hl[e.toLowerCase()]},dl=function(e){return(Oe(e)?e:null)||cl(e)||ll(e)||fl(e)||vl(e)},hl={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},ro=function(e){for(var t=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(Te(s))throw Error("Tried to set map with object key");i<a.length-1?(t[s]==null&&(t[s]={}),t=t[s]):t[s]=e.value}},to=function(e){for(var t=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(Te(s))throw Error("Tried to get map with object key");if(t=t[s],t==null)return t}return t};function gl(r){var e=typeof r;return r!=null&&(e=="object"||e=="function")}var ct=gl,ta=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function pl(r,e){return e={exports:{}},r(e,e.exports),e.exports}var yl=typeof ta=="object"&&ta&&ta.Object===Object&&ta,ml=yl,bl=typeof self=="object"&&self&&self.Object===Object&&self,wl=ml||bl||Function("return this")(),fn=wl,xl=function(){return fn.Date.now()},Mn=xl,El=/\s/;function Cl(r){for(var e=r.length;e--&&El.test(r.charAt(e)););return e}var Sl=Cl,Tl=/^\s+/;function Dl(r){return r&&r.slice(0,Sl(r)+1).replace(Tl,"")}var kl=Dl,Pl=fn.Symbol,Ot=Pl,ao=Object.prototype,Bl=ao.hasOwnProperty,Ml=ao.toString,Jt=Ot?Ot.toStringTag:void 0;function Ll(r){var e=Bl.call(r,Jt),t=r[Jt];try{r[Jt]=void 0;var a=!0}catch{}var n=Ml.call(r);return a&&(e?r[Jt]=t:delete r[Jt]),n}var Al=Ll,Rl=Object.prototype,Ol=Rl.toString;function Il(r){return Ol.call(r)}var zl=Il,Nl="[object Null]",Fl="[object Undefined]",zi=Ot?Ot.toStringTag:void 0;function Vl(r){return r==null?r===void 0?Fl:Nl:zi&&zi in Object(r)?Al(r):zl(r)}var no=Vl;function ql(r){return r!=null&&typeof r=="object"}var $l=ql,Hl="[object Symbol]";function Gl(r){return typeof r=="symbol"||$l(r)&&no(r)==Hl}var Ea=Gl,Ni=NaN,Kl=/^[-+]0x[0-9a-f]+$/i,Wl=/^0b[01]+$/i,Ul=/^0o[0-7]+$/i,Yl=parseInt;function Xl(r){if(typeof r=="number")return r;if(Ea(r))return Ni;if(ct(r)){var e=typeof r.valueOf=="function"?r.valueOf():r;r=ct(e)?e+"":e}if(typeof r!="string")return r===0?r:+r;r=kl(r);var t=Wl.test(r);return t||Ul.test(r)?Yl(r.slice(2),t?2:8):Kl.test(r)?Ni:+r}var Fi=Xl,Zl="Expected a function",Ql=Math.max,Jl=Math.min;function _l(r,e,t){var a,n,i,s,o,l,u=0,v=!1,f=!1,c=!0;if(typeof r!="function")throw new TypeError(Zl);e=Fi(e)||0,ct(t)&&(v=!!t.leading,f="maxWait"in t,i=f?Ql(Fi(t.maxWait)||0,e):i,c="trailing"in t?!!t.trailing:c);function h(E){var x=a,D=n;return a=n=void 0,u=E,s=r.apply(D,x),s}function d(E){return u=E,o=setTimeout(g,e),v?h(E):s}function y(E){var x=E-l,D=E-u,C=e-x;return f?Jl(C,i-D):C}function p(E){var x=E-l,D=E-u;return l===void 0||x>=e||x<0||f&&D>=i}function g(){var E=Mn();if(p(E))return m(E);o=setTimeout(g,y(E))}function m(E){return o=void 0,c&&a?h(E):(a=n=void 0,s)}function b(){o!==void 0&&clearTimeout(o),u=0,a=l=n=o=void 0}function w(){return o===void 0?s:m(Mn())}function S(){var E=Mn(),x=p(E);if(a=arguments,n=this,l=E,x){if(o===void 0)return d(l);if(f)return clearTimeout(o),o=setTimeout(g,e),h(l)}return o===void 0&&(o=setTimeout(g,e)),s}return S.cancel=b,S.flush=w,S}var cn=_l,Ln=Ke?Ke.performance:null,io=Ln&&Ln.now?function(){return Ln.now()}:function(){return Date.now()},jl=function(){if(Ke){if(Ke.requestAnimationFrame)return function(r){Ke.requestAnimationFrame(r)};if(Ke.mozRequestAnimationFrame)return function(r){Ke.mozRequestAnimationFrame(r)};if(Ke.webkitRequestAnimationFrame)return function(r){Ke.webkitRequestAnimationFrame(r)};if(Ke.msRequestAnimationFrame)return function(r){Ke.msRequestAnimationFrame(r)}}return function(r){r&&setTimeout(function(){r(io())},1e3/60)}}(),ja=function(e){return jl(e)},Hr=io,kt=9261,so=65599,aa=5381,oo=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kt,a=t,n;n=e.next(),!n.done;)a=a*so+n.value|0;return a},va=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kt;return t*so+e|0},fa=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:aa;return(t<<5)+t+e|0},ev=function(e,t){return e*2097152+t},Yr=function(e){return e[0]*2097152+e[1]},La=function(e,t){return[va(e[0],t[0]),fa(e[1],t[1])]},rv=function(e,t){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e[n++]:a.done=!0,a}};return oo(s,t)},dt=function(e,t){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e.charCodeAt(n++):a.done=!0,a}};return oo(s,t)},uo=function(){return tv(arguments)},tv=function(e){for(var t,a=0;a<e.length;a++){var n=e[a];a===0?t=dt(n):t=dt(n,t)}return t},Vi=!0,av=console.warn!=null,nv=console.trace!=null,li=Number.MAX_SAFE_INTEGER||9007199254740991,lo=function(){return!0},en=function(){return!1},qi=function(){return 0},vi=function(){},$e=function(e){throw new Error(e)},vo=function(e){if(e!==void 0)Vi=!!e;else return Vi},Me=function(e){vo()},iv=function(e){return pe({},e)},Or=function(e){return e==null?e:Oe(e)?e.slice():Te(e)?iv(e):e},sv=function(e){return e.slice()},fo=function(e,t){for(t=e="";e++<36;t+=e*51&52?(e^15?8^Math.random()*(e^20?16:4):4).toString(16):"-");return t},ov={},co=function(){return ov},rr=function(e){var t=Object.keys(e);return function(a){for(var n={},i=0;i<t.length;i++){var s=t[i],o=a==null?void 0:a[s];n[s]=o===void 0?e[s]:o}return n}},et=function(e,t,a){for(var n=e.length-1;n>=0;n--)e[n]===t&&e.splice(n,1)},fi=function(e){e.splice(0,e.length)},uv=function(e,t){for(var a=0;a<t.length;a++){var n=t[a];e.push(n)}},kr=function(e,t,a){return a&&(t=js(a,t)),e[t]},Zr=function(e,t,a,n){a&&(t=js(a,t)),e[t]=n},lv=function(){function r(){ii(this,r),this._obj={}}return si(r,[{key:"set",value:function(t,a){return this._obj[t]=a,this}},{key:"delete",value:function(t){return this._obj[t]=void 0,this}},{key:"clear",value:function(){this._obj={}}},{key:"has",value:function(t){return this._obj[t]!==void 0}},{key:"get",value:function(t){return this._obj[t]}}]),r}(),Ir=typeof Map<"u"?Map:lv,vv="undefined",fv=function(){function r(e){if(ii(this,r),this._obj=Object.create(null),this.size=0,e!=null){var t;e.instanceString!=null&&e.instanceString()===this.instanceString()?t=e.toArray():t=e;for(var a=0;a<t.length;a++)this.add(t[a])}}return si(r,[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(t){var a=this._obj;a[t]!==1&&(a[t]=1,this.size++)}},{key:"delete",value:function(t){var a=this._obj;a[t]===1&&(a[t]=0,this.size--)}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(t){return this._obj[t]===1}},{key:"toArray",value:function(){var t=this;return Object.keys(this._obj).filter(function(a){return t.has(a)})}},{key:"forEach",value:function(t,a){return this.toArray().forEach(t,a)}}]),r}(),Vt=(typeof Set>"u"?"undefined":Ue(Set))!==vv?Set:fv,dn=function(e,t){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(e===void 0||t===void 0||!oi(e)){$e("An element must have a core reference and parameters set");return}var n=t.group;if(n==null&&(t.data&&t.data.source!=null&&t.data.target!=null?n="edges":n="nodes"),n!=="nodes"&&n!=="edges"){$e("An element must be of type `nodes` or `edges`; you specified `"+n+"`");return}this.length=1,this[0]=this;var i=this._private={cy:e,single:!0,data:t.data||{},position:t.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:n,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!t.selected,selectable:t.selectable===void 0?!0:!!t.selectable,locked:!!t.locked,grabbed:!1,grabbable:t.grabbable===void 0?!0:!!t.grabbable,pannable:t.pannable===void 0?n==="edges":!!t.pannable,active:!1,classes:new Vt,animation:{current:[],queue:[]},rscratch:{},scratch:t.scratch||{},edges:[],children:[],parent:t.parent&&t.parent.isNode()?t.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(i.position.x==null&&(i.position.x=0),i.position.y==null&&(i.position.y=0),t.renderedPosition){var s=t.renderedPosition,o=e.pan(),l=e.zoom();i.position={x:(s.x-o.x)/l,y:(s.y-o.y)/l}}var u=[];Oe(t.classes)?u=t.classes:ce(t.classes)&&(u=t.classes.split(/\s+/));for(var v=0,f=u.length;v<f;v++){var c=u[v];!c||c===""||i.classes.add(c)}this.createEmitter();var h=t.style||t.css;h&&(Me("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(h)),(a===void 0||a)&&this.restore()},$i=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(a,n,i){var s;Te(a)&&!yr(a)&&(s=a,a=s.roots||s.root,n=s.visit,i=s.directed),i=arguments.length===2&&!qe(n)?n:i,n=qe(n)?n:function(){};for(var o=this._private.cy,l=a=ce(a)?this.filter(a):a,u=[],v=[],f={},c={},h={},d=0,y,p=this.byGroup(),g=p.nodes,m=p.edges,b=0;b<l.length;b++){var w=l[b],S=w.id();w.isNode()&&(u.unshift(w),e.bfs&&(h[S]=!0,v.push(w)),c[S]=0)}for(var E=function(){var L=e.bfs?u.shift():u.pop(),k=L.id();if(e.dfs){if(h[k])return"continue";h[k]=!0,v.push(L)}var O=c[k],A=f[k],R=A!=null?A.source():null,I=A!=null?A.target():null,V=A==null?void 0:L.same(R)?I[0]:R[0],G=void 0;if(G=n(L,A,V,d++,O),G===!0)return y=L,"break";if(G===!1)return"break";for(var F=L.connectedEdges().filter(function(_){return(!i||_.source().same(L))&&m.has(_)}),q=0;q<F.length;q++){var Y=F[q],Q=Y.connectedNodes().filter(function(_){return!_.same(L)&&g.has(_)}),J=Q.id();Q.length!==0&&!h[J]&&(Q=Q[0],u.push(Q),e.bfs&&(h[J]=!0,v.push(Q)),f[J]=Y,c[J]=c[k]+1)}};u.length!==0;){var x=E();if(x!=="continue"&&x==="break")break}for(var D=o.collection(),C=0;C<v.length;C++){var M=v[C],P=f[M.id()];P!=null&&D.push(P),D.push(M)}return{path:o.collection(D),found:o.collection(y)}}},ca={breadthFirstSearch:$i({bfs:!0}),depthFirstSearch:$i({dfs:!0})};ca.bfs=ca.breadthFirstSearch;ca.dfs=ca.depthFirstSearch;var cv=pl(function(r,e){(function(){var t,a,n,i,s,o,l,u,v,f,c,h,d,y,p;n=Math.floor,f=Math.min,a=function(g,m){return g<m?-1:g>m?1:0},v=function(g,m,b,w,S){var E;if(b==null&&(b=0),S==null&&(S=a),b<0)throw new Error("lo must be non-negative");for(w==null&&(w=g.length);b<w;)E=n((b+w)/2),S(m,g[E])<0?w=E:b=E+1;return[].splice.apply(g,[b,b-b].concat(m)),m},o=function(g,m,b){return b==null&&(b=a),g.push(m),y(g,0,g.length-1,b)},s=function(g,m){var b,w;return m==null&&(m=a),b=g.pop(),g.length?(w=g[0],g[0]=b,p(g,0,m)):w=b,w},u=function(g,m,b){var w;return b==null&&(b=a),w=g[0],g[0]=m,p(g,0,b),w},l=function(g,m,b){var w;return b==null&&(b=a),g.length&&b(g[0],m)<0&&(w=[g[0],m],m=w[0],g[0]=w[1],p(g,0,b)),m},i=function(g,m){var b,w,S,E,x,D;for(m==null&&(m=a),E=(function(){D=[];for(var C=0,M=n(g.length/2);0<=M?C<M:C>M;0<=M?C++:C--)D.push(C);return D}).apply(this).reverse(),x=[],w=0,S=E.length;w<S;w++)b=E[w],x.push(p(g,b,m));return x},d=function(g,m,b){var w;if(b==null&&(b=a),w=g.indexOf(m),w!==-1)return y(g,0,w,b),p(g,w,b)},c=function(g,m,b){var w,S,E,x,D;if(b==null&&(b=a),S=g.slice(0,m),!S.length)return S;for(i(S,b),D=g.slice(m),E=0,x=D.length;E<x;E++)w=D[E],l(S,w,b);return S.sort(b).reverse()},h=function(g,m,b){var w,S,E,x,D,C,M,P,B;if(b==null&&(b=a),m*10<=g.length){if(E=g.slice(0,m).sort(b),!E.length)return E;for(S=E[E.length-1],M=g.slice(m),x=0,C=M.length;x<C;x++)w=M[x],b(w,S)<0&&(v(E,w,0,null,b),E.pop(),S=E[E.length-1]);return E}for(i(g,b),B=[],D=0,P=f(m,g.length);0<=P?D<P:D>P;0<=P?++D:--D)B.push(s(g,b));return B},y=function(g,m,b,w){var S,E,x;for(w==null&&(w=a),S=g[b];b>m;){if(x=b-1>>1,E=g[x],w(S,E)<0){g[b]=E,b=x;continue}break}return g[b]=S},p=function(g,m,b){var w,S,E,x,D;for(b==null&&(b=a),S=g.length,D=m,E=g[m],w=2*m+1;w<S;)x=w+1,x<S&&!(b(g[w],g[x])<0)&&(w=x),g[m]=g[w],m=w,w=2*m+1;return g[m]=E,y(g,D,m,b)},t=function(){g.push=o,g.pop=s,g.replace=u,g.pushpop=l,g.heapify=i,g.updateItem=d,g.nlargest=c,g.nsmallest=h;function g(m){this.cmp=m??a,this.nodes=[]}return g.prototype.push=function(m){return o(this.nodes,m,this.cmp)},g.prototype.pop=function(){return s(this.nodes,this.cmp)},g.prototype.peek=function(){return this.nodes[0]},g.prototype.contains=function(m){return this.nodes.indexOf(m)!==-1},g.prototype.replace=function(m){return u(this.nodes,m,this.cmp)},g.prototype.pushpop=function(m){return l(this.nodes,m,this.cmp)},g.prototype.heapify=function(){return i(this.nodes,this.cmp)},g.prototype.updateItem=function(m){return d(this.nodes,m,this.cmp)},g.prototype.clear=function(){return this.nodes=[]},g.prototype.empty=function(){return this.nodes.length===0},g.prototype.size=function(){return this.nodes.length},g.prototype.clone=function(){var m;return m=new g,m.nodes=this.nodes.slice(0),m},g.prototype.toArray=function(){return this.nodes.slice(0)},g.prototype.insert=g.prototype.push,g.prototype.top=g.prototype.peek,g.prototype.front=g.prototype.peek,g.prototype.has=g.prototype.contains,g.prototype.copy=g.prototype.clone,g}(),function(g,m){return r.exports=m()}(this,function(){return t})}).call(ta)}),Ca=cv,dv=rr({root:null,weight:function(e){return 1},directed:!1}),hv={dijkstra:function(e){if(!Te(e)){var t=arguments;e={root:t[0],weight:t[1],directed:t[2]}}var a=dv(e),n=a.root,i=a.weight,s=a.directed,o=this,l=i,u=ce(n)?this.filter(n)[0]:n[0],v={},f={},c={},h=this.byGroup(),d=h.nodes,y=h.edges;y.unmergeBy(function(O){return O.isLoop()});for(var p=function(A){return v[A.id()]},g=function(A,R){v[A.id()]=R,m.updateItem(A)},m=new Ca(function(O,A){return p(O)-p(A)}),b=0;b<d.length;b++){var w=d[b];v[w.id()]=w.same(u)?0:1/0,m.push(w)}for(var S=function(A,R){for(var I=(s?A.edgesTo(R):A.edgesWith(R)).intersect(y),V=1/0,G,F=0;F<I.length;F++){var q=I[F],Y=l(q);(Y<V||!G)&&(V=Y,G=q)}return{edge:G,dist:V}};m.size()>0;){var E=m.pop(),x=p(E),D=E.id();if(c[D]=x,x!==1/0)for(var C=E.neighborhood().intersect(d),M=0;M<C.length;M++){var P=C[M],B=P.id(),L=S(E,P),k=x+L.dist;k<p(P)&&(g(P,k),f[B]={node:E,edge:L.edge})}}return{distanceTo:function(A){var R=ce(A)?d.filter(A)[0]:A[0];return c[R.id()]},pathTo:function(A){var R=ce(A)?d.filter(A)[0]:A[0],I=[],V=R,G=V.id();if(R.length>0)for(I.unshift(R);f[G];){var F=f[G];I.unshift(F.edge),I.unshift(F.node),V=F.node,G=V.id()}return o.spawn(I)}}}},gv={kruskal:function(e){e=e||function(b){return 1};for(var t=this.byGroup(),a=t.nodes,n=t.edges,i=a.length,s=new Array(i),o=a,l=function(w){for(var S=0;S<s.length;S++){var E=s[S];if(E.has(w))return S}},u=0;u<i;u++)s[u]=this.spawn(a[u]);for(var v=n.sort(function(b,w){return e(b)-e(w)}),f=0;f<v.length;f++){var c=v[f],h=c.source()[0],d=c.target()[0],y=l(h),p=l(d),g=s[y],m=s[p];y!==p&&(o.merge(c),g.merge(m),s.splice(p,1))}return o}},pv=rr({root:null,goal:null,weight:function(e){return 1},heuristic:function(e){return 0},directed:!1}),yv={aStar:function(e){var t=this.cy(),a=pv(e),n=a.root,i=a.goal,s=a.heuristic,o=a.directed,l=a.weight;n=t.collection(n)[0],i=t.collection(i)[0];var u=n.id(),v=i.id(),f={},c={},h={},d=new Ca(function(G,F){return c[G.id()]-c[F.id()]}),y=new Vt,p={},g={},m=function(F,q){d.push(F),y.add(q)},b,w,S=function(){b=d.pop(),w=b.id(),y.delete(w)},E=function(F){return y.has(F)};m(n,u),f[u]=0,c[u]=s(n);for(var x=0;d.size()>0;){if(S(),x++,w===v){for(var D=[],C=i,M=v,P=g[M];D.unshift(C),P!=null&&D.unshift(P),C=p[M],C!=null;)M=C.id(),P=g[M];return{found:!0,distance:f[w],path:this.spawn(D),steps:x}}h[w]=!0;for(var B=b._private.edges,L=0;L<B.length;L++){var k=B[L];if(this.hasElementWithId(k.id())&&!(o&&k.data("source")!==w)){var O=k.source(),A=k.target(),R=O.id()!==w?O:A,I=R.id();if(this.hasElementWithId(I)&&!h[I]){var V=f[w]+l(k);if(!E(I)){f[I]=V,c[I]=V+s(R),m(R,I),p[I]=b,g[I]=k;continue}V<f[I]&&(f[I]=V,c[I]=V+s(R),p[I]=b,g[I]=k)}}}}return{found:!1,distance:void 0,path:void 0,steps:x}}},mv=rr({weight:function(e){return 1},directed:!1}),bv={floydWarshall:function(e){for(var t=this.cy(),a=mv(e),n=a.weight,i=a.directed,s=n,o=this.byGroup(),l=o.nodes,u=o.edges,v=l.length,f=v*v,c=function(Y){return l.indexOf(Y)},h=function(Y){return l[Y]},d=new Array(f),y=0;y<f;y++){var p=y%v,g=(y-p)/v;g===p?d[y]=0:d[y]=1/0}for(var m=new Array(f),b=new Array(f),w=0;w<u.length;w++){var S=u[w],E=S.source()[0],x=S.target()[0];if(E!==x){var D=c(E),C=c(x),M=D*v+C,P=s(S);if(d[M]>P&&(d[M]=P,m[M]=C,b[M]=S),!i){var B=C*v+D;!i&&d[B]>P&&(d[B]=P,m[B]=D,b[B]=S)}}}for(var L=0;L<v;L++)for(var k=0;k<v;k++)for(var O=k*v+L,A=0;A<v;A++){var R=k*v+A,I=L*v+A;d[O]+d[I]<d[R]&&(d[R]=d[O]+d[I],m[R]=m[O])}var V=function(Y){return(ce(Y)?t.filter(Y):Y)[0]},G=function(Y){return c(V(Y))},F={distance:function(Y,Q){var J=G(Y),_=G(Q);return d[J*v+_]},path:function(Y,Q){var J=G(Y),_=G(Q),j=h(J);if(J===_)return j.collection();if(m[J*v+_]==null)return t.collection();var W=t.collection(),z=J,K;for(W.merge(j);J!==_;)z=J,J=m[J*v+_],K=b[z*v+J],W.merge(K),W.merge(h(J));return W}};return F}},wv=rr({weight:function(e){return 1},directed:!1,root:null}),xv={bellmanFord:function(e){var t=this,a=wv(e),n=a.weight,i=a.directed,s=a.root,o=n,l=this,u=this.cy(),v=this.byGroup(),f=v.edges,c=v.nodes,h=c.length,d=new Ir,y=!1,p=[];s=u.collection(s)[0],f.unmergeBy(function(le){return le.isLoop()});for(var g=f.length,m=function(oe){var de=d.get(oe.id());return de||(de={},d.set(oe.id(),de)),de},b=function(oe){return(ce(oe)?u.$(oe):oe)[0]},w=function(oe){return m(b(oe)).dist},S=function(oe){for(var de=arguments.length>1&&arguments[1]!==void 0?arguments[1]:s,Le=b(oe),Ce=[],xe=Le;;){if(xe==null)return t.spawn();var Ae=m(xe),Ee=Ae.edge,Pe=Ae.pred;if(Ce.unshift(xe[0]),xe.same(de)&&Ce.length>0)break;Ee!=null&&Ce.unshift(Ee),xe=Pe}return l.spawn(Ce)},E=0;E<h;E++){var x=c[E],D=m(x);x.same(s)?D.dist=0:D.dist=1/0,D.pred=null,D.edge=null}for(var C=!1,M=function(oe,de,Le,Ce,xe,Ae){var Ee=Ce.dist+Ae;Ee<xe.dist&&!Le.same(Ce.edge)&&(xe.dist=Ee,xe.pred=oe,xe.edge=Le,C=!0)},P=1;P<h;P++){C=!1;for(var B=0;B<g;B++){var L=f[B],k=L.source(),O=L.target(),A=o(L),R=m(k),I=m(O);M(k,O,L,R,I,A),i||M(O,k,L,I,R,A)}if(!C)break}if(C)for(var V=[],G=0;G<g;G++){var F=f[G],q=F.source(),Y=F.target(),Q=o(F),J=m(q).dist,_=m(Y).dist;if(J+Q<_||!i&&_+Q<J)if(y||(Me("Graph contains a negative weight cycle for Bellman-Ford"),y=!0),e.findNegativeWeightCycles!==!1){var j=[];J+Q<_&&j.push(q),!i&&_+Q<J&&j.push(Y);for(var W=j.length,z=0;z<W;z++){var K=j[z],X=[K];X.push(m(K).edge);for(var ae=m(K).pred;X.indexOf(ae)===-1;)X.push(ae),X.push(m(ae).edge),ae=m(ae).pred;X=X.slice(X.indexOf(ae));for(var he=X[0].id(),te=0,re=2;re<X.length;re+=2)X[re].id()<he&&(he=X[re].id(),te=re);X=X.slice(te).concat(X.slice(0,te)),X.push(X[0]);var ve=X.map(function(le){return le.id()}).join(",");V.indexOf(ve)===-1&&(p.push(l.spawn(X)),V.push(ve))}}else break}return{distanceTo:w,pathTo:S,hasNegativeWeightCycle:y,negativeWeightCycles:p}}},Ev=Math.sqrt(2),Cv=function(e,t,a){a.length===0&&$e("Karger-Stein must be run on a connected (sub)graph");for(var n=a[e],i=n[1],s=n[2],o=t[i],l=t[s],u=a,v=u.length-1;v>=0;v--){var f=u[v],c=f[1],h=f[2];(t[c]===o&&t[h]===l||t[c]===l&&t[h]===o)&&u.splice(v,1)}for(var d=0;d<u.length;d++){var y=u[d];y[1]===l?(u[d]=y.slice(),u[d][1]=o):y[2]===l&&(u[d]=y.slice(),u[d][2]=o)}for(var p=0;p<t.length;p++)t[p]===l&&(t[p]=o);return u},An=function(e,t,a,n){for(;a>n;){var i=Math.floor(Math.random()*t.length);t=Cv(i,e,t),a--}return t},Sv={kargerStein:function(){var e=this,t=this.byGroup(),a=t.nodes,n=t.edges;n.unmergeBy(function(I){return I.isLoop()});var i=a.length,s=n.length,o=Math.ceil(Math.pow(Math.log(i)/Math.LN2,2)),l=Math.floor(i/Ev);if(i<2){$e("At least 2 nodes are required for Karger-Stein algorithm");return}for(var u=[],v=0;v<s;v++){var f=n[v];u.push([v,a.indexOf(f.source()),a.indexOf(f.target())])}for(var c=1/0,h=[],d=new Array(i),y=new Array(i),p=new Array(i),g=function(V,G){for(var F=0;F<i;F++)G[F]=V[F]},m=0;m<=o;m++){for(var b=0;b<i;b++)y[b]=b;var w=An(y,u.slice(),i,l),S=w.slice();g(y,p);var E=An(y,w,l,2),x=An(p,S,l,2);E.length<=x.length&&E.length<c?(c=E.length,h=E,g(y,d)):x.length<=E.length&&x.length<c&&(c=x.length,h=x,g(p,d))}for(var D=this.spawn(h.map(function(I){return n[I[0]]})),C=this.spawn(),M=this.spawn(),P=d[0],B=0;B<d.length;B++){var L=d[B],k=a[B];L===P?C.merge(k):M.merge(k)}var O=function(V){var G=e.spawn();return V.forEach(function(F){G.merge(F),F.connectedEdges().forEach(function(q){e.contains(q)&&!D.contains(q)&&G.merge(q)})}),G},A=[O(C),O(M)],R={cut:D,components:A,partition1:C,partition2:M};return R}},Tv=function(e){return{x:e.x,y:e.y}},hn=function(e,t,a){return{x:e.x*t+a.x,y:e.y*t+a.y}},ho=function(e,t,a){return{x:(e.x-a.x)/t,y:(e.y-a.y)/t}},Pt=function(e){return{x:e[0],y:e[1]}},Dv=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=1/0,i=t;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.min(s,n))}return n},kv=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=-1/0,i=t;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.max(s,n))}return n},Pv=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=0,i=0,s=t;s<a;s++){var o=e[s];isFinite(o)&&(n+=o,i++)}return n/i},Bv=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0;n?e=e.slice(t,a):(a<e.length&&e.splice(a,e.length-a),t>0&&e.splice(0,t));for(var o=0,l=e.length-1;l>=0;l--){var u=e[l];s?isFinite(u)||(e[l]=-1/0,o++):e.splice(l,1)}i&&e.sort(function(c,h){return c-h});var v=e.length,f=Math.floor(v/2);return v%2!==0?e[f+1+o]:(e[f-1+o]+e[f+o])/2},Mv=function(e){return Math.PI*e/180},Aa=function(e,t){return Math.atan2(t,e)-Math.PI/2},ci=Math.log2||function(r){return Math.log(r)/Math.log(2)},go=function(e){return e>0?1:e<0?-1:0},ht=function(e,t){return Math.sqrt(ot(e,t))},ot=function(e,t){var a=t.x-e.x,n=t.y-e.y;return a*a+n*n},Lv=function(e){for(var t=e.length,a=0,n=0;n<t;n++)a+=e[n];for(var i=0;i<t;i++)e[i]=e[i]/a;return e},Je=function(e,t,a,n){return(1-n)*(1-n)*e+2*(1-n)*n*t+n*n*a},Mt=function(e,t,a,n){return{x:Je(e.x,t.x,a.x,n),y:Je(e.y,t.y,a.y,n)}},Av=function(e,t,a,n){var i={x:t.x-e.x,y:t.y-e.y},s=ht(e,t),o={x:i.x/s,y:i.y/s};return a=a??0,n=n??a*s,{x:e.x+o.x*n,y:e.y+o.y*n}},da=function(e,t,a){return Math.max(e,Math.min(a,t))},hr=function(e){if(e==null)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(e.x1!=null&&e.y1!=null){if(e.x2!=null&&e.y2!=null&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(e.w!=null&&e.h!=null&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},Rv=function(e){return{x1:e.x1,x2:e.x2,w:e.w,y1:e.y1,y2:e.y2,h:e.h}},Ov=function(e){e.x1=1/0,e.y1=1/0,e.x2=-1/0,e.y2=-1/0,e.w=0,e.h=0},Iv=function(e,t,a){return{x1:e.x1+t,x2:e.x2+t,y1:e.y1+a,y2:e.y2+a,w:e.w,h:e.h}},po=function(e,t){e.x1=Math.min(e.x1,t.x1),e.x2=Math.max(e.x2,t.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,t.y1),e.y2=Math.max(e.y2,t.y2),e.h=e.y2-e.y1},zv=function(e,t,a){e.x1=Math.min(e.x1,t),e.x2=Math.max(e.x2,t),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,a),e.y2=Math.max(e.y2,a),e.h=e.y2-e.y1},Ga=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.x1-=t,e.x2+=t,e.y1-=t,e.y2+=t,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},Ka=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[0],a,n,i,s;if(t.length===1)a=n=i=s=t[0];else if(t.length===2)a=i=t[0],s=n=t[1];else if(t.length===4){var o=Tr(t,4);a=o[0],n=o[1],i=o[2],s=o[3]}return e.x1-=s,e.x2+=n,e.y1-=a,e.y2+=i,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},Hi=function(e,t){e.x1=t.x1,e.y1=t.y1,e.x2=t.x2,e.y2=t.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},di=function(e,t){return!(e.x1>t.x2||t.x1>e.x2||e.x2<t.x1||t.x2<e.x1||e.y2<t.y1||t.y2<e.y1||e.y1>t.y2||t.y1>e.y2)},It=function(e,t,a){return e.x1<=t&&t<=e.x2&&e.y1<=a&&a<=e.y2},Nv=function(e,t){return It(e,t.x,t.y)},yo=function(e,t){return It(e,t.x1,t.y1)&&It(e,t.x2,t.y2)},mo=function(e,t,a,n,i,s,o){var l=arguments.length>7&&arguments[7]!==void 0?arguments[7]:"auto",u=l==="auto"?gt(i,s):l,v=i/2,f=s/2;u=Math.min(u,v,f);var c=u!==v,h=u!==f,d;if(c){var y=a-v+u-o,p=n-f-o,g=a+v-u+o,m=p;if(d=Qr(e,t,a,n,y,p,g,m,!1),d.length>0)return d}if(h){var b=a+v+o,w=n-f+u-o,S=b,E=n+f-u+o;if(d=Qr(e,t,a,n,b,w,S,E,!1),d.length>0)return d}if(c){var x=a-v+u-o,D=n+f+o,C=a+v-u+o,M=D;if(d=Qr(e,t,a,n,x,D,C,M,!1),d.length>0)return d}if(h){var P=a-v-o,B=n-f+u-o,L=P,k=n+f-u+o;if(d=Qr(e,t,a,n,P,B,L,k,!1),d.length>0)return d}var O;{var A=a-v+u,R=n-f+u;if(O=na(e,t,a,n,A,R,u+o),O.length>0&&O[0]<=A&&O[1]<=R)return[O[0],O[1]]}{var I=a+v-u,V=n-f+u;if(O=na(e,t,a,n,I,V,u+o),O.length>0&&O[0]>=I&&O[1]<=V)return[O[0],O[1]]}{var G=a+v-u,F=n+f-u;if(O=na(e,t,a,n,G,F,u+o),O.length>0&&O[0]>=G&&O[1]>=F)return[O[0],O[1]]}{var q=a-v+u,Y=n+f-u;if(O=na(e,t,a,n,q,Y,u+o),O.length>0&&O[0]<=q&&O[1]>=Y)return[O[0],O[1]]}return[]},Fv=function(e,t,a,n,i,s,o){var l=o,u=Math.min(a,i),v=Math.max(a,i),f=Math.min(n,s),c=Math.max(n,s);return u-l<=e&&e<=v+l&&f-l<=t&&t<=c+l},Vv=function(e,t,a,n,i,s,o,l,u){var v={x1:Math.min(a,o,i)-u,x2:Math.max(a,o,i)+u,y1:Math.min(n,l,s)-u,y2:Math.max(n,l,s)+u};return!(e<v.x1||e>v.x2||t<v.y1||t>v.y2)},qv=function(e,t,a,n){a-=n;var i=t*t-4*e*a;if(i<0)return[];var s=Math.sqrt(i),o=2*e,l=(-t+s)/o,u=(-t-s)/o;return[l,u]},$v=function(e,t,a,n,i){var s=1e-5;e===0&&(e=s),t/=e,a/=e,n/=e;var o,l,u,v,f,c,h,d;if(l=(3*a-t*t)/9,u=-(27*n)+t*(9*a-2*(t*t)),u/=54,o=l*l*l+u*u,i[1]=0,h=t/3,o>0){f=u+Math.sqrt(o),f=f<0?-Math.pow(-f,1/3):Math.pow(f,1/3),c=u-Math.sqrt(o),c=c<0?-Math.pow(-c,1/3):Math.pow(c,1/3),i[0]=-h+f+c,h+=(f+c)/2,i[4]=i[2]=-h,h=Math.sqrt(3)*(-c+f)/2,i[3]=h,i[5]=-h;return}if(i[5]=i[3]=0,o===0){d=u<0?-Math.pow(-u,1/3):Math.pow(u,1/3),i[0]=-h+2*d,i[4]=i[2]=-(d+h);return}l=-l,v=l*l*l,v=Math.acos(u/Math.sqrt(v)),d=2*Math.sqrt(l),i[0]=-h+d*Math.cos(v/3),i[2]=-h+d*Math.cos((v+2*Math.PI)/3),i[4]=-h+d*Math.cos((v+4*Math.PI)/3)},Hv=function(e,t,a,n,i,s,o,l){var u=1*a*a-4*a*i+2*a*o+4*i*i-4*i*o+o*o+n*n-4*n*s+2*n*l+4*s*s-4*s*l+l*l,v=1*9*a*i-3*a*a-3*a*o-6*i*i+3*i*o+9*n*s-3*n*n-3*n*l-6*s*s+3*s*l,f=1*3*a*a-6*a*i+a*o-a*e+2*i*i+2*i*e-o*e+3*n*n-6*n*s+n*l-n*t+2*s*s+2*s*t-l*t,c=1*a*i-a*a+a*e-i*e+n*s-n*n+n*t-s*t,h=[];$v(u,v,f,c,h);for(var d=1e-7,y=[],p=0;p<6;p+=2)Math.abs(h[p+1])<d&&h[p]>=0&&h[p]<=1&&y.push(h[p]);y.push(1),y.push(0);for(var g=-1,m,b,w,S=0;S<y.length;S++)m=Math.pow(1-y[S],2)*a+2*(1-y[S])*y[S]*i+y[S]*y[S]*o,b=Math.pow(1-y[S],2)*n+2*(1-y[S])*y[S]*s+y[S]*y[S]*l,w=Math.pow(m-e,2)+Math.pow(b-t,2),g>=0?w<g&&(g=w):g=w;return g},Gv=function(e,t,a,n,i,s){var o=[e-a,t-n],l=[i-a,s-n],u=l[0]*l[0]+l[1]*l[1],v=o[0]*o[0]+o[1]*o[1],f=o[0]*l[0]+o[1]*l[1],c=f*f/u;return f<0?v:c>u?(e-i)*(e-i)+(t-s)*(t-s):v-c},dr=function(e,t,a){for(var n,i,s,o,l,u=0,v=0;v<a.length/2;v++)if(n=a[v*2],i=a[v*2+1],v+1<a.length/2?(s=a[(v+1)*2],o=a[(v+1)*2+1]):(s=a[(v+1-a.length/2)*2],o=a[(v+1-a.length/2)*2+1]),!(n==e&&s==e))if(n>=e&&e>=s||n<=e&&e<=s)l=(e-n)/(s-n)*(o-i)+i,l>t&&u++;else continue;return u%2!==0},Gr=function(e,t,a,n,i,s,o,l,u){var v=new Array(a.length),f;l[0]!=null?(f=Math.atan(l[1]/l[0]),l[0]<0?f=f+Math.PI/2:f=-f-Math.PI/2):f=l;for(var c=Math.cos(-f),h=Math.sin(-f),d=0;d<v.length/2;d++)v[d*2]=s/2*(a[d*2]*c-a[d*2+1]*h),v[d*2+1]=o/2*(a[d*2+1]*c+a[d*2]*h),v[d*2]+=n,v[d*2+1]+=i;var y;if(u>0){var p=tn(v,-u);y=rn(p)}else y=v;return dr(e,t,y)},Kv=function(e,t,a,n,i,s,o,l){for(var u=new Array(a.length*2),v=0;v<l.length;v++){var f=l[v];u[v*4+0]=f.startX,u[v*4+1]=f.startY,u[v*4+2]=f.stopX,u[v*4+3]=f.stopY;var c=Math.pow(f.cx-e,2)+Math.pow(f.cy-t,2);if(c<=Math.pow(f.radius,2))return!0}return dr(e,t,u)},rn=function(e){for(var t=new Array(e.length/2),a,n,i,s,o,l,u,v,f=0;f<e.length/4;f++){a=e[f*4],n=e[f*4+1],i=e[f*4+2],s=e[f*4+3],f<e.length/4-1?(o=e[(f+1)*4],l=e[(f+1)*4+1],u=e[(f+1)*4+2],v=e[(f+1)*4+3]):(o=e[0],l=e[1],u=e[2],v=e[3]);var c=Qr(a,n,i,s,o,l,u,v,!0);t[f*2]=c[0],t[f*2+1]=c[1]}return t},tn=function(e,t){for(var a=new Array(e.length*2),n,i,s,o,l=0;l<e.length/2;l++){n=e[l*2],i=e[l*2+1],l<e.length/2-1?(s=e[(l+1)*2],o=e[(l+1)*2+1]):(s=e[0],o=e[1]);var u=o-i,v=-(s-n),f=Math.sqrt(u*u+v*v),c=u/f,h=v/f;a[l*4]=n+c*t,a[l*4+1]=i+h*t,a[l*4+2]=s+c*t,a[l*4+3]=o+h*t}return a},Wv=function(e,t,a,n,i,s){var o=a-e,l=n-t;o/=i,l/=s;var u=Math.sqrt(o*o+l*l),v=u-1;if(v<0)return[];var f=v/u;return[(a-e)*f+e,(n-t)*f+t]},ft=function(e,t,a,n,i,s,o){return e-=i,t-=s,e/=a/2+o,t/=n/2+o,e*e+t*t<=1},na=function(e,t,a,n,i,s,o){var l=[a-e,n-t],u=[e-i,t-s],v=l[0]*l[0]+l[1]*l[1],f=2*(u[0]*l[0]+u[1]*l[1]),c=u[0]*u[0]+u[1]*u[1]-o*o,h=f*f-4*v*c;if(h<0)return[];var d=(-f+Math.sqrt(h))/(2*v),y=(-f-Math.sqrt(h))/(2*v),p=Math.min(d,y),g=Math.max(d,y),m=[];if(p>=0&&p<=1&&m.push(p),g>=0&&g<=1&&m.push(g),m.length===0)return[];var b=m[0]*l[0]+e,w=m[0]*l[1]+t;if(m.length>1){if(m[0]==m[1])return[b,w];var S=m[1]*l[0]+e,E=m[1]*l[1]+t;return[b,w,S,E]}else return[b,w]},Rn=function(e,t,a){return t<=e&&e<=a||a<=e&&e<=t?e:e<=t&&t<=a||a<=t&&t<=e?t:a},Qr=function(e,t,a,n,i,s,o,l,u){var v=e-i,f=a-e,c=o-i,h=t-s,d=n-t,y=l-s,p=c*h-y*v,g=f*h-d*v,m=y*f-c*d;if(m!==0){var b=p/m,w=g/m,S=.001,E=0-S,x=1+S;return E<=b&&b<=x&&E<=w&&w<=x?[e+b*f,t+b*d]:u?[e+b*f,t+b*d]:[]}else return p===0||g===0?Rn(e,a,o)===o?[o,l]:Rn(e,a,i)===i?[i,s]:Rn(i,o,a)===a?[a,n]:[]:[]},ha=function(e,t,a,n,i,s,o,l){var u=[],v,f=new Array(a.length),c=!0;s==null&&(c=!1);var h;if(c){for(var d=0;d<f.length/2;d++)f[d*2]=a[d*2]*s+n,f[d*2+1]=a[d*2+1]*o+i;if(l>0){var y=tn(f,-l);h=rn(y)}else h=f}else h=a;for(var p,g,m,b,w=0;w<h.length/2;w++)p=h[w*2],g=h[w*2+1],w<h.length/2-1?(m=h[(w+1)*2],b=h[(w+1)*2+1]):(m=h[0],b=h[1]),v=Qr(e,t,n,i,p,g,m,b),v.length!==0&&u.push(v[0],v[1]);return u},Uv=function(e,t,a,n,i,s,o,l,u){var v=[],f,c=new Array(a.length*2);u.forEach(function(m,b){b===0?(c[c.length-2]=m.startX,c[c.length-1]=m.startY):(c[b*4-2]=m.startX,c[b*4-1]=m.startY),c[b*4]=m.stopX,c[b*4+1]=m.stopY,f=na(e,t,n,i,m.cx,m.cy,m.radius),f.length!==0&&v.push(f[0],f[1])});for(var h=0;h<c.length/4;h++)f=Qr(e,t,n,i,c[h*4],c[h*4+1],c[h*4+2],c[h*4+3],!1),f.length!==0&&v.push(f[0],f[1]);if(v.length>2){for(var d=[v[0],v[1]],y=Math.pow(d[0]-e,2)+Math.pow(d[1]-t,2),p=1;p<v.length/2;p++){var g=Math.pow(v[p*2]-e,2)+Math.pow(v[p*2+1]-t,2);g<=y&&(d[0]=v[p*2],d[1]=v[p*2+1],y=g)}return d}return v},Ra=function(e,t,a){var n=[e[0]-t[0],e[1]-t[1]],i=Math.sqrt(n[0]*n[0]+n[1]*n[1]),s=(i-a)/i;return s<0&&(s=1e-5),[t[0]+s*n[0],t[1]+s*n[1]]},fr=function(e,t){var a=Gn(e,t);return a=bo(a),a},bo=function(e){for(var t,a,n=e.length/2,i=1/0,s=1/0,o=-1/0,l=-1/0,u=0;u<n;u++)t=e[2*u],a=e[2*u+1],i=Math.min(i,t),o=Math.max(o,t),s=Math.min(s,a),l=Math.max(l,a);for(var v=2/(o-i),f=2/(l-s),c=0;c<n;c++)t=e[2*c]=e[2*c]*v,a=e[2*c+1]=e[2*c+1]*f,i=Math.min(i,t),o=Math.max(o,t),s=Math.min(s,a),l=Math.max(l,a);if(s<-1)for(var h=0;h<n;h++)a=e[2*h+1]=e[2*h+1]+(-1-s);return e},Gn=function(e,t){var a=1/e*2*Math.PI,n=e%2===0?Math.PI/2+a/2:Math.PI/2;n+=t;for(var i=new Array(e*2),s,o=0;o<e;o++)s=o*a+n,i[2*o]=Math.cos(s),i[2*o+1]=Math.sin(-s);return i},gt=function(e,t){return Math.min(e/4,t/4,8)},wo=function(e,t){return Math.min(e/10,t/10,8)},hi=function(){return 8},Yv=function(e,t,a){return[e-2*t+a,2*(t-e),e]},Kn=function(e,t){return{heightOffset:Math.min(15,.05*t),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}},Xv=rr({dampingFactor:.8,precision:1e-6,iterations:200,weight:function(e){return 1}}),Zv={pageRank:function(e){for(var t=Xv(e),a=t.dampingFactor,n=t.precision,i=t.iterations,s=t.weight,o=this._private.cy,l=this.byGroup(),u=l.nodes,v=l.edges,f=u.length,c=f*f,h=v.length,d=new Array(c),y=new Array(f),p=(1-a)/f,g=0;g<f;g++){for(var m=0;m<f;m++){var b=g*f+m;d[b]=0}y[g]=0}for(var w=0;w<h;w++){var S=v[w],E=S.data("source"),x=S.data("target");if(E!==x){var D=u.indexOfId(E),C=u.indexOfId(x),M=s(S),P=C*f+D;d[P]+=M,y[D]+=M}}for(var B=1/f+p,L=0;L<f;L++)if(y[L]===0)for(var k=0;k<f;k++){var O=k*f+L;d[O]=B}else for(var A=0;A<f;A++){var R=A*f+L;d[R]=d[R]/y[L]+p}for(var I=new Array(f),V=new Array(f),G,F=0;F<f;F++)I[F]=1;for(var q=0;q<i;q++){for(var Y=0;Y<f;Y++)V[Y]=0;for(var Q=0;Q<f;Q++)for(var J=0;J<f;J++){var _=Q*f+J;V[Q]+=d[_]*I[J]}Lv(V),G=I,I=V,V=G;for(var j=0,W=0;W<f;W++){var z=G[W]-I[W];j+=z*z}if(j<n)break}var K={rank:function(ae){return ae=o.collection(ae)[0],I[u.indexOf(ae)]}};return K}},Gi=rr({root:null,weight:function(e){return 1},directed:!1,alpha:0}),Lt={degreeCentralityNormalized:function(e){e=Gi(e);var t=this.cy(),a=this.nodes(),n=a.length;if(e.directed){for(var v={},f={},c=0,h=0,d=0;d<n;d++){var y=a[d],p=y.id();e.root=y;var g=this.degreeCentrality(e);c<g.indegree&&(c=g.indegree),h<g.outdegree&&(h=g.outdegree),v[p]=g.indegree,f[p]=g.outdegree}return{indegree:function(b){return c==0?0:(ce(b)&&(b=t.filter(b)),v[b.id()]/c)},outdegree:function(b){return h===0?0:(ce(b)&&(b=t.filter(b)),f[b.id()]/h)}}}else{for(var i={},s=0,o=0;o<n;o++){var l=a[o];e.root=l;var u=this.degreeCentrality(e);s<u.degree&&(s=u.degree),i[l.id()]=u.degree}return{degree:function(b){return s===0?0:(ce(b)&&(b=t.filter(b)),i[b.id()]/s)}}}},degreeCentrality:function(e){e=Gi(e);var t=this.cy(),a=this,n=e,i=n.root,s=n.weight,o=n.directed,l=n.alpha;if(i=t.collection(i)[0],o){for(var h=i.connectedEdges(),d=h.filter(function(E){return E.target().same(i)&&a.has(E)}),y=h.filter(function(E){return E.source().same(i)&&a.has(E)}),p=d.length,g=y.length,m=0,b=0,w=0;w<d.length;w++)m+=s(d[w]);for(var S=0;S<y.length;S++)b+=s(y[S]);return{indegree:Math.pow(p,1-l)*Math.pow(m,l),outdegree:Math.pow(g,1-l)*Math.pow(b,l)}}else{for(var u=i.connectedEdges().intersection(a),v=u.length,f=0,c=0;c<u.length;c++)f+=s(u[c]);return{degree:Math.pow(v,1-l)*Math.pow(f,l)}}}};Lt.dc=Lt.degreeCentrality;Lt.dcn=Lt.degreeCentralityNormalised=Lt.degreeCentralityNormalized;var Ki=rr({harmonic:!0,weight:function(){return 1},directed:!1,root:null}),At={closenessCentralityNormalized:function(e){for(var t=Ki(e),a=t.harmonic,n=t.weight,i=t.directed,s=this.cy(),o={},l=0,u=this.nodes(),v=this.floydWarshall({weight:n,directed:i}),f=0;f<u.length;f++){for(var c=0,h=u[f],d=0;d<u.length;d++)if(f!==d){var y=v.distance(h,u[d]);a?c+=1/y:c+=y}a||(c=1/c),l<c&&(l=c),o[h.id()]=c}return{closeness:function(g){return l==0?0:(ce(g)?g=s.filter(g)[0].id():g=g.id(),o[g]/l)}}},closenessCentrality:function(e){var t=Ki(e),a=t.root,n=t.weight,i=t.directed,s=t.harmonic;a=this.filter(a)[0];for(var o=this.dijkstra({root:a,weight:n,directed:i}),l=0,u=this.nodes(),v=0;v<u.length;v++){var f=u[v];if(!f.same(a)){var c=o.distanceTo(f);s?l+=1/c:l+=c}}return s?l:1/l}};At.cc=At.closenessCentrality;At.ccn=At.closenessCentralityNormalised=At.closenessCentralityNormalized;var Qv=rr({weight:null,directed:!1}),Wn={betweennessCentrality:function(e){for(var t=Qv(e),a=t.directed,n=t.weight,i=n!=null,s=this.cy(),o=this.nodes(),l={},u={},v=0,f={set:function(b,w){u[b]=w,w>v&&(v=w)},get:function(b){return u[b]}},c=0;c<o.length;c++){var h=o[c],d=h.id();a?l[d]=h.outgoers().nodes():l[d]=h.openNeighborhood().nodes(),f.set(d,0)}for(var y=function(b){for(var w=o[b].id(),S=[],E={},x={},D={},C=new Ca(function(J,_){return D[J]-D[_]}),M=0;M<o.length;M++){var P=o[M].id();E[P]=[],x[P]=0,D[P]=1/0}for(x[w]=1,D[w]=0,C.push(w);!C.empty();){var B=C.pop();if(S.push(B),i)for(var L=0;L<l[B].length;L++){var k=l[B][L],O=s.getElementById(B),A=void 0;O.edgesTo(k).length>0?A=O.edgesTo(k)[0]:A=k.edgesTo(O)[0];var R=n(A);k=k.id(),D[k]>D[B]+R&&(D[k]=D[B]+R,C.nodes.indexOf(k)<0?C.push(k):C.updateItem(k),x[k]=0,E[k]=[]),D[k]==D[B]+R&&(x[k]=x[k]+x[B],E[k].push(B))}else for(var I=0;I<l[B].length;I++){var V=l[B][I].id();D[V]==1/0&&(C.push(V),D[V]=D[B]+1),D[V]==D[B]+1&&(x[V]=x[V]+x[B],E[V].push(B))}}for(var G={},F=0;F<o.length;F++)G[o[F].id()]=0;for(;S.length>0;){for(var q=S.pop(),Y=0;Y<E[q].length;Y++){var Q=E[q][Y];G[Q]=G[Q]+x[Q]/x[q]*(1+G[q])}q!=o[b].id()&&f.set(q,f.get(q)+G[q])}},p=0;p<o.length;p++)y(p);var g={betweenness:function(b){var w=s.collection(b).id();return f.get(w)},betweennessNormalized:function(b){if(v==0)return 0;var w=s.collection(b).id();return f.get(w)/v}};return g.betweennessNormalised=g.betweennessNormalized,g}};Wn.bc=Wn.betweennessCentrality;var Jv=rr({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(r){return 1}]}),_v=function(e){return Jv(e)},jv=function(e,t){for(var a=0,n=0;n<t.length;n++)a+=t[n](e);return a},ef=function(e,t,a){for(var n=0;n<t;n++)e[n*t+n]=a},xo=function(e,t){for(var a,n=0;n<t;n++){a=0;for(var i=0;i<t;i++)a+=e[i*t+n];for(var s=0;s<t;s++)e[s*t+n]=e[s*t+n]/a}},rf=function(e,t,a){for(var n=new Array(a*a),i=0;i<a;i++){for(var s=0;s<a;s++)n[i*a+s]=0;for(var o=0;o<a;o++)for(var l=0;l<a;l++)n[i*a+l]+=e[i*a+o]*t[o*a+l]}return n},tf=function(e,t,a){for(var n=e.slice(0),i=1;i<a;i++)e=rf(e,n,t);return e},af=function(e,t,a){for(var n=new Array(t*t),i=0;i<t*t;i++)n[i]=Math.pow(e[i],a);return xo(n,t),n},nf=function(e,t,a,n){for(var i=0;i<a;i++){var s=Math.round(e[i]*Math.pow(10,n))/Math.pow(10,n),o=Math.round(t[i]*Math.pow(10,n))/Math.pow(10,n);if(s!==o)return!1}return!0},sf=function(e,t,a,n){for(var i=[],s=0;s<t;s++){for(var o=[],l=0;l<t;l++)Math.round(e[s*t+l]*1e3)/1e3>0&&o.push(a[l]);o.length!==0&&i.push(n.collection(o))}return i},of=function(e,t){for(var a=0;a<e.length;a++)if(!t[a]||e[a].id()!==t[a].id())return!1;return!0},uf=function(e){for(var t=0;t<e.length;t++)for(var a=0;a<e.length;a++)t!=a&&of(e[t],e[a])&&e.splice(a,1);return e},Wi=function(e){for(var t=this.nodes(),a=this.edges(),n=this.cy(),i=_v(e),s={},o=0;o<t.length;o++)s[t[o].id()]=o;for(var l=t.length,u=l*l,v=new Array(u),f,c=0;c<u;c++)v[c]=0;for(var h=0;h<a.length;h++){var d=a[h],y=s[d.source().id()],p=s[d.target().id()],g=jv(d,i.attributes);v[y*l+p]+=g,v[p*l+y]+=g}ef(v,l,i.multFactor),xo(v,l);for(var m=!0,b=0;m&&b<i.maxIterations;)m=!1,f=tf(v,l,i.expandFactor),v=af(f,l,i.inflateFactor),nf(v,f,u,4)||(m=!0),b++;var w=sf(v,l,t,n);return w=uf(w),w},lf={markovClustering:Wi,mcl:Wi},vf=function(e){return e},Eo=function(e,t){return Math.abs(t-e)},Ui=function(e,t,a){return e+Eo(t,a)},Yi=function(e,t,a){return e+Math.pow(a-t,2)},ff=function(e){return Math.sqrt(e)},cf=function(e,t,a){return Math.max(e,Eo(t,a))},_t=function(e,t,a,n,i){for(var s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:vf,o=n,l,u,v=0;v<e;v++)l=t(v),u=a(v),o=i(o,l,u);return s(o)},zt={euclidean:function(e,t,a){return e>=2?_t(e,t,a,0,Yi,ff):_t(e,t,a,0,Ui)},squaredEuclidean:function(e,t,a){return _t(e,t,a,0,Yi)},manhattan:function(e,t,a){return _t(e,t,a,0,Ui)},max:function(e,t,a){return _t(e,t,a,-1/0,cf)}};zt["squared-euclidean"]=zt.squaredEuclidean;zt.squaredeuclidean=zt.squaredEuclidean;function gn(r,e,t,a,n,i){var s;return qe(r)?s=r:s=zt[r]||zt.euclidean,e===0&&qe(r)?s(n,i):s(e,t,a,n,i)}var df=rr({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),gi=function(e){return df(e)},an=function(e,t,a,n,i){var s=i!=="kMedoids",o=s?function(f){return a[f]}:function(f){return n[f](a)},l=function(c){return n[c](t)},u=a,v=t;return gn(e,n.length,o,l,u,v)},On=function(e,t,a){for(var n=a.length,i=new Array(n),s=new Array(n),o=new Array(t),l=null,u=0;u<n;u++)i[u]=e.min(a[u]).value,s[u]=e.max(a[u]).value;for(var v=0;v<t;v++){l=[];for(var f=0;f<n;f++)l[f]=Math.random()*(s[f]-i[f])+i[f];o[v]=l}return o},Co=function(e,t,a,n,i){for(var s=1/0,o=0,l=0;l<t.length;l++){var u=an(a,e,t[l],n,i);u<s&&(s=u,o=l)}return o},So=function(e,t,a){for(var n=[],i=null,s=0;s<t.length;s++)i=t[s],a[i.id()]===e&&n.push(i);return n},hf=function(e,t,a){return Math.abs(t-e)<=a},gf=function(e,t,a){for(var n=0;n<e.length;n++)for(var i=0;i<e[n].length;i++){var s=Math.abs(e[n][i]-t[n][i]);if(s>a)return!1}return!0},pf=function(e,t,a){for(var n=0;n<a;n++)if(e===t[n])return!0;return!1},Xi=function(e,t){var a=new Array(t);if(e.length<50)for(var n=0;n<t;n++){for(var i=e[Math.floor(Math.random()*e.length)];pf(i,a,n);)i=e[Math.floor(Math.random()*e.length)];a[n]=i}else for(var s=0;s<t;s++)a[s]=e[Math.floor(Math.random()*e.length)];return a},Zi=function(e,t,a){for(var n=0,i=0;i<t.length;i++)n+=an("manhattan",t[i],e,a,"kMedoids");return n},yf=function(e){var t=this.cy(),a=this.nodes(),n=null,i=gi(e),s=new Array(i.k),o={},l;i.testMode?typeof i.testCentroids=="number"?(i.testCentroids,l=On(a,i.k,i.attributes)):Ue(i.testCentroids)==="object"?l=i.testCentroids:l=On(a,i.k,i.attributes):l=On(a,i.k,i.attributes);for(var u=!0,v=0;u&&v<i.maxIterations;){for(var f=0;f<a.length;f++)n=a[f],o[n.id()]=Co(n,l,i.distance,i.attributes,"kMeans");u=!1;for(var c=0;c<i.k;c++){var h=So(c,a,o);if(h.length!==0){for(var d=i.attributes.length,y=l[c],p=new Array(d),g=new Array(d),m=0;m<d;m++){g[m]=0;for(var b=0;b<h.length;b++)n=h[b],g[m]+=i.attributes[m](n);p[m]=g[m]/h.length,hf(p[m],y[m],i.sensitivityThreshold)||(u=!0)}l[c]=p,s[c]=t.collection(h)}}v++}return s},mf=function(e){var t=this.cy(),a=this.nodes(),n=null,i=gi(e),s=new Array(i.k),o,l={},u,v=new Array(i.k);i.testMode?typeof i.testCentroids=="number"||(Ue(i.testCentroids)==="object"?o=i.testCentroids:o=Xi(a,i.k)):o=Xi(a,i.k);for(var f=!0,c=0;f&&c<i.maxIterations;){for(var h=0;h<a.length;h++)n=a[h],l[n.id()]=Co(n,o,i.distance,i.attributes,"kMedoids");f=!1;for(var d=0;d<o.length;d++){var y=So(d,a,l);if(y.length!==0){v[d]=Zi(o[d],y,i.attributes);for(var p=0;p<y.length;p++)u=Zi(y[p],y,i.attributes),u<v[d]&&(v[d]=u,o[d]=y[p],f=!0);s[d]=t.collection(y)}}c++}return s},bf=function(e,t,a,n,i){for(var s,o,l=0;l<t.length;l++)for(var u=0;u<e.length;u++)n[l][u]=Math.pow(a[l][u],i.m);for(var v=0;v<e.length;v++)for(var f=0;f<i.attributes.length;f++){s=0,o=0;for(var c=0;c<t.length;c++)s+=n[c][v]*i.attributes[f](t[c]),o+=n[c][v];e[v][f]=s/o}},wf=function(e,t,a,n,i){for(var s=0;s<e.length;s++)t[s]=e[s].slice();for(var o,l,u,v=2/(i.m-1),f=0;f<a.length;f++)for(var c=0;c<n.length;c++){o=0;for(var h=0;h<a.length;h++)l=an(i.distance,n[c],a[f],i.attributes,"cmeans"),u=an(i.distance,n[c],a[h],i.attributes,"cmeans"),o+=Math.pow(l/u,v);e[c][f]=1/o}},xf=function(e,t,a,n){for(var i=new Array(a.k),s=0;s<i.length;s++)i[s]=[];for(var o,l,u=0;u<t.length;u++){o=-1/0,l=-1;for(var v=0;v<t[0].length;v++)t[u][v]>o&&(o=t[u][v],l=v);i[l].push(e[u])}for(var f=0;f<i.length;f++)i[f]=n.collection(i[f]);return i},Qi=function(e){var t=this.cy(),a=this.nodes(),n=gi(e),i,s,o,l,u;l=new Array(a.length);for(var v=0;v<a.length;v++)l[v]=new Array(n.k);o=new Array(a.length);for(var f=0;f<a.length;f++)o[f]=new Array(n.k);for(var c=0;c<a.length;c++){for(var h=0,d=0;d<n.k;d++)o[c][d]=Math.random(),h+=o[c][d];for(var y=0;y<n.k;y++)o[c][y]=o[c][y]/h}s=new Array(n.k);for(var p=0;p<n.k;p++)s[p]=new Array(n.attributes.length);u=new Array(a.length);for(var g=0;g<a.length;g++)u[g]=new Array(n.k);for(var m=!0,b=0;m&&b<n.maxIterations;)m=!1,bf(s,a,o,u,n),wf(o,l,s,a,n),gf(o,l,n.sensitivityThreshold)||(m=!0),b++;return i=xf(a,o,n,t),{clusters:i,degreeOfMembership:o}},Ef={kMeans:yf,kMedoids:mf,fuzzyCMeans:Qi,fcm:Qi},Cf=rr({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),Sf={single:"min",complete:"max"},Tf=function(e){var t=Cf(e),a=Sf[t.linkage];return a!=null&&(t.linkage=a),t},Ji=function(e,t,a,n,i){for(var s=0,o=1/0,l,u=i.attributes,v=function(C,M){return gn(i.distance,u.length,function(P){return u[P](C)},function(P){return u[P](M)},C,M)},f=0;f<e.length;f++){var c=e[f].key,h=a[c][n[c]];h<o&&(s=c,o=h)}if(i.mode==="threshold"&&o>=i.threshold||i.mode==="dendrogram"&&e.length===1)return!1;var d=t[s],y=t[n[s]],p;i.mode==="dendrogram"?p={left:d,right:y,key:d.key}:p={value:d.value.concat(y.value),key:d.key},e[d.index]=p,e.splice(y.index,1),t[d.key]=p;for(var g=0;g<e.length;g++){var m=e[g];d.key===m.key?l=1/0:i.linkage==="min"?(l=a[d.key][m.key],a[d.key][m.key]>a[y.key][m.key]&&(l=a[y.key][m.key])):i.linkage==="max"?(l=a[d.key][m.key],a[d.key][m.key]<a[y.key][m.key]&&(l=a[y.key][m.key])):i.linkage==="mean"?l=(a[d.key][m.key]*d.size+a[y.key][m.key]*y.size)/(d.size+y.size):i.mode==="dendrogram"?l=v(m.value,d.value):l=v(m.value[0],d.value[0]),a[d.key][m.key]=a[m.key][d.key]=l}for(var b=0;b<e.length;b++){var w=e[b].key;if(n[w]===d.key||n[w]===y.key){for(var S=w,E=0;E<e.length;E++){var x=e[E].key;a[w][x]<a[w][S]&&(S=x)}n[w]=S}e[b].index=b}return d.key=y.key=d.index=y.index=null,!0},Oa=function r(e,t,a){e&&(e.value?t.push(e.value):(e.left&&r(e.left,t),e.right&&r(e.right,t)))},Df=function r(e,t){if(!e)return"";if(e.left&&e.right){var a=r(e.left,t),n=r(e.right,t),i=t.add({group:"nodes",data:{id:a+","+n}});return t.add({group:"edges",data:{source:a,target:i.id()}}),t.add({group:"edges",data:{source:n,target:i.id()}}),i.id()}else if(e.value)return e.value.id()},kf=function r(e,t,a){if(!e)return[];var n=[],i=[],s=[];return t===0?(e.left&&Oa(e.left,n),e.right&&Oa(e.right,i),s=n.concat(i),[a.collection(s)]):t===1?e.value?[a.collection(e.value)]:(e.left&&Oa(e.left,n),e.right&&Oa(e.right,i),[a.collection(n),a.collection(i)]):e.value?[a.collection(e.value)]:(e.left&&(n=r(e.left,t-1,a)),e.right&&(i=r(e.right,t-1,a)),n.concat(i))},_i=function(e){for(var t=this.cy(),a=this.nodes(),n=Tf(e),i=n.attributes,s=function(b,w){return gn(n.distance,i.length,function(S){return i[S](b)},function(S){return i[S](w)},b,w)},o=[],l=[],u=[],v=[],f=0;f<a.length;f++){var c={value:n.mode==="dendrogram"?a[f]:[a[f]],key:f,index:f};o[f]=c,v[f]=c,l[f]=[],u[f]=0}for(var h=0;h<o.length;h++)for(var d=0;d<=h;d++){var y=void 0;n.mode==="dendrogram"?y=h===d?1/0:s(o[h].value,o[d].value):y=h===d?1/0:s(o[h].value[0],o[d].value[0]),l[h][d]=y,l[d][h]=y,y<l[h][u[h]]&&(u[h]=d)}for(var p=Ji(o,v,l,u,n);p;)p=Ji(o,v,l,u,n);var g;return n.mode==="dendrogram"?(g=kf(o[0],n.dendrogramDepth,t),n.addDendrogram&&Df(o[0],t)):(g=new Array(o.length),o.forEach(function(m,b){m.key=m.index=null,g[b]=t.collection(m.value)})),g},Pf={hierarchicalClustering:_i,hca:_i},Bf=rr({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),Mf=function(e){var t=e.damping,a=e.preference;.5<=t&&t<1||$e("Damping must range on [0.5, 1).  Got: ".concat(t));var n=["median","mean","min","max"];return n.some(function(i){return i===a})||ne(a)||$e("Preference must be one of [".concat(n.map(function(i){return"'".concat(i,"'")}).join(", "),"] or a number.  Got: ").concat(a)),Bf(e)},Lf=function(e,t,a,n){var i=function(o,l){return n[l](o)};return-gn(e,n.length,function(s){return i(t,s)},function(s){return i(a,s)},t,a)},Af=function(e,t){var a=null;return t==="median"?a=Bv(e):t==="mean"?a=Pv(e):t==="min"?a=Dv(e):t==="max"?a=kv(e):a=t,a},Rf=function(e,t,a){for(var n=[],i=0;i<e;i++)t[i*e+i]+a[i*e+i]>0&&n.push(i);return n},ji=function(e,t,a){for(var n=[],i=0;i<e;i++){for(var s=-1,o=-1/0,l=0;l<a.length;l++){var u=a[l];t[i*e+u]>o&&(s=u,o=t[i*e+u])}s>0&&n.push(s)}for(var v=0;v<a.length;v++)n[a[v]]=a[v];return n},Of=function(e,t,a){for(var n=ji(e,t,a),i=0;i<a.length;i++){for(var s=[],o=0;o<n.length;o++)n[o]===a[i]&&s.push(o);for(var l=-1,u=-1/0,v=0;v<s.length;v++){for(var f=0,c=0;c<s.length;c++)f+=t[s[c]*e+s[v]];f>u&&(l=v,u=f)}a[i]=s[l]}return n=ji(e,t,a),n},es=function(e){for(var t=this.cy(),a=this.nodes(),n=Mf(e),i={},s=0;s<a.length;s++)i[a[s].id()]=s;var o,l,u,v,f,c;o=a.length,l=o*o,u=new Array(l);for(var h=0;h<l;h++)u[h]=-1/0;for(var d=0;d<o;d++)for(var y=0;y<o;y++)d!==y&&(u[d*o+y]=Lf(n.distance,a[d],a[y],n.attributes));v=Af(u,n.preference);for(var p=0;p<o;p++)u[p*o+p]=v;f=new Array(l);for(var g=0;g<l;g++)f[g]=0;c=new Array(l);for(var m=0;m<l;m++)c[m]=0;for(var b=new Array(o),w=new Array(o),S=new Array(o),E=0;E<o;E++)b[E]=0,w[E]=0,S[E]=0;for(var x=new Array(o*n.minIterations),D=0;D<x.length;D++)x[D]=0;var C;for(C=0;C<n.maxIterations;C++){for(var M=0;M<o;M++){for(var P=-1/0,B=-1/0,L=-1,k=0,O=0;O<o;O++)b[O]=f[M*o+O],k=c[M*o+O]+u[M*o+O],k>=P?(B=P,P=k,L=O):k>B&&(B=k);for(var A=0;A<o;A++)f[M*o+A]=(1-n.damping)*(u[M*o+A]-P)+n.damping*b[A];f[M*o+L]=(1-n.damping)*(u[M*o+L]-B)+n.damping*b[L]}for(var R=0;R<o;R++){for(var I=0,V=0;V<o;V++)b[V]=c[V*o+R],w[V]=Math.max(0,f[V*o+R]),I+=w[V];I-=w[R],w[R]=f[R*o+R],I+=w[R];for(var G=0;G<o;G++)c[G*o+R]=(1-n.damping)*Math.min(0,I-w[G])+n.damping*b[G];c[R*o+R]=(1-n.damping)*(I-w[R])+n.damping*b[R]}for(var F=0,q=0;q<o;q++){var Y=c[q*o+q]+f[q*o+q]>0?1:0;x[C%n.minIterations*o+q]=Y,F+=Y}if(F>0&&(C>=n.minIterations-1||C==n.maxIterations-1)){for(var Q=0,J=0;J<o;J++){S[J]=0;for(var _=0;_<n.minIterations;_++)S[J]+=x[_*o+J];(S[J]===0||S[J]===n.minIterations)&&Q++}if(Q===o)break}}for(var j=Rf(o,f,c),W=Of(o,u,j),z={},K=0;K<j.length;K++)z[j[K]]=[];for(var X=0;X<a.length;X++){var ae=i[a[X].id()],he=W[ae];he!=null&&z[he].push(a[X])}for(var te=new Array(j.length),re=0;re<j.length;re++)te[re]=t.collection(z[j[re]]);return te},If={affinityPropagation:es,ap:es},zf=rr({root:void 0,directed:!1}),Nf={hierholzer:function(e){if(!Te(e)){var t=arguments;e={root:t[0],directed:t[1]}}var a=zf(e),n=a.root,i=a.directed,s=this,o=!1,l,u,v;n&&(v=ce(n)?this.filter(n)[0].id():n[0].id());var f={},c={};i?s.forEach(function(m){var b=m.id();if(m.isNode()){var w=m.indegree(!0),S=m.outdegree(!0),E=w-S,x=S-w;E==1?l?o=!0:l=b:x==1?u?o=!0:u=b:(x>1||E>1)&&(o=!0),f[b]=[],m.outgoers().forEach(function(D){D.isEdge()&&f[b].push(D.id())})}else c[b]=[void 0,m.target().id()]}):s.forEach(function(m){var b=m.id();if(m.isNode()){var w=m.degree(!0);w%2&&(l?u?o=!0:u=b:l=b),f[b]=[],m.connectedEdges().forEach(function(S){return f[b].push(S.id())})}else c[b]=[m.source().id(),m.target().id()]});var h={found:!1,trail:void 0};if(o)return h;if(u&&l)if(i){if(v&&u!=v)return h;v=u}else{if(v&&u!=v&&l!=v)return h;v||(v=u)}else v||(v=s[0].id());var d=function(b){for(var w=b,S=[b],E,x,D;f[w].length;)E=f[w].shift(),x=c[E][0],D=c[E][1],w!=D?(f[D]=f[D].filter(function(C){return C!=E}),w=D):!i&&w!=x&&(f[x]=f[x].filter(function(C){return C!=E}),w=x),S.unshift(E),S.unshift(w);return S},y=[],p=[];for(p=d(v);p.length!=1;)f[p[0]].length==0?(y.unshift(s.getElementById(p.shift())),y.unshift(s.getElementById(p.shift()))):p=d(p.shift()).concat(p);y.unshift(s.getElementById(p.shift()));for(var g in f)if(f[g].length)return h;return h.found=!0,h.trail=this.spawn(y,!0),h}},Ia=function(){var e=this,t={},a=0,n=0,i=[],s=[],o={},l=function(c,h){for(var d=s.length-1,y=[],p=e.spawn();s[d].x!=c||s[d].y!=h;)y.push(s.pop().edge),d--;y.push(s.pop().edge),y.forEach(function(g){var m=g.connectedNodes().intersection(e);p.merge(g),m.forEach(function(b){var w=b.id(),S=b.connectedEdges().intersection(e);p.merge(b),t[w].cutVertex?p.merge(S.filter(function(E){return E.isLoop()})):p.merge(S)})}),i.push(p)},u=function f(c,h,d){c===d&&(n+=1),t[h]={id:a,low:a++,cutVertex:!1};var y=e.getElementById(h).connectedEdges().intersection(e);if(y.size()===0)i.push(e.spawn(e.getElementById(h)));else{var p,g,m,b;y.forEach(function(w){p=w.source().id(),g=w.target().id(),m=p===h?g:p,m!==d&&(b=w.id(),o[b]||(o[b]=!0,s.push({x:h,y:m,edge:w})),m in t?t[h].low=Math.min(t[h].low,t[m].id):(f(c,m,h),t[h].low=Math.min(t[h].low,t[m].low),t[h].id<=t[m].low&&(t[h].cutVertex=!0,l(h,m))))})}};e.forEach(function(f){if(f.isNode()){var c=f.id();c in t||(n=0,u(c,c),t[c].cutVertex=n>1)}});var v=Object.keys(t).filter(function(f){return t[f].cutVertex}).map(function(f){return e.getElementById(f)});return{cut:e.spawn(v),components:i}},Ff={hopcroftTarjanBiconnected:Ia,htbc:Ia,htb:Ia,hopcroftTarjanBiconnectedComponents:Ia},za=function(){var e=this,t={},a=0,n=[],i=[],s=e.spawn(e),o=function l(u){i.push(u),t[u]={index:a,low:a++,explored:!1};var v=e.getElementById(u).connectedEdges().intersection(e);if(v.forEach(function(y){var p=y.target().id();p!==u&&(p in t||l(p),t[p].explored||(t[u].low=Math.min(t[u].low,t[p].low)))}),t[u].index===t[u].low){for(var f=e.spawn();;){var c=i.pop();if(f.merge(e.getElementById(c)),t[c].low=t[u].index,t[c].explored=!0,c===u)break}var h=f.edgesWith(f),d=f.merge(h);n.push(d),s=s.difference(d)}};return e.forEach(function(l){if(l.isNode()){var u=l.id();u in t||o(u)}}),{cut:s,components:n}},Vf={tarjanStronglyConnected:za,tsc:za,tscc:za,tarjanStronglyConnectedComponents:za},To={};[ca,hv,gv,yv,bv,xv,Sv,Zv,Lt,At,Wn,lf,Ef,Pf,If,Nf,Ff,Vf].forEach(function(r){pe(To,r)});/*!
Embeddable Minimum Strictly-Compliant Promises/A+ 1.1.1 Thenable
Copyright (c) 2013-2014 Ralf S. Engelschall (http://engelschall.com)
Licensed under The MIT License (http://opensource.org/licenses/MIT)
*/var Do=0,ko=1,Po=2,Kr=function r(e){if(!(this instanceof r))return new r(e);this.id="Thenable/1.0.7",this.state=Do,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},typeof e=="function"&&e.call(this,this.fulfill.bind(this),this.reject.bind(this))};Kr.prototype={fulfill:function(e){return rs(this,ko,"fulfillValue",e)},reject:function(e){return rs(this,Po,"rejectReason",e)},then:function(e,t){var a=this,n=new Kr;return a.onFulfilled.push(as(e,n,"fulfill")),a.onRejected.push(as(t,n,"reject")),Bo(a),n.proxy}};var rs=function(e,t,a,n){return e.state===Do&&(e.state=t,e[a]=n,Bo(e)),e},Bo=function(e){e.state===ko?ts(e,"onFulfilled",e.fulfillValue):e.state===Po&&ts(e,"onRejected",e.rejectReason)},ts=function(e,t,a){if(e[t].length!==0){var n=e[t];e[t]=[];var i=function(){for(var o=0;o<n.length;o++)n[o](a)};typeof setImmediate=="function"?setImmediate(i):setTimeout(i,0)}},as=function(e,t,a){return function(n){if(typeof e!="function")t[a].call(t,n);else{var i;try{i=e(n)}catch(s){t.reject(s);return}qf(t,i)}}},qf=function r(e,t){if(e===t||e.proxy===t){e.reject(new TypeError("cannot resolve promise with itself"));return}var a;if(Ue(t)==="object"&&t!==null||typeof t=="function")try{a=t.then}catch(i){e.reject(i);return}if(typeof a=="function"){var n=!1;try{a.call(t,function(i){n||(n=!0,i===t?e.reject(new TypeError("circular thenable chain")):r(e,i))},function(i){n||(n=!0,e.reject(i))})}catch(i){n||e.reject(i)}return}e.fulfill(t)};Kr.all=function(r){return new Kr(function(e,t){for(var a=new Array(r.length),n=0,i=function(l,u){a[l]=u,n++,n===r.length&&e(a)},s=0;s<r.length;s++)(function(o){var l=r[o],u=l!=null&&l.then!=null;if(u)l.then(function(f){i(o,f)},function(f){t(f)});else{var v=l;i(o,v)}})(s)})};Kr.resolve=function(r){return new Kr(function(e,t){e(r)})};Kr.reject=function(r){return new Kr(function(e,t){t(r)})};var qt=typeof Promise<"u"?Promise:Kr,Un=function(e,t,a){var n=oi(e),i=!n,s=this._private=pe({duration:1e3},t,a);if(s.target=e,s.style=s.style||s.css,s.started=!1,s.playing=!1,s.hooked=!1,s.applying=!1,s.progress=0,s.completes=[],s.frames=[],s.complete&&qe(s.complete)&&s.completes.push(s.complete),i){var o=e.position();s.startPosition=s.startPosition||{x:o.x,y:o.y},s.startStyle=s.startStyle||e.cy().style().getAnimationStartStyle(e,s.style)}if(n){var l=e.pan();s.startPan={x:l.x,y:l.y},s.startZoom=e.zoom()}this.length=1,this[0]=this},pt=Un.prototype;pe(pt,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var t,a=e.target._private.animation;e.queue?t=a.queue:t=a.current,t.push(this),yr(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return e.progress===1&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var t=this._private;return e===void 0?t.progress*t.duration:this.progress(e/t.duration)},progress:function(e){var t=this._private,a=t.playing;return e===void 0?t.progress:(a&&this.pause(),t.progress=e,t.started=!1,a&&this.play(),this)},completed:function(){return this._private.progress===1},reverse:function(){var e=this._private,t=e.playing;t&&this.pause(),e.progress=1-e.progress,e.started=!1;var a=function(u,v){var f=e[u];f!=null&&(e[u]=e[v],e[v]=f)};if(a("zoom","startZoom"),a("pan","startPan"),a("position","startPosition"),e.style)for(var n=0;n<e.style.length;n++){var i=e.style[n],s=i.name,o=e.startStyle[s];e.startStyle[s]=i,e.style[n]=o}return t&&this.play(),this},promise:function(e){var t=this._private,a;switch(e){case"frame":a=t.frames;break;default:case"complete":case"completed":a=t.completes}return new qt(function(n,i){a.push(function(){n()})})}});pt.complete=pt.completed;pt.run=pt.play;pt.running=pt.playing;var $f={animated:function(){return function(){var t=this,a=t.length!==void 0,n=a?t:[t],i=this._private.cy||this;if(!i.styleEnabled())return!1;var s=n[0];if(s)return s._private.animation.current.length>0}},clearQueue:function(){return function(){var t=this,a=t.length!==void 0,n=a?t:[t],i=this._private.cy||this;if(!i.styleEnabled())return this;for(var s=0;s<n.length;s++){var o=n[s];o._private.animation.queue=[]}return this}},delay:function(){return function(t,a){var n=this._private.cy||this;return n.styleEnabled()?this.animate({delay:t,duration:t,complete:a}):this}},delayAnimation:function(){return function(t,a){var n=this._private.cy||this;return n.styleEnabled()?this.animation({delay:t,duration:t,complete:a}):this}},animation:function(){return function(t,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this,l=!i,u=!l;if(!o.styleEnabled())return this;var v=o.style();t=pe({},t,a);var f=Object.keys(t).length===0;if(f)return new Un(s[0],t);switch(t.duration===void 0&&(t.duration=400),t.duration){case"slow":t.duration=600;break;case"fast":t.duration=200;break}if(u&&(t.style=v.getPropsList(t.style||t.css),t.css=void 0),u&&t.renderedPosition!=null){var c=t.renderedPosition,h=o.pan(),d=o.zoom();t.position=ho(c,d,h)}if(l&&t.panBy!=null){var y=t.panBy,p=o.pan();t.pan={x:p.x+y.x,y:p.y+y.y}}var g=t.center||t.centre;if(l&&g!=null){var m=o.getCenterPan(g.eles,t.zoom);m!=null&&(t.pan=m)}if(l&&t.fit!=null){var b=t.fit,w=o.getFitViewport(b.eles||b.boundingBox,b.padding);w!=null&&(t.pan=w.pan,t.zoom=w.zoom)}if(l&&Te(t.zoom)){var S=o.getZoomedViewport(t.zoom);S!=null?(S.zoomed&&(t.zoom=S.zoom),S.panned&&(t.pan=S.pan)):t.zoom=null}return new Un(s[0],t)}},animate:function(){return function(t,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;a&&(t=pe({},t,a));for(var l=0;l<s.length;l++){var u=s[l],v=u.animated()&&(t.queue===void 0||t.queue),f=u.animation(t,v?{queue:!0}:void 0);f.play()}return this}},stop:function(){return function(t,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;for(var l=0;l<s.length;l++){for(var u=s[l],v=u._private,f=v.animation.current,c=0;c<f.length;c++){var h=f[c],d=h._private;a&&(d.duration=0)}t&&(v.animation.queue=[]),a||(v.animation.current=[])}return o.notify("draw"),this}}},Hf=Array.isArray,pn=Hf,Gf=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Kf=/^\w*$/;function Wf(r,e){if(pn(r))return!1;var t=typeof r;return t=="number"||t=="symbol"||t=="boolean"||r==null||Ea(r)?!0:Kf.test(r)||!Gf.test(r)||e!=null&&r in Object(e)}var Uf=Wf,Yf="[object AsyncFunction]",Xf="[object Function]",Zf="[object GeneratorFunction]",Qf="[object Proxy]";function Jf(r){if(!ct(r))return!1;var e=no(r);return e==Xf||e==Zf||e==Yf||e==Qf}var _f=Jf,jf=fn["__core-js_shared__"],In=jf,ns=function(){var r=/[^.]+$/.exec(In&&In.keys&&In.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}();function ec(r){return!!ns&&ns in r}var rc=ec,tc=Function.prototype,ac=tc.toString;function nc(r){if(r!=null){try{return ac.call(r)}catch{}try{return r+""}catch{}}return""}var ic=nc,sc=/[\\^$.*+?()[\]{}|]/g,oc=/^\[object .+?Constructor\]$/,uc=Function.prototype,lc=Object.prototype,vc=uc.toString,fc=lc.hasOwnProperty,cc=RegExp("^"+vc.call(fc).replace(sc,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function dc(r){if(!ct(r)||rc(r))return!1;var e=_f(r)?cc:oc;return e.test(ic(r))}var hc=dc;function gc(r,e){return r==null?void 0:r[e]}var pc=gc;function yc(r,e){var t=pc(r,e);return hc(t)?t:void 0}var pi=yc,mc=pi(Object,"create"),ga=mc;function bc(){this.__data__=ga?ga(null):{},this.size=0}var wc=bc;function xc(r){var e=this.has(r)&&delete this.__data__[r];return this.size-=e?1:0,e}var Ec=xc,Cc="__lodash_hash_undefined__",Sc=Object.prototype,Tc=Sc.hasOwnProperty;function Dc(r){var e=this.__data__;if(ga){var t=e[r];return t===Cc?void 0:t}return Tc.call(e,r)?e[r]:void 0}var kc=Dc,Pc=Object.prototype,Bc=Pc.hasOwnProperty;function Mc(r){var e=this.__data__;return ga?e[r]!==void 0:Bc.call(e,r)}var Lc=Mc,Ac="__lodash_hash_undefined__";function Rc(r,e){var t=this.__data__;return this.size+=this.has(r)?0:1,t[r]=ga&&e===void 0?Ac:e,this}var Oc=Rc;function $t(r){var e=-1,t=r==null?0:r.length;for(this.clear();++e<t;){var a=r[e];this.set(a[0],a[1])}}$t.prototype.clear=wc;$t.prototype.delete=Ec;$t.prototype.get=kc;$t.prototype.has=Lc;$t.prototype.set=Oc;var is=$t;function Ic(){this.__data__=[],this.size=0}var zc=Ic;function Nc(r,e){return r===e||r!==r&&e!==e}var Mo=Nc;function Fc(r,e){for(var t=r.length;t--;)if(Mo(r[t][0],e))return t;return-1}var yn=Fc,Vc=Array.prototype,qc=Vc.splice;function $c(r){var e=this.__data__,t=yn(e,r);if(t<0)return!1;var a=e.length-1;return t==a?e.pop():qc.call(e,t,1),--this.size,!0}var Hc=$c;function Gc(r){var e=this.__data__,t=yn(e,r);return t<0?void 0:e[t][1]}var Kc=Gc;function Wc(r){return yn(this.__data__,r)>-1}var Uc=Wc;function Yc(r,e){var t=this.__data__,a=yn(t,r);return a<0?(++this.size,t.push([r,e])):t[a][1]=e,this}var Xc=Yc;function Ht(r){var e=-1,t=r==null?0:r.length;for(this.clear();++e<t;){var a=r[e];this.set(a[0],a[1])}}Ht.prototype.clear=zc;Ht.prototype.delete=Hc;Ht.prototype.get=Kc;Ht.prototype.has=Uc;Ht.prototype.set=Xc;var Zc=Ht,Qc=pi(fn,"Map"),Jc=Qc;function _c(){this.size=0,this.__data__={hash:new is,map:new(Jc||Zc),string:new is}}var jc=_c;function ed(r){var e=typeof r;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?r!=="__proto__":r===null}var rd=ed;function td(r,e){var t=r.__data__;return rd(e)?t[typeof e=="string"?"string":"hash"]:t.map}var mn=td;function ad(r){var e=mn(this,r).delete(r);return this.size-=e?1:0,e}var nd=ad;function id(r){return mn(this,r).get(r)}var sd=id;function od(r){return mn(this,r).has(r)}var ud=od;function ld(r,e){var t=mn(this,r),a=t.size;return t.set(r,e),this.size+=t.size==a?0:1,this}var vd=ld;function Gt(r){var e=-1,t=r==null?0:r.length;for(this.clear();++e<t;){var a=r[e];this.set(a[0],a[1])}}Gt.prototype.clear=jc;Gt.prototype.delete=nd;Gt.prototype.get=sd;Gt.prototype.has=ud;Gt.prototype.set=vd;var Lo=Gt,fd="Expected a function";function yi(r,e){if(typeof r!="function"||e!=null&&typeof e!="function")throw new TypeError(fd);var t=function(){var a=arguments,n=e?e.apply(this,a):a[0],i=t.cache;if(i.has(n))return i.get(n);var s=r.apply(this,a);return t.cache=i.set(n,s)||i,s};return t.cache=new(yi.Cache||Lo),t}yi.Cache=Lo;var cd=yi,dd=500;function hd(r){var e=cd(r,function(a){return t.size===dd&&t.clear(),a}),t=e.cache;return e}var gd=hd,pd=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,yd=/\\(\\)?/g,md=gd(function(r){var e=[];return r.charCodeAt(0)===46&&e.push(""),r.replace(pd,function(t,a,n,i){e.push(n?i.replace(yd,"$1"):a||t)}),e}),Ao=md;function bd(r,e){for(var t=-1,a=r==null?0:r.length,n=Array(a);++t<a;)n[t]=e(r[t],t,r);return n}var Ro=bd,wd=1/0,ss=Ot?Ot.prototype:void 0,os=ss?ss.toString:void 0;function Oo(r){if(typeof r=="string")return r;if(pn(r))return Ro(r,Oo)+"";if(Ea(r))return os?os.call(r):"";var e=r+"";return e=="0"&&1/r==-wd?"-0":e}var xd=Oo;function Ed(r){return r==null?"":xd(r)}var Io=Ed;function Cd(r,e){return pn(r)?r:Uf(r,e)?[r]:Ao(Io(r))}var zo=Cd,Sd=1/0;function Td(r){if(typeof r=="string"||Ea(r))return r;var e=r+"";return e=="0"&&1/r==-Sd?"-0":e}var mi=Td;function Dd(r,e){e=zo(e,r);for(var t=0,a=e.length;r!=null&&t<a;)r=r[mi(e[t++])];return t&&t==a?r:void 0}var kd=Dd;function Pd(r,e,t){var a=r==null?void 0:kd(r,e);return a===void 0?t:a}var Bd=Pd,Md=function(){try{var r=pi(Object,"defineProperty");return r({},"",{}),r}catch{}}(),us=Md;function Ld(r,e,t){e=="__proto__"&&us?us(r,e,{configurable:!0,enumerable:!0,value:t,writable:!0}):r[e]=t}var Ad=Ld,Rd=Object.prototype,Od=Rd.hasOwnProperty;function Id(r,e,t){var a=r[e];(!(Od.call(r,e)&&Mo(a,t))||t===void 0&&!(e in r))&&Ad(r,e,t)}var zd=Id,Nd=9007199254740991,Fd=/^(?:0|[1-9]\d*)$/;function Vd(r,e){var t=typeof r;return e=e??Nd,!!e&&(t=="number"||t!="symbol"&&Fd.test(r))&&r>-1&&r%1==0&&r<e}var qd=Vd;function $d(r,e,t,a){if(!ct(r))return r;e=zo(e,r);for(var n=-1,i=e.length,s=i-1,o=r;o!=null&&++n<i;){var l=mi(e[n]),u=t;if(l==="__proto__"||l==="constructor"||l==="prototype")return r;if(n!=s){var v=o[l];u=a?a(v,l,o):void 0,u===void 0&&(u=ct(v)?v:qd(e[n+1])?[]:{})}zd(o,l,u),o=o[l]}return r}var Hd=$d;function Gd(r,e,t){return r==null?r:Hd(r,e,t)}var Kd=Gd;function Wd(r,e){var t=-1,a=r.length;for(e||(e=Array(a));++t<a;)e[t]=r[t];return e}var Ud=Wd;function Yd(r){return pn(r)?Ro(r,mi):Ea(r)?[r]:Ud(Ao(Io(r)))}var Xd=Yd,Zd={data:function(e){var t={field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(n){},beforeSet:function(n,i){},onSet:function(n){},canSet:function(n){return!0}};return e=pe({},t,e),function(n,i){var s=e,o=this,l=o.length!==void 0,u=l?o:[o],v=l?o[0]:o;if(ce(n)){var f=n.indexOf(".")!==-1,c=f&&Xd(n);if(s.allowGetting&&i===void 0){var h;return v&&(s.beforeGet(v),c&&v._private[s.field][n]===void 0?h=Bd(v._private[s.field],c):h=v._private[s.field][n]),h}else if(s.allowSetting&&i!==void 0){var d=!s.immutableKeys[n];if(d){var y=Ys({},n,i);s.beforeSet(o,y);for(var p=0,g=u.length;p<g;p++){var m=u[p];s.canSet(m)&&(c&&v._private[s.field][n]===void 0?Kd(m._private[s.field],c,i):m._private[s.field][n]=i)}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}}}else if(s.allowSetting&&Te(n)){var b=n,w,S,E=Object.keys(b);s.beforeSet(o,b);for(var x=0;x<E.length;x++){w=E[x],S=b[w];var D=!s.immutableKeys[w];if(D)for(var C=0;C<u.length;C++){var M=u[C];s.canSet(M)&&(M._private[s.field][w]=S)}}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}else if(s.allowBinding&&qe(n)){var P=n;o.on(s.bindingEvent,P)}else if(s.allowGetting&&n===void 0){var B;return v&&(s.beforeGet(v),B=v._private[s.field]),B}return o}},removeData:function(e){var t={field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}};return e=pe({},t,e),function(n){var i=e,s=this,o=s.length!==void 0,l=o?s:[s];if(ce(n)){for(var u=n.split(/\s+/),v=u.length,f=0;f<v;f++){var c=u[f];if(!jr(c)){var h=!i.immutableKeys[c];if(h)for(var d=0,y=l.length;d<y;d++)l[d]._private[i.field][c]=void 0}}i.triggerEvent&&s[i.triggerFnName](i.event)}else if(n===void 0){for(var p=0,g=l.length;p<g;p++)for(var m=l[p]._private[i.field],b=Object.keys(m),w=0;w<b.length;w++){var S=b[w],E=!i.immutableKeys[S];E&&(m[S]=void 0)}i.triggerEvent&&s[i.triggerFnName](i.event)}return s}}},Qd={eventAliasesOn:function(e){var t=e;t.addListener=t.listen=t.bind=t.on,t.unlisten=t.unbind=t.off=t.removeListener,t.trigger=t.emit,t.pon=t.promiseOn=function(a,n){var i=this,s=Array.prototype.slice.call(arguments,0);return new qt(function(o,l){var u=function(h){i.off.apply(i,f),o(h)},v=s.concat([u]),f=v.concat([]);i.on.apply(i,v)})}}},Be={};[$f,Zd,Qd].forEach(function(r){pe(Be,r)});var Jd={animate:Be.animate(),animation:Be.animation(),animated:Be.animated(),clearQueue:Be.clearQueue(),delay:Be.delay(),delayAnimation:Be.delayAnimation(),stop:Be.stop()},Wa={classes:function(e){var t=this;if(e===void 0){var a=[];return t[0]._private.classes.forEach(function(d){return a.push(d)}),a}else Oe(e)||(e=(e||"").match(/\S+/g)||[]);for(var n=[],i=new Vt(e),s=0;s<t.length;s++){for(var o=t[s],l=o._private,u=l.classes,v=!1,f=0;f<e.length;f++){var c=e[f],h=u.has(c);if(!h){v=!0;break}}v||(v=u.size!==e.length),v&&(l.classes=i,n.push(o))}return n.length>0&&this.spawn(n).updateStyle().emit("class"),t},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var t=this[0];return t!=null&&t._private.classes.has(e)},toggleClass:function(e,t){Oe(e)||(e=e.match(/\S+/g)||[]);for(var a=this,n=t===void 0,i=[],s=0,o=a.length;s<o;s++)for(var l=a[s],u=l._private.classes,v=!1,f=0;f<e.length;f++){var c=e[f],h=u.has(c),d=!1;t||n&&!h?(u.add(c),d=!0):(!t||n&&h)&&(u.delete(c),d=!0),!v&&d&&(i.push(l),v=!0)}return i.length>0&&this.spawn(i).updateStyle().emit("class"),a},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,t){var a=this;if(t==null)t=250;else if(t===0)return a;return a.addClass(e),setTimeout(function(){a.removeClass(e)},t),a}};Wa.className=Wa.classNames=Wa.classes;var Se={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:`"(?:\\\\"|[^"])*"|'(?:\\\\'|[^'])*'`,number:We,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};Se.variable="(?:[\\w-.]|(?:\\\\"+Se.metaChar+"))+";Se.className="(?:[\\w-]|(?:\\\\"+Se.metaChar+"))+";Se.value=Se.string+"|"+Se.number;Se.id=Se.variable;(function(){var r,e,t;for(r=Se.comparatorOp.split("|"),t=0;t<r.length;t++)e=r[t],Se.comparatorOp+="|@"+e;for(r=Se.comparatorOp.split("|"),t=0;t<r.length;t++)e=r[t],!(e.indexOf("!")>=0)&&e!=="="&&(Se.comparatorOp+="|\\!"+e)})();var Re=function(){return{checks:[]}},ue={GROUP:0,COLLECTION:1,FILTER:2,DATA_COMPARE:3,DATA_EXIST:4,DATA_BOOL:5,META_COMPARE:6,STATE:7,ID:8,CLASS:9,UNDIRECTED_EDGE:10,DIRECTED_EDGE:11,NODE_SOURCE:12,NODE_TARGET:13,NODE_NEIGHBOR:14,CHILD:15,DESCENDANT:16,PARENT:17,ANCESTOR:18,COMPOUND_SPLIT:19,TRUE:20},Yn=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":compound",matches:function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort(function(r,e){return ul(r.selector,e.selector)}),_d=function(){for(var r={},e,t=0;t<Yn.length;t++)e=Yn[t],r[e.selector]=e.matches;return r}(),jd=function(e,t){return _d[e](t)},eh="("+Yn.map(function(r){return r.selector}).join("|")+")",xt=function(e){return e.replace(new RegExp("\\\\("+Se.metaChar+")","g"),function(t,a){return a})},Xr=function(e,t,a){e[e.length-1]=a},Xn=[{name:"group",query:!0,regex:"("+Se.group+")",populate:function(e,t,a){var n=Tr(a,1),i=n[0];t.checks.push({type:ue.GROUP,value:i==="*"?i:i+"s"})}},{name:"state",query:!0,regex:eh,populate:function(e,t,a){var n=Tr(a,1),i=n[0];t.checks.push({type:ue.STATE,value:i})}},{name:"id",query:!0,regex:"\\#("+Se.id+")",populate:function(e,t,a){var n=Tr(a,1),i=n[0];t.checks.push({type:ue.ID,value:xt(i)})}},{name:"className",query:!0,regex:"\\.("+Se.className+")",populate:function(e,t,a){var n=Tr(a,1),i=n[0];t.checks.push({type:ue.CLASS,value:xt(i)})}},{name:"dataExists",query:!0,regex:"\\[\\s*("+Se.variable+")\\s*\\]",populate:function(e,t,a){var n=Tr(a,1),i=n[0];t.checks.push({type:ue.DATA_EXIST,field:xt(i)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+Se.variable+")\\s*("+Se.comparatorOp+")\\s*("+Se.value+")\\s*\\]",populate:function(e,t,a){var n=Tr(a,3),i=n[0],s=n[1],o=n[2],l=new RegExp("^"+Se.string+"$").exec(o)!=null;l?o=o.substring(1,o.length-1):o=parseFloat(o),t.checks.push({type:ue.DATA_COMPARE,field:xt(i),operator:s,value:o})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+Se.boolOp+")\\s*("+Se.variable+")\\s*\\]",populate:function(e,t,a){var n=Tr(a,2),i=n[0],s=n[1];t.checks.push({type:ue.DATA_BOOL,field:xt(s),operator:i})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+Se.meta+")\\s*("+Se.comparatorOp+")\\s*("+Se.number+")\\s*\\]\\]",populate:function(e,t,a){var n=Tr(a,3),i=n[0],s=n[1],o=n[2];t.checks.push({type:ue.META_COMPARE,field:xt(i),operator:s,value:parseFloat(o)})}},{name:"nextQuery",separator:!0,regex:Se.separator,populate:function(e,t){var a=e.currentSubject,n=e.edgeCount,i=e.compoundCount,s=e[e.length-1];a!=null&&(s.subject=a,e.currentSubject=null),s.edgeCount=n,s.compoundCount=i,e.edgeCount=0,e.compoundCount=0;var o=e[e.length++]=Re();return o}},{name:"directedEdge",separator:!0,regex:Se.directedEdge,populate:function(e,t){if(e.currentSubject==null){var a=Re(),n=t,i=Re();return a.checks.push({type:ue.DIRECTED_EDGE,source:n,target:i}),Xr(e,t,a),e.edgeCount++,i}else{var s=Re(),o=t,l=Re();return s.checks.push({type:ue.NODE_SOURCE,source:o,target:l}),Xr(e,t,s),e.edgeCount++,l}}},{name:"undirectedEdge",separator:!0,regex:Se.undirectedEdge,populate:function(e,t){if(e.currentSubject==null){var a=Re(),n=t,i=Re();return a.checks.push({type:ue.UNDIRECTED_EDGE,nodes:[n,i]}),Xr(e,t,a),e.edgeCount++,i}else{var s=Re(),o=t,l=Re();return s.checks.push({type:ue.NODE_NEIGHBOR,node:o,neighbor:l}),Xr(e,t,s),l}}},{name:"child",separator:!0,regex:Se.child,populate:function(e,t){if(e.currentSubject==null){var a=Re(),n=Re(),i=e[e.length-1];return a.checks.push({type:ue.CHILD,parent:i,child:n}),Xr(e,t,a),e.compoundCount++,n}else if(e.currentSubject===t){var s=Re(),o=e[e.length-1],l=Re(),u=Re(),v=Re(),f=Re();return s.checks.push({type:ue.COMPOUND_SPLIT,left:o,right:l,subject:u}),u.checks=t.checks,t.checks=[{type:ue.TRUE}],f.checks.push({type:ue.TRUE}),l.checks.push({type:ue.PARENT,parent:f,child:v}),Xr(e,o,s),e.currentSubject=u,e.compoundCount++,v}else{var c=Re(),h=Re(),d=[{type:ue.PARENT,parent:c,child:h}];return c.checks=t.checks,t.checks=d,e.compoundCount++,h}}},{name:"descendant",separator:!0,regex:Se.descendant,populate:function(e,t){if(e.currentSubject==null){var a=Re(),n=Re(),i=e[e.length-1];return a.checks.push({type:ue.DESCENDANT,ancestor:i,descendant:n}),Xr(e,t,a),e.compoundCount++,n}else if(e.currentSubject===t){var s=Re(),o=e[e.length-1],l=Re(),u=Re(),v=Re(),f=Re();return s.checks.push({type:ue.COMPOUND_SPLIT,left:o,right:l,subject:u}),u.checks=t.checks,t.checks=[{type:ue.TRUE}],f.checks.push({type:ue.TRUE}),l.checks.push({type:ue.ANCESTOR,ancestor:f,descendant:v}),Xr(e,o,s),e.currentSubject=u,e.compoundCount++,v}else{var c=Re(),h=Re(),d=[{type:ue.ANCESTOR,ancestor:c,descendant:h}];return c.checks=t.checks,t.checks=d,e.compoundCount++,h}}},{name:"subject",modifier:!0,regex:Se.subject,populate:function(e,t){if(e.currentSubject!=null&&e.currentSubject!==t)return Me("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=t;var a=e[e.length-1],n=a.checks[0],i=n==null?null:n.type;i===ue.DIRECTED_EDGE?n.type=ue.NODE_TARGET:i===ue.UNDIRECTED_EDGE&&(n.type=ue.NODE_NEIGHBOR,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)}}];Xn.forEach(function(r){return r.regexObj=new RegExp("^"+r.regex)});var rh=function(e){for(var t,a,n,i=0;i<Xn.length;i++){var s=Xn[i],o=s.name,l=e.match(s.regexObj);if(l!=null){a=l,t=s,n=o;var u=l[0];e=e.substring(u.length);break}}return{expr:t,match:a,name:n,remaining:e}},th=function(e){var t=e.match(/^\s+/);if(t){var a=t[0];e=e.substring(a.length)}return e},ah=function(e){var t=this,a=t.inputText=e,n=t[0]=Re();for(t.length=1,a=th(a);;){var i=rh(a);if(i.expr==null)return Me("The selector `"+e+"`is invalid"),!1;var s=i.match.slice(1),o=i.expr.populate(t,n,s);if(o===!1)return!1;if(o!=null&&(n=o),a=i.remaining,a.match(/^\s*$/))break}var l=t[t.length-1];t.currentSubject!=null&&(l.subject=t.currentSubject),l.edgeCount=t.edgeCount,l.compoundCount=t.compoundCount;for(var u=0;u<t.length;u++){var v=t[u];if(v.compoundCount>0&&v.edgeCount>0)return Me("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(v.edgeCount>1)return Me("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;v.edgeCount===1&&Me("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},nh=function(){if(this.toStringCache!=null)return this.toStringCache;for(var e=function(v){return v??""},t=function(v){return ce(v)?'"'+v+'"':e(v)},a=function(v){return" "+v+" "},n=function(v,f){var c=v.type,h=v.value;switch(c){case ue.GROUP:{var d=e(h);return d.substring(0,d.length-1)}case ue.DATA_COMPARE:{var y=v.field,p=v.operator;return"["+y+a(e(p))+t(h)+"]"}case ue.DATA_BOOL:{var g=v.operator,m=v.field;return"["+e(g)+m+"]"}case ue.DATA_EXIST:{var b=v.field;return"["+b+"]"}case ue.META_COMPARE:{var w=v.operator,S=v.field;return"[["+S+a(e(w))+t(h)+"]]"}case ue.STATE:return h;case ue.ID:return"#"+h;case ue.CLASS:return"."+h;case ue.PARENT:case ue.CHILD:return i(v.parent,f)+a(">")+i(v.child,f);case ue.ANCESTOR:case ue.DESCENDANT:return i(v.ancestor,f)+" "+i(v.descendant,f);case ue.COMPOUND_SPLIT:{var E=i(v.left,f),x=i(v.subject,f),D=i(v.right,f);return E+(E.length>0?" ":"")+x+D}case ue.TRUE:return""}},i=function(v,f){return v.checks.reduce(function(c,h,d){return c+(f===v&&d===0?"$":"")+n(h,f)},"")},s="",o=0;o<this.length;o++){var l=this[o];s+=i(l,l.subject),this.length>1&&o<this.length-1&&(s+=", ")}return this.toStringCache=s,s},ih={parse:ah,toString:nh},No=function(e,t,a){var n,i=ce(e),s=ne(e),o=ce(a),l,u,v=!1,f=!1,c=!1;switch(t.indexOf("!")>=0&&(t=t.replace("!",""),f=!0),t.indexOf("@")>=0&&(t=t.replace("@",""),v=!0),(i||o||v)&&(l=!i&&!s?"":""+e,u=""+a),v&&(e=l=l.toLowerCase(),a=u=u.toLowerCase()),t){case"*=":n=l.indexOf(u)>=0;break;case"$=":n=l.indexOf(u,l.length-u.length)>=0;break;case"^=":n=l.indexOf(u)===0;break;case"=":n=e===a;break;case">":c=!0,n=e>a;break;case">=":c=!0,n=e>=a;break;case"<":c=!0,n=e<a;break;case"<=":c=!0,n=e<=a;break;default:n=!1;break}return f&&(e!=null||!c)&&(n=!n),n},sh=function(e,t){switch(t){case"?":return!!e;case"!":return!e;case"^":return e===void 0}},oh=function(e){return e!==void 0},bi=function(e,t){return e.data(t)},uh=function(e,t){return e[t]()},He=[],Fe=function(e,t){return e.checks.every(function(a){return He[a.type](a,t)})};He[ue.GROUP]=function(r,e){var t=r.value;return t==="*"||t===e.group()};He[ue.STATE]=function(r,e){var t=r.value;return jd(t,e)};He[ue.ID]=function(r,e){var t=r.value;return e.id()===t};He[ue.CLASS]=function(r,e){var t=r.value;return e.hasClass(t)};He[ue.META_COMPARE]=function(r,e){var t=r.field,a=r.operator,n=r.value;return No(uh(e,t),a,n)};He[ue.DATA_COMPARE]=function(r,e){var t=r.field,a=r.operator,n=r.value;return No(bi(e,t),a,n)};He[ue.DATA_BOOL]=function(r,e){var t=r.field,a=r.operator;return sh(bi(e,t),a)};He[ue.DATA_EXIST]=function(r,e){var t=r.field;return r.operator,oh(bi(e,t))};He[ue.UNDIRECTED_EDGE]=function(r,e){var t=r.nodes[0],a=r.nodes[1],n=e.source(),i=e.target();return Fe(t,n)&&Fe(a,i)||Fe(a,n)&&Fe(t,i)};He[ue.NODE_NEIGHBOR]=function(r,e){return Fe(r.node,e)&&e.neighborhood().some(function(t){return t.isNode()&&Fe(r.neighbor,t)})};He[ue.DIRECTED_EDGE]=function(r,e){return Fe(r.source,e.source())&&Fe(r.target,e.target())};He[ue.NODE_SOURCE]=function(r,e){return Fe(r.source,e)&&e.outgoers().some(function(t){return t.isNode()&&Fe(r.target,t)})};He[ue.NODE_TARGET]=function(r,e){return Fe(r.target,e)&&e.incomers().some(function(t){return t.isNode()&&Fe(r.source,t)})};He[ue.CHILD]=function(r,e){return Fe(r.child,e)&&Fe(r.parent,e.parent())};He[ue.PARENT]=function(r,e){return Fe(r.parent,e)&&e.children().some(function(t){return Fe(r.child,t)})};He[ue.DESCENDANT]=function(r,e){return Fe(r.descendant,e)&&e.ancestors().some(function(t){return Fe(r.ancestor,t)})};He[ue.ANCESTOR]=function(r,e){return Fe(r.ancestor,e)&&e.descendants().some(function(t){return Fe(r.descendant,t)})};He[ue.COMPOUND_SPLIT]=function(r,e){return Fe(r.subject,e)&&Fe(r.left,e)&&Fe(r.right,e)};He[ue.TRUE]=function(){return!0};He[ue.COLLECTION]=function(r,e){var t=r.value;return t.has(e)};He[ue.FILTER]=function(r,e){var t=r.value;return t(e)};var lh=function(e){var t=this;if(t.length===1&&t[0].checks.length===1&&t[0].checks[0].type===ue.ID)return e.getElementById(t[0].checks[0].value).collection();var a=function(i){for(var s=0;s<t.length;s++){var o=t[s];if(Fe(o,i))return!0}return!1};return t.text()==null&&(a=function(){return!0}),e.filter(a)},vh=function(e){for(var t=this,a=0;a<t.length;a++){var n=t[a];if(Fe(n,e))return!0}return!1},fh={matches:vh,filter:lh},rt=function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,e==null||ce(e)&&e.match(/^\s*$/)||(yr(e)?this.addQuery({checks:[{type:ue.COLLECTION,value:e.collection()}]}):qe(e)?this.addQuery({checks:[{type:ue.FILTER,value:e}]}):ce(e)?this.parse(e)||(this.invalid=!0):$e("A selector must be created from a string; found "))},tt=rt.prototype;[ih,fh].forEach(function(r){return pe(tt,r)});tt.text=function(){return this.inputText};tt.size=function(){return this.length};tt.eq=function(r){return this[r]};tt.sameText=function(r){return!this.invalid&&!r.invalid&&this.text()===r.text()};tt.addQuery=function(r){this[this.length++]=r};tt.selector=tt.toString;var Jr={allAre:function(e){var t=new rt(e);return this.every(function(a){return t.matches(a)})},is:function(e){var t=new rt(e);return this.some(function(a){return t.matches(a)})},some:function(e,t){for(var a=0;a<this.length;a++){var n=t?e.apply(t,[this[a],a,this]):e(this[a],a,this);if(n)return!0}return!1},every:function(e,t){for(var a=0;a<this.length;a++){var n=t?e.apply(t,[this[a],a,this]):e(this[a],a,this);if(!n)return!1}return!0},same:function(e){if(this===e)return!0;e=this.cy().collection(e);var t=this.length,a=e.length;return t!==a?!1:t===1?this[0]===e[0]:this.every(function(n){return e.hasElementWithId(n.id())})},anySame:function(e){return e=this.cy().collection(e),this.some(function(t){return e.hasElementWithId(t.id())})},allAreNeighbors:function(e){e=this.cy().collection(e);var t=this.neighborhood();return e.every(function(a){return t.hasElementWithId(a.id())})},contains:function(e){e=this.cy().collection(e);var t=this;return e.every(function(a){return t.hasElementWithId(a.id())})}};Jr.allAreNeighbours=Jr.allAreNeighbors;Jr.has=Jr.contains;Jr.equal=Jr.equals=Jr.same;var Er=function(e,t){return function(n,i,s,o){var l=n,u=this,v;if(l==null?v="":yr(l)&&l.length===1&&(v=l.id()),u.length===1&&v){var f=u[0]._private,c=f.traversalCache=f.traversalCache||{},h=c[t]=c[t]||[],d=dt(v),y=h[d];return y||(h[d]=e.call(u,n,i,s,o))}else return e.call(u,n,i,s,o)}},Nt={parent:function(e){var t=[];if(this.length===1){var a=this[0]._private.parent;if(a)return a}for(var n=0;n<this.length;n++){var i=this[n],s=i._private.parent;s&&t.push(s)}return this.spawn(t,!0).filter(e)},parents:function(e){for(var t=[],a=this.parent();a.nonempty();){for(var n=0;n<a.length;n++){var i=a[n];t.push(i)}a=a.parent()}return this.spawn(t,!0).filter(e)},commonAncestors:function(e){for(var t,a=0;a<this.length;a++){var n=this[a],i=n.parents();t=t||i,t=t.intersect(i)}return t.filter(e)},orphans:function(e){return this.stdFilter(function(t){return t.isOrphan()}).filter(e)},nonorphans:function(e){return this.stdFilter(function(t){return t.isChild()}).filter(e)},children:Er(function(r){for(var e=[],t=0;t<this.length;t++)for(var a=this[t],n=a._private.children,i=0;i<n.length;i++)e.push(n[i]);return this.spawn(e,!0).filter(r)},"children"),siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length!==0},isChildless:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length===0},isChild:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent!=null},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent==null},descendants:function(e){var t=[];function a(n){for(var i=0;i<n.length;i++){var s=n[i];t.push(s),s.children().nonempty()&&a(s.children())}}return a(this.children()),this.spawn(t,!0).filter(e)}};function wi(r,e,t,a){for(var n=[],i=new Vt,s=r.cy(),o=s.hasCompoundNodes(),l=0;l<r.length;l++){var u=r[l];t?n.push(u):o&&a(n,i,u)}for(;n.length>0;){var v=n.shift();e(v),i.add(v.id()),o&&a(n,i,v)}return r}function Fo(r,e,t){if(t.isParent())for(var a=t._private.children,n=0;n<a.length;n++){var i=a[n];e.has(i.id())||r.push(i)}}Nt.forEachDown=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return wi(this,r,e,Fo)};function Vo(r,e,t){if(t.isChild()){var a=t._private.parent;e.has(a.id())||r.push(a)}}Nt.forEachUp=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return wi(this,r,e,Vo)};function ch(r,e,t){Vo(r,e,t),Fo(r,e,t)}Nt.forEachUpAndDown=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return wi(this,r,e,ch)};Nt.ancestors=Nt.parents;var pa,qo;pa=qo={data:Be.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:Be.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:Be.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Be.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:Be.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:Be.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}};pa.attr=pa.data;pa.removeAttr=pa.removeData;var dh=qo,bn={};function zn(r){return function(e){var t=this;if(e===void 0&&(e=!0),t.length!==0)if(t.isNode()&&!t.removed()){for(var a=0,n=t[0],i=n._private.edges,s=0;s<i.length;s++){var o=i[s];!e&&o.isLoop()||(a+=r(n,o))}return a}else return}}pe(bn,{degree:zn(function(r,e){return e.source().same(e.target())?2:1}),indegree:zn(function(r,e){return e.target().same(r)?1:0}),outdegree:zn(function(r,e){return e.source().same(r)?1:0})});function Et(r,e){return function(t){for(var a,n=this.nodes(),i=0;i<n.length;i++){var s=n[i],o=s[r](t);o!==void 0&&(a===void 0||e(o,a))&&(a=o)}return a}}pe(bn,{minDegree:Et("degree",function(r,e){return r<e}),maxDegree:Et("degree",function(r,e){return r>e}),minIndegree:Et("indegree",function(r,e){return r<e}),maxIndegree:Et("indegree",function(r,e){return r>e}),minOutdegree:Et("outdegree",function(r,e){return r<e}),maxOutdegree:Et("outdegree",function(r,e){return r>e})});pe(bn,{totalDegree:function(e){for(var t=0,a=this.nodes(),n=0;n<a.length;n++)t+=a[n].degree(e);return t}});var Pr,$o,Ho=function(e,t,a){for(var n=0;n<e.length;n++){var i=e[n];if(!i.locked()){var s=i._private.position,o={x:t.x!=null?t.x-s.x:0,y:t.y!=null?t.y-s.y:0};i.isParent()&&!(o.x===0&&o.y===0)&&i.children().shift(o,a),i.dirtyBoundingBoxCache()}}},ls={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:function(e,t){Ho(e,t,!1)},onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}};Pr=$o={position:Be.data(ls),silentPosition:Be.data(pe({},ls,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:function(e,t){Ho(e,t,!0)},onSet:function(e){e.dirtyCompoundBoundsCache()}})),positions:function(e,t){if(Te(e))t?this.silentPosition(e):this.position(e);else if(qe(e)){var a=e,n=this.cy();n.startBatch();for(var i=0;i<this.length;i++){var s=this[i],o=void 0;(o=a(s,i))&&(t?s.silentPosition(o):s.position(o))}n.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,t,a){var n;if(Te(e)?(n={x:ne(e.x)?e.x:0,y:ne(e.y)?e.y:0},a=t):ce(e)&&ne(t)&&(n={x:0,y:0},n[e]=t),n!=null){var i=this.cy();i.startBatch();for(var s=0;s<this.length;s++){var o=this[s];if(!(i.hasCompoundNodes()&&o.isChild()&&o.ancestors().anySame(this))){var l=o.position(),u={x:l.x+n.x,y:l.y+n.y};a?o.silentPosition(u):o.position(u)}}i.endBatch()}return this},silentShift:function(e,t){return Te(e)?this.shift(e,!0):ce(e)&&ne(t)&&this.shift(e,t,!0),this},renderedPosition:function(e,t){var a=this[0],n=this.cy(),i=n.zoom(),s=n.pan(),o=Te(e)?e:void 0,l=o!==void 0||t!==void 0&&ce(e);if(a&&a.isNode())if(l)for(var u=0;u<this.length;u++){var v=this[u];t!==void 0?v.position(e,(t-s[e])/i):o!==void 0&&v.position(ho(o,i,s))}else{var f=a.position();return o=hn(f,i,s),e===void 0?o:o[e]}else if(!l)return;return this},relativePosition:function(e,t){var a=this[0],n=this.cy(),i=Te(e)?e:void 0,s=i!==void 0||t!==void 0&&ce(e),o=n.hasCompoundNodes();if(a&&a.isNode())if(s)for(var l=0;l<this.length;l++){var u=this[l],v=o?u.parent():null,f=v&&v.length>0,c=f;f&&(v=v[0]);var h=c?v.position():{x:0,y:0};t!==void 0?u.position(e,t+h[e]):i!==void 0&&u.position({x:i.x+h.x,y:i.y+h.y})}else{var d=a.position(),y=o?a.parent():null,p=y&&y.length>0,g=p;p&&(y=y[0]);var m=g?y.position():{x:0,y:0};return i={x:d.x-m.x,y:d.y-m.y},e===void 0?i:i[e]}else if(!s)return;return this}};Pr.modelPosition=Pr.point=Pr.position;Pr.modelPositions=Pr.points=Pr.positions;Pr.renderedPoint=Pr.renderedPosition;Pr.relativePoint=Pr.relativePosition;var hh=$o,Rt,nt;Rt=nt={};nt.renderedBoundingBox=function(r){var e=this.boundingBox(r),t=this.cy(),a=t.zoom(),n=t.pan(),i=e.x1*a+n.x,s=e.x2*a+n.x,o=e.y1*a+n.y,l=e.y2*a+n.y;return{x1:i,x2:s,y1:o,y2:l,w:s-i,h:l-o}};nt.dirtyCompoundBoundsCache=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();return!e.styleEnabled()||!e.hasCompoundNodes()?this:(this.forEachUp(function(t){if(t.isParent()){var a=t._private;a.compoundBoundsClean=!1,a.bbCache=null,r||t.emitAndNotify("bounds")}}),this)};nt.updateCompoundBounds=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();if(!e.styleEnabled()||!e.hasCompoundNodes())return this;if(!r&&e.batching())return this;function t(s){if(!s.isParent())return;var o=s._private,l=s.children(),u=s.pstyle("compound-sizing-wrt-labels").value==="include",v={width:{val:s.pstyle("min-width").pfValue,left:s.pstyle("min-width-bias-left"),right:s.pstyle("min-width-bias-right")},height:{val:s.pstyle("min-height").pfValue,top:s.pstyle("min-height-bias-top"),bottom:s.pstyle("min-height-bias-bottom")}},f=l.boundingBox({includeLabels:u,includeOverlays:!1,useCache:!1}),c=o.position;(f.w===0||f.h===0)&&(f={w:s.pstyle("width").pfValue,h:s.pstyle("height").pfValue},f.x1=c.x-f.w/2,f.x2=c.x+f.w/2,f.y1=c.y-f.h/2,f.y2=c.y+f.h/2);function h(C,M,P){var B=0,L=0,k=M+P;return C>0&&k>0&&(B=M/k*C,L=P/k*C),{biasDiff:B,biasComplementDiff:L}}function d(C,M,P,B){if(P.units==="%")switch(B){case"width":return C>0?P.pfValue*C:0;case"height":return M>0?P.pfValue*M:0;case"average":return C>0&&M>0?P.pfValue*(C+M)/2:0;case"min":return C>0&&M>0?C>M?P.pfValue*M:P.pfValue*C:0;case"max":return C>0&&M>0?C>M?P.pfValue*C:P.pfValue*M:0;default:return 0}else return P.units==="px"?P.pfValue:0}var y=v.width.left.value;v.width.left.units==="px"&&v.width.val>0&&(y=y*100/v.width.val);var p=v.width.right.value;v.width.right.units==="px"&&v.width.val>0&&(p=p*100/v.width.val);var g=v.height.top.value;v.height.top.units==="px"&&v.height.val>0&&(g=g*100/v.height.val);var m=v.height.bottom.value;v.height.bottom.units==="px"&&v.height.val>0&&(m=m*100/v.height.val);var b=h(v.width.val-f.w,y,p),w=b.biasDiff,S=b.biasComplementDiff,E=h(v.height.val-f.h,g,m),x=E.biasDiff,D=E.biasComplementDiff;o.autoPadding=d(f.w,f.h,s.pstyle("padding"),s.pstyle("padding-relative-to").value),o.autoWidth=Math.max(f.w,v.width.val),c.x=(-w+f.x1+f.x2+S)/2,o.autoHeight=Math.max(f.h,v.height.val),c.y=(-x+f.y1+f.y2+D)/2}for(var a=0;a<this.length;a++){var n=this[a],i=n._private;(!i.compoundBoundsClean||r)&&(t(n),e.batching()||(i.compoundBoundsClean=!0))}return this};var xr=function(e){return e===1/0||e===-1/0?0:e},Dr=function(e,t,a,n,i){n-t===0||i-a===0||t==null||a==null||n==null||i==null||(e.x1=t<e.x1?t:e.x1,e.x2=n>e.x2?n:e.x2,e.y1=a<e.y1?a:e.y1,e.y2=i>e.y2?i:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},ut=function(e,t){return t==null?e:Dr(e,t.x1,t.y1,t.x2,t.y2)},jt=function(e,t,a){return kr(e,t,a)},Na=function(e,t,a){if(!t.cy().headless()){var n=t._private,i=n.rstyle,s=i.arrowWidth/2,o=t.pstyle(a+"-arrow-shape").value,l,u;if(o!=="none"){a==="source"?(l=i.srcX,u=i.srcY):a==="target"?(l=i.tgtX,u=i.tgtY):(l=i.midX,u=i.midY);var v=n.arrowBounds=n.arrowBounds||{},f=v[a]=v[a]||{};f.x1=l-s,f.y1=u-s,f.x2=l+s,f.y2=u+s,f.w=f.x2-f.x1,f.h=f.y2-f.y1,Ga(f,1),Dr(e,f.x1,f.y1,f.x2,f.y2)}}},Nn=function(e,t,a){if(!t.cy().headless()){var n;a?n=a+"-":n="";var i=t._private,s=i.rstyle,o=t.pstyle(n+"label").strValue;if(o){var l=t.pstyle("text-halign"),u=t.pstyle("text-valign"),v=jt(s,"labelWidth",a),f=jt(s,"labelHeight",a),c=jt(s,"labelX",a),h=jt(s,"labelY",a),d=t.pstyle(n+"text-margin-x").pfValue,y=t.pstyle(n+"text-margin-y").pfValue,p=t.isEdge(),g=t.pstyle(n+"text-rotation"),m=t.pstyle("text-outline-width").pfValue,b=t.pstyle("text-border-width").pfValue,w=b/2,S=t.pstyle("text-background-padding").pfValue,E=2,x=f,D=v,C=D/2,M=x/2,P,B,L,k;if(p)P=c-C,B=c+C,L=h-M,k=h+M;else{switch(l.value){case"left":P=c-D,B=c;break;case"center":P=c-C,B=c+C;break;case"right":P=c,B=c+D;break}switch(u.value){case"top":L=h-x,k=h;break;case"center":L=h-M,k=h+M;break;case"bottom":L=h,k=h+x;break}}var O=d-Math.max(m,w)-S-E,A=d+Math.max(m,w)+S+E,R=y-Math.max(m,w)-S-E,I=y+Math.max(m,w)+S+E;P+=O,B+=A,L+=R,k+=I;var V=a||"main",G=i.labelBounds,F=G[V]=G[V]||{};F.x1=P,F.y1=L,F.x2=B,F.y2=k,F.w=B-P,F.h=k-L,F.leftPad=O,F.rightPad=A,F.topPad=R,F.botPad=I;var q=p&&g.strValue==="autorotate",Y=g.pfValue!=null&&g.pfValue!==0;if(q||Y){var Q=q?jt(i.rstyle,"labelAngle",a):g.pfValue,J=Math.cos(Q),_=Math.sin(Q),j=(P+B)/2,W=(L+k)/2;if(!p){switch(l.value){case"left":j=B;break;case"right":j=P;break}switch(u.value){case"top":W=k;break;case"bottom":W=L;break}}var z=function(le,oe){return le=le-j,oe=oe-W,{x:le*J-oe*_+j,y:le*_+oe*J+W}},K=z(P,L),X=z(P,k),ae=z(B,L),he=z(B,k);P=Math.min(K.x,X.x,ae.x,he.x),B=Math.max(K.x,X.x,ae.x,he.x),L=Math.min(K.y,X.y,ae.y,he.y),k=Math.max(K.y,X.y,ae.y,he.y)}var te=V+"Rot",re=G[te]=G[te]||{};re.x1=P,re.y1=L,re.x2=B,re.y2=k,re.w=B-P,re.h=k-L,Dr(e,P,L,B,k),Dr(i.labelBounds.all,P,L,B,k)}return e}},gh=function(e,t){if(!t.cy().headless()){var a=t.pstyle("outline-opacity").value,n=t.pstyle("outline-width").value;if(a>0&&n>0){var i=t.pstyle("outline-offset").value,s=t.pstyle("shape").value,o=n+i,l=(e.w+o*2)/e.w,u=(e.h+o*2)/e.h,v=0,f=0;["diamond","pentagon","round-triangle"].includes(s)?(l=(e.w+o*2.4)/e.w,f=-o/3.6):["concave-hexagon","rhomboid","right-rhomboid"].includes(s)?l=(e.w+o*2.4)/e.w:s==="star"?(l=(e.w+o*2.8)/e.w,u=(e.h+o*2.6)/e.h,f=-o/3.8):s==="triangle"?(l=(e.w+o*2.8)/e.w,u=(e.h+o*2.4)/e.h,f=-o/1.4):s==="vee"&&(l=(e.w+o*4.4)/e.w,u=(e.h+o*3.8)/e.h,f=-o*.5);var c=e.h*u-e.h,h=e.w*l-e.w;if(Ka(e,[Math.ceil(c/2),Math.ceil(h/2)]),v!=0||f!==0){var d=Iv(e,v,f);po(e,d)}}}},ph=function(e,t){var a=e._private.cy,n=a.styleEnabled(),i=a.headless(),s=hr(),o=e._private,l=e.isNode(),u=e.isEdge(),v,f,c,h,d,y,p=o.rstyle,g=l&&n?e.pstyle("bounds-expansion").pfValue:[0],m=function(ve){return ve.pstyle("display").value!=="none"},b=!n||m(e)&&(!u||m(e.source())&&m(e.target()));if(b){var w=0,S=0;n&&t.includeOverlays&&(w=e.pstyle("overlay-opacity").value,w!==0&&(S=e.pstyle("overlay-padding").value));var E=0,x=0;n&&t.includeUnderlays&&(E=e.pstyle("underlay-opacity").value,E!==0&&(x=e.pstyle("underlay-padding").value));var D=Math.max(S,x),C=0,M=0;if(n&&(C=e.pstyle("width").pfValue,M=C/2),l&&t.includeNodes){var P=e.position();d=P.x,y=P.y;var B=e.outerWidth(),L=B/2,k=e.outerHeight(),O=k/2;v=d-L,f=d+L,c=y-O,h=y+O,Dr(s,v,c,f,h),n&&t.includeOutlines&&gh(s,e)}else if(u&&t.includeEdges)if(n&&!i){var A=e.pstyle("curve-style").strValue;if(v=Math.min(p.srcX,p.midX,p.tgtX),f=Math.max(p.srcX,p.midX,p.tgtX),c=Math.min(p.srcY,p.midY,p.tgtY),h=Math.max(p.srcY,p.midY,p.tgtY),v-=M,f+=M,c-=M,h+=M,Dr(s,v,c,f,h),A==="haystack"){var R=p.haystackPts;if(R&&R.length===2){if(v=R[0].x,c=R[0].y,f=R[1].x,h=R[1].y,v>f){var I=v;v=f,f=I}if(c>h){var V=c;c=h,h=V}Dr(s,v-M,c-M,f+M,h+M)}}else if(A==="bezier"||A==="unbundled-bezier"||A.endsWith("segments")||A.endsWith("taxi")){var G;switch(A){case"bezier":case"unbundled-bezier":G=p.bezierPts;break;case"segments":case"taxi":case"round-segments":case"round-taxi":G=p.linePts;break}if(G!=null)for(var F=0;F<G.length;F++){var q=G[F];v=q.x-M,f=q.x+M,c=q.y-M,h=q.y+M,Dr(s,v,c,f,h)}}}else{var Y=e.source(),Q=Y.position(),J=e.target(),_=J.position();if(v=Q.x,f=_.x,c=Q.y,h=_.y,v>f){var j=v;v=f,f=j}if(c>h){var W=c;c=h,h=W}v-=M,f+=M,c-=M,h+=M,Dr(s,v,c,f,h)}if(n&&t.includeEdges&&u&&(Na(s,e,"mid-source"),Na(s,e,"mid-target"),Na(s,e,"source"),Na(s,e,"target")),n){var z=e.pstyle("ghost").value==="yes";if(z){var K=e.pstyle("ghost-offset-x").pfValue,X=e.pstyle("ghost-offset-y").pfValue;Dr(s,s.x1+K,s.y1+X,s.x2+K,s.y2+X)}}var ae=o.bodyBounds=o.bodyBounds||{};Hi(ae,s),Ka(ae,g),Ga(ae,1),n&&(v=s.x1,f=s.x2,c=s.y1,h=s.y2,Dr(s,v-D,c-D,f+D,h+D));var he=o.overlayBounds=o.overlayBounds||{};Hi(he,s),Ka(he,g),Ga(he,1);var te=o.labelBounds=o.labelBounds||{};te.all!=null?Ov(te.all):te.all=hr(),n&&t.includeLabels&&(t.includeMainLabels&&Nn(s,e,null),u&&(t.includeSourceLabels&&Nn(s,e,"source"),t.includeTargetLabels&&Nn(s,e,"target")))}return s.x1=xr(s.x1),s.y1=xr(s.y1),s.x2=xr(s.x2),s.y2=xr(s.y2),s.w=xr(s.x2-s.x1),s.h=xr(s.y2-s.y1),s.w>0&&s.h>0&&b&&(Ka(s,g),Ga(s,1)),s},Go=function(e){var t=0,a=function(s){return(s?1:0)<<t++},n=0;return n+=a(e.incudeNodes),n+=a(e.includeEdges),n+=a(e.includeLabels),n+=a(e.includeMainLabels),n+=a(e.includeSourceLabels),n+=a(e.includeTargetLabels),n+=a(e.includeOverlays),n+=a(e.includeOutlines),n},Ko=function(e){if(e.isEdge()){var t=e.source().position(),a=e.target().position(),n=function(s){return Math.round(s)};return rv([n(t.x),n(t.y),n(a.x),n(a.y)])}else return 0},vs=function(e,t){var a=e._private,n,i=e.isEdge(),s=t==null?fs:Go(t),o=s===fs,l=Ko(e),u=a.bbCachePosKey===l,v=t.useCache&&u,f=function(y){return y._private.bbCache==null||y._private.styleDirty},c=!v||f(e)||i&&(f(e.source())||f(e.target()));if(c?(u||e.recalculateRenderedStyle(v),n=ph(e,ya),a.bbCache=n,a.bbCachePosKey=l):n=a.bbCache,!o){var h=e.isNode();n=hr(),(t.includeNodes&&h||t.includeEdges&&!h)&&(t.includeOverlays?ut(n,a.overlayBounds):ut(n,a.bodyBounds)),t.includeLabels&&(t.includeMainLabels&&(!i||t.includeSourceLabels&&t.includeTargetLabels)?ut(n,a.labelBounds.all):(t.includeMainLabels&&ut(n,a.labelBounds.mainRot),t.includeSourceLabels&&ut(n,a.labelBounds.sourceRot),t.includeTargetLabels&&ut(n,a.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},ya={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,includeOutlines:!0,useCache:!0},fs=Go(ya),cs=rr(ya);nt.boundingBox=function(r){var e;if(this.length===1&&this[0]._private.bbCache!=null&&!this[0]._private.styleDirty&&(r===void 0||r.useCache===void 0||r.useCache===!0))r===void 0?r=ya:r=cs(r),e=vs(this[0],r);else{e=hr(),r=r||ya;var t=cs(r),a=this,n=a.cy(),i=n.styleEnabled();if(i)for(var s=0;s<a.length;s++){var o=a[s],l=o._private,u=Ko(o),v=l.bbCachePosKey===u,f=t.useCache&&v&&!l.styleDirty;o.recalculateRenderedStyle(f)}this.updateCompoundBounds(!r.useCache);for(var c=0;c<a.length;c++){var h=a[c];ut(e,vs(h,t))}}return e.x1=xr(e.x1),e.y1=xr(e.y1),e.x2=xr(e.x2),e.y2=xr(e.y2),e.w=xr(e.x2-e.x1),e.h=xr(e.y2-e.y1),e};nt.dirtyBoundingBoxCache=function(){for(var r=0;r<this.length;r++){var e=this[r]._private;e.bbCache=null,e.bbCachePosKey=null,e.bodyBounds=null,e.overlayBounds=null,e.labelBounds.all=null,e.labelBounds.source=null,e.labelBounds.target=null,e.labelBounds.main=null,e.labelBounds.sourceRot=null,e.labelBounds.targetRot=null,e.labelBounds.mainRot=null,e.arrowBounds.source=null,e.arrowBounds.target=null,e.arrowBounds["mid-source"]=null,e.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this};nt.boundingBoxAt=function(r){var e=this.nodes(),t=this.cy(),a=t.hasCompoundNodes(),n=t.collection();if(a&&(n=e.filter(function(u){return u.isParent()}),e=e.not(n)),Te(r)){var i=r;r=function(){return i}}var s=function(v,f){return v._private.bbAtOldPos=r(v,f)},o=function(v){return v._private.bbAtOldPos};t.startBatch(),e.forEach(s).silentPositions(r),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0));var l=Rv(this.boundingBox({useCache:!1}));return e.silentPositions(o),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0)),t.endBatch(),l};Rt.boundingbox=Rt.bb=Rt.boundingBox;Rt.renderedBoundingbox=Rt.renderedBoundingBox;var yh=nt,ia,Sa;ia=Sa={};var Wo=function(e){e.uppercaseName=Ii(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=Ii(e.outerName),ia[e.name]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){if(a.isParent())return a.updateCompoundBounds(),n[e.autoName]||0;var o=a.pstyle(e.name);switch(o.strValue){case"label":return a.recalculateRenderedStyle(),n.rstyle[e.labelName]||0;default:return o.pfValue}}else return 1},ia["outer"+e.uppercaseName]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){var o=a[e.name](),l=a.pstyle("border-width").pfValue,u=2*a.padding();return o+l+u}else return 1},ia["rendered"+e.uppercaseName]=function(){var a=this[0];if(a){var n=a[e.name]();return n*this.cy().zoom()}},ia["rendered"+e.uppercaseOuterName]=function(){var a=this[0];if(a){var n=a[e.outerName]();return n*this.cy().zoom()}}};Wo({name:"width"});Wo({name:"height"});Sa.padding=function(){var r=this[0],e=r._private;return r.isParent()?(r.updateCompoundBounds(),e.autoPadding!==void 0?e.autoPadding:r.pstyle("padding").pfValue):r.pstyle("padding").pfValue};Sa.paddedHeight=function(){var r=this[0];return r.height()+2*r.padding()};Sa.paddedWidth=function(){var r=this[0];return r.width()+2*r.padding()};var mh=Sa,bh=function(e,t){if(e.isEdge())return t(e)},wh=function(e,t){if(e.isEdge()){var a=e.cy();return hn(t(e),a.zoom(),a.pan())}},xh=function(e,t){if(e.isEdge()){var a=e.cy(),n=a.pan(),i=a.zoom();return t(e).map(function(s){return hn(s,i,n)})}},Eh=function(e){return e.renderer().getControlPoints(e)},Ch=function(e){return e.renderer().getSegmentPoints(e)},Sh=function(e){return e.renderer().getSourceEndpoint(e)},Th=function(e){return e.renderer().getTargetEndpoint(e)},Dh=function(e){return e.renderer().getEdgeMidpoint(e)},ds={controlPoints:{get:Eh,mult:!0},segmentPoints:{get:Ch,mult:!0},sourceEndpoint:{get:Sh},targetEndpoint:{get:Th},midpoint:{get:Dh}},kh=function(e){return"rendered"+e[0].toUpperCase()+e.substr(1)},Ph=Object.keys(ds).reduce(function(r,e){var t=ds[e],a=kh(e);return r[e]=function(){return bh(this,t.get)},t.mult?r[a]=function(){return xh(this,t.get)}:r[a]=function(){return wh(this,t.get)},r},{}),Bh=pe({},hh,yh,mh,Ph);/*!
Event object based on jQuery events, MIT license

https://jquery.org/license/
https://tldrlegal.com/license/mit-license
https://github.com/jquery/jquery/blob/master/src/event.js
*/var Uo=function(e,t){this.recycle(e,t)};function ea(){return!1}function Fa(){return!0}Uo.prototype={instanceString:function(){return"event"},recycle:function(e,t){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=ea,e!=null&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?Fa:ea):e!=null&&e.type?t=e:this.type=e,t!=null&&(this.originalEvent=t.originalEvent,this.type=t.type!=null?t.type:this.type,this.cy=t.cy,this.target=t.target,this.position=t.position,this.renderedPosition=t.renderedPosition,this.namespace=t.namespace,this.layout=t.layout),this.cy!=null&&this.position!=null&&this.renderedPosition==null){var a=this.position,n=this.cy.zoom(),i=this.cy.pan();this.renderedPosition={x:a.x*n+i.x,y:a.y*n+i.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=Fa;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=Fa;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=Fa,this.stopPropagation()},isDefaultPrevented:ea,isPropagationStopped:ea,isImmediatePropagationStopped:ea};var Yo=/^([^.]+)(\.(?:[^.]+))?$/,Mh=".*",Xo={qualifierCompare:function(e,t){return e===t},eventMatches:function(){return!0},addEventFields:function(){},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:null},hs=Object.keys(Xo),Lh={};function wn(){for(var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Lh,e=arguments.length>1?arguments[1]:void 0,t=0;t<hs.length;t++){var a=hs[t];this[a]=r[a]||Xo[a]}this.context=e||this.context,this.listeners=[],this.emitting=0}var at=wn.prototype,Zo=function(e,t,a,n,i,s,o){qe(n)&&(i=n,n=null),o&&(s==null?s=o:s=pe({},s,o));for(var l=Oe(a)?a:a.split(/\s+/),u=0;u<l.length;u++){var v=l[u];if(!jr(v)){var f=v.match(Yo);if(f){var c=f[1],h=f[2]?f[2]:null,d=t(e,v,c,h,n,i,s);if(d===!1)break}}}},gs=function(e,t){return e.addEventFields(e.context,t),new Uo(t.type,t)},Ah=function(e,t,a){if(Ju(a)){t(e,a);return}else if(Te(a)){t(e,gs(e,a));return}for(var n=Oe(a)?a:a.split(/\s+/),i=0;i<n.length;i++){var s=n[i];if(!jr(s)){var o=s.match(Yo);if(o){var l=o[1],u=o[2]?o[2]:null,v=gs(e,{type:l,namespace:u,target:e.context});t(e,v)}}}};at.on=at.addListener=function(r,e,t,a,n){return Zo(this,function(i,s,o,l,u,v,f){qe(v)&&i.listeners.push({event:s,callback:v,type:o,namespace:l,qualifier:u,conf:f})},r,e,t,a,n),this};at.one=function(r,e,t,a){return this.on(r,e,t,a,{one:!0})};at.removeListener=at.off=function(r,e,t,a){var n=this;this.emitting!==0&&(this.listeners=sv(this.listeners));for(var i=this.listeners,s=function(u){var v=i[u];Zo(n,function(f,c,h,d,y,p){if((v.type===h||r==="*")&&(!d&&v.namespace!==".*"||v.namespace===d)&&(!y||f.qualifierCompare(v.qualifier,y))&&(!p||v.callback===p))return i.splice(u,1),!1},r,e,t,a)},o=i.length-1;o>=0;o--)s(o);return this};at.removeAllListeners=function(){return this.removeListener("*")};at.emit=at.trigger=function(r,e,t){var a=this.listeners,n=a.length;return this.emitting++,Oe(e)||(e=[e]),Ah(this,function(i,s){t!=null&&(a=[{event:s.event,type:s.type,namespace:s.namespace,callback:t}],n=a.length);for(var o=function(v){var f=a[v];if(f.type===s.type&&(!f.namespace||f.namespace===s.namespace||f.namespace===Mh)&&i.eventMatches(i.context,f,s)){var c=[s];e!=null&&uv(c,e),i.beforeEmit(i.context,f,s),f.conf&&f.conf.one&&(i.listeners=i.listeners.filter(function(y){return y!==f}));var h=i.callbackContext(i.context,f,s),d=f.callback.apply(h,c);i.afterEmit(i.context,f,s),d===!1&&(s.stopPropagation(),s.preventDefault())}},l=0;l<n;l++)o(l);i.bubble(i.context)&&!s.isPropagationStopped()&&i.parent(i.context).emit(s,e)},r),this.emitting--,this};var Rh={qualifierCompare:function(e,t){return e==null||t==null?e==null&&t==null:e.sameText(t)},eventMatches:function(e,t,a){var n=t.qualifier;return n!=null?e!==a.target&&xa(a.target)&&n.matches(a.target):!0},addEventFields:function(e,t){t.cy=e.cy(),t.target=e},callbackContext:function(e,t,a){return t.qualifier!=null?a.target:e},beforeEmit:function(e,t){t.conf&&t.conf.once&&t.conf.onceCollection.removeListener(t.event,t.qualifier,t.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},Va=function(e){return ce(e)?new rt(e):e},Qo={createEmitter:function(){for(var e=0;e<this.length;e++){var t=this[e],a=t._private;a.emitter||(a.emitter=new wn(Rh,t))}return this},emitter:function(){return this._private.emitter},on:function(e,t,a){for(var n=Va(t),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a)}return this},removeListener:function(e,t,a){for(var n=Va(t),i=0;i<this.length;i++){var s=this[i];s.emitter().removeListener(e,n,a)}return this},removeAllListeners:function(){for(var e=0;e<this.length;e++){var t=this[e];t.emitter().removeAllListeners()}return this},one:function(e,t,a){for(var n=Va(t),i=0;i<this.length;i++){var s=this[i];s.emitter().one(e,n,a)}return this},once:function(e,t,a){for(var n=Va(t),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a,{once:!0,onceCollection:this})}},emit:function(e,t){for(var a=0;a<this.length;a++){var n=this[a];n.emitter().emit(e,t)}return this},emitAndNotify:function(e,t){if(this.length!==0)return this.cy().notify(e,this),this.emit(e,t),this}};Be.eventAliasesOn(Qo);var Jo={nodes:function(e){return this.filter(function(t){return t.isNode()}).filter(e)},edges:function(e){return this.filter(function(t){return t.isEdge()}).filter(e)},byGroup:function(){for(var e=this.spawn(),t=this.spawn(),a=0;a<this.length;a++){var n=this[a];n.isNode()?e.push(n):t.push(n)}return{nodes:e,edges:t}},filter:function(e,t){if(e===void 0)return this;if(ce(e)||yr(e))return new rt(e).filter(this);if(qe(e)){for(var a=this.spawn(),n=this,i=0;i<n.length;i++){var s=n[i],o=t?e.apply(t,[s,i,n]):e(s,i,n);o&&a.push(s)}return a}return this.spawn()},not:function(e){if(e){ce(e)&&(e=this.filter(e));for(var t=this.spawn(),a=0;a<this.length;a++){var n=this[a],i=e.has(n);i||t.push(n)}return t}else return this},absoluteComplement:function(){var e=this.cy();return e.mutableElements().not(this)},intersect:function(e){if(ce(e)){var t=e;return this.filter(t)}for(var a=this.spawn(),n=this,i=e,s=this.length<e.length,o=s?n:i,l=s?i:n,u=0;u<o.length;u++){var v=o[u];l.has(v)&&a.push(v)}return a},xor:function(e){var t=this._private.cy;ce(e)&&(e=t.$(e));var a=this.spawn(),n=this,i=e,s=function(l,u){for(var v=0;v<l.length;v++){var f=l[v],c=f._private.data.id,h=u.hasElementWithId(c);h||a.push(f)}};return s(n,i),s(i,n),a},diff:function(e){var t=this._private.cy;ce(e)&&(e=t.$(e));var a=this.spawn(),n=this.spawn(),i=this.spawn(),s=this,o=e,l=function(v,f,c){for(var h=0;h<v.length;h++){var d=v[h],y=d._private.data.id,p=f.hasElementWithId(y);p?i.merge(d):c.push(d)}};return l(s,o,a),l(o,s,n),{left:a,right:n,both:i}},add:function(e){var t=this._private.cy;if(!e)return this;if(ce(e)){var a=e;e=t.mutableElements().filter(a)}for(var n=this.spawnSelf(),i=0;i<e.length;i++){var s=e[i],o=!this.has(s);o&&n.push(s)}return n},merge:function(e){var t=this._private,a=t.cy;if(!e)return this;if(e&&ce(e)){var n=e;e=a.mutableElements().filter(n)}for(var i=t.map,s=0;s<e.length;s++){var o=e[s],l=o._private.data.id,u=!i.has(l);if(u){var v=this.length++;this[v]=o,i.set(l,{ele:o,index:v})}}return this},unmergeAt:function(e){var t=this[e],a=t.id(),n=this._private,i=n.map;this[e]=void 0,i.delete(a);var s=e===this.length-1;if(this.length>1&&!s){var o=this.length-1,l=this[o],u=l._private.data.id;this[o]=void 0,this[e]=l,i.set(u,{ele:l,index:e})}return this.length--,this},unmergeOne:function(e){e=e[0];var t=this._private,a=e._private.data.id,n=t.map,i=n.get(a);if(!i)return this;var s=i.index;return this.unmergeAt(s),this},unmerge:function(e){var t=this._private.cy;if(!e)return this;if(e&&ce(e)){var a=e;e=t.mutableElements().filter(a)}for(var n=0;n<e.length;n++)this.unmergeOne(e[n]);return this},unmergeBy:function(e){for(var t=this.length-1;t>=0;t--){var a=this[t];e(a)&&this.unmergeAt(t)}return this},map:function(e,t){for(var a=[],n=this,i=0;i<n.length;i++){var s=n[i],o=t?e.apply(t,[s,i,n]):e(s,i,n);a.push(o)}return a},reduce:function(e,t){for(var a=t,n=this,i=0;i<n.length;i++)a=e(a,n[i],i,n);return a},max:function(e,t){for(var a=-1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],l=t?e.apply(t,[o,s,i]):e(o,s,i);l>a&&(a=l,n=o)}return{value:a,ele:n}},min:function(e,t){for(var a=1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],l=t?e.apply(t,[o,s,i]):e(o,s,i);l<a&&(a=l,n=o)}return{value:a,ele:n}}},ke=Jo;ke.u=ke["|"]=ke["+"]=ke.union=ke.or=ke.add;ke["\\"]=ke["!"]=ke["-"]=ke.difference=ke.relativeComplement=ke.subtract=ke.not;ke.n=ke["&"]=ke["."]=ke.and=ke.intersection=ke.intersect;ke["^"]=ke["(+)"]=ke["(-)"]=ke.symmetricDifference=ke.symdiff=ke.xor;ke.fnFilter=ke.filterFn=ke.stdFilter=ke.filter;ke.complement=ke.abscomp=ke.absoluteComplement;var Oh={isNode:function(){return this.group()==="nodes"},isEdge:function(){return this.group()==="edges"},isLoop:function(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function(){var e=this[0];if(e)return e._private.group}},_o=function(e,t){var a=e.cy(),n=a.hasCompoundNodes();function i(v){var f=v.pstyle("z-compound-depth");return f.value==="auto"?n?v.zDepth():0:f.value==="bottom"?-1:f.value==="top"?li:0}var s=i(e)-i(t);if(s!==0)return s;function o(v){var f=v.pstyle("z-index-compare");return f.value==="auto"&&v.isNode()?1:0}var l=o(e)-o(t);if(l!==0)return l;var u=e.pstyle("z-index").value-t.pstyle("z-index").value;return u!==0?u:e.poolIndex()-t.poolIndex()},nn={forEach:function(e,t){if(qe(e))for(var a=this.length,n=0;n<a;n++){var i=this[n],s=t?e.apply(t,[i,n,this]):e(i,n,this);if(s===!1)break}return this},toArray:function(){for(var e=[],t=0;t<this.length;t++)e.push(this[t]);return e},slice:function(e,t){var a=[],n=this.length;t==null&&(t=n),e==null&&(e=0),e<0&&(e=n+e),t<0&&(t=n+t);for(var i=e;i>=0&&i<t&&i<n;i++)a.push(this[i]);return this.spawn(a)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return this.length===0},nonempty:function(){return!this.empty()},sort:function(e){if(!qe(e))return this;var t=this.toArray().sort(e);return this.spawn(t)},sortByZIndex:function(){return this.sort(_o)},zDepth:function(){var e=this[0];if(e){var t=e._private,a=t.group;if(a==="nodes"){var n=t.data.parent?e.parents().size():0;return e.isParent()?n:li-1}else{var i=t.source,s=t.target,o=i.zDepth(),l=s.zDepth();return Math.max(o,l,0)}}}};nn.each=nn.forEach;var Ih=function(){var e="undefined",t=(typeof Symbol>"u"?"undefined":Ue(Symbol))!=e&&Ue(Symbol.iterator)!=e;t&&(nn[Symbol.iterator]=function(){var a=this,n={value:void 0,done:!1},i=0,s=this.length;return Ys({next:function(){return i<s?n.value=a[i++]:(n.value=void 0,n.done=!0),n}},Symbol.iterator,function(){return this})})};Ih();var zh=rr({nodeDimensionsIncludeLabels:!1}),Ua={layoutDimensions:function(e){e=zh(e);var t;if(!this.takesUpSpace())t={w:0,h:0};else if(e.nodeDimensionsIncludeLabels){var a=this.boundingBox();t={w:a.w,h:a.h}}else t={w:this.outerWidth(),h:this.outerHeight()};return(t.w===0||t.h===0)&&(t.w=t.h=1),t},layoutPositions:function(e,t,a){var n=this.nodes().filter(function(S){return!S.isParent()}),i=this.cy(),s=t.eles,o=function(E){return E.id()},l=la(a,o);e.emit({type:"layoutstart",layout:e}),e.animations=[];var u=function(E,x,D){var C={x:x.x1+x.w/2,y:x.y1+x.h/2},M={x:(D.x-C.x)*E,y:(D.y-C.y)*E};return{x:C.x+M.x,y:C.y+M.y}},v=t.spacingFactor&&t.spacingFactor!==1,f=function(){if(!v)return null;for(var E=hr(),x=0;x<n.length;x++){var D=n[x],C=l(D,x);zv(E,C.x,C.y)}return E},c=f(),h=la(function(S,E){var x=l(S,E);if(v){var D=Math.abs(t.spacingFactor);x=u(D,c,x)}return t.transform!=null&&(x=t.transform(S,x)),x},o);if(t.animate){for(var d=0;d<n.length;d++){var y=n[d],p=h(y,d),g=t.animateFilter==null||t.animateFilter(y,d);if(g){var m=y.animation({position:p,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(m)}else y.position(p)}if(t.fit){var b=i.animation({fit:{boundingBox:s.boundingBoxAt(h),padding:t.padding},duration:t.animationDuration,easing:t.animationEasing});e.animations.push(b)}else if(t.zoom!==void 0&&t.pan!==void 0){var w=i.animation({zoom:t.zoom,pan:t.pan,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(w)}e.animations.forEach(function(S){return S.play()}),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),qt.all(e.animations.map(function(S){return S.promise()})).then(function(){e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e})})}else n.positions(h),t.fit&&i.fit(t.eles,t.padding),t.zoom!=null&&i.zoom(t.zoom),t.pan&&i.pan(t.pan),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){var t=this.cy();return t.makeLayout(pe({},e,{eles:this}))}};Ua.createLayout=Ua.makeLayout=Ua.layout;function jo(r,e,t){var a=t._private,n=a.styleCache=a.styleCache||[],i;return(i=n[r])!=null||(i=n[r]=e(t)),i}function xn(r,e){return r=dt(r),function(a){return jo(r,e,a)}}function En(r,e){r=dt(r);var t=function(n){return e.call(n)};return function(){var n=this[0];if(n)return jo(r,t,n)}}var je={recalculateRenderedStyle:function(e){var t=this.cy(),a=t.renderer(),n=t.styleEnabled();return a&&n&&a.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e=this.cy(),t=function(i){return i._private.styleCache=null};if(e.hasCompoundNodes()){var a;a=this.spawnSelf().merge(this.descendants()).merge(this.parents()),a.merge(a.connectedEdges()),a.forEach(t)}else this.forEach(function(n){t(n),n.connectedEdges().forEach(t)});return this},updateStyle:function(e){var t=this._private.cy;if(!t.styleEnabled())return this;if(t.batching()){var a=t._private.batchStyleEles;return a.merge(this),this}var n=t.hasCompoundNodes(),i=this;e=!!(e||e===void 0),n&&(i=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var s=i;return e?s.emitAndNotify("style"):s.emit("style"),i.forEach(function(o){return o._private.styleDirty=!0}),this},cleanStyle:function(){var e=this.cy();if(e.styleEnabled())for(var t=0;t<this.length;t++){var a=this[t];a._private.styleDirty&&(a._private.styleDirty=!1,e.style().apply(a))}},parsedStyle:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,a=this[0],n=a.cy();if(n.styleEnabled()&&a){this.cleanStyle();var i=a._private.style[e];return i??(t?n.style().getDefaultProperty(e):null)}},numericStyle:function(e){var t=this[0];if(t.cy().styleEnabled()&&t){var a=t.pstyle(e);return a.pfValue!==void 0?a.pfValue:a.value}},numericStyleUnits:function(e){var t=this[0];if(t.cy().styleEnabled()&&t)return t.pstyle(e).units},renderedStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var a=this[0];if(a)return t.style().getRenderedStyle(a,e)},style:function(e,t){var a=this.cy();if(!a.styleEnabled())return this;var n=!1,i=a.style();if(Te(e)){var s=e;i.applyBypass(this,s,n),this.emitAndNotify("style")}else if(ce(e))if(t===void 0){var o=this[0];return o?i.getStylePropertyValue(o,e):void 0}else i.applyBypass(this,e,t,n),this.emitAndNotify("style");else if(e===void 0){var l=this[0];return l?i.getRawStyle(l):void 0}return this},removeStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var a=!1,n=t.style(),i=this;if(e===void 0)for(var s=0;s<i.length;s++){var o=i[s];n.removeAllBypasses(o,a)}else{e=e.split(/\s+/);for(var l=0;l<i.length;l++){var u=i[l];n.removeBypasses(u,e,a)}}return this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var t=e.hasCompoundNodes(),a=this[0];if(a){var n=a._private,i=a.pstyle("opacity").value;if(!t)return i;var s=n.data.parent?a.parents():null;if(s)for(var o=0;o<s.length;o++){var l=s[o],u=l.pstyle("opacity").value;i=u*i}return i}},transparent:function(){var e=this.cy();if(!e.styleEnabled())return!1;var t=this[0],a=t.cy().hasCompoundNodes();if(t)return a?t.effectiveOpacity()===0:t.pstyle("opacity").value===0},backgrounding:function(){var e=this.cy();if(!e.styleEnabled())return!1;var t=this[0];return!!t._private.backgrounding}};function Fn(r,e){var t=r._private,a=t.data.parent?r.parents():null;if(a)for(var n=0;n<a.length;n++){var i=a[n];if(!e(i))return!1}return!0}function xi(r){var e=r.ok,t=r.edgeOkViaNode||r.ok,a=r.parentOk||r.ok;return function(){var n=this.cy();if(!n.styleEnabled())return!0;var i=this[0],s=n.hasCompoundNodes();if(i){var o=i._private;if(!e(i))return!1;if(i.isNode())return!s||Fn(i,a);var l=o.source,u=o.target;return t(l)&&(!s||Fn(l,t))&&(l===u||t(u)&&(!s||Fn(u,t)))}}}var Kt=xn("eleTakesUpSpace",function(r){return r.pstyle("display").value==="element"&&r.width()!==0&&(r.isNode()?r.height()!==0:!0)});je.takesUpSpace=En("takesUpSpace",xi({ok:Kt}));var Nh=xn("eleInteractive",function(r){return r.pstyle("events").value==="yes"&&r.pstyle("visibility").value==="visible"&&Kt(r)}),Fh=xn("parentInteractive",function(r){return r.pstyle("visibility").value==="visible"&&Kt(r)});je.interactive=En("interactive",xi({ok:Nh,parentOk:Fh,edgeOkViaNode:Kt}));je.noninteractive=function(){var r=this[0];if(r)return!r.interactive()};var Vh=xn("eleVisible",function(r){return r.pstyle("visibility").value==="visible"&&r.pstyle("opacity").pfValue!==0&&Kt(r)}),qh=Kt;je.visible=En("visible",xi({ok:Vh,edgeOkViaNode:qh}));je.hidden=function(){var r=this[0];if(r)return!r.visible()};je.isBundledBezier=En("isBundledBezier",function(){return this.cy().styleEnabled()?!this.removed()&&this.pstyle("curve-style").value==="bezier"&&this.takesUpSpace():!1});je.bypass=je.css=je.style;je.renderedCss=je.renderedStyle;je.removeBypass=je.removeCss=je.removeStyle;je.pstyle=je.parsedStyle;var _r={};function ps(r){return function(){var e=arguments,t=[];if(e.length===2){var a=e[0],n=e[1];this.on(r.event,a,n)}else if(e.length===1&&qe(e[0])){var i=e[0];this.on(r.event,i)}else if(e.length===0||e.length===1&&Oe(e[0])){for(var s=e.length===1?e[0]:null,o=0;o<this.length;o++){var l=this[o],u=!r.ableField||l._private[r.ableField],v=l._private[r.field]!=r.value;if(r.overrideAble){var f=r.overrideAble(l);if(f!==void 0&&(u=f,!f))return this}u&&(l._private[r.field]=r.value,v&&t.push(l))}var c=this.spawn(t);c.updateStyle(),c.emit(r.event),s&&c.emit(s)}return this}}function Wt(r){_r[r.field]=function(){var e=this[0];if(e){if(r.overrideField){var t=r.overrideField(e);if(t!==void 0)return t}return e._private[r.field]}},_r[r.on]=ps({event:r.on,field:r.field,ableField:r.ableField,overrideAble:r.overrideAble,value:!0}),_r[r.off]=ps({event:r.off,field:r.field,ableField:r.ableField,overrideAble:r.overrideAble,value:!1})}Wt({field:"locked",overrideField:function(e){return e.cy().autolock()?!0:void 0},on:"lock",off:"unlock"});Wt({field:"grabbable",overrideField:function(e){return e.cy().autoungrabify()||e.pannable()?!1:void 0},on:"grabify",off:"ungrabify"});Wt({field:"selected",ableField:"selectable",overrideAble:function(e){return e.cy().autounselectify()?!1:void 0},on:"select",off:"unselect"});Wt({field:"selectable",overrideField:function(e){return e.cy().autounselectify()?!1:void 0},on:"selectify",off:"unselectify"});_r.deselect=_r.unselect;_r.grabbed=function(){var r=this[0];if(r)return r._private.grabbed};Wt({field:"active",on:"activate",off:"unactivate"});Wt({field:"pannable",on:"panify",off:"unpanify"});_r.inactive=function(){var r=this[0];if(r)return!r._private.active};var ir={},ys=function(e){return function(a){for(var n=this,i=[],s=0;s<n.length;s++){var o=n[s];if(o.isNode()){for(var l=!1,u=o.connectedEdges(),v=0;v<u.length;v++){var f=u[v],c=f.source(),h=f.target();if(e.noIncomingEdges&&h===o&&c!==o||e.noOutgoingEdges&&c===o&&h!==o){l=!0;break}}l||i.push(o)}}return this.spawn(i,!0).filter(a)}},ms=function(e){return function(t){for(var a=this,n=[],i=0;i<a.length;i++){var s=a[i];if(s.isNode())for(var o=s.connectedEdges(),l=0;l<o.length;l++){var u=o[l],v=u.source(),f=u.target();e.outgoing&&v===s?(n.push(u),n.push(f)):e.incoming&&f===s&&(n.push(u),n.push(v))}}return this.spawn(n,!0).filter(t)}},bs=function(e){return function(t){for(var a=this,n=[],i={};;){var s=e.outgoing?a.outgoers():a.incomers();if(s.length===0)break;for(var o=!1,l=0;l<s.length;l++){var u=s[l],v=u.id();i[v]||(i[v]=!0,n.push(u),o=!0)}if(!o)break;a=s}return this.spawn(n,!0).filter(t)}};ir.clearTraversalCache=function(){for(var r=0;r<this.length;r++)this[r]._private.traversalCache=null};pe(ir,{roots:ys({noIncomingEdges:!0}),leaves:ys({noOutgoingEdges:!0}),outgoers:Er(ms({outgoing:!0}),"outgoers"),successors:bs({outgoing:!0}),incomers:Er(ms({incoming:!0}),"incomers"),predecessors:bs({incoming:!0})});pe(ir,{neighborhood:Er(function(r){for(var e=[],t=this.nodes(),a=0;a<t.length;a++)for(var n=t[a],i=n.connectedEdges(),s=0;s<i.length;s++){var o=i[s],l=o.source(),u=o.target(),v=n===l?u:l;v.length>0&&e.push(v[0]),e.push(o[0])}return this.spawn(e,!0).filter(r)},"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}});ir.neighbourhood=ir.neighborhood;ir.closedNeighbourhood=ir.closedNeighborhood;ir.openNeighbourhood=ir.openNeighborhood;pe(ir,{source:Er(function(e){var t=this[0],a;return t&&(a=t._private.source||t.cy().collection()),a&&e?a.filter(e):a},"source"),target:Er(function(e){var t=this[0],a;return t&&(a=t._private.target||t.cy().collection()),a&&e?a.filter(e):a},"target"),sources:ws({attr:"source"}),targets:ws({attr:"target"})});function ws(r){return function(t){for(var a=[],n=0;n<this.length;n++){var i=this[n],s=i._private[r.attr];s&&a.push(s)}return this.spawn(a,!0).filter(t)}}pe(ir,{edgesWith:Er(xs(),"edgesWith"),edgesTo:Er(xs({thisIsSrc:!0}),"edgesTo")});function xs(r){return function(t){var a=[],n=this._private.cy,i=r||{};ce(t)&&(t=n.$(t));for(var s=0;s<t.length;s++)for(var o=t[s]._private.edges,l=0;l<o.length;l++){var u=o[l],v=u._private.data,f=this.hasElementWithId(v.source)&&t.hasElementWithId(v.target),c=t.hasElementWithId(v.source)&&this.hasElementWithId(v.target),h=f||c;h&&((i.thisIsSrc||i.thisIsTgt)&&(i.thisIsSrc&&!f||i.thisIsTgt&&!c)||a.push(u))}return this.spawn(a,!0)}}pe(ir,{connectedEdges:Er(function(r){for(var e=[],t=this,a=0;a<t.length;a++){var n=t[a];if(n.isNode())for(var i=n._private.edges,s=0;s<i.length;s++){var o=i[s];e.push(o)}}return this.spawn(e,!0).filter(r)},"connectedEdges"),connectedNodes:Er(function(r){for(var e=[],t=this,a=0;a<t.length;a++){var n=t[a];n.isEdge()&&(e.push(n.source()[0]),e.push(n.target()[0]))}return this.spawn(e,!0).filter(r)},"connectedNodes"),parallelEdges:Er(Es(),"parallelEdges"),codirectedEdges:Er(Es({codirected:!0}),"codirectedEdges")});function Es(r){var e={codirected:!1};return r=pe({},e,r),function(a){for(var n=[],i=this.edges(),s=r,o=0;o<i.length;o++)for(var l=i[o],u=l._private,v=u.source,f=v._private.data.id,c=u.data.target,h=v._private.edges,d=0;d<h.length;d++){var y=h[d],p=y._private.data,g=p.target,m=p.source,b=g===c&&m===f,w=f===g&&c===m;(s.codirected&&b||!s.codirected&&(b||w))&&n.push(y)}return this.spawn(n,!0).filter(a)}}pe(ir,{components:function(e){var t=this,a=t.cy(),n=a.collection(),i=e==null?t.nodes():e.nodes(),s=[];e!=null&&i.empty()&&(i=e.sources());var o=function(v,f){n.merge(v),i.unmerge(v),f.merge(v)};if(i.empty())return t.spawn();var l=function(){var v=a.collection();s.push(v);var f=i[0];o(f,v),t.bfs({directed:!1,roots:f,visit:function(h){return o(h,v)}}),v.forEach(function(c){c.connectedEdges().forEach(function(h){t.has(h)&&v.has(h.source())&&v.has(h.target())&&v.merge(h)})})};do l();while(i.length>0);return s},component:function(){var e=this[0];return e.cy().mutableElements().components(e)[0]}});ir.componentsOf=ir.components;var er=function(e,t){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e===void 0){$e("A collection must have a reference to the core");return}var i=new Ir,s=!1;if(!t)t=[];else if(t.length>0&&Te(t[0])&&!xa(t[0])){s=!0;for(var o=[],l=new Vt,u=0,v=t.length;u<v;u++){var f=t[u];f.data==null&&(f.data={});var c=f.data;if(c.id==null)c.id=fo();else if(e.hasElementWithId(c.id)||l.has(c.id))continue;var h=new dn(e,f,!1);o.push(h),l.add(c.id)}t=o}this.length=0;for(var d=0,y=t.length;d<y;d++){var p=t[d][0];if(p!=null){var g=p._private.data.id;(!a||!i.has(g))&&(a&&i.set(g,{index:this.length,ele:p}),this[this.length]=p,this.length++)}}this._private={eles:this,cy:e,get map(){return this.lazyMap==null&&this.rebuildMap(),this.lazyMap},set map(m){this.lazyMap=m},rebuildMap:function(){for(var b=this.lazyMap=new Ir,w=this.eles,S=0;S<w.length;S++){var E=w[S];b.set(E.id(),{index:S,ele:E})}}},a&&(this._private.map=i),s&&!n&&this.restore()},Ne=dn.prototype=er.prototype=Object.create(Array.prototype);Ne.instanceString=function(){return"collection"};Ne.spawn=function(r,e){return new er(this.cy(),r,e)};Ne.spawnSelf=function(){return this.spawn(this)};Ne.cy=function(){return this._private.cy};Ne.renderer=function(){return this._private.cy.renderer()};Ne.element=function(){return this[0]};Ne.collection=function(){return Js(this)?this:new er(this._private.cy,[this])};Ne.unique=function(){return new er(this._private.cy,this,!0)};Ne.hasElementWithId=function(r){return r=""+r,this._private.map.has(r)};Ne.getElementById=function(r){r=""+r;var e=this._private.cy,t=this._private.map.get(r);return t?t.ele:new er(e)};Ne.$id=Ne.getElementById;Ne.poolIndex=function(){var r=this._private.cy,e=r._private.elements,t=this[0]._private.data.id;return e._private.map.get(t).index};Ne.indexOf=function(r){var e=r[0]._private.data.id;return this._private.map.get(e).index};Ne.indexOfId=function(r){return r=""+r,this._private.map.get(r).index};Ne.json=function(r){var e=this.element(),t=this.cy();if(e==null&&r)return this;if(e!=null){var a=e._private;if(Te(r)){if(t.startBatch(),r.data){e.data(r.data);var n=a.data;if(e.isEdge()){var i=!1,s={},o=r.data.source,l=r.data.target;o!=null&&o!=n.source&&(s.source=""+o,i=!0),l!=null&&l!=n.target&&(s.target=""+l,i=!0),i&&(e=e.move(s))}else{var u="parent"in r.data,v=r.data.parent;u&&(v!=null||n.parent!=null)&&v!=n.parent&&(v===void 0&&(v=null),v!=null&&(v=""+v),e=e.move({parent:v}))}}r.position&&e.position(r.position);var f=function(y,p,g){var m=r[y];m!=null&&m!==a[y]&&(m?e[p]():e[g]())};return f("removed","remove","restore"),f("selected","select","unselect"),f("selectable","selectify","unselectify"),f("locked","lock","unlock"),f("grabbable","grabify","ungrabify"),f("pannable","panify","unpanify"),r.classes!=null&&e.classes(r.classes),t.endBatch(),this}else if(r===void 0){var c={data:Or(a.data),position:Or(a.position),group:a.group,removed:a.removed,selected:a.selected,selectable:a.selectable,locked:a.locked,grabbable:a.grabbable,pannable:a.pannable,classes:null};c.classes="";var h=0;return a.classes.forEach(function(d){return c.classes+=h++===0?d:" "+d}),c}}};Ne.jsons=function(){for(var r=[],e=0;e<this.length;e++){var t=this[e],a=t.json();r.push(a)}return r};Ne.clone=function(){for(var r=this.cy(),e=[],t=0;t<this.length;t++){var a=this[t],n=a.json(),i=new dn(r,n,!1);e.push(i)}return new er(r,e)};Ne.copy=Ne.clone;Ne.restore=function(){for(var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,t=this,a=t.cy(),n=a._private,i=[],s=[],o,l=0,u=t.length;l<u;l++){var v=t[l];e&&!v.removed()||(v.isNode()?i.push(v):s.push(v))}o=i.concat(s);var f,c=function(){o.splice(f,1),f--};for(f=0;f<o.length;f++){var h=o[f],d=h._private,y=d.data;if(h.clearTraversalCache(),!(!e&&!d.removed)){if(y.id===void 0)y.id=fo();else if(ne(y.id))y.id=""+y.id;else if(jr(y.id)||!ce(y.id)){$e("Can not create element with invalid string ID `"+y.id+"`"),c();continue}else if(a.hasElementWithId(y.id)){$e("Can not create second element with ID `"+y.id+"`"),c();continue}}var p=y.id;if(h.isNode()){var g=d.position;g.x==null&&(g.x=0),g.y==null&&(g.y=0)}if(h.isEdge()){for(var m=h,b=["source","target"],w=b.length,S=!1,E=0;E<w;E++){var x=b[E],D=y[x];ne(D)&&(D=y[x]=""+y[x]),D==null||D===""?($e("Can not create edge `"+p+"` with unspecified "+x),S=!0):a.hasElementWithId(D)||($e("Can not create edge `"+p+"` with nonexistant "+x+" `"+D+"`"),S=!0)}if(S){c();continue}var C=a.getElementById(y.source),M=a.getElementById(y.target);C.same(M)?C._private.edges.push(m):(C._private.edges.push(m),M._private.edges.push(m)),m._private.source=C,m._private.target=M}d.map=new Ir,d.map.set(p,{ele:h,index:0}),d.removed=!1,e&&a.addToPool(h)}for(var P=0;P<i.length;P++){var B=i[P],L=B._private.data;ne(L.parent)&&(L.parent=""+L.parent);var k=L.parent,O=k!=null;if(O||B._private.parent){var A=B._private.parent?a.collection().merge(B._private.parent):a.getElementById(k);if(A.empty())L.parent=void 0;else if(A[0].removed())Me("Node added with missing parent, reference to parent removed"),L.parent=void 0,B._private.parent=null;else{for(var R=!1,I=A;!I.empty();){if(B.same(I)){R=!0,L.parent=void 0;break}I=I.parent()}R||(A[0]._private.children.push(B),B._private.parent=A[0],n.hasCompoundNodes=!0)}}}if(o.length>0){for(var V=o.length===t.length?t:new er(a,o),G=0;G<V.length;G++){var F=V[G];F.isNode()||(F.parallelEdges().clearTraversalCache(),F.source().clearTraversalCache(),F.target().clearTraversalCache())}var q;n.hasCompoundNodes?q=a.collection().merge(V).merge(V.connectedNodes()).merge(V.parent()):q=V,q.dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(r),r?V.emitAndNotify("add"):e&&V.emit("add")}return t};Ne.removed=function(){var r=this[0];return r&&r._private.removed};Ne.inside=function(){var r=this[0];return r&&!r._private.removed};Ne.remove=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,t=this,a=[],n={},i=t._private.cy;function s(k){for(var O=k._private.edges,A=0;A<O.length;A++)l(O[A])}function o(k){for(var O=k._private.children,A=0;A<O.length;A++)l(O[A])}function l(k){var O=n[k.id()];e&&k.removed()||O||(n[k.id()]=!0,k.isNode()?(a.push(k),s(k),o(k)):a.unshift(k))}for(var u=0,v=t.length;u<v;u++){var f=t[u];l(f)}function c(k,O){var A=k._private.edges;et(A,O),k.clearTraversalCache()}function h(k){k.clearTraversalCache()}var d=[];d.ids={};function y(k,O){O=O[0],k=k[0];var A=k._private.children,R=k.id();et(A,O),O._private.parent=null,d.ids[R]||(d.ids[R]=!0,d.push(k))}t.dirtyCompoundBoundsCache(),e&&i.removeFromPool(a);for(var p=0;p<a.length;p++){var g=a[p];if(g.isEdge()){var m=g.source()[0],b=g.target()[0];c(m,g),c(b,g);for(var w=g.parallelEdges(),S=0;S<w.length;S++){var E=w[S];h(E),E.isBundledBezier()&&E.dirtyBoundingBoxCache()}}else{var x=g.parent();x.length!==0&&y(x,g)}e&&(g._private.removed=!0)}var D=i._private.elements;i._private.hasCompoundNodes=!1;for(var C=0;C<D.length;C++){var M=D[C];if(M.isParent()){i._private.hasCompoundNodes=!0;break}}var P=new er(this.cy(),a);P.size()>0&&(r?P.emitAndNotify("remove"):e&&P.emit("remove"));for(var B=0;B<d.length;B++){var L=d[B];(!e||!L.removed())&&L.updateStyle()}return P};Ne.move=function(r){var e=this._private.cy,t=this,a=!1,n=!1,i=function(d){return d==null?d:""+d};if(r.source!==void 0||r.target!==void 0){var s=i(r.source),o=i(r.target),l=s!=null&&e.hasElementWithId(s),u=o!=null&&e.hasElementWithId(o);(l||u)&&(e.batch(function(){t.remove(a,n),t.emitAndNotify("moveout");for(var h=0;h<t.length;h++){var d=t[h],y=d._private.data;d.isEdge()&&(l&&(y.source=s),u&&(y.target=o))}t.restore(a,n)}),t.emitAndNotify("move"))}else if(r.parent!==void 0){var v=i(r.parent),f=v===null||e.hasElementWithId(v);if(f){var c=v===null?void 0:v;e.batch(function(){var h=t.remove(a,n);h.emitAndNotify("moveout");for(var d=0;d<t.length;d++){var y=t[d],p=y._private.data;y.isNode()&&(p.parent=c)}h.restore(a,n)}),t.emitAndNotify("move")}}return this};[To,Jd,Wa,Jr,Nt,dh,bn,Bh,Qo,Jo,Oh,nn,Ua,je,_r,ir].forEach(function(r){pe(Ne,r)});var $h={add:function(e){var t,a=this;if(yr(e)){var n=e;if(n._private.cy===a)t=n.restore();else{for(var i=[],s=0;s<n.length;s++){var o=n[s];i.push(o.json())}t=new er(a,i)}}else if(Oe(e)){var l=e;t=new er(a,l)}else if(Te(e)&&(Oe(e.nodes)||Oe(e.edges))){for(var u=e,v=[],f=["nodes","edges"],c=0,h=f.length;c<h;c++){var d=f[c],y=u[d];if(Oe(y))for(var p=0,g=y.length;p<g;p++){var m=pe({group:d},y[p]);v.push(m)}}t=new er(a,v)}else{var b=e;t=new dn(a,b).collection()}return t},remove:function(e){if(!yr(e)){if(ce(e)){var t=e;e=this.$(t)}}return e.remove()}};/*! Bezier curve function generator. Copyright Gaetan Renaudeau. MIT License: http://en.wikipedia.org/wiki/MIT_License */function Hh(r,e,t,a){var n=4,i=.001,s=1e-7,o=10,l=11,u=1/(l-1),v=typeof Float32Array<"u";if(arguments.length!==4)return!1;for(var f=0;f<4;++f)if(typeof arguments[f]!="number"||isNaN(arguments[f])||!isFinite(arguments[f]))return!1;r=Math.min(r,1),t=Math.min(t,1),r=Math.max(r,0),t=Math.max(t,0);var c=v?new Float32Array(l):new Array(l);function h(M,P){return 1-3*P+3*M}function d(M,P){return 3*P-6*M}function y(M){return 3*M}function p(M,P,B){return((h(P,B)*M+d(P,B))*M+y(P))*M}function g(M,P,B){return 3*h(P,B)*M*M+2*d(P,B)*M+y(P)}function m(M,P){for(var B=0;B<n;++B){var L=g(P,r,t);if(L===0)return P;var k=p(P,r,t)-M;P-=k/L}return P}function b(){for(var M=0;M<l;++M)c[M]=p(M*u,r,t)}function w(M,P,B){var L,k,O=0;do k=P+(B-P)/2,L=p(k,r,t)-M,L>0?B=k:P=k;while(Math.abs(L)>s&&++O<o);return k}function S(M){for(var P=0,B=1,L=l-1;B!==L&&c[B]<=M;++B)P+=u;--B;var k=(M-c[B])/(c[B+1]-c[B]),O=P+k*u,A=g(O,r,t);return A>=i?m(M,O):A===0?O:w(M,P,P+u)}var E=!1;function x(){E=!0,(r!==e||t!==a)&&b()}var D=function(P){return E||x(),r===e&&t===a?P:P===0?0:P===1?1:p(S(P),e,a)};D.getControlPoints=function(){return[{x:r,y:e},{x:t,y:a}]};var C="generateBezier("+[r,e,t,a]+")";return D.toString=function(){return C},D}/*! Runge-Kutta spring physics function generator. Adapted from Framer.js, copyright Koen Bok. MIT License: http://en.wikipedia.org/wiki/MIT_License */var Gh=function(){function r(a){return-a.tension*a.x-a.friction*a.v}function e(a,n,i){var s={x:a.x+i.dx*n,v:a.v+i.dv*n,tension:a.tension,friction:a.friction};return{dx:s.v,dv:r(s)}}function t(a,n){var i={dx:a.v,dv:r(a)},s=e(a,n*.5,i),o=e(a,n*.5,s),l=e(a,n,o),u=1/6*(i.dx+2*(s.dx+o.dx)+l.dx),v=1/6*(i.dv+2*(s.dv+o.dv)+l.dv);return a.x=a.x+u*n,a.v=a.v+v*n,a}return function a(n,i,s){var o={x:-1,v:0,tension:null,friction:null},l=[0],u=0,v=1/1e4,f=16/1e3,c,h,d;for(n=parseFloat(n)||500,i=parseFloat(i)||20,s=s||null,o.tension=n,o.friction=i,c=s!==null,c?(u=a(n,i),h=u/s*f):h=f;d=t(d||o,h),l.push(1+d.x),u+=16,Math.abs(d.x)>v&&Math.abs(d.v)>v;);return c?function(y){return l[y*(l.length-1)|0]}:u}}(),ze=function(e,t,a,n){var i=Hh(e,t,a,n);return function(s,o,l){return s+(o-s)*i(l)}},Ya={linear:function(e,t,a){return e+(t-e)*a},ease:ze(.25,.1,.25,1),"ease-in":ze(.42,0,1,1),"ease-out":ze(0,0,.58,1),"ease-in-out":ze(.42,0,.58,1),"ease-in-sine":ze(.47,0,.745,.715),"ease-out-sine":ze(.39,.575,.565,1),"ease-in-out-sine":ze(.445,.05,.55,.95),"ease-in-quad":ze(.55,.085,.68,.53),"ease-out-quad":ze(.25,.46,.45,.94),"ease-in-out-quad":ze(.455,.03,.515,.955),"ease-in-cubic":ze(.55,.055,.675,.19),"ease-out-cubic":ze(.215,.61,.355,1),"ease-in-out-cubic":ze(.645,.045,.355,1),"ease-in-quart":ze(.895,.03,.685,.22),"ease-out-quart":ze(.165,.84,.44,1),"ease-in-out-quart":ze(.77,0,.175,1),"ease-in-quint":ze(.755,.05,.855,.06),"ease-out-quint":ze(.23,1,.32,1),"ease-in-out-quint":ze(.86,0,.07,1),"ease-in-expo":ze(.95,.05,.795,.035),"ease-out-expo":ze(.19,1,.22,1),"ease-in-out-expo":ze(1,0,0,1),"ease-in-circ":ze(.6,.04,.98,.335),"ease-out-circ":ze(.075,.82,.165,1),"ease-in-out-circ":ze(.785,.135,.15,.86),spring:function(e,t,a){if(a===0)return Ya.linear;var n=Gh(e,t,a);return function(i,s,o){return i+(s-i)*n(o)}},"cubic-bezier":ze};function Cs(r,e,t,a,n){if(a===1||e===t)return t;var i=n(e,t,a);return r==null||((r.roundValue||r.color)&&(i=Math.round(i)),r.min!==void 0&&(i=Math.max(i,r.min)),r.max!==void 0&&(i=Math.min(i,r.max))),i}function Ss(r,e){return r.pfValue!=null||r.value!=null?r.pfValue!=null&&(e==null||e.type.units!=="%")?r.pfValue:r.value:r}function Ct(r,e,t,a,n){var i=n!=null?n.type:null;t<0?t=0:t>1&&(t=1);var s=Ss(r,n),o=Ss(e,n);if(ne(s)&&ne(o))return Cs(i,s,o,t,a);if(Oe(s)&&Oe(o)){for(var l=[],u=0;u<o.length;u++){var v=s[u],f=o[u];if(v!=null&&f!=null){var c=Cs(i,v,f,t,a);l.push(c)}else l.push(f)}return l}}function Kh(r,e,t,a){var n=!a,i=r._private,s=e._private,o=s.easing,l=s.startTime,u=a?r:r.cy(),v=u.style();if(!s.easingImpl)if(o==null)s.easingImpl=Ya.linear;else{var f;if(ce(o)){var c=v.parse("transition-timing-function",o);f=c.value}else f=o;var h,d;ce(f)?(h=f,d=[]):(h=f[1],d=f.slice(2).map(function(V){return+V})),d.length>0?(h==="spring"&&d.push(s.duration),s.easingImpl=Ya[h].apply(null,d)):s.easingImpl=Ya[h]}var y=s.easingImpl,p;if(s.duration===0?p=1:p=(t-l)/s.duration,s.applying&&(p=s.progress),p<0?p=0:p>1&&(p=1),s.delay==null){var g=s.startPosition,m=s.position;if(m&&n&&!r.locked()){var b={};ra(g.x,m.x)&&(b.x=Ct(g.x,m.x,p,y)),ra(g.y,m.y)&&(b.y=Ct(g.y,m.y,p,y)),r.position(b)}var w=s.startPan,S=s.pan,E=i.pan,x=S!=null&&a;x&&(ra(w.x,S.x)&&(E.x=Ct(w.x,S.x,p,y)),ra(w.y,S.y)&&(E.y=Ct(w.y,S.y,p,y)),r.emit("pan"));var D=s.startZoom,C=s.zoom,M=C!=null&&a;M&&(ra(D,C)&&(i.zoom=da(i.minZoom,Ct(D,C,p,y),i.maxZoom)),r.emit("zoom")),(x||M)&&r.emit("viewport");var P=s.style;if(P&&P.length>0&&n){for(var B=0;B<P.length;B++){var L=P[B],k=L.name,O=L,A=s.startStyle[k],R=v.properties[A.name],I=Ct(A,O,p,y,R);v.overrideBypass(r,k,I)}r.emit("style")}}return s.progress=p,p}function ra(r,e){return r==null||e==null?!1:ne(r)&&ne(e)?!0:!!(r&&e)}function Wh(r,e,t,a){var n=e._private;n.started=!0,n.startTime=t-n.progress*n.duration}function Ts(r,e){var t=e._private.aniEles,a=[];function n(v,f){var c=v._private,h=c.animation.current,d=c.animation.queue,y=!1;if(h.length===0){var p=d.shift();p&&h.push(p)}for(var g=function(E){for(var x=E.length-1;x>=0;x--){var D=E[x];D()}E.splice(0,E.length)},m=h.length-1;m>=0;m--){var b=h[m],w=b._private;if(w.stopped){h.splice(m,1),w.hooked=!1,w.playing=!1,w.started=!1,g(w.frames);continue}!w.playing&&!w.applying||(w.playing&&w.applying&&(w.applying=!1),w.started||Wh(v,b,r),Kh(v,b,r,f),w.applying&&(w.applying=!1),g(w.frames),w.step!=null&&w.step(r),b.completed()&&(h.splice(m,1),w.hooked=!1,w.playing=!1,w.started=!1,g(w.completes)),y=!0)}return!f&&h.length===0&&d.length===0&&a.push(v),y}for(var i=!1,s=0;s<t.length;s++){var o=t[s],l=n(o);i=i||l}var u=n(e,!0);(i||u)&&(t.length>0?e.notify("draw",t):e.notify("draw")),t.unmerge(a),e.emit("step")}var Uh={animate:Be.animate(),animation:Be.animation(),animated:Be.animated(),clearQueue:Be.clearQueue(),delay:Be.delay(),delayAnimation:Be.delayAnimation(),stop:Be.stop(),addToAnimationPool:function(e){var t=this;t.styleEnabled()&&t._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){var e=this;if(e._private.animationsRunning=!0,!e.styleEnabled())return;function t(){e._private.animationsRunning&&ja(function(i){Ts(i,e),t()})}var a=e.renderer();a&&a.beforeRender?a.beforeRender(function(i,s){Ts(s,e)},a.beforeRenderPriorities.animations):t()}},Yh={qualifierCompare:function(e,t){return e==null||t==null?e==null&&t==null:e.sameText(t)},eventMatches:function(e,t,a){var n=t.qualifier;return n!=null?e!==a.target&&xa(a.target)&&n.matches(a.target):!0},addEventFields:function(e,t){t.cy=e,t.target=e},callbackContext:function(e,t,a){return t.qualifier!=null?a.target:e}},qa=function(e){return ce(e)?new rt(e):e},eu={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new wn(Yh,this)),this},emitter:function(){return this._private.emitter},on:function(e,t,a){return this.emitter().on(e,qa(t),a),this},removeListener:function(e,t,a){return this.emitter().removeListener(e,qa(t),a),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},one:function(e,t,a){return this.emitter().one(e,qa(t),a),this},once:function(e,t,a){return this.emitter().one(e,qa(t),a),this},emit:function(e,t){return this.emitter().emit(e,t),this},emitAndNotify:function(e,t){return this.emit(e),this.notify(e,t),this}};Be.eventAliasesOn(eu);var Zn={png:function(e){var t=this._private.renderer;return e=e||{},t.png(e)},jpg:function(e){var t=this._private.renderer;return e=e||{},e.bg=e.bg||"#fff",t.jpg(e)}};Zn.jpeg=Zn.jpg;var Xa={layout:function(e){var t=this;if(e==null){$e("Layout options must be specified to make a layout");return}if(e.name==null){$e("A `name` must be specified to make a layout");return}var a=e.name,n=t.extension("layout",a);if(n==null){$e("No such layout `"+a+"` found.  Did you forget to import it and `cytoscape.use()` it?");return}var i;ce(e.eles)?i=t.$(e.eles):i=e.eles!=null?e.eles:t.$();var s=new n(pe({},e,{cy:t,eles:i}));return s}};Xa.createLayout=Xa.makeLayout=Xa.layout;var Xh={notify:function(e,t){var a=this._private;if(this.batching()){a.batchNotifications=a.batchNotifications||{};var n=a.batchNotifications[e]=a.batchNotifications[e]||this.collection();t!=null&&n.merge(t);return}if(a.notificationsEnabled){var i=this.renderer();this.destroyed()||!i||i.notify(e,t)}},notifications:function(e){var t=this._private;return e===void 0?t.notificationsEnabled:(t.notificationsEnabled=!!e,this)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return e.batchCount==null&&(e.batchCount=0),e.batchCount===0&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},endBatch:function(){var e=this._private;if(e.batchCount===0)return this;if(e.batchCount--,e.batchCount===0){e.batchStyleEles.updateStyle();var t=this.renderer();Object.keys(e.batchNotifications).forEach(function(a){var n=e.batchNotifications[a];n.empty()?t.notify(a):t.notify(a,n)})}return this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var t=this;return this.batch(function(){for(var a=Object.keys(e),n=0;n<a.length;n++){var i=a[n],s=e[i],o=t.getElementById(i);o.data(s)}})}},Zh=rr({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1}),Qn={renderTo:function(e,t,a,n){var i=this._private.renderer;return i.renderTo(e,t,a,n),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify("draw"),this},resize:function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},initRenderer:function(e){var t=this,a=t.extension("renderer",e.name);if(a==null){$e("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"));return}e.wheelSensitivity!==void 0&&Me("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var n=Zh(e);n.cy=t,t._private.renderer=new a(n),this.notify("init")},destroyRenderer:function(){var e=this;e.notify("destroy");var t=e.container();if(t)for(t._cyreg=null;t.childNodes.length>0;)t.removeChild(t.childNodes[0]);e._private.renderer=null,e.mutableElements().forEach(function(a){var n=a._private;n.rscratch={},n.rstyle={},n.animation.current=[],n.animation.queue=[]})},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};Qn.invalidateDimensions=Qn.resize;var Za={collection:function(e,t){return ce(e)?this.$(e):yr(e)?e.collection():Oe(e)?(t||(t={}),new er(this,e,t.unique,t.removed)):new er(this)},nodes:function(e){var t=this.$(function(a){return a.isNode()});return e?t.filter(e):t},edges:function(e){var t=this.$(function(a){return a.isEdge()});return e?t.filter(e):t},$:function(e){var t=this._private.elements;return e?t.filter(e):t.spawnSelf()},mutableElements:function(){return this._private.elements}};Za.elements=Za.filter=Za.$;var or={},oa="t",Qh="f";or.apply=function(r){for(var e=this,t=e._private,a=t.cy,n=a.collection(),i=0;i<r.length;i++){var s=r[i],o=e.getContextMeta(s);if(!o.empty){var l=e.getContextStyle(o),u=e.applyContextStyle(o,l,s);s._private.appliedInitStyle?e.updateTransitions(s,u.diffProps):s._private.appliedInitStyle=!0;var v=e.updateStyleHints(s);v&&n.push(s)}}return n};or.getPropertiesDiff=function(r,e){var t=this,a=t._private.propDiffs=t._private.propDiffs||{},n=r+"-"+e,i=a[n];if(i)return i;for(var s=[],o={},l=0;l<t.length;l++){var u=t[l],v=r[l]===oa,f=e[l]===oa,c=v!==f,h=u.mappedProperties.length>0;if(c||f&&h){var d=void 0;c&&h||c?d=u.properties:h&&(d=u.mappedProperties);for(var y=0;y<d.length;y++){for(var p=d[y],g=p.name,m=!1,b=l+1;b<t.length;b++){var w=t[b],S=e[b]===oa;if(S&&(m=w.properties[p.name]!=null,m))break}!o[g]&&!m&&(o[g]=!0,s.push(g))}}}return a[n]=s,s};or.getContextMeta=function(r){for(var e=this,t="",a,n=r._private.styleCxtKey||"",i=0;i<e.length;i++){var s=e[i],o=s.selector&&s.selector.matches(r);o?t+=oa:t+=Qh}return a=e.getPropertiesDiff(n,t),r._private.styleCxtKey=t,{key:t,diffPropNames:a,empty:a.length===0}};or.getContextStyle=function(r){var e=r.key,t=this,a=this._private.contextStyles=this._private.contextStyles||{};if(a[e])return a[e];for(var n={_private:{key:e}},i=0;i<t.length;i++){var s=t[i],o=e[i]===oa;if(o)for(var l=0;l<s.properties.length;l++){var u=s.properties[l];n[u.name]=u}}return a[e]=n,n};or.applyContextStyle=function(r,e,t){for(var a=this,n=r.diffPropNames,i={},s=a.types,o=0;o<n.length;o++){var l=n[o],u=e[l],v=t.pstyle(l);if(!u)if(v)v.bypass?u={name:l,deleteBypassed:!0}:u={name:l,delete:!0};else continue;if(v!==u){if(u.mapped===s.fn&&v!=null&&v.mapping!=null&&v.mapping.value===u.value){var f=v.mapping,c=f.fnValue=u.value(t);if(c===f.prevFnValue)continue}var h=i[l]={prev:v};a.applyParsedProperty(t,u),h.next=t.pstyle(l),h.next&&h.next.bypass&&(h.next=h.next.bypassed)}}return{diffProps:i}};or.updateStyleHints=function(r){var e=r._private,t=this,a=t.propertyGroupNames,n=t.propertyGroupKeys,i=function(X,ae,he){return t.getPropertiesHash(X,ae,he)},s=e.styleKey;if(r.removed())return!1;var o=e.group==="nodes",l=r._private.style;a=Object.keys(l);for(var u=0;u<n.length;u++){var v=n[u];e.styleKeys[v]=[kt,aa]}for(var f=function(X,ae){return e.styleKeys[ae][0]=va(X,e.styleKeys[ae][0])},c=function(X,ae){return e.styleKeys[ae][1]=fa(X,e.styleKeys[ae][1])},h=function(X,ae){f(X,ae),c(X,ae)},d=function(X,ae){for(var he=0;he<X.length;he++){var te=X.charCodeAt(he);f(te,ae),c(te,ae)}},y=2e9,p=function(X){return-128<X&&X<128&&Math.floor(X)!==X?y-(X*1024|0):X},g=0;g<a.length;g++){var m=a[g],b=l[m];if(b!=null){var w=this.properties[m],S=w.type,E=w.groupKey,x=void 0;w.hashOverride!=null?x=w.hashOverride(r,b):b.pfValue!=null&&(x=b.pfValue);var D=w.enums==null?b.value:null,C=x!=null,M=D!=null,P=C||M,B=b.units;if(S.number&&P&&!S.multiple){var L=C?x:D;h(p(L),E),!C&&B!=null&&d(B,E)}else d(b.strValue,E)}}for(var k=[kt,aa],O=0;O<n.length;O++){var A=n[O],R=e.styleKeys[A];k[0]=va(R[0],k[0]),k[1]=fa(R[1],k[1])}e.styleKey=ev(k[0],k[1]);var I=e.styleKeys;e.labelDimsKey=Yr(I.labelDimensions);var V=i(r,["label"],I.labelDimensions);if(e.labelKey=Yr(V),e.labelStyleKey=Yr(La(I.commonLabel,V)),!o){var G=i(r,["source-label"],I.labelDimensions);e.sourceLabelKey=Yr(G),e.sourceLabelStyleKey=Yr(La(I.commonLabel,G));var F=i(r,["target-label"],I.labelDimensions);e.targetLabelKey=Yr(F),e.targetLabelStyleKey=Yr(La(I.commonLabel,F))}if(o){var q=e.styleKeys,Y=q.nodeBody,Q=q.nodeBorder,J=q.nodeOutline,_=q.backgroundImage,j=q.compound,W=q.pie,z=[Y,Q,J,_,j,W].filter(function(K){return K!=null}).reduce(La,[kt,aa]);e.nodeKey=Yr(z),e.hasPie=W!=null&&W[0]!==kt&&W[1]!==aa}return s!==e.styleKey};or.clearStyleHints=function(r){var e=r._private;e.styleCxtKey="",e.styleKeys={},e.styleKey=null,e.labelKey=null,e.labelStyleKey=null,e.sourceLabelKey=null,e.sourceLabelStyleKey=null,e.targetLabelKey=null,e.targetLabelStyleKey=null,e.nodeKey=null,e.hasPie=null};or.applyParsedProperty=function(r,e){var t=this,a=e,n=r._private.style,i,s=t.types,o=t.properties[a.name].type,l=a.bypass,u=n[a.name],v=u&&u.bypass,f=r._private,c="mapping",h=function(Y){return Y==null?null:Y.pfValue!=null?Y.pfValue:Y.value},d=function(){var Y=h(u),Q=h(a);t.checkTriggers(r,a.name,Y,Q)};if(e.name==="curve-style"&&r.isEdge()&&(e.value!=="bezier"&&r.isLoop()||e.value==="haystack"&&(r.source().isParent()||r.target().isParent()))&&(a=e=this.parse(e.name,"bezier",l)),a.delete)return n[a.name]=void 0,d(),!0;if(a.deleteBypassed)return u?u.bypass?(u.bypassed=void 0,d(),!0):!1:(d(),!0);if(a.deleteBypass)return u?u.bypass?(n[a.name]=u.bypassed,d(),!0):!1:(d(),!0);var y=function(){Me("Do not assign mappings to elements without corresponding data (i.e. ele `"+r.id()+"` has no mapping for property `"+a.name+"` with data field `"+a.field+"`); try a `["+a.field+"]` selector to limit scope to elements with `"+a.field+"` defined")};switch(a.mapped){case s.mapData:{for(var p=a.field.split("."),g=f.data,m=0;m<p.length&&g;m++){var b=p[m];g=g[b]}if(g==null)return y(),!1;var w;if(ne(g)){var S=a.fieldMax-a.fieldMin;S===0?w=0:w=(g-a.fieldMin)/S}else return Me("Do not use continuous mappers without specifying numeric data (i.e. `"+a.field+": "+g+"` for `"+r.id()+"` is non-numeric)"),!1;if(w<0?w=0:w>1&&(w=1),o.color){var E=a.valueMin[0],x=a.valueMax[0],D=a.valueMin[1],C=a.valueMax[1],M=a.valueMin[2],P=a.valueMax[2],B=a.valueMin[3]==null?1:a.valueMin[3],L=a.valueMax[3]==null?1:a.valueMax[3],k=[Math.round(E+(x-E)*w),Math.round(D+(C-D)*w),Math.round(M+(P-M)*w),Math.round(B+(L-B)*w)];i={bypass:a.bypass,name:a.name,value:k,strValue:"rgb("+k[0]+", "+k[1]+", "+k[2]+")"}}else if(o.number){var O=a.valueMin+(a.valueMax-a.valueMin)*w;i=this.parse(a.name,O,a.bypass,c)}else return!1;if(!i)return y(),!1;i.mapping=a,a=i;break}case s.data:{for(var A=a.field.split("."),R=f.data,I=0;I<A.length&&R;I++){var V=A[I];R=R[V]}if(R!=null&&(i=this.parse(a.name,R,a.bypass,c)),!i)return y(),!1;i.mapping=a,a=i;break}case s.fn:{var G=a.value,F=a.fnValue!=null?a.fnValue:G(r);if(a.prevFnValue=F,F==null)return Me("Custom function mappers may not return null (i.e. `"+a.name+"` for ele `"+r.id()+"` is null)"),!1;if(i=this.parse(a.name,F,a.bypass,c),!i)return Me("Custom function mappers may not return invalid values for the property type (i.e. `"+a.name+"` for ele `"+r.id()+"` is invalid)"),!1;i.mapping=Or(a),a=i;break}case void 0:break;default:return!1}return l?(v?a.bypassed=u.bypassed:a.bypassed=u,n[a.name]=a):v?u.bypassed=a:n[a.name]=a,d(),!0};or.cleanElements=function(r,e){for(var t=0;t<r.length;t++){var a=r[t];if(this.clearStyleHints(a),a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),!e)a._private.style={};else for(var n=a._private.style,i=Object.keys(n),s=0;s<i.length;s++){var o=i[s],l=n[o];l!=null&&(l.bypass?l.bypassed=null:n[o]=null)}}};or.update=function(){var r=this._private.cy,e=r.mutableElements();e.updateStyle()};or.updateTransitions=function(r,e){var t=this,a=r._private,n=r.pstyle("transition-property").value,i=r.pstyle("transition-duration").pfValue,s=r.pstyle("transition-delay").pfValue;if(n.length>0&&i>0){for(var o={},l=!1,u=0;u<n.length;u++){var v=n[u],f=r.pstyle(v),c=e[v];if(c){var h=c.prev,d=h,y=c.next!=null?c.next:f,p=!1,g=void 0,m=1e-6;d&&(ne(d.pfValue)&&ne(y.pfValue)?(p=y.pfValue-d.pfValue,g=d.pfValue+m*p):ne(d.value)&&ne(y.value)?(p=y.value-d.value,g=d.value+m*p):Oe(d.value)&&Oe(y.value)&&(p=d.value[0]!==y.value[0]||d.value[1]!==y.value[1]||d.value[2]!==y.value[2],g=d.strValue),p&&(o[v]=y.strValue,this.applyBypass(r,v,g),l=!0))}}if(!l)return;a.transitioning=!0,new qt(function(b){s>0?r.delayAnimation(s).play().promise().then(b):b()}).then(function(){return r.animation({style:o,duration:i,easing:r.pstyle("transition-timing-function").value,queue:!1}).play().promise()}).then(function(){t.removeBypasses(r,n),r.emitAndNotify("style"),a.transitioning=!1})}else a.transitioning&&(this.removeBypasses(r,n),r.emitAndNotify("style"),a.transitioning=!1)};or.checkTrigger=function(r,e,t,a,n,i){var s=this.properties[e],o=n(s);o!=null&&o(t,a)&&i(s)};or.checkZOrderTrigger=function(r,e,t,a){var n=this;this.checkTrigger(r,e,t,a,function(i){return i.triggersZOrder},function(){n._private.cy.notify("zorder",r)})};or.checkBoundsTrigger=function(r,e,t,a){this.checkTrigger(r,e,t,a,function(n){return n.triggersBounds},function(n){r.dirtyCompoundBoundsCache(),r.dirtyBoundingBoxCache(),n.triggersBoundsOfParallelBeziers&&e==="curve-style"&&(t==="bezier"||a==="bezier")&&r.parallelEdges().forEach(function(i){i.isBundledBezier()&&i.dirtyBoundingBoxCache()}),n.triggersBoundsOfConnectedEdges&&e==="display"&&(t==="none"||a==="none")&&r.connectedEdges().forEach(function(i){i.dirtyBoundingBoxCache()})})};or.checkTriggers=function(r,e,t,a){r.dirtyStyleCache(),this.checkZOrderTrigger(r,e,t,a),this.checkBoundsTrigger(r,e,t,a)};var Ta={};Ta.applyBypass=function(r,e,t,a){var n=this,i=[],s=!0;if(e==="*"||e==="**"){if(t!==void 0)for(var o=0;o<n.properties.length;o++){var l=n.properties[o],u=l.name,v=this.parse(u,t,!0);v&&i.push(v)}}else if(ce(e)){var f=this.parse(e,t,!0);f&&i.push(f)}else if(Te(e)){var c=e;a=t;for(var h=Object.keys(c),d=0;d<h.length;d++){var y=h[d],p=c[y];if(p===void 0&&(p=c[vn(y)]),p!==void 0){var g=this.parse(y,p,!0);g&&i.push(g)}}}else return!1;if(i.length===0)return!1;for(var m=!1,b=0;b<r.length;b++){for(var w=r[b],S={},E=void 0,x=0;x<i.length;x++){var D=i[x];if(a){var C=w.pstyle(D.name);E=S[D.name]={prev:C}}m=this.applyParsedProperty(w,Or(D))||m,a&&(E.next=w.pstyle(D.name))}m&&this.updateStyleHints(w),a&&this.updateTransitions(w,S,s)}return m};Ta.overrideBypass=function(r,e,t){e=ui(e);for(var a=0;a<r.length;a++){var n=r[a],i=n._private.style[e],s=this.properties[e].type,o=s.color,l=s.mutiple,u=i?i.pfValue!=null?i.pfValue:i.value:null;!i||!i.bypass?this.applyBypass(n,e,t):(i.value=t,i.pfValue!=null&&(i.pfValue=t),o?i.strValue="rgb("+t.join(",")+")":l?i.strValue=t.join(" "):i.strValue=""+t,this.updateStyleHints(n)),this.checkTriggers(n,e,u,t)}};Ta.removeAllBypasses=function(r,e){return this.removeBypasses(r,this.propertyNames,e)};Ta.removeBypasses=function(r,e,t){for(var a=!0,n=0;n<r.length;n++){for(var i=r[n],s={},o=0;o<e.length;o++){var l=e[o],u=this.properties[l],v=i.pstyle(u.name);if(!(!v||!v.bypass)){var f="",c=this.parse(l,f,!0),h=s[u.name]={prev:v};this.applyParsedProperty(i,c),h.next=i.pstyle(u.name)}}this.updateStyleHints(i),t&&this.updateTransitions(i,s,a)}};var Ei={};Ei.getEmSizeInPixels=function(){var r=this.containerCss("font-size");return r!=null?parseFloat(r):1};Ei.containerCss=function(r){var e=this._private.cy,t=e.container(),a=e.window();if(a&&t&&a.getComputedStyle)return a.getComputedStyle(t).getPropertyValue(r)};var zr={};zr.getRenderedStyle=function(r,e){return e?this.getStylePropertyValue(r,e,!0):this.getRawStyle(r,!0)};zr.getRawStyle=function(r,e){var t=this;if(r=r[0],r){for(var a={},n=0;n<t.properties.length;n++){var i=t.properties[n],s=t.getStylePropertyValue(r,i.name,e);s!=null&&(a[i.name]=s,a[vn(i.name)]=s)}return a}};zr.getIndexedStyle=function(r,e,t,a){var n=r.pstyle(e)[t][a];return n??r.cy().style().getDefaultProperty(e)[t][0]};zr.getStylePropertyValue=function(r,e,t){var a=this;if(r=r[0],r){var n=a.properties[e];n.alias&&(n=n.pointsTo);var i=n.type,s=r.pstyle(n.name);if(s){var o=s.value,l=s.units,u=s.strValue;if(t&&i.number&&o!=null&&ne(o)){var v=r.cy().zoom(),f=function(p){return p*v},c=function(p,g){return f(p)+g},h=Oe(o),d=h?l.every(function(y){return y!=null}):l!=null;return d?h?o.map(function(y,p){return c(y,l[p])}).join(" "):c(o,l):h?o.map(function(y){return ce(y)?y:""+f(y)}).join(" "):""+f(o)}else if(u!=null)return u}return null}};zr.getAnimationStartStyle=function(r,e){for(var t={},a=0;a<e.length;a++){var n=e[a],i=n.name,s=r.pstyle(i);s!==void 0&&(Te(s)?s=this.parse(i,s.strValue):s=this.parse(i,s)),s&&(t[i]=s)}return t};zr.getPropsList=function(r){var e=this,t=[],a=r,n=e.properties;if(a)for(var i=Object.keys(a),s=0;s<i.length;s++){var o=i[s],l=a[o],u=n[o]||n[ui(o)],v=this.parse(u.name,l);v&&t.push(v)}return t};zr.getNonDefaultPropertiesHash=function(r,e,t){var a=t.slice(),n,i,s,o,l,u;for(l=0;l<e.length;l++)if(n=e[l],i=r.pstyle(n,!1),i!=null)if(i.pfValue!=null)a[0]=va(o,a[0]),a[1]=fa(o,a[1]);else for(s=i.strValue,u=0;u<s.length;u++)o=s.charCodeAt(u),a[0]=va(o,a[0]),a[1]=fa(o,a[1]);return a};zr.getPropertiesHash=zr.getNonDefaultPropertiesHash;var Cn={};Cn.appendFromJson=function(r){for(var e=this,t=0;t<r.length;t++){var a=r[t],n=a.selector,i=a.style||a.css,s=Object.keys(i);e.selector(n);for(var o=0;o<s.length;o++){var l=s[o],u=i[l];e.css(l,u)}}return e};Cn.fromJson=function(r){var e=this;return e.resetToDefault(),e.appendFromJson(r),e};Cn.json=function(){for(var r=[],e=this.defaultLength;e<this.length;e++){for(var t=this[e],a=t.selector,n=t.properties,i={},s=0;s<n.length;s++){var o=n[s];i[o.name]=o.strValue}r.push({selector:a?a.toString():"core",style:i})}return r};var Ci={};Ci.appendFromString=function(r){var e=this,t=this,a=""+r,n,i,s;a=a.replace(/[/][*](\s|.)+?[*][/]/g,"");function o(){a.length>n.length?a=a.substr(n.length):a=""}function l(){i.length>s.length?i=i.substr(s.length):i=""}for(;;){var u=a.match(/^\s*$/);if(u)break;var v=a.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!v){Me("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+a);break}n=v[0];var f=v[1];if(f!=="core"){var c=new rt(f);if(c.invalid){Me("Skipping parsing of block: Invalid selector found in string stylesheet: "+f),o();continue}}var h=v[2],d=!1;i=h;for(var y=[];;){var p=i.match(/^\s*$/);if(p)break;var g=i.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!g){Me("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+h),d=!0;break}s=g[0];var m=g[1],b=g[2],w=e.properties[m];if(!w){Me("Skipping property: Invalid property name in: "+s),l();continue}var S=t.parse(m,b);if(!S){Me("Skipping property: Invalid property definition in: "+s),l();continue}y.push({name:m,val:b}),l()}if(d){o();break}t.selector(f);for(var E=0;E<y.length;E++){var x=y[E];t.css(x.name,x.val)}o()}return t};Ci.fromString=function(r){var e=this;return e.resetToDefault(),e.appendFromString(r),e};var _e={};(function(){var r=We,e=al,t=il,a=sl,n=ol,i=function(z){return"^"+z+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},s=function(z){var K=r+"|\\w+|"+e+"|"+t+"|"+a+"|"+n;return"^"+z+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+r+")\\s*\\,\\s*("+r+")\\s*,\\s*("+K+")\\s*\\,\\s*("+K+")\\)$"},o=[`^url\\s*\\(\\s*['"]?(.+?)['"]?\\s*\\)$`,"^(none)$","^(.+)$"];_e.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},nonNegativeNumber:{number:!0,min:0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},linePosition:{enums:["center","inside","outside"]},lineJoin:{enums:["round","bevel","miter"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi","round-segments","round-taxi"]},radiusType:{enums:["arc-radius","influence-radius"],multiple:!0},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},cornerRadius:{number:!0,min:0,units:"px|em",implicitUnits:"px",enums:["auto"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},arrowWidth:{number:!0,units:"%|px|em",implicitUnits:"px",enums:["match-line"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:i("data")},layoutData:{mapping:!0,regex:i("layoutData")},scratch:{mapping:!0,regex:i("scratch")},mapData:{mapping:!0,regex:s("mapData")},mapLayoutData:{mapping:!0,regex:s("mapLayoutData")},mapScratch:{mapping:!0,regex:s("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:o,singleRegexMatchValue:!0},urls:{regexes:o,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position","endpoints"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:function(z,K){switch(z.length){case 2:return K[0]!=="deg"&&K[0]!=="rad"&&K[1]!=="deg"&&K[1]!=="rad";case 1:return ce(z[0])||K[0]==="deg"||K[0]==="rad";default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+r+")\\s*,\\s*("+r+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+r+")\\s*,\\s*("+r+")\\s*,\\s*("+r+")\\s*,\\s*("+r+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:function(z){var K=z.length;return K===1||K===2||K===4}}};var l={zeroNonZero:function(z,K){return(z==null||K==null)&&z!==K||z==0&&K!=0?!0:z!=0&&K==0},any:function(z,K){return z!=K},emptyNonEmpty:function(z,K){var X=jr(z),ae=jr(K);return X&&!ae||!X&&ae}},u=_e.types,v=[{name:"label",type:u.text,triggersBounds:l.any,triggersZOrder:l.emptyNonEmpty},{name:"text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any}],f=[{name:"source-label",type:u.text,triggersBounds:l.any},{name:"source-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"source-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-offset",type:u.size,triggersBounds:l.any}],c=[{name:"target-label",type:u.text,triggersBounds:l.any},{name:"target-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"target-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-offset",type:u.size,triggersBounds:l.any}],h=[{name:"font-family",type:u.fontFamily,triggersBounds:l.any},{name:"font-style",type:u.fontStyle,triggersBounds:l.any},{name:"font-weight",type:u.fontWeight,triggersBounds:l.any},{name:"font-size",type:u.size,triggersBounds:l.any},{name:"text-transform",type:u.textTransform,triggersBounds:l.any},{name:"text-wrap",type:u.textWrap,triggersBounds:l.any},{name:"text-overflow-wrap",type:u.textOverflowWrap,triggersBounds:l.any},{name:"text-max-width",type:u.size,triggersBounds:l.any},{name:"text-outline-width",type:u.size,triggersBounds:l.any},{name:"line-height",type:u.positiveNumber,triggersBounds:l.any}],d=[{name:"text-valign",type:u.valign,triggersBounds:l.any},{name:"text-halign",type:u.halign,triggersBounds:l.any},{name:"color",type:u.color},{name:"text-outline-color",type:u.color},{name:"text-outline-opacity",type:u.zeroOneNumber},{name:"text-background-color",type:u.color},{name:"text-background-opacity",type:u.zeroOneNumber},{name:"text-background-padding",type:u.size,triggersBounds:l.any},{name:"text-border-opacity",type:u.zeroOneNumber},{name:"text-border-color",type:u.color},{name:"text-border-width",type:u.size,triggersBounds:l.any},{name:"text-border-style",type:u.borderStyle,triggersBounds:l.any},{name:"text-background-shape",type:u.textBackgroundShape,triggersBounds:l.any},{name:"text-justification",type:u.justification}],y=[{name:"events",type:u.bool,triggersZOrder:l.any},{name:"text-events",type:u.bool,triggersZOrder:l.any}],p=[{name:"display",type:u.display,triggersZOrder:l.any,triggersBounds:l.any,triggersBoundsOfConnectedEdges:!0},{name:"visibility",type:u.visibility,triggersZOrder:l.any},{name:"opacity",type:u.zeroOneNumber,triggersZOrder:l.zeroNonZero},{name:"text-opacity",type:u.zeroOneNumber},{name:"min-zoomed-font-size",type:u.size},{name:"z-compound-depth",type:u.zCompoundDepth,triggersZOrder:l.any},{name:"z-index-compare",type:u.zIndexCompare,triggersZOrder:l.any},{name:"z-index",type:u.number,triggersZOrder:l.any}],g=[{name:"overlay-padding",type:u.size,triggersBounds:l.any},{name:"overlay-color",type:u.color},{name:"overlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"overlay-shape",type:u.overlayShape,triggersBounds:l.any},{name:"overlay-corner-radius",type:u.cornerRadius}],m=[{name:"underlay-padding",type:u.size,triggersBounds:l.any},{name:"underlay-color",type:u.color},{name:"underlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"underlay-shape",type:u.overlayShape,triggersBounds:l.any},{name:"underlay-corner-radius",type:u.cornerRadius}],b=[{name:"transition-property",type:u.propList},{name:"transition-duration",type:u.time},{name:"transition-delay",type:u.time},{name:"transition-timing-function",type:u.easing}],w=function(z,K){return K.value==="label"?-z.poolIndex():K.pfValue},S=[{name:"height",type:u.nodeSize,triggersBounds:l.any,hashOverride:w},{name:"width",type:u.nodeSize,triggersBounds:l.any,hashOverride:w},{name:"shape",type:u.nodeShape,triggersBounds:l.any},{name:"shape-polygon-points",type:u.polygonPointList,triggersBounds:l.any},{name:"corner-radius",type:u.cornerRadius},{name:"background-color",type:u.color},{name:"background-fill",type:u.fill},{name:"background-opacity",type:u.zeroOneNumber},{name:"background-blacken",type:u.nOneOneNumber},{name:"background-gradient-stop-colors",type:u.colors},{name:"background-gradient-stop-positions",type:u.percentages},{name:"background-gradient-direction",type:u.gradientDirection},{name:"padding",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"padding-relative-to",type:u.paddingRelativeTo,triggersBounds:l.any},{name:"bounds-expansion",type:u.boundsExpansion,triggersBounds:l.any}],E=[{name:"border-color",type:u.color},{name:"border-opacity",type:u.zeroOneNumber},{name:"border-width",type:u.size,triggersBounds:l.any},{name:"border-style",type:u.borderStyle},{name:"border-cap",type:u.lineCap},{name:"border-join",type:u.lineJoin},{name:"border-dash-pattern",type:u.numbers},{name:"border-dash-offset",type:u.number},{name:"border-position",type:u.linePosition}],x=[{name:"outline-color",type:u.color},{name:"outline-opacity",type:u.zeroOneNumber},{name:"outline-width",type:u.size,triggersBounds:l.any},{name:"outline-style",type:u.borderStyle},{name:"outline-offset",type:u.size,triggersBounds:l.any}],D=[{name:"background-image",type:u.urls},{name:"background-image-crossorigin",type:u.bgCrossOrigin},{name:"background-image-opacity",type:u.zeroOneNumbers},{name:"background-image-containment",type:u.bgContainment},{name:"background-image-smoothing",type:u.bools},{name:"background-position-x",type:u.bgPos},{name:"background-position-y",type:u.bgPos},{name:"background-width-relative-to",type:u.bgRelativeTo},{name:"background-height-relative-to",type:u.bgRelativeTo},{name:"background-repeat",type:u.bgRepeat},{name:"background-fit",type:u.bgFit},{name:"background-clip",type:u.bgClip},{name:"background-width",type:u.bgWH},{name:"background-height",type:u.bgWH},{name:"background-offset-x",type:u.bgPos},{name:"background-offset-y",type:u.bgPos}],C=[{name:"position",type:u.position,triggersBounds:l.any},{name:"compound-sizing-wrt-labels",type:u.compoundIncludeLabels,triggersBounds:l.any},{name:"min-width",type:u.size,triggersBounds:l.any},{name:"min-width-bias-left",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-width-bias-right",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height",type:u.size,triggersBounds:l.any},{name:"min-height-bias-top",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height-bias-bottom",type:u.sizeMaybePercent,triggersBounds:l.any}],M=[{name:"line-style",type:u.lineStyle},{name:"line-color",type:u.color},{name:"line-fill",type:u.fill},{name:"line-cap",type:u.lineCap},{name:"line-opacity",type:u.zeroOneNumber},{name:"line-dash-pattern",type:u.numbers},{name:"line-dash-offset",type:u.number},{name:"line-outline-width",type:u.size},{name:"line-outline-color",type:u.color},{name:"line-gradient-stop-colors",type:u.colors},{name:"line-gradient-stop-positions",type:u.percentages},{name:"curve-style",type:u.curveStyle,triggersBounds:l.any,triggersBoundsOfParallelBeziers:!0},{name:"haystack-radius",type:u.zeroOneNumber,triggersBounds:l.any},{name:"source-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"target-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"control-point-step-size",type:u.size,triggersBounds:l.any},{name:"control-point-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"control-point-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"segment-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-radii",type:u.numbers,triggersBounds:l.any},{name:"radius-type",type:u.radiusType,triggersBounds:l.any},{name:"taxi-turn",type:u.bidirectionalSizeMaybePercent,triggersBounds:l.any},{name:"taxi-turn-min-distance",type:u.size,triggersBounds:l.any},{name:"taxi-direction",type:u.axisDirection,triggersBounds:l.any},{name:"taxi-radius",type:u.number,triggersBounds:l.any},{name:"edge-distances",type:u.edgeDistances,triggersBounds:l.any},{name:"arrow-scale",type:u.positiveNumber,triggersBounds:l.any},{name:"loop-direction",type:u.angle,triggersBounds:l.any},{name:"loop-sweep",type:u.angle,triggersBounds:l.any},{name:"source-distance-from-node",type:u.size,triggersBounds:l.any},{name:"target-distance-from-node",type:u.size,triggersBounds:l.any}],P=[{name:"ghost",type:u.bool,triggersBounds:l.any},{name:"ghost-offset-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-offset-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-opacity",type:u.zeroOneNumber}],B=[{name:"selection-box-color",type:u.color},{name:"selection-box-opacity",type:u.zeroOneNumber},{name:"selection-box-border-color",type:u.color},{name:"selection-box-border-width",type:u.size},{name:"active-bg-color",type:u.color},{name:"active-bg-opacity",type:u.zeroOneNumber},{name:"active-bg-size",type:u.size},{name:"outside-texture-bg-color",type:u.color},{name:"outside-texture-bg-opacity",type:u.zeroOneNumber}],L=[];_e.pieBackgroundN=16,L.push({name:"pie-size",type:u.sizeMaybePercent});for(var k=1;k<=_e.pieBackgroundN;k++)L.push({name:"pie-"+k+"-background-color",type:u.color}),L.push({name:"pie-"+k+"-background-size",type:u.percent}),L.push({name:"pie-"+k+"-background-opacity",type:u.zeroOneNumber});var O=[],A=_e.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:u.arrowShape,triggersBounds:l.any},{name:"arrow-color",type:u.color},{name:"arrow-fill",type:u.arrowFill},{name:"arrow-width",type:u.arrowWidth}].forEach(function(W){A.forEach(function(z){var K=z+"-"+W.name,X=W.type,ae=W.triggersBounds;O.push({name:K,type:X,triggersBounds:ae})})},{});var R=_e.properties=[].concat(y,b,p,g,m,P,d,h,v,f,c,S,E,x,D,L,C,M,O,B),I=_e.propertyGroups={behavior:y,transition:b,visibility:p,overlay:g,underlay:m,ghost:P,commonLabel:d,labelDimensions:h,mainLabel:v,sourceLabel:f,targetLabel:c,nodeBody:S,nodeBorder:E,nodeOutline:x,backgroundImage:D,pie:L,compound:C,edgeLine:M,edgeArrow:O,core:B},V=_e.propertyGroupNames={},G=_e.propertyGroupKeys=Object.keys(I);G.forEach(function(W){V[W]=I[W].map(function(z){return z.name}),I[W].forEach(function(z){return z.groupKey=W})});var F=_e.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"segment-distance",pointsTo:"segment-distances"},{name:"segment-weight",pointsTo:"segment-weights"},{name:"segment-radius",pointsTo:"segment-radii"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];_e.propertyNames=R.map(function(W){return W.name});for(var q=0;q<R.length;q++){var Y=R[q];R[Y.name]=Y}for(var Q=0;Q<F.length;Q++){var J=F[Q],_=R[J.pointsTo],j={name:J.name,alias:!0,pointsTo:_};R.push(j),R[J.name]=j}})();_e.getDefaultProperty=function(r){return this.getDefaultProperties()[r]};_e.getDefaultProperties=function(){var r=this._private;if(r.defaultProperties!=null)return r.defaultProperties;for(var e=pe({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","overlay-corner-radius":"auto","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","underlay-corner-radius":"auto","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid","border-dash-pattern":[4,2],"border-dash-offset":0,"border-cap":"butt","border-join":"miter","border-position":"center","outline-color":"#999","outline-opacity":1,"outline-width":0,"outline-offset":0,"outline-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","corner-radius":"auto","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce(function(l,u){for(var v=1;v<=_e.pieBackgroundN;v++){var f=u.name.replace("{{i}}",v),c=u.value;l[f]=c}return l},{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-outline-width":0,"line-outline-color":"#000","line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"segment-radii":15,"radius-type":"arc-radius","taxi-turn":"50%","taxi-radius":15,"taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"},{name:"arrow-width",value:1}].reduce(function(l,u){return _e.arrowPrefixes.forEach(function(v){var f=v+"-"+u.name,c=u.value;l[f]=c}),l},{})),t={},a=0;a<this.properties.length;a++){var n=this.properties[a];if(!n.pointsTo){var i=n.name,s=e[i],o=this.parse(i,s);t[i]=o}}return r.defaultProperties=t,r.defaultProperties};_e.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var Sn={};Sn.parse=function(r,e,t,a){var n=this;if(qe(e))return n.parseImplWarn(r,e,t,a);var i=a==="mapping"||a===!0||a===!1||a==null?"dontcare":a,s=t?"t":"f",o=""+e,l=uo(r,o,s,i),u=n.propCache=n.propCache||[],v;return(v=u[l])||(v=u[l]=n.parseImplWarn(r,e,t,a)),(t||a==="mapping")&&(v=Or(v),v&&(v.value=Or(v.value))),v};Sn.parseImplWarn=function(r,e,t,a){var n=this.parseImpl(r,e,t,a);return!n&&e!=null&&Me("The style property `".concat(r,": ").concat(e,"` is invalid")),n&&(n.name==="width"||n.name==="height")&&e==="label"&&Me("The style value of `label` is deprecated for `"+n.name+"`"),n};Sn.parseImpl=function(r,e,t,a){var n=this;r=ui(r);var i=n.properties[r],s=e,o=n.types;if(!i||e===void 0)return null;i.alias&&(i=i.pointsTo,r=i.name);var l=ce(e);l&&(e=e.trim());var u=i.type;if(!u)return null;if(t&&(e===""||e===null))return{name:r,value:e,bypass:!0,deleteBypass:!0};if(qe(e))return{name:r,value:e,strValue:"fn",mapped:o.fn,bypass:t};var v,f;if(!(!l||a||e.length<7||e[1]!=="a")){if(e.length>=7&&e[0]==="d"&&(v=new RegExp(o.data.regex).exec(e))){if(t)return!1;var c=o.data;return{name:r,value:v,strValue:""+e,mapped:c,field:v[1],bypass:t}}else if(e.length>=10&&e[0]==="m"&&(f=new RegExp(o.mapData.regex).exec(e))){if(t||u.multiple)return!1;var h=o.mapData;if(!(u.color||u.number))return!1;var d=this.parse(r,f[4]);if(!d||d.mapped)return!1;var y=this.parse(r,f[5]);if(!y||y.mapped)return!1;if(d.pfValue===y.pfValue||d.strValue===y.strValue)return Me("`"+r+": "+e+"` is not a valid mapper because the output range is zero; converting to `"+r+": "+d.strValue+"`"),this.parse(r,d.strValue);if(u.color){var p=d.value,g=y.value,m=p[0]===g[0]&&p[1]===g[1]&&p[2]===g[2]&&(p[3]===g[3]||(p[3]==null||p[3]===1)&&(g[3]==null||g[3]===1));if(m)return!1}return{name:r,value:f,strValue:""+e,mapped:h,field:f[1],fieldMin:parseFloat(f[2]),fieldMax:parseFloat(f[3]),valueMin:d.value,valueMax:y.value,bypass:t}}}if(u.multiple&&a!=="multiple"){var b;if(l?b=e.split(/\s+/):Oe(e)?b=e:b=[e],u.evenMultiple&&b.length%2!==0)return null;for(var w=[],S=[],E=[],x="",D=!1,C=0;C<b.length;C++){var M=n.parse(r,b[C],t,"multiple");D=D||ce(M.value),w.push(M.value),E.push(M.pfValue!=null?M.pfValue:M.value),S.push(M.units),x+=(C>0?" ":"")+M.strValue}return u.validate&&!u.validate(w,S)?null:u.singleEnum&&D?w.length===1&&ce(w[0])?{name:r,value:w[0],strValue:w[0],bypass:t}:null:{name:r,value:w,pfValue:E,strValue:x,bypass:t,units:S}}var P=function(){for(var z=0;z<u.enums.length;z++){var K=u.enums[z];if(K===e)return{name:r,value:e,strValue:""+e,bypass:t}}return null};if(u.number){var B,L="px";if(u.units&&(B=u.units),u.implicitUnits&&(L=u.implicitUnits),!u.unitless)if(l){var k="px|em"+(u.allowPercent?"|\\%":"");B&&(k=B);var O=e.match("^("+We+")("+k+")?$");O&&(e=O[1],B=O[2]||L)}else(!B||u.implicitUnits)&&(B=L);if(e=parseFloat(e),isNaN(e)&&u.enums===void 0)return null;if(isNaN(e)&&u.enums!==void 0)return e=s,P();if(u.integer&&!Qu(e)||u.min!==void 0&&(e<u.min||u.strictMin&&e===u.min)||u.max!==void 0&&(e>u.max||u.strictMax&&e===u.max))return null;var A={name:r,value:e,strValue:""+e+(B||""),units:B,bypass:t};return u.unitless||B!=="px"&&B!=="em"?A.pfValue=e:A.pfValue=B==="px"||!B?e:this.getEmSizeInPixels()*e,(B==="ms"||B==="s")&&(A.pfValue=B==="ms"?e:1e3*e),(B==="deg"||B==="rad")&&(A.pfValue=B==="rad"?e:Mv(e)),B==="%"&&(A.pfValue=e/100),A}else if(u.propList){var R=[],I=""+e;if(I!=="none"){for(var V=I.split(/\s*,\s*|\s+/),G=0;G<V.length;G++){var F=V[G].trim();n.properties[F]?R.push(F):Me("`"+F+"` is not a valid property name")}if(R.length===0)return null}return{name:r,value:R,strValue:R.length===0?"none":R.join(" "),bypass:t}}else if(u.color){var q=dl(e);return q?{name:r,value:q,pfValue:q,strValue:"rgb("+q[0]+","+q[1]+","+q[2]+")",bypass:t}:null}else if(u.regex||u.regexes){if(u.enums){var Y=P();if(Y)return Y}for(var Q=u.regexes?u.regexes:[u.regex],J=0;J<Q.length;J++){var _=new RegExp(Q[J]),j=_.exec(e);if(j)return{name:r,value:u.singleRegexMatchValue?j[1]:j,strValue:""+e,bypass:t}}return null}else return u.string?{name:r,value:""+e,strValue:""+e,bypass:t}:u.enums?P():null};var nr=function r(e){if(!(this instanceof r))return new r(e);if(!oi(e)){$e("A style must have a core reference");return}this._private={cy:e,coreStyle:{}},this.length=0,this.resetToDefault()},sr=nr.prototype;sr.instanceString=function(){return"style"};sr.clear=function(){for(var r=this._private,e=r.cy,t=e.elements(),a=0;a<this.length;a++)this[a]=void 0;return this.length=0,r.contextStyles={},r.propDiffs={},this.cleanElements(t,!0),t.forEach(function(n){var i=n[0]._private;i.styleDirty=!0,i.appliedInitStyle=!1}),this};sr.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this};sr.core=function(r){return this._private.coreStyle[r]||this.getDefaultProperty(r)};sr.selector=function(r){var e=r==="core"?null:new rt(r),t=this.length++;return this[t]={selector:e,properties:[],mappedProperties:[],index:t},this};sr.css=function(){var r=this,e=arguments;if(e.length===1)for(var t=e[0],a=0;a<r.properties.length;a++){var n=r.properties[a],i=t[n.name];i===void 0&&(i=t[vn(n.name)]),i!==void 0&&this.cssRule(n.name,i)}else e.length===2&&this.cssRule(e[0],e[1]);return this};sr.style=sr.css;sr.cssRule=function(r,e){var t=this.parse(r,e);if(t){var a=this.length-1;this[a].properties.push(t),this[a].properties[t.name]=t,t.name.match(/pie-(\d+)-background-size/)&&t.value&&(this._private.hasPie=!0),t.mapped&&this[a].mappedProperties.push(t);var n=!this[a].selector;n&&(this._private.coreStyle[t.name]=t)}return this};sr.append=function(r){return _s(r)?r.appendToStyle(this):Oe(r)?this.appendFromJson(r):ce(r)&&this.appendFromString(r),this};nr.fromJson=function(r,e){var t=new nr(r);return t.fromJson(e),t};nr.fromString=function(r,e){return new nr(r).fromString(e)};[or,Ta,Ei,zr,Cn,Ci,_e,Sn].forEach(function(r){pe(sr,r)});nr.types=sr.types;nr.properties=sr.properties;nr.propertyGroups=sr.propertyGroups;nr.propertyGroupNames=sr.propertyGroupNames;nr.propertyGroupKeys=sr.propertyGroupKeys;var Jh={style:function(e){if(e){var t=this.setStyle(e);t.update()}return this._private.style},setStyle:function(e){var t=this._private;return _s(e)?t.style=e.generateStyle(this):Oe(e)?t.style=nr.fromJson(this,e):ce(e)?t.style=nr.fromString(this,e):t.style=nr(this),t.style},updateStyle:function(){this.mutableElements().updateStyle()}},_h="single",yt={autolock:function(e){if(e!==void 0)this._private.autolock=!!e;else return this._private.autolock;return this},autoungrabify:function(e){if(e!==void 0)this._private.autoungrabify=!!e;else return this._private.autoungrabify;return this},autounselectify:function(e){if(e!==void 0)this._private.autounselectify=!!e;else return this._private.autounselectify;return this},selectionType:function(e){var t=this._private;if(t.selectionType==null&&(t.selectionType=_h),e!==void 0)(e==="additive"||e==="single")&&(t.selectionType=e);else return t.selectionType;return this},panningEnabled:function(e){if(e!==void 0)this._private.panningEnabled=!!e;else return this._private.panningEnabled;return this},userPanningEnabled:function(e){if(e!==void 0)this._private.userPanningEnabled=!!e;else return this._private.userPanningEnabled;return this},zoomingEnabled:function(e){if(e!==void 0)this._private.zoomingEnabled=!!e;else return this._private.zoomingEnabled;return this},userZoomingEnabled:function(e){if(e!==void 0)this._private.userZoomingEnabled=!!e;else return this._private.userZoomingEnabled;return this},boxSelectionEnabled:function(e){if(e!==void 0)this._private.boxSelectionEnabled=!!e;else return this._private.boxSelectionEnabled;return this},pan:function(){var e=arguments,t=this._private.pan,a,n,i,s,o;switch(e.length){case 0:return t;case 1:if(ce(e[0]))return a=e[0],t[a];if(Te(e[0])){if(!this._private.panningEnabled)return this;i=e[0],s=i.x,o=i.y,ne(s)&&(t.x=s),ne(o)&&(t.y=o),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;a=e[0],n=e[1],(a==="x"||a==="y")&&ne(n)&&(t[a]=n),this.emit("pan viewport");break}return this.notify("viewport"),this},panBy:function(e,t){var a=arguments,n=this._private.pan,i,s,o,l,u;if(!this._private.panningEnabled)return this;switch(a.length){case 1:Te(e)&&(o=a[0],l=o.x,u=o.y,ne(l)&&(n.x+=l),ne(u)&&(n.y+=u),this.emit("pan viewport"));break;case 2:i=e,s=t,(i==="x"||i==="y")&&ne(s)&&(n[i]+=s),this.emit("pan viewport");break}return this.notify("viewport"),this},fit:function(e,t){var a=this.getFitViewport(e,t);if(a){var n=this._private;n.zoom=a.zoom,n.pan=a.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},getFitViewport:function(e,t){if(ne(e)&&t===void 0&&(t=e,e=void 0),!(!this._private.panningEnabled||!this._private.zoomingEnabled)){var a;if(ce(e)){var n=e;e=this.$(n)}else if(ju(e)){var i=e;a={x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2},a.w=a.x2-a.x1,a.h=a.y2-a.y1}else yr(e)||(e=this.mutableElements());if(!(yr(e)&&e.empty())){a=a||e.boundingBox();var s=this.width(),o=this.height(),l;if(t=ne(t)?t:0,!isNaN(s)&&!isNaN(o)&&s>0&&o>0&&!isNaN(a.w)&&!isNaN(a.h)&&a.w>0&&a.h>0){l=Math.min((s-2*t)/a.w,(o-2*t)/a.h),l=l>this._private.maxZoom?this._private.maxZoom:l,l=l<this._private.minZoom?this._private.minZoom:l;var u={x:(s-l*(a.x1+a.x2))/2,y:(o-l*(a.y1+a.y2))/2};return{zoom:l,pan:u}}}}},zoomRange:function(e,t){var a=this._private;if(t==null){var n=e;e=n.min,t=n.max}return ne(e)&&ne(t)&&e<=t?(a.minZoom=e,a.maxZoom=t):ne(e)&&t===void 0&&e<=a.maxZoom?a.minZoom=e:ne(t)&&e===void 0&&t>=a.minZoom&&(a.maxZoom=t),this},minZoom:function(e){return e===void 0?this._private.minZoom:this.zoomRange({min:e})},maxZoom:function(e){return e===void 0?this._private.maxZoom:this.zoomRange({max:e})},getZoomedViewport:function(e){var t=this._private,a=t.pan,n=t.zoom,i,s,o=!1;if(t.zoomingEnabled||(o=!0),ne(e)?s=e:Te(e)&&(s=e.level,e.position!=null?i=hn(e.position,n,a):e.renderedPosition!=null&&(i=e.renderedPosition),i!=null&&!t.panningEnabled&&(o=!0)),s=s>t.maxZoom?t.maxZoom:s,s=s<t.minZoom?t.minZoom:s,o||!ne(s)||s===n||i!=null&&(!ne(i.x)||!ne(i.y)))return null;if(i!=null){var l=a,u=n,v=s,f={x:-v/u*(i.x-l.x)+i.x,y:-v/u*(i.y-l.y)+i.y};return{zoomed:!0,panned:!0,zoom:v,pan:f}}else return{zoomed:!0,panned:!1,zoom:s,pan:a}},zoom:function(e){if(e===void 0)return this._private.zoom;var t=this.getZoomedViewport(e),a=this._private;return t==null||!t.zoomed?this:(a.zoom=t.zoom,t.panned&&(a.pan.x=t.pan.x,a.pan.y=t.pan.y),this.emit("zoom"+(t.panned?" pan":"")+" viewport"),this.notify("viewport"),this)},viewport:function(e){var t=this._private,a=!0,n=!0,i=[],s=!1,o=!1;if(!e)return this;if(ne(e.zoom)||(a=!1),Te(e.pan)||(n=!1),!a&&!n)return this;if(a){var l=e.zoom;l<t.minZoom||l>t.maxZoom||!t.zoomingEnabled?s=!0:(t.zoom=l,i.push("zoom"))}if(n&&(!s||!e.cancelOnFailedZoom)&&t.panningEnabled){var u=e.pan;ne(u.x)&&(t.pan.x=u.x,o=!1),ne(u.y)&&(t.pan.y=u.y,o=!1),o||i.push("pan")}return i.length>0&&(i.push("viewport"),this.emit(i.join(" ")),this.notify("viewport")),this},center:function(e){var t=this.getCenterPan(e);return t&&(this._private.pan=t,this.emit("pan viewport"),this.notify("viewport")),this},getCenterPan:function(e,t){if(this._private.panningEnabled){if(ce(e)){var a=e;e=this.mutableElements().filter(a)}else yr(e)||(e=this.mutableElements());if(e.length!==0){var n=e.boundingBox(),i=this.width(),s=this.height();t=t===void 0?this._private.zoom:t;var o={x:(i-t*(n.x1+n.x2))/2,y:(s-t*(n.y1+n.y2))/2};return o}}},reset:function(){return!this._private.panningEnabled||!this._private.zoomingEnabled?this:(this.viewport({pan:{x:0,y:0},zoom:1}),this)},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e=this._private,t=e.container,a=this;return e.sizeCache=e.sizeCache||(t?function(){var n=a.window().getComputedStyle(t),i=function(o){return parseFloat(n.getPropertyValue(o))};return{width:t.clientWidth-i("padding-left")-i("padding-right"),height:t.clientHeight-i("padding-top")-i("padding-bottom")}}():{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,t=this._private.zoom,a=this.renderedExtent(),n={x1:(a.x1-e.x)/t,x2:(a.x2-e.x)/t,y1:(a.y1-e.y)/t,y2:(a.y2-e.y)/t};return n.w=n.x2-n.x1,n.h=n.y2-n.y1,n},renderedExtent:function(){var e=this.width(),t=this.height();return{x1:0,y1:0,x2:e,y2:t,w:e,h:t}},multiClickDebounceTime:function(e){if(e)this._private.multiClickDebounceTime=e;else return this._private.multiClickDebounceTime;return this}};yt.centre=yt.center;yt.autolockNodes=yt.autolock;yt.autoungrabifyNodes=yt.autoungrabify;var ma={data:Be.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:Be.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:Be.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Be.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};ma.attr=ma.data;ma.removeAttr=ma.removeData;var ba=function(e){var t=this;e=pe({},e);var a=e.container;a&&!_a(a)&&_a(a[0])&&(a=a[0]);var n=a?a._cyreg:null;n=n||{},n&&n.cy&&(n.cy.destroy(),n={});var i=n.readies=n.readies||[];a&&(a._cyreg=n),n.cy=t;var s=Ke!==void 0&&a!==void 0&&!e.headless,o=e;o.layout=pe({name:s?"grid":"null"},o.layout),o.renderer=pe({name:s?"canvas":"null"},o.renderer);var l=function(d,y,p){return y!==void 0?y:p!==void 0?p:d},u=this._private={container:a,ready:!1,options:o,elements:new er(this),listeners:[],aniEles:new er(this),data:o.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:l(!0,o.zoomingEnabled),userZoomingEnabled:l(!0,o.userZoomingEnabled),panningEnabled:l(!0,o.panningEnabled),userPanningEnabled:l(!0,o.userPanningEnabled),boxSelectionEnabled:l(!0,o.boxSelectionEnabled),autolock:l(!1,o.autolock,o.autolockNodes),autoungrabify:l(!1,o.autoungrabify,o.autoungrabifyNodes),autounselectify:l(!1,o.autounselectify),styleEnabled:o.styleEnabled===void 0?s:o.styleEnabled,zoom:ne(o.zoom)?o.zoom:1,pan:{x:Te(o.pan)&&ne(o.pan.x)?o.pan.x:0,y:Te(o.pan)&&ne(o.pan.y)?o.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:l(250,o.multiClickDebounceTime)};this.createEmitter(),this.selectionType(o.selectionType),this.zoomRange({min:o.minZoom,max:o.maxZoom});var v=function(d,y){var p=d.some(el);if(p)return qt.all(d).then(y);y(d)};u.styleEnabled&&t.setStyle([]);var f=pe({},o,o.renderer);t.initRenderer(f);var c=function(d,y,p){t.notifications(!1);var g=t.mutableElements();g.length>0&&g.remove(),d!=null&&(Te(d)||Oe(d))&&t.add(d),t.one("layoutready",function(b){t.notifications(!0),t.emit(b),t.one("load",y),t.emitAndNotify("load")}).one("layoutstop",function(){t.one("done",p),t.emit("done")});var m=pe({},t._private.options.layout);m.eles=t.elements(),t.layout(m).run()};v([o.style,o.elements],function(h){var d=h[0],y=h[1];u.styleEnabled&&t.style().append(d),c(y,function(){t.startAnimationLoop(),u.ready=!0,qe(o.ready)&&t.on("ready",o.ready);for(var p=0;p<i.length;p++){var g=i[p];t.on("ready",g)}n&&(n.readies=[]),t.emit("ready")},o.done)})},sn=ba.prototype;pe(sn,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},destroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){var e=this;if(!e.destroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return this._private.renderer.isHeadless()},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container||null},window:function(){var e=this._private.container;if(e==null)return Ke;var t=this._private.container.ownerDocument;return t===void 0||t==null?Ke:t.defaultView||Ke},mount:function(e){if(e!=null){var t=this,a=t._private,n=a.options;return!_a(e)&&_a(e[0])&&(e=e[0]),t.stopAnimationLoop(),t.destroyRenderer(),a.container=e,a.styleEnabled=!0,t.invalidateSize(),t.initRenderer(pe({},n,n.renderer,{name:n.renderer.name==="null"?"canvas":n.renderer.name})),t.startAnimationLoop(),t.style(n.style),t.emit("mount"),t}},unmount:function(){var e=this;return e.stopAnimationLoop(),e.destroyRenderer(),e.initRenderer({name:"null"}),e.emit("unmount"),e},options:function(){return Or(this._private.options)},json:function(e){var t=this,a=t._private,n=t.mutableElements(),i=function(w){return t.getElementById(w.id())};if(Te(e)){if(t.startBatch(),e.elements){var s={},o=function(w,S){for(var E=[],x=[],D=0;D<w.length;D++){var C=w[D];if(!C.data.id){Me("cy.json() cannot handle elements without an ID attribute");continue}var M=""+C.data.id,P=t.getElementById(M);s[M]=!0,P.length!==0?x.push({ele:P,json:C}):(S&&(C.group=S),E.push(C))}t.add(E);for(var B=0;B<x.length;B++){var L=x[B],k=L.ele,O=L.json;k.json(O)}};if(Oe(e.elements))o(e.elements);else for(var l=["nodes","edges"],u=0;u<l.length;u++){var v=l[u],f=e.elements[v];Oe(f)&&o(f,v)}var c=t.collection();n.filter(function(b){return!s[b.id()]}).forEach(function(b){b.isParent()?c.merge(b):b.remove()}),c.forEach(function(b){return b.children().move({parent:null})}),c.forEach(function(b){return i(b).remove()})}e.style&&t.style(e.style),e.zoom!=null&&e.zoom!==a.zoom&&t.zoom(e.zoom),e.pan&&(e.pan.x!==a.pan.x||e.pan.y!==a.pan.y)&&t.pan(e.pan),e.data&&t.data(e.data);for(var h=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],d=0;d<h.length;d++){var y=h[d];e[y]!=null&&t[y](e[y])}return t.endBatch(),this}else{var p=!!e,g={};p?g.elements=this.elements().map(function(b){return b.json()}):(g.elements={},n.forEach(function(b){var w=b.group();g.elements[w]||(g.elements[w]=[]),g.elements[w].push(b.json())})),this._private.styleEnabled&&(g.style=t.style().json()),g.data=Or(t.data());var m=a.options;return g.zoomingEnabled=a.zoomingEnabled,g.userZoomingEnabled=a.userZoomingEnabled,g.zoom=a.zoom,g.minZoom=a.minZoom,g.maxZoom=a.maxZoom,g.panningEnabled=a.panningEnabled,g.userPanningEnabled=a.userPanningEnabled,g.pan=Or(a.pan),g.boxSelectionEnabled=a.boxSelectionEnabled,g.renderer=Or(m.renderer),g.hideEdgesOnViewport=m.hideEdgesOnViewport,g.textureOnViewport=m.textureOnViewport,g.wheelSensitivity=m.wheelSensitivity,g.motionBlur=m.motionBlur,g.multiClickDebounceTime=m.multiClickDebounceTime,g}}});sn.$id=sn.getElementById;[$h,Uh,eu,Zn,Xa,Xh,Qn,Za,Jh,yt,ma].forEach(function(r){pe(sn,r)});var jh={fit:!0,directed:!1,padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}},eg={maximal:!1,acyclic:!1},St=function(e){return e.scratch("breadthfirst")},Ds=function(e,t){return e.scratch("breadthfirst",t)};function ru(r){this.options=pe({},jh,eg,r)}ru.prototype.run=function(){var r=this.options,e=r,t=r.cy,a=e.eles,n=a.nodes().filter(function(te){return!te.isParent()}),i=a,s=e.directed,o=e.acyclic||e.maximal||e.maximalAdjustments>0,l=hr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()}),u;if(yr(e.roots))u=e.roots;else if(Oe(e.roots)){for(var v=[],f=0;f<e.roots.length;f++){var c=e.roots[f],h=t.getElementById(c);v.push(h)}u=t.collection(v)}else if(ce(e.roots))u=t.$(e.roots);else if(s)u=n.roots();else{var d=a.components();u=t.collection();for(var y=function(re){var ve=d[re],le=ve.maxDegree(!1),oe=ve.filter(function(de){return de.degree(!1)===le});u=u.add(oe)},p=0;p<d.length;p++)y(p)}var g=[],m={},b=function(re,ve){g[ve]==null&&(g[ve]=[]);var le=g[ve].length;g[ve].push(re),Ds(re,{index:le,depth:ve})},w=function(re,ve){var le=St(re),oe=le.depth,de=le.index;g[oe][de]=null,b(re,ve)};i.bfs({roots:u,directed:e.directed,visit:function(re,ve,le,oe,de){var Le=re[0],Ce=Le.id();b(Le,de),m[Ce]=!0}});for(var S=[],E=0;E<n.length;E++){var x=n[E];m[x.id()]||S.push(x)}var D=function(re){for(var ve=g[re],le=0;le<ve.length;le++){var oe=ve[le];if(oe==null){ve.splice(le,1),le--;continue}Ds(oe,{depth:re,index:le})}},C=function(){for(var re=0;re<g.length;re++)D(re)},M=function(re,ve){for(var le=St(re),oe=re.incomers().filter(function(Pe){return Pe.isNode()&&a.has(Pe)}),de=-1,Le=re.id(),Ce=0;Ce<oe.length;Ce++){var xe=oe[Ce],Ae=St(xe);de=Math.max(de,Ae.depth)}if(le.depth<=de){if(!e.acyclic&&ve[Le])return null;var Ee=de+1;return w(re,Ee),ve[Le]=Ee,!0}return!1};if(s&&o){var P=[],B={},L=function(re){return P.push(re)},k=function(){return P.shift()};for(n.forEach(function(te){return P.push(te)});P.length>0;){var O=k(),A=M(O,B);if(A)O.outgoers().filter(function(te){return te.isNode()&&a.has(te)}).forEach(L);else if(A===null){Me("Detected double maximal shift for node `"+O.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}C();var R=0;if(e.avoidOverlap)for(var I=0;I<n.length;I++){var V=n[I],G=V.layoutDimensions(e),F=G.w,q=G.h;R=Math.max(R,F,q)}var Y={},Q=function(re){if(Y[re.id()])return Y[re.id()];for(var ve=St(re).depth,le=re.neighborhood(),oe=0,de=0,Le=0;Le<le.length;Le++){var Ce=le[Le];if(!(Ce.isEdge()||Ce.isParent()||!n.has(Ce))){var xe=St(Ce);if(xe!=null){var Ae=xe.index,Ee=xe.depth;if(!(Ae==null||Ee==null)){var Pe=g[Ee].length;Ee<ve&&(oe+=Ae/Pe,de++)}}}}return de=Math.max(1,de),oe=oe/de,de===0&&(oe=0),Y[re.id()]=oe,oe},J=function(re,ve){var le=Q(re),oe=Q(ve),de=le-oe;return de===0?eo(re.id(),ve.id()):de};e.depthSort!==void 0&&(J=e.depthSort);for(var _=0;_<g.length;_++)g[_].sort(J),D(_);for(var j=[],W=0;W<S.length;W++)j.push(S[W]);g.unshift(j),C();for(var z=0,K=0;K<g.length;K++)z=Math.max(g[K].length,z);var X={x:l.x1+l.w/2,y:l.x1+l.h/2},ae=g.reduce(function(te,re){return Math.max(te,re.length)},0),he=function(re){var ve=St(re),le=ve.depth,oe=ve.index,de=g[le].length,Le=Math.max(l.w/((e.grid?ae:de)+1),R),Ce=Math.max(l.h/(g.length+1),R),xe=Math.min(l.w/2/g.length,l.h/2/g.length);if(xe=Math.max(xe,R),e.circle){var Ee=xe*le+xe-(g.length>0&&g[0].length<=3?xe/2:0),Pe=2*Math.PI/g[le].length*oe;return le===0&&g[0].length===1&&(Ee=1),{x:X.x+Ee*Math.cos(Pe),y:X.y+Ee*Math.sin(Pe)}}else{var Ae={x:X.x+(oe+1-(de+1)/2)*Le,y:(le+1)*Ce};return Ae}};return a.nodes().layoutPositions(this,e,he),this};var rg={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function tu(r){this.options=pe({},rg,r)}tu.prototype.run=function(){var r=this.options,e=r,t=r.cy,a=e.eles,n=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,i=a.nodes().not(":parent");e.sort&&(i=i.sort(e.sort));for(var s=hr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},l=e.sweep===void 0?2*Math.PI-2*Math.PI/i.length:e.sweep,u=l/Math.max(1,i.length-1),v,f=0,c=0;c<i.length;c++){var h=i[c],d=h.layoutDimensions(e),y=d.w,p=d.h;f=Math.max(f,y,p)}if(ne(e.radius)?v=e.radius:i.length<=1?v=0:v=Math.min(s.h,s.w)/2-f,i.length>1&&e.avoidOverlap){f*=1.75;var g=Math.cos(u)-Math.cos(0),m=Math.sin(u)-Math.sin(0),b=Math.sqrt(f*f/(g*g+m*m));v=Math.max(b,v)}var w=function(E,x){var D=e.startAngle+x*u*(n?1:-1),C=v*Math.cos(D),M=v*Math.sin(D),P={x:o.x+C,y:o.y+M};return P};return a.nodes().layoutPositions(this,e,w),this};var tg={fit:!0,padding:30,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function au(r){this.options=pe({},tg,r)}au.prototype.run=function(){for(var r=this.options,e=r,t=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,a=r.cy,n=e.eles,i=n.nodes().not(":parent"),s=hr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:a.width(),h:a.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},l=[],u=0,v=0;v<i.length;v++){var f=i[v],c=void 0;c=e.concentric(f),l.push({value:c,node:f}),f._private.scratch.concentric=c}i.updateStyle();for(var h=0;h<i.length;h++){var d=i[h],y=d.layoutDimensions(e);u=Math.max(u,y.w,y.h)}l.sort(function(te,re){return re.value-te.value});for(var p=e.levelWidth(i),g=[[]],m=g[0],b=0;b<l.length;b++){var w=l[b];if(m.length>0){var S=Math.abs(m[0].value-w.value);S>=p&&(m=[],g.push(m))}m.push(w)}var E=u+e.minNodeSpacing;if(!e.avoidOverlap){var x=g.length>0&&g[0].length>1,D=Math.min(s.w,s.h)/2-E,C=D/(g.length+x?1:0);E=Math.min(E,C)}for(var M=0,P=0;P<g.length;P++){var B=g[P],L=e.sweep===void 0?2*Math.PI-2*Math.PI/B.length:e.sweep,k=B.dTheta=L/Math.max(1,B.length-1);if(B.length>1&&e.avoidOverlap){var O=Math.cos(k)-Math.cos(0),A=Math.sin(k)-Math.sin(0),R=Math.sqrt(E*E/(O*O+A*A));M=Math.max(R,M)}B.r=M,M+=E}if(e.equidistant){for(var I=0,V=0,G=0;G<g.length;G++){var F=g[G],q=F.r-V;I=Math.max(I,q)}V=0;for(var Y=0;Y<g.length;Y++){var Q=g[Y];Y===0&&(V=Q.r),Q.r=V,V+=I}}for(var J={},_=0;_<g.length;_++)for(var j=g[_],W=j.dTheta,z=j.r,K=0;K<j.length;K++){var X=j[K],ae=e.startAngle+(t?1:-1)*W*K,he={x:o.x+z*Math.cos(ae),y:o.y+z*Math.sin(ae)};J[X.node.id()]=he}return n.nodes().layoutPositions(this,e,function(te){var re=te.id();return J[re]}),this};var Vn,ag={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,t){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function Tn(r){this.options=pe({},ag,r),this.options.layout=this;var e=this.options.eles.nodes(),t=this.options.eles.edges(),a=t.filter(function(n){var i=n.source().data("id"),s=n.target().data("id"),o=e.some(function(u){return u.data("id")===i}),l=e.some(function(u){return u.data("id")===s});return!o||!l});this.options.eles=this.options.eles.not(a)}Tn.prototype.run=function(){var r=this.options,e=r.cy,t=this;t.stopped=!1,(r.animate===!0||r.animate===!1)&&t.emit({type:"layoutstart",layout:t}),r.debug===!0?Vn=!0:Vn=!1;var a=ng(e,t,r);Vn&&og(a),r.randomize&&ug(a);var n=Hr(),i=function(){lg(a,e,r),r.fit===!0&&e.fit(r.padding)},s=function(c){return!(t.stopped||c>=r.numIter||(vg(a,r),a.temperature=a.temperature*r.coolingFactor,a.temperature<r.minTemp))},o=function(){if(r.animate===!0||r.animate===!1)i(),t.one("layoutstop",r.stop),t.emit({type:"layoutstop",layout:t});else{var c=r.eles.nodes(),h=nu(a,r,c);c.layoutPositions(t,r,h)}},l=0,u=!0;if(r.animate===!0){var v=function f(){for(var c=0;u&&c<r.refresh;)u=s(l),l++,c++;if(!u)Ps(a,r),o();else{var h=Hr();h-n>=r.animationThreshold&&i(),ja(f)}};v()}else{for(;u;)u=s(l),l++;Ps(a,r),o()}return this};Tn.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this};Tn.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var ng=function(e,t,a){for(var n=a.eles.edges(),i=a.eles.nodes(),s=hr(a.boundingBox?a.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),o={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:n.size(),temperature:a.initialTemp,clientWidth:s.w,clientHeight:s.h,boundingBox:s},l=a.eles.components(),u={},v=0;v<l.length;v++)for(var f=l[v],c=0;c<f.length;c++){var h=f[c];u[h.id()]=v}for(var v=0;v<o.nodeSize;v++){var d=i[v],y=d.layoutDimensions(a),p={};p.isLocked=d.locked(),p.id=d.data("id"),p.parentId=d.data("parent"),p.cmptId=u[d.id()],p.children=[],p.positionX=d.position("x"),p.positionY=d.position("y"),p.offsetX=0,p.offsetY=0,p.height=y.w,p.width=y.h,p.maxX=p.positionX+p.width/2,p.minX=p.positionX-p.width/2,p.maxY=p.positionY+p.height/2,p.minY=p.positionY-p.height/2,p.padLeft=parseFloat(d.style("padding")),p.padRight=parseFloat(d.style("padding")),p.padTop=parseFloat(d.style("padding")),p.padBottom=parseFloat(d.style("padding")),p.nodeRepulsion=qe(a.nodeRepulsion)?a.nodeRepulsion(d):a.nodeRepulsion,o.layoutNodes.push(p),o.idToIndex[p.id]=v}for(var g=[],m=0,b=-1,w=[],v=0;v<o.nodeSize;v++){var d=o.layoutNodes[v],S=d.parentId;S!=null?o.layoutNodes[o.idToIndex[S]].children.push(d.id):(g[++b]=d.id,w.push(d.id))}for(o.graphSet.push(w);m<=b;){var E=g[m++],x=o.idToIndex[E],h=o.layoutNodes[x],D=h.children;if(D.length>0){o.graphSet.push(D);for(var v=0;v<D.length;v++)g[++b]=D[v]}}for(var v=0;v<o.graphSet.length;v++)for(var C=o.graphSet[v],c=0;c<C.length;c++){var M=o.idToIndex[C[c]];o.indexToGraph[M]=v}for(var v=0;v<o.edgeSize;v++){var P=n[v],B={};B.id=P.data("id"),B.sourceId=P.data("source"),B.targetId=P.data("target");var L=qe(a.idealEdgeLength)?a.idealEdgeLength(P):a.idealEdgeLength,k=qe(a.edgeElasticity)?a.edgeElasticity(P):a.edgeElasticity,O=o.idToIndex[B.sourceId],A=o.idToIndex[B.targetId],R=o.indexToGraph[O],I=o.indexToGraph[A];if(R!=I){for(var V=ig(B.sourceId,B.targetId,o),G=o.graphSet[V],F=0,p=o.layoutNodes[O];G.indexOf(p.id)===-1;)p=o.layoutNodes[o.idToIndex[p.parentId]],F++;for(p=o.layoutNodes[A];G.indexOf(p.id)===-1;)p=o.layoutNodes[o.idToIndex[p.parentId]],F++;L*=F*a.nestingFactor}B.idealLength=L,B.elasticity=k,o.layoutEdges.push(B)}return o},ig=function(e,t,a){var n=sg(e,t,0,a);return 2>n.count?0:n.graph},sg=function r(e,t,a,n){var i=n.graphSet[a];if(-1<i.indexOf(e)&&-1<i.indexOf(t))return{count:2,graph:a};for(var s=0,o=0;o<i.length;o++){var l=i[o],u=n.idToIndex[l],v=n.layoutNodes[u].children;if(v.length!==0){var f=n.indexToGraph[n.idToIndex[v[0]]],c=r(e,t,f,n);if(c.count!==0)if(c.count===1){if(s++,s===2)break}else return c}}return{count:s,graph:a}},og,ug=function(e,t){for(var a=e.clientWidth,n=e.clientHeight,i=0;i<e.nodeSize;i++){var s=e.layoutNodes[i];s.children.length===0&&!s.isLocked&&(s.positionX=Math.random()*a,s.positionY=Math.random()*n)}},nu=function(e,t,a){var n=e.boundingBox,i={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return t.boundingBox&&(a.forEach(function(s){var o=e.layoutNodes[e.idToIndex[s.data("id")]];i.x1=Math.min(i.x1,o.positionX),i.x2=Math.max(i.x2,o.positionX),i.y1=Math.min(i.y1,o.positionY),i.y2=Math.max(i.y2,o.positionY)}),i.w=i.x2-i.x1,i.h=i.y2-i.y1),function(s,o){var l=e.layoutNodes[e.idToIndex[s.data("id")]];if(t.boundingBox){var u=(l.positionX-i.x1)/i.w,v=(l.positionY-i.y1)/i.h;return{x:n.x1+u*n.w,y:n.y1+v*n.h}}else return{x:l.positionX,y:l.positionY}}},lg=function(e,t,a){var n=a.layout,i=a.eles.nodes(),s=nu(e,a,i);i.positions(s),e.ready!==!0&&(e.ready=!0,n.one("layoutready",a.ready),n.emit({type:"layoutready",layout:this}))},vg=function(e,t,a){fg(e,t),hg(e),gg(e,t),pg(e),yg(e)},fg=function(e,t){for(var a=0;a<e.graphSet.length;a++)for(var n=e.graphSet[a],i=n.length,s=0;s<i;s++)for(var o=e.layoutNodes[e.idToIndex[n[s]]],l=s+1;l<i;l++){var u=e.layoutNodes[e.idToIndex[n[l]]];cg(o,u,e,t)}},ks=function(e){return-e+2*e*Math.random()},cg=function(e,t,a,n){var i=e.cmptId,s=t.cmptId;if(!(i!==s&&!a.isCompound)){var o=t.positionX-e.positionX,l=t.positionY-e.positionY,u=1;o===0&&l===0&&(o=ks(u),l=ks(u));var v=dg(e,t,o,l);if(v>0)var f=n.nodeOverlap*v,c=Math.sqrt(o*o+l*l),h=f*o/c,d=f*l/c;else var y=on(e,o,l),p=on(t,-1*o,-1*l),g=p.x-y.x,m=p.y-y.y,b=g*g+m*m,c=Math.sqrt(b),f=(e.nodeRepulsion+t.nodeRepulsion)/b,h=f*g/c,d=f*m/c;e.isLocked||(e.offsetX-=h,e.offsetY-=d),t.isLocked||(t.offsetX+=h,t.offsetY+=d)}},dg=function(e,t,a,n){if(a>0)var i=e.maxX-t.minX;else var i=t.maxX-e.minX;if(n>0)var s=e.maxY-t.minY;else var s=t.maxY-e.minY;return i>=0&&s>=0?Math.sqrt(i*i+s*s):0},on=function(e,t,a){var n=e.positionX,i=e.positionY,s=e.height||1,o=e.width||1,l=a/t,u=s/o,v={};return t===0&&0<a||t===0&&0>a?(v.x=n,v.y=i+s/2,v):0<t&&-1*u<=l&&l<=u?(v.x=n+o/2,v.y=i+o*a/2/t,v):0>t&&-1*u<=l&&l<=u?(v.x=n-o/2,v.y=i-o*a/2/t,v):0<a&&(l<=-1*u||l>=u)?(v.x=n+s*t/2/a,v.y=i+s/2,v):(0>a&&(l<=-1*u||l>=u)&&(v.x=n-s*t/2/a,v.y=i-s/2),v)},hg=function(e,t){for(var a=0;a<e.edgeSize;a++){var n=e.layoutEdges[a],i=e.idToIndex[n.sourceId],s=e.layoutNodes[i],o=e.idToIndex[n.targetId],l=e.layoutNodes[o],u=l.positionX-s.positionX,v=l.positionY-s.positionY;if(!(u===0&&v===0)){var f=on(s,u,v),c=on(l,-1*u,-1*v),h=c.x-f.x,d=c.y-f.y,y=Math.sqrt(h*h+d*d),p=Math.pow(n.idealLength-y,2)/n.elasticity;if(y!==0)var g=p*h/y,m=p*d/y;else var g=0,m=0;s.isLocked||(s.offsetX+=g,s.offsetY+=m),l.isLocked||(l.offsetX-=g,l.offsetY-=m)}}},gg=function(e,t){if(t.gravity!==0)for(var a=1,n=0;n<e.graphSet.length;n++){var i=e.graphSet[n],s=i.length;if(n===0)var o=e.clientHeight/2,l=e.clientWidth/2;else var u=e.layoutNodes[e.idToIndex[i[0]]],v=e.layoutNodes[e.idToIndex[u.parentId]],o=v.positionX,l=v.positionY;for(var f=0;f<s;f++){var c=e.layoutNodes[e.idToIndex[i[f]]];if(!c.isLocked){var h=o-c.positionX,d=l-c.positionY,y=Math.sqrt(h*h+d*d);if(y>a){var p=t.gravity*h/y,g=t.gravity*d/y;c.offsetX+=p,c.offsetY+=g}}}}},pg=function(e,t){var a=[],n=0,i=-1;for(a.push.apply(a,e.graphSet[0]),i+=e.graphSet[0].length;n<=i;){var s=a[n++],o=e.idToIndex[s],l=e.layoutNodes[o],u=l.children;if(0<u.length&&!l.isLocked){for(var v=l.offsetX,f=l.offsetY,c=0;c<u.length;c++){var h=e.layoutNodes[e.idToIndex[u[c]]];h.offsetX+=v,h.offsetY+=f,a[++i]=u[c]}l.offsetX=0,l.offsetY=0}}},yg=function(e,t){for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&(n.maxX=void 0,n.minX=void 0,n.maxY=void 0,n.minY=void 0)}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];if(!(0<n.children.length||n.isLocked)){var i=mg(n.offsetX,n.offsetY,e.temperature);n.positionX+=i.x,n.positionY+=i.y,n.offsetX=0,n.offsetY=0,n.minX=n.positionX-n.width,n.maxX=n.positionX+n.width,n.minY=n.positionY-n.height,n.maxY=n.positionY+n.height,bg(n,e)}}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&!n.isLocked&&(n.positionX=(n.maxX+n.minX)/2,n.positionY=(n.maxY+n.minY)/2,n.width=n.maxX-n.minX,n.height=n.maxY-n.minY)}},mg=function(e,t,a){var n=Math.sqrt(e*e+t*t);if(n>a)var i={x:a*e/n,y:a*t/n};else var i={x:e,y:t};return i},bg=function r(e,t){var a=e.parentId;if(a!=null){var n=t.layoutNodes[t.idToIndex[a]],i=!1;if((n.maxX==null||e.maxX+n.padRight>n.maxX)&&(n.maxX=e.maxX+n.padRight,i=!0),(n.minX==null||e.minX-n.padLeft<n.minX)&&(n.minX=e.minX-n.padLeft,i=!0),(n.maxY==null||e.maxY+n.padBottom>n.maxY)&&(n.maxY=e.maxY+n.padBottom,i=!0),(n.minY==null||e.minY-n.padTop<n.minY)&&(n.minY=e.minY-n.padTop,i=!0),i)return r(n,t)}},Ps=function(e,t){for(var a=e.layoutNodes,n=[],i=0;i<a.length;i++){var s=a[i],o=s.cmptId,l=n[o]=n[o]||[];l.push(s)}for(var u=0,i=0;i<n.length;i++){var v=n[i];if(v){v.x1=1/0,v.x2=-1/0,v.y1=1/0,v.y2=-1/0;for(var f=0;f<v.length;f++){var c=v[f];v.x1=Math.min(v.x1,c.positionX-c.width/2),v.x2=Math.max(v.x2,c.positionX+c.width/2),v.y1=Math.min(v.y1,c.positionY-c.height/2),v.y2=Math.max(v.y2,c.positionY+c.height/2)}v.w=v.x2-v.x1,v.h=v.y2-v.y1,u+=v.w*v.h}}n.sort(function(m,b){return b.w*b.h-m.w*m.h});for(var h=0,d=0,y=0,p=0,g=Math.sqrt(u)*e.clientWidth/e.clientHeight,i=0;i<n.length;i++){var v=n[i];if(v){for(var f=0;f<v.length;f++){var c=v[f];c.isLocked||(c.positionX+=h-v.x1,c.positionY+=d-v.y1)}h+=v.w+t.componentSpacing,y+=v.w+t.componentSpacing,p=Math.max(p,v.h),y>g&&(d+=p+t.componentSpacing,h=0,y=0,p=0)}}},wg={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function iu(r){this.options=pe({},wg,r)}iu.prototype.run=function(){var r=this.options,e=r,t=r.cy,a=e.eles,n=a.nodes().not(":parent");e.sort&&(n=n.sort(e.sort));var i=hr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});if(i.h===0||i.w===0)a.nodes().layoutPositions(this,e,function(Y){return{x:i.x1,y:i.y1}});else{var s=n.size(),o=Math.sqrt(s*i.h/i.w),l=Math.round(o),u=Math.round(i.w/i.h*o),v=function(Q){if(Q==null)return Math.min(l,u);var J=Math.min(l,u);J==l?l=Q:u=Q},f=function(Q){if(Q==null)return Math.max(l,u);var J=Math.max(l,u);J==l?l=Q:u=Q},c=e.rows,h=e.cols!=null?e.cols:e.columns;if(c!=null&&h!=null)l=c,u=h;else if(c!=null&&h==null)l=c,u=Math.ceil(s/l);else if(c==null&&h!=null)u=h,l=Math.ceil(s/u);else if(u*l>s){var d=v(),y=f();(d-1)*y>=s?v(d-1):(y-1)*d>=s&&f(y-1)}else for(;u*l<s;){var p=v(),g=f();(g+1)*p>=s?f(g+1):v(p+1)}var m=i.w/u,b=i.h/l;if(e.condense&&(m=0,b=0),e.avoidOverlap)for(var w=0;w<n.length;w++){var S=n[w],E=S._private.position;(E.x==null||E.y==null)&&(E.x=0,E.y=0);var x=S.layoutDimensions(e),D=e.avoidOverlapPadding,C=x.w+D,M=x.h+D;m=Math.max(m,C),b=Math.max(b,M)}for(var P={},B=function(Q,J){return!!P["c-"+Q+"-"+J]},L=function(Q,J){P["c-"+Q+"-"+J]=!0},k=0,O=0,A=function(){O++,O>=u&&(O=0,k++)},R={},I=0;I<n.length;I++){var V=n[I],G=e.position(V);if(G&&(G.row!==void 0||G.col!==void 0)){var F={row:G.row,col:G.col};if(F.col===void 0)for(F.col=0;B(F.row,F.col);)F.col++;else if(F.row===void 0)for(F.row=0;B(F.row,F.col);)F.row++;R[V.id()]=F,L(F.row,F.col)}}var q=function(Q,J){var _,j;if(Q.locked()||Q.isParent())return!1;var W=R[Q.id()];if(W)_=W.col*m+m/2+i.x1,j=W.row*b+b/2+i.y1;else{for(;B(k,O);)A();_=O*m+m/2+i.x1,j=k*b+b/2+i.y1,L(k,O),A()}return{x:_,y:j}};n.layoutPositions(this,e,q)}return this};var xg={ready:function(){},stop:function(){}};function Si(r){this.options=pe({},xg,r)}Si.prototype.run=function(){var r=this.options,e=r.eles,t=this;return r.cy,t.emit("layoutstart"),e.nodes().positions(function(){return{x:0,y:0}}),t.one("layoutready",r.ready),t.emit("layoutready"),t.one("layoutstop",r.stop),t.emit("layoutstop"),this};Si.prototype.stop=function(){return this};var Eg={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,spacingFactor:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function su(r){this.options=pe({},Eg,r)}su.prototype.run=function(){var r=this.options,e=r.eles,t=e.nodes(),a=qe(r.positions);function n(i){if(r.positions==null)return Tv(i.position());if(a)return r.positions(i);var s=r.positions[i._private.data.id];return s??null}return t.layoutPositions(this,r,function(i,s){var o=n(i);return i.locked()||o==null?!1:o}),this};var Cg={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function ou(r){this.options=pe({},Cg,r)}ou.prototype.run=function(){var r=this.options,e=r.cy,t=r.eles,a=hr(r.boundingBox?r.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),n=function(s,o){return{x:a.x1+Math.round(Math.random()*a.w),y:a.y1+Math.round(Math.random()*a.h)}};return t.nodes().layoutPositions(this,r,n),this};var Sg=[{name:"breadthfirst",impl:ru},{name:"circle",impl:tu},{name:"concentric",impl:au},{name:"cose",impl:Tn},{name:"grid",impl:iu},{name:"null",impl:Si},{name:"preset",impl:su},{name:"random",impl:ou}];function uu(r){this.options=r,this.notifications=0}var Bs=function(){},Ms=function(){throw new Error("A headless instance can not render images")};uu.prototype={recalculateRenderedStyle:Bs,notify:function(){this.notifications++},init:Bs,isHeadless:function(){return!0},png:Ms,jpg:Ms};var Ti={};Ti.arrowShapeWidth=.3;Ti.registerArrowShapes=function(){var r=this.arrowShapes={},e=this,t=function(u,v,f,c,h,d,y){var p=h.x-f/2-y,g=h.x+f/2+y,m=h.y-f/2-y,b=h.y+f/2+y,w=p<=u&&u<=g&&m<=v&&v<=b;return w},a=function(u,v,f,c,h){var d=u*Math.cos(c)-v*Math.sin(c),y=u*Math.sin(c)+v*Math.cos(c),p=d*f,g=y*f,m=p+h.x,b=g+h.y;return{x:m,y:b}},n=function(u,v,f,c){for(var h=[],d=0;d<u.length;d+=2){var y=u[d],p=u[d+1];h.push(a(y,p,v,f,c))}return h},i=function(u){for(var v=[],f=0;f<u.length;f++){var c=u[f];v.push(c.x,c.y)}return v},s=function(u){return u.pstyle("width").pfValue*u.pstyle("arrow-scale").pfValue*2},o=function(u,v){ce(v)&&(v=r[v]),r[u]=pe({name:u,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(c,h,d,y,p,g){var m=i(n(this.points,d+2*g,y,p)),b=dr(c,h,m);return b},roughCollide:t,draw:function(c,h,d,y){var p=n(this.points,h,d,y);e.arrowShapeImpl("polygon")(c,p)},spacing:function(c){return 0},gap:s},v)};o("none",{collide:en,roughCollide:en,draw:vi,spacing:qi,gap:qi}),o("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),o("arrow","triangle"),o("triangle-backcurve",{points:r.triangle.points,controlPoint:[0,-.15],roughCollide:t,draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),y=this.controlPoint,p=a(y[0],y[1],v,f,c);e.arrowShapeImpl(this.name)(u,d,p)},gap:function(u){return s(u)*.8}}),o("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(u,v,f,c,h,d,y){var p=i(n(this.points,f+2*y,c,h)),g=i(n(this.pointsTee,f+2*y,c,h)),m=dr(u,v,p)||dr(u,v,g);return m},draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),y=n(this.pointsTee,v,f,c);e.arrowShapeImpl(this.name)(u,d,y)}}),o("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function(u,v,f,c,h,d,y){var p=h,g=Math.pow(p.x-u,2)+Math.pow(p.y-v,2)<=Math.pow((f+2*y)*this.radius,2),m=i(n(this.points,f+2*y,c,h));return dr(u,v,m)||g},draw:function(u,v,f,c,h){var d=n(this.pointsTr,v,f,c);e.arrowShapeImpl(this.name)(u,d,c.x,c.y,this.radius*v)},spacing:function(u){return e.getArrowWidth(u.pstyle("width").pfValue,u.pstyle("arrow-scale").value)*this.radius}}),o("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(u,v){var f=this.baseCrossLinePts.slice(),c=v/u,h=3,d=5;return f[h]=f[h]-c,f[d]=f[d]-c,f},collide:function(u,v,f,c,h,d,y){var p=i(n(this.points,f+2*y,c,h)),g=i(n(this.crossLinePts(f,d),f+2*y,c,h)),m=dr(u,v,p)||dr(u,v,g);return m},draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),y=n(this.crossLinePts(v,h),v,f,c);e.arrowShapeImpl(this.name)(u,d,y)}}),o("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(u){return s(u)*.525}}),o("circle",{radius:.15,collide:function(u,v,f,c,h,d,y){var p=h,g=Math.pow(p.x-u,2)+Math.pow(p.y-v,2)<=Math.pow((f+2*y)*this.radius,2);return g},draw:function(u,v,f,c,h){e.arrowShapeImpl(this.name)(u,c.x,c.y,this.radius*v)},spacing:function(u){return e.getArrowWidth(u.pstyle("width").pfValue,u.pstyle("arrow-scale").value)*this.radius}}),o("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(u){return 1},gap:function(u){return 1}}),o("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),o("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(u){return u.pstyle("width").pfValue*u.pstyle("arrow-scale").value}}),o("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function(u){return .95*u.pstyle("width").pfValue*u.pstyle("arrow-scale").value}})};var bt={};bt.projectIntoViewport=function(r,e){var t=this.cy,a=this.findContainerClientCoords(),n=a[0],i=a[1],s=a[4],o=t.pan(),l=t.zoom(),u=((r-n)/s-o.x)/l,v=((e-i)/s-o.y)/l;return[u,v]};bt.findContainerClientCoords=function(){if(this.containerBB)return this.containerBB;var r=this.container,e=r.getBoundingClientRect(),t=this.cy.window().getComputedStyle(r),a=function(g){return parseFloat(t.getPropertyValue(g))},n={left:a("padding-left"),right:a("padding-right"),top:a("padding-top"),bottom:a("padding-bottom")},i={left:a("border-left-width"),right:a("border-right-width"),top:a("border-top-width"),bottom:a("border-bottom-width")},s=r.clientWidth,o=r.clientHeight,l=n.left+n.right,u=n.top+n.bottom,v=i.left+i.right,f=e.width/(s+v),c=s-l,h=o-u,d=e.left+n.left+i.left,y=e.top+n.top+i.top;return this.containerBB=[d,y,c,h,f]};bt.invalidateContainerClientCoordsCache=function(){this.containerBB=null};bt.findNearestElement=function(r,e,t,a){return this.findNearestElements(r,e,t,a)[0]};bt.findNearestElements=function(r,e,t,a){var n=this,i=this,s=i.getCachedZSortedEles(),o=[],l=i.cy.zoom(),u=i.cy.hasCompoundNodes(),v=(a?24:8)/l,f=(a?8:2)/l,c=(a?8:2)/l,h=1/0,d,y;t&&(s=s.interactive);function p(x,D){if(x.isNode()){if(y)return;y=x,o.push(x)}if(x.isEdge()&&(D==null||D<h))if(d){if(d.pstyle("z-compound-depth").value===x.pstyle("z-compound-depth").value&&d.pstyle("z-compound-depth").value===x.pstyle("z-compound-depth").value){for(var C=0;C<o.length;C++)if(o[C].isEdge()){o[C]=x,d=x,h=D??h;break}}}else o.push(x),d=x,h=D??h}function g(x){var D=x.outerWidth()+2*f,C=x.outerHeight()+2*f,M=D/2,P=C/2,B=x.position(),L=x.pstyle("corner-radius").value==="auto"?"auto":x.pstyle("corner-radius").pfValue,k=x._private.rscratch;if(B.x-M<=r&&r<=B.x+M&&B.y-P<=e&&e<=B.y+P){var O=i.nodeShapes[n.getNodeShape(x)];if(O.checkPoint(r,e,0,D,C,B.x,B.y,L,k))return p(x,0),!0}}function m(x){var D=x._private,C=D.rscratch,M=x.pstyle("width").pfValue,P=x.pstyle("arrow-scale").value,B=M/2+v,L=B*B,k=B*2,I=D.source,V=D.target,O;if(C.edgeType==="segments"||C.edgeType==="straight"||C.edgeType==="haystack"){for(var A=C.allpts,R=0;R+3<A.length;R+=2)if(Fv(r,e,A[R],A[R+1],A[R+2],A[R+3],k)&&L>(O=Gv(r,e,A[R],A[R+1],A[R+2],A[R+3])))return p(x,O),!0}else if(C.edgeType==="bezier"||C.edgeType==="multibezier"||C.edgeType==="self"||C.edgeType==="compound"){for(var A=C.allpts,R=0;R+5<C.allpts.length;R+=4)if(Vv(r,e,A[R],A[R+1],A[R+2],A[R+3],A[R+4],A[R+5],k)&&L>(O=Hv(r,e,A[R],A[R+1],A[R+2],A[R+3],A[R+4],A[R+5])))return p(x,O),!0}for(var I=I||D.source,V=V||D.target,G=n.getArrowWidth(M,P),F=[{name:"source",x:C.arrowStartX,y:C.arrowStartY,angle:C.srcArrowAngle},{name:"target",x:C.arrowEndX,y:C.arrowEndY,angle:C.tgtArrowAngle},{name:"mid-source",x:C.midX,y:C.midY,angle:C.midsrcArrowAngle},{name:"mid-target",x:C.midX,y:C.midY,angle:C.midtgtArrowAngle}],R=0;R<F.length;R++){var q=F[R],Y=i.arrowShapes[x.pstyle(q.name+"-arrow-shape").value],Q=x.pstyle("width").pfValue;if(Y.roughCollide(r,e,G,q.angle,{x:q.x,y:q.y},Q,v)&&Y.collide(r,e,G,q.angle,{x:q.x,y:q.y},Q,v))return p(x),!0}u&&o.length>0&&(g(I),g(V))}function b(x,D,C){return kr(x,D,C)}function w(x,D){var C=x._private,M=c,P;D?P=D+"-":P="",x.boundingBox();var B=C.labelBounds[D||"main"],L=x.pstyle(P+"label").value,k=x.pstyle("text-events").strValue==="yes";if(!(!k||!L)){var O=b(C.rscratch,"labelX",D),A=b(C.rscratch,"labelY",D),R=b(C.rscratch,"labelAngle",D),I=x.pstyle(P+"text-margin-x").pfValue,V=x.pstyle(P+"text-margin-y").pfValue,G=B.x1-M-I,F=B.x2+M-I,q=B.y1-M-V,Y=B.y2+M-V;if(R){var Q=Math.cos(R),J=Math.sin(R),_=function(he,te){return he=he-O,te=te-A,{x:he*Q-te*J+O,y:he*J+te*Q+A}},j=_(G,q),W=_(G,Y),z=_(F,q),K=_(F,Y),X=[j.x+I,j.y+V,z.x+I,z.y+V,K.x+I,K.y+V,W.x+I,W.y+V];if(dr(r,e,X))return p(x),!0}else if(It(B,r,e))return p(x),!0}}for(var S=s.length-1;S>=0;S--){var E=s[S];E.isNode()?g(E)||w(E):m(E)||w(E)||w(E,"source")||w(E,"target")}return o};bt.getAllInBox=function(r,e,t,a){var n=this.getCachedZSortedEles().interactive,i=[],s=Math.min(r,t),o=Math.max(r,t),l=Math.min(e,a),u=Math.max(e,a);r=s,t=o,e=l,a=u;for(var v=hr({x1:r,y1:e,x2:t,y2:a}),f=0;f<n.length;f++){var c=n[f];if(c.isNode()){var h=c,d=h.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:!1});di(v,d)&&!yo(d,v)&&i.push(h)}else{var y=c,p=y._private,g=p.rscratch;if(g.startX!=null&&g.startY!=null&&!It(v,g.startX,g.startY)||g.endX!=null&&g.endY!=null&&!It(v,g.endX,g.endY))continue;if(g.edgeType==="bezier"||g.edgeType==="multibezier"||g.edgeType==="self"||g.edgeType==="compound"||g.edgeType==="segments"||g.edgeType==="haystack"){for(var m=p.rstyle.bezierPts||p.rstyle.linePts||p.rstyle.haystackPts,b=!0,w=0;w<m.length;w++)if(!Nv(v,m[w])){b=!1;break}b&&i.push(y)}else(g.edgeType==="haystack"||g.edgeType==="straight")&&i.push(y)}}return i};var un={};un.calculateArrowAngles=function(r){var e=r._private.rscratch,t=e.edgeType==="haystack",a=e.edgeType==="bezier",n=e.edgeType==="multibezier",i=e.edgeType==="segments",s=e.edgeType==="compound",o=e.edgeType==="self",l,u,v,f,c,h,g,m;if(t?(v=e.haystackPts[0],f=e.haystackPts[1],c=e.haystackPts[2],h=e.haystackPts[3]):(v=e.arrowStartX,f=e.arrowStartY,c=e.arrowEndX,h=e.arrowEndY),g=e.midX,m=e.midY,i)l=v-e.segpts[0],u=f-e.segpts[1];else if(n||s||o||a){var d=e.allpts,y=Je(d[0],d[2],d[4],.1),p=Je(d[1],d[3],d[5],.1);l=v-y,u=f-p}else l=v-g,u=f-m;e.srcArrowAngle=Aa(l,u);var g=e.midX,m=e.midY;if(t&&(g=(v+c)/2,m=(f+h)/2),l=c-v,u=h-f,i){var d=e.allpts;if(d.length/2%2===0){var b=d.length/2,w=b-2;l=d[b]-d[w],u=d[b+1]-d[w+1]}else if(e.isRound)l=e.midVector[1],u=-e.midVector[0];else{var b=d.length/2-1,w=b-2;l=d[b]-d[w],u=d[b+1]-d[w+1]}}else if(n||s||o){var d=e.allpts,S=e.ctrlpts,E,x,D,C;if(S.length/2%2===0){var M=d.length/2-1,P=M+2,B=P+2;E=Je(d[M],d[P],d[B],0),x=Je(d[M+1],d[P+1],d[B+1],0),D=Je(d[M],d[P],d[B],1e-4),C=Je(d[M+1],d[P+1],d[B+1],1e-4)}else{var P=d.length/2-1,M=P-2,B=P+2;E=Je(d[M],d[P],d[B],.4999),x=Je(d[M+1],d[P+1],d[B+1],.4999),D=Je(d[M],d[P],d[B],.5),C=Je(d[M+1],d[P+1],d[B+1],.5)}l=D-E,u=C-x}if(e.midtgtArrowAngle=Aa(l,u),e.midDispX=l,e.midDispY=u,l*=-1,u*=-1,i){var d=e.allpts;if(d.length/2%2!==0){if(!e.isRound){var b=d.length/2-1,L=b+2;l=-(d[L]-d[b]),u=-(d[L+1]-d[b+1])}}}if(e.midsrcArrowAngle=Aa(l,u),i)l=c-e.segpts[e.segpts.length-2],u=h-e.segpts[e.segpts.length-1];else if(n||s||o||a){var d=e.allpts,k=d.length,y=Je(d[k-6],d[k-4],d[k-2],.9),p=Je(d[k-5],d[k-3],d[k-1],.9);l=c-y,u=h-p}else l=c-g,u=h-m;e.tgtArrowAngle=Aa(l,u)};un.getArrowWidth=un.getArrowHeight=function(r,e){var t=this.arrowWidthCache=this.arrowWidthCache||{},a=t[r+", "+e];return a||(a=Math.max(Math.pow(r*13.37,.9),29)*e,t[r+", "+e]=a,a)};var Jn,_n,Rr={},wr={},Ls,As,vt,Qa,$r,st,lt,Ar,Tt,$a,lu,vu,jn,ei,Rs,Os=function(e,t,a){a.x=t.x-e.x,a.y=t.y-e.y,a.len=Math.sqrt(a.x*a.x+a.y*a.y),a.nx=a.x/a.len,a.ny=a.y/a.len,a.ang=Math.atan2(a.ny,a.nx)},Tg=function(e,t){t.x=e.x*-1,t.y=e.y*-1,t.nx=e.nx*-1,t.ny=e.ny*-1,t.ang=e.ang>0?-(Math.PI-e.ang):Math.PI+e.ang},Dg=function(e,t,a,n,i){if(e!==Rs?Os(t,e,Rr):Tg(wr,Rr),Os(t,a,wr),Ls=Rr.nx*wr.ny-Rr.ny*wr.nx,As=Rr.nx*wr.nx-Rr.ny*-wr.ny,$r=Math.asin(Math.max(-1,Math.min(1,Ls))),Math.abs($r)<1e-6){Jn=t.x,_n=t.y,lt=Tt=0;return}vt=1,Qa=!1,As<0?$r<0?$r=Math.PI+$r:($r=Math.PI-$r,vt=-1,Qa=!0):$r>0&&(vt=-1,Qa=!0),t.radius!==void 0?Tt=t.radius:Tt=n,st=$r/2,$a=Math.min(Rr.len/2,wr.len/2),i?(Ar=Math.abs(Math.cos(st)*Tt/Math.sin(st)),Ar>$a?(Ar=$a,lt=Math.abs(Ar*Math.sin(st)/Math.cos(st))):lt=Tt):(Ar=Math.min($a,Tt),lt=Math.abs(Ar*Math.sin(st)/Math.cos(st))),jn=t.x+wr.nx*Ar,ei=t.y+wr.ny*Ar,Jn=jn-wr.ny*lt*vt,_n=ei+wr.nx*lt*vt,lu=t.x+Rr.nx*Ar,vu=t.y+Rr.ny*Ar,Rs=t};function fu(r,e){e.radius===0?r.lineTo(e.cx,e.cy):r.arc(e.cx,e.cy,e.radius,e.startAngle,e.endAngle,e.counterClockwise)}function Di(r,e,t,a){var n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0;return a===0||e.radius===0?{cx:e.x,cy:e.y,radius:0,startX:e.x,startY:e.y,stopX:e.x,stopY:e.y,startAngle:void 0,endAngle:void 0,counterClockwise:void 0}:(Dg(r,e,t,a,n),{cx:Jn,cy:_n,radius:lt,startX:lu,startY:vu,stopX:jn,stopY:ei,startAngle:Rr.ang+Math.PI/2*vt,endAngle:wr.ang-Math.PI/2*vt,counterClockwise:Qa})}var ur={};ur.findMidptPtsEtc=function(r,e){var t=e.posPts,a=e.intersectionPts,n=e.vectorNormInverse,i,s=r.pstyle("source-endpoint"),o=r.pstyle("target-endpoint"),l=s.units!=null&&o.units!=null,u=function(S,E,x,D){var C=D-E,M=x-S,P=Math.sqrt(M*M+C*C);return{x:-C/P,y:M/P}},v=r.pstyle("edge-distances").value;switch(v){case"node-position":i=t;break;case"intersection":i=a;break;case"endpoints":{if(l){var f=this.manualEndptToPx(r.source()[0],s),c=Tr(f,2),h=c[0],d=c[1],y=this.manualEndptToPx(r.target()[0],o),p=Tr(y,2),g=p[0],m=p[1],b={x1:h,y1:d,x2:g,y2:m};n=u(h,d,g,m),i=b}else Me("Edge ".concat(r.id()," has edge-distances:endpoints specified without manual endpoints specified via source-endpoint and target-endpoint.  Falling back on edge-distances:intersection (default).")),i=a;break}}return{midptPts:i,vectorNormInverse:n}};ur.findHaystackPoints=function(r){for(var e=0;e<r.length;e++){var t=r[e],a=t._private,n=a.rscratch;if(!n.haystack){var i=Math.random()*2*Math.PI;n.source={x:Math.cos(i),y:Math.sin(i)},i=Math.random()*2*Math.PI,n.target={x:Math.cos(i),y:Math.sin(i)}}var s=a.source,o=a.target,l=s.position(),u=o.position(),v=s.width(),f=o.width(),c=s.height(),h=o.height(),d=t.pstyle("haystack-radius").value,y=d/2;n.haystackPts=n.allpts=[n.source.x*v*y+l.x,n.source.y*c*y+l.y,n.target.x*f*y+u.x,n.target.y*h*y+u.y],n.midX=(n.allpts[0]+n.allpts[2])/2,n.midY=(n.allpts[1]+n.allpts[3])/2,n.edgeType="haystack",n.haystack=!0,this.storeEdgeProjections(t),this.calculateArrowAngles(t),this.recalculateEdgeLabelProjections(t),this.calculateLabelAngles(t)}};ur.findSegmentsPoints=function(r,e){var t=r._private.rscratch,a=r.pstyle("segment-weights"),n=r.pstyle("segment-distances"),i=r.pstyle("segment-radii"),s=r.pstyle("radius-type"),o=Math.min(a.pfValue.length,n.pfValue.length),l=i.pfValue[i.pfValue.length-1],u=s.pfValue[s.pfValue.length-1];t.edgeType="segments",t.segpts=[],t.radii=[],t.isArcRadius=[];for(var v=0;v<o;v++){var f=a.pfValue[v],c=n.pfValue[v],h=1-f,d=f,y=this.findMidptPtsEtc(r,e),p=y.midptPts,g=y.vectorNormInverse,m={x:p.x1*h+p.x2*d,y:p.y1*h+p.y2*d};t.segpts.push(m.x+g.x*c,m.y+g.y*c),t.radii.push(i.pfValue[v]!==void 0?i.pfValue[v]:l),t.isArcRadius.push((s.pfValue[v]!==void 0?s.pfValue[v]:u)==="arc-radius")}};ur.findLoopPoints=function(r,e,t,a){var n=r._private.rscratch,i=e.dirCounts,s=e.srcPos,o=r.pstyle("control-point-distances"),l=o?o.pfValue[0]:void 0,u=r.pstyle("loop-direction").pfValue,v=r.pstyle("loop-sweep").pfValue,f=r.pstyle("control-point-step-size").pfValue;n.edgeType="self";var c=t,h=f;a&&(c=0,h=l);var d=u-Math.PI/2,y=d-v/2,p=d+v/2,g=u+"_"+v;c=i[g]===void 0?i[g]=0:++i[g],n.ctrlpts=[s.x+Math.cos(y)*1.4*h*(c/3+1),s.y+Math.sin(y)*1.4*h*(c/3+1),s.x+Math.cos(p)*1.4*h*(c/3+1),s.y+Math.sin(p)*1.4*h*(c/3+1)]};ur.findCompoundLoopPoints=function(r,e,t,a){var n=r._private.rscratch;n.edgeType="compound";var i=e.srcPos,s=e.tgtPos,o=e.srcW,l=e.srcH,u=e.tgtW,v=e.tgtH,f=r.pstyle("control-point-step-size").pfValue,c=r.pstyle("control-point-distances"),h=c?c.pfValue[0]:void 0,d=t,y=f;a&&(d=0,y=h);var p=50,g={x:i.x-o/2,y:i.y-l/2},m={x:s.x-u/2,y:s.y-v/2},b={x:Math.min(g.x,m.x),y:Math.min(g.y,m.y)},w=.5,S=Math.max(w,Math.log(o*.01)),E=Math.max(w,Math.log(u*.01));n.ctrlpts=[b.x,b.y-(1+Math.pow(p,1.12)/100)*y*(d/3+1)*S,b.x-(1+Math.pow(p,1.12)/100)*y*(d/3+1)*E,b.y]};ur.findStraightEdgePoints=function(r){r._private.rscratch.edgeType="straight"};ur.findBezierPoints=function(r,e,t,a,n){var i=r._private.rscratch,s=r.pstyle("control-point-step-size").pfValue,o=r.pstyle("control-point-distances"),l=r.pstyle("control-point-weights"),u=o&&l?Math.min(o.value.length,l.value.length):1,v=o?o.pfValue[0]:void 0,f=l.value[0],c=a;i.edgeType=c?"multibezier":"bezier",i.ctrlpts=[];for(var h=0;h<u;h++){var d=(.5-e.eles.length/2+t)*s*(n?-1:1),y=void 0,p=go(d);c&&(v=o?o.pfValue[h]:s,f=l.value[h]),a?y=v:y=v!==void 0?p*v:void 0;var g=y!==void 0?y:d,m=1-f,b=f,w=this.findMidptPtsEtc(r,e),S=w.midptPts,E=w.vectorNormInverse,x={x:S.x1*m+S.x2*b,y:S.y1*m+S.y2*b};i.ctrlpts.push(x.x+E.x*g,x.y+E.y*g)}};ur.findTaxiPoints=function(r,e){var t=r._private.rscratch;t.edgeType="segments";var a="vertical",n="horizontal",i="leftward",s="rightward",o="downward",l="upward",u="auto",v=e.posPts,f=e.srcW,c=e.srcH,h=e.tgtW,d=e.tgtH,y=r.pstyle("edge-distances").value,p=y!=="node-position",g=r.pstyle("taxi-direction").value,m=g,b=r.pstyle("taxi-turn"),w=b.units==="%",S=b.pfValue,E=S<0,x=r.pstyle("taxi-turn-min-distance").pfValue,D=p?(f+h)/2:0,C=p?(c+d)/2:0,M=v.x2-v.x1,P=v.y2-v.y1,B=function(Z,se){return Z>0?Math.max(Z-se,0):Math.min(Z+se,0)},L=B(M,D),k=B(P,C),O=!1;m===u?g=Math.abs(L)>Math.abs(k)?n:a:m===l||m===o?(g=a,O=!0):(m===i||m===s)&&(g=n,O=!0);var A=g===a,R=A?k:L,I=A?P:M,V=go(I),G=!1;!(O&&(w||E))&&(m===o&&I<0||m===l&&I>0||m===i&&I>0||m===s&&I<0)&&(V*=-1,R=V*Math.abs(R),G=!0);var F;if(w){var q=S<0?1+S:S;F=q*R}else{var Y=S<0?R:0;F=Y+S*V}var Q=function(Z){return Math.abs(Z)<x||Math.abs(Z)>=Math.abs(R)},J=Q(F),_=Q(Math.abs(R)-Math.abs(F)),j=J||_;if(j&&!G)if(A){var W=Math.abs(I)<=c/2,z=Math.abs(M)<=h/2;if(W){var K=(v.x1+v.x2)/2,X=v.y1,ae=v.y2;t.segpts=[K,X,K,ae]}else if(z){var he=(v.y1+v.y2)/2,te=v.x1,re=v.x2;t.segpts=[te,he,re,he]}else t.segpts=[v.x1,v.y2]}else{var ve=Math.abs(I)<=f/2,le=Math.abs(P)<=d/2;if(ve){var oe=(v.y1+v.y2)/2,de=v.x1,Le=v.x2;t.segpts=[de,oe,Le,oe]}else if(le){var Ce=(v.x1+v.x2)/2,xe=v.y1,Ae=v.y2;t.segpts=[Ce,xe,Ce,Ae]}else t.segpts=[v.x2,v.y1]}else if(A){var Ee=v.y1+F+(p?c/2*V:0),Pe=v.x1,ee=v.x2;t.segpts=[Pe,Ee,ee,Ee]}else{var T=v.x1+F+(p?f/2*V:0),$=v.y1,U=v.y2;t.segpts=[T,$,T,U]}if(t.isRound){var N=r.pstyle("taxi-radius").value,H=r.pstyle("radius-type").value[0]==="arc-radius";t.radii=new Array(t.segpts.length/2).fill(N),t.isArcRadius=new Array(t.segpts.length/2).fill(H)}};ur.tryToCorrectInvalidPoints=function(r,e){var t=r._private.rscratch;if(t.edgeType==="bezier"){var a=e.srcPos,n=e.tgtPos,i=e.srcW,s=e.srcH,o=e.tgtW,l=e.tgtH,u=e.srcShape,v=e.tgtShape,f=e.srcCornerRadius,c=e.tgtCornerRadius,h=e.srcRs,d=e.tgtRs,y=!ne(t.startX)||!ne(t.startY),p=!ne(t.arrowStartX)||!ne(t.arrowStartY),g=!ne(t.endX)||!ne(t.endY),m=!ne(t.arrowEndX)||!ne(t.arrowEndY),b=3,w=this.getArrowWidth(r.pstyle("width").pfValue,r.pstyle("arrow-scale").value)*this.arrowShapeWidth,S=b*w,E=ht({x:t.ctrlpts[0],y:t.ctrlpts[1]},{x:t.startX,y:t.startY}),x=E<S,D=ht({x:t.ctrlpts[0],y:t.ctrlpts[1]},{x:t.endX,y:t.endY}),C=D<S,M=!1;if(y||p||x){M=!0;var P={x:t.ctrlpts[0]-a.x,y:t.ctrlpts[1]-a.y},B=Math.sqrt(P.x*P.x+P.y*P.y),L={x:P.x/B,y:P.y/B},k=Math.max(i,s),O={x:t.ctrlpts[0]+L.x*2*k,y:t.ctrlpts[1]+L.y*2*k},A=u.intersectLine(a.x,a.y,i,s,O.x,O.y,0,f,h);x?(t.ctrlpts[0]=t.ctrlpts[0]+L.x*(S-E),t.ctrlpts[1]=t.ctrlpts[1]+L.y*(S-E)):(t.ctrlpts[0]=A[0]+L.x*S,t.ctrlpts[1]=A[1]+L.y*S)}if(g||m||C){M=!0;var R={x:t.ctrlpts[0]-n.x,y:t.ctrlpts[1]-n.y},I=Math.sqrt(R.x*R.x+R.y*R.y),V={x:R.x/I,y:R.y/I},G=Math.max(i,s),F={x:t.ctrlpts[0]+V.x*2*G,y:t.ctrlpts[1]+V.y*2*G},q=v.intersectLine(n.x,n.y,o,l,F.x,F.y,0,c,d);C?(t.ctrlpts[0]=t.ctrlpts[0]+V.x*(S-D),t.ctrlpts[1]=t.ctrlpts[1]+V.y*(S-D)):(t.ctrlpts[0]=q[0]+V.x*S,t.ctrlpts[1]=q[1]+V.y*S)}M&&this.findEndpoints(r)}};ur.storeAllpts=function(r){var e=r._private.rscratch;if(e.edgeType==="multibezier"||e.edgeType==="bezier"||e.edgeType==="self"||e.edgeType==="compound"){e.allpts=[],e.allpts.push(e.startX,e.startY);for(var t=0;t+1<e.ctrlpts.length;t+=2)e.allpts.push(e.ctrlpts[t],e.ctrlpts[t+1]),t+3<e.ctrlpts.length&&e.allpts.push((e.ctrlpts[t]+e.ctrlpts[t+2])/2,(e.ctrlpts[t+1]+e.ctrlpts[t+3])/2);e.allpts.push(e.endX,e.endY);var a,n;e.ctrlpts.length/2%2===0?(a=e.allpts.length/2-1,e.midX=e.allpts[a],e.midY=e.allpts[a+1]):(a=e.allpts.length/2-3,n=.5,e.midX=Je(e.allpts[a],e.allpts[a+2],e.allpts[a+4],n),e.midY=Je(e.allpts[a+1],e.allpts[a+3],e.allpts[a+5],n))}else if(e.edgeType==="straight")e.allpts=[e.startX,e.startY,e.endX,e.endY],e.midX=(e.startX+e.endX+e.arrowStartX+e.arrowEndX)/4,e.midY=(e.startY+e.endY+e.arrowStartY+e.arrowEndY)/4;else if(e.edgeType==="segments"){if(e.allpts=[],e.allpts.push(e.startX,e.startY),e.allpts.push.apply(e.allpts,e.segpts),e.allpts.push(e.endX,e.endY),e.isRound){e.roundCorners=[];for(var i=2;i+3<e.allpts.length;i+=2){var s=e.radii[i/2-1],o=e.isArcRadius[i/2-1];e.roundCorners.push(Di({x:e.allpts[i-2],y:e.allpts[i-1]},{x:e.allpts[i],y:e.allpts[i+1],radius:s},{x:e.allpts[i+2],y:e.allpts[i+3]},s,o))}}if(e.segpts.length%4===0){var l=e.segpts.length/2,u=l-2;e.midX=(e.segpts[u]+e.segpts[l])/2,e.midY=(e.segpts[u+1]+e.segpts[l+1])/2}else{var v=e.segpts.length/2-1;if(!e.isRound)e.midX=e.segpts[v],e.midY=e.segpts[v+1];else{var f={x:e.segpts[v],y:e.segpts[v+1]},c=e.roundCorners[v/2],h=[f.x-c.cx,f.y-c.cy],d=c.radius/Math.sqrt(Math.pow(h[0],2)+Math.pow(h[1],2));h=h.map(function(y){return y*d}),e.midX=c.cx+h[0],e.midY=c.cy+h[1],e.midVector=h}}}};ur.checkForInvalidEdgeWarning=function(r){var e=r[0]._private.rscratch;e.nodesOverlap||ne(e.startX)&&ne(e.startY)&&ne(e.endX)&&ne(e.endY)?e.loggedErr=!1:e.loggedErr||(e.loggedErr=!0,Me("Edge `"+r.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))};ur.findEdgeControlPoints=function(r){var e=this;if(!(!r||r.length===0)){for(var t=this,a=t.cy,n=a.hasCompoundNodes(),i={map:new Ir,get:function(x){var D=this.map.get(x[0]);return D!=null?D.get(x[1]):null},set:function(x,D){var C=this.map.get(x[0]);C==null&&(C=new Ir,this.map.set(x[0],C)),C.set(x[1],D)}},s=[],o=[],l=0;l<r.length;l++){var u=r[l],v=u._private,f=u.pstyle("curve-style").value;if(!(u.removed()||!u.takesUpSpace())){if(f==="haystack"){o.push(u);continue}var c=f==="unbundled-bezier"||f.endsWith("segments")||f==="straight"||f==="straight-triangle"||f.endsWith("taxi"),h=f==="unbundled-bezier"||f==="bezier",d=v.source,y=v.target,p=d.poolIndex(),g=y.poolIndex(),m=[p,g].sort(),b=i.get(m);b==null&&(b={eles:[]},i.set(m,b),s.push(m)),b.eles.push(u),c&&(b.hasUnbundled=!0),h&&(b.hasBezier=!0)}}for(var w=function(x){var D=s[x],C=i.get(D),M=void 0;if(!C.hasUnbundled){var P=C.eles[0].parallelEdges().filter(function(T){return T.isBundledBezier()});fi(C.eles),P.forEach(function(T){return C.eles.push(T)}),C.eles.sort(function(T,$){return T.poolIndex()-$.poolIndex()})}var B=C.eles[0],L=B.source(),k=B.target();if(L.poolIndex()>k.poolIndex()){var O=L;L=k,k=O}var A=C.srcPos=L.position(),R=C.tgtPos=k.position(),I=C.srcW=L.outerWidth(),V=C.srcH=L.outerHeight(),G=C.tgtW=k.outerWidth(),F=C.tgtH=k.outerHeight(),q=C.srcShape=t.nodeShapes[e.getNodeShape(L)],Y=C.tgtShape=t.nodeShapes[e.getNodeShape(k)],Q=C.srcCornerRadius=L.pstyle("corner-radius").value==="auto"?"auto":L.pstyle("corner-radius").pfValue,J=C.tgtCornerRadius=k.pstyle("corner-radius").value==="auto"?"auto":k.pstyle("corner-radius").pfValue,_=C.tgtRs=k._private.rscratch,j=C.srcRs=L._private.rscratch;C.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var W=0;W<C.eles.length;W++){var z=C.eles[W],K=z[0]._private.rscratch,X=z.pstyle("curve-style").value,ae=X==="unbundled-bezier"||X.endsWith("segments")||X.endsWith("taxi"),he=!L.same(z.source());if(!C.calculatedIntersection&&L!==k&&(C.hasBezier||C.hasUnbundled)){C.calculatedIntersection=!0;var te=q.intersectLine(A.x,A.y,I,V,R.x,R.y,0,Q,j),re=C.srcIntn=te,ve=Y.intersectLine(R.x,R.y,G,F,A.x,A.y,0,J,_),le=C.tgtIntn=ve,oe=C.intersectionPts={x1:te[0],x2:ve[0],y1:te[1],y2:ve[1]},de=C.posPts={x1:A.x,x2:R.x,y1:A.y,y2:R.y},Le=ve[1]-te[1],Ce=ve[0]-te[0],xe=Math.sqrt(Ce*Ce+Le*Le),Ae=C.vector={x:Ce,y:Le},Ee=C.vectorNorm={x:Ae.x/xe,y:Ae.y/xe},Pe={x:-Ee.y,y:Ee.x};C.nodesOverlap=!ne(xe)||Y.checkPoint(te[0],te[1],0,G,F,R.x,R.y,J,_)||q.checkPoint(ve[0],ve[1],0,I,V,A.x,A.y,Q,j),C.vectorNormInverse=Pe,M={nodesOverlap:C.nodesOverlap,dirCounts:C.dirCounts,calculatedIntersection:!0,hasBezier:C.hasBezier,hasUnbundled:C.hasUnbundled,eles:C.eles,srcPos:R,srcRs:_,tgtPos:A,tgtRs:j,srcW:G,srcH:F,tgtW:I,tgtH:V,srcIntn:le,tgtIntn:re,srcShape:Y,tgtShape:q,posPts:{x1:de.x2,y1:de.y2,x2:de.x1,y2:de.y1},intersectionPts:{x1:oe.x2,y1:oe.y2,x2:oe.x1,y2:oe.y1},vector:{x:-Ae.x,y:-Ae.y},vectorNorm:{x:-Ee.x,y:-Ee.y},vectorNormInverse:{x:-Pe.x,y:-Pe.y}}}var ee=he?M:C;K.nodesOverlap=ee.nodesOverlap,K.srcIntn=ee.srcIntn,K.tgtIntn=ee.tgtIntn,K.isRound=X.startsWith("round"),n&&(L.isParent()||L.isChild()||k.isParent()||k.isChild())&&(L.parents().anySame(k)||k.parents().anySame(L)||L.same(k)&&L.isParent())?e.findCompoundLoopPoints(z,ee,W,ae):L===k?e.findLoopPoints(z,ee,W,ae):X.endsWith("segments")?e.findSegmentsPoints(z,ee):X.endsWith("taxi")?e.findTaxiPoints(z,ee):X==="straight"||!ae&&C.eles.length%2===1&&W===Math.floor(C.eles.length/2)?e.findStraightEdgePoints(z):e.findBezierPoints(z,ee,W,ae,he),e.findEndpoints(z),e.tryToCorrectInvalidPoints(z,ee),e.checkForInvalidEdgeWarning(z),e.storeAllpts(z),e.storeEdgeProjections(z),e.calculateArrowAngles(z),e.recalculateEdgeLabelProjections(z),e.calculateLabelAngles(z)}},S=0;S<s.length;S++)w(S);this.findHaystackPoints(o)}};function cu(r){var e=[];if(r!=null){for(var t=0;t<r.length;t+=2){var a=r[t],n=r[t+1];e.push({x:a,y:n})}return e}}ur.getSegmentPoints=function(r){var e=r[0]._private.rscratch;this.recalculateRenderedStyle(r);var t=e.edgeType;if(t==="segments")return cu(e.segpts)};ur.getControlPoints=function(r){var e=r[0]._private.rscratch;this.recalculateRenderedStyle(r);var t=e.edgeType;if(t==="bezier"||t==="multibezier"||t==="self"||t==="compound")return cu(e.ctrlpts)};ur.getEdgeMidpoint=function(r){var e=r[0]._private.rscratch;return this.recalculateRenderedStyle(r),{x:e.midX,y:e.midY}};var Da={};Da.manualEndptToPx=function(r,e){var t=this,a=r.position(),n=r.outerWidth(),i=r.outerHeight(),s=r._private.rscratch;if(e.value.length===2){var o=[e.pfValue[0],e.pfValue[1]];return e.units[0]==="%"&&(o[0]=o[0]*n),e.units[1]==="%"&&(o[1]=o[1]*i),o[0]+=a.x,o[1]+=a.y,o}else{var l=e.pfValue[0];l=-Math.PI/2+l;var u=2*Math.max(n,i),v=[a.x+Math.cos(l)*u,a.y+Math.sin(l)*u];return t.nodeShapes[this.getNodeShape(r)].intersectLine(a.x,a.y,n,i,v[0],v[1],0,r.pstyle("corner-radius").value==="auto"?"auto":r.pstyle("corner-radius").pfValue,s)}};Da.findEndpoints=function(r){var e=this,t,a=r.source()[0],n=r.target()[0],i=a.position(),s=n.position(),o=r.pstyle("target-arrow-shape").value,l=r.pstyle("source-arrow-shape").value,u=r.pstyle("target-distance-from-node").pfValue,v=r.pstyle("source-distance-from-node").pfValue,f=a._private.rscratch,c=n._private.rscratch,h=r.pstyle("curve-style").value,d=r._private.rscratch,y=d.edgeType,p=h==="taxi",g=y==="self"||y==="compound",m=y==="bezier"||y==="multibezier"||g,b=y!=="bezier",w=y==="straight"||y==="segments",S=y==="segments",E=m||b||w,x=g||p,D=r.pstyle("source-endpoint"),C=x?"outside-to-node":D.value,M=a.pstyle("corner-radius").value==="auto"?"auto":a.pstyle("corner-radius").pfValue,P=r.pstyle("target-endpoint"),B=x?"outside-to-node":P.value,L=n.pstyle("corner-radius").value==="auto"?"auto":n.pstyle("corner-radius").pfValue;d.srcManEndpt=D,d.tgtManEndpt=P;var k,O,A,R;if(m){var I=[d.ctrlpts[0],d.ctrlpts[1]],V=b?[d.ctrlpts[d.ctrlpts.length-2],d.ctrlpts[d.ctrlpts.length-1]]:I;k=V,O=I}else if(w){var G=S?d.segpts.slice(0,2):[s.x,s.y],F=S?d.segpts.slice(d.segpts.length-2):[i.x,i.y];k=F,O=G}if(B==="inside-to-node")t=[s.x,s.y];else if(P.units)t=this.manualEndptToPx(n,P);else if(B==="outside-to-line")t=d.tgtIntn;else if(B==="outside-to-node"||B==="outside-to-node-or-label"?A=k:(B==="outside-to-line"||B==="outside-to-line-or-label")&&(A=[i.x,i.y]),t=e.nodeShapes[this.getNodeShape(n)].intersectLine(s.x,s.y,n.outerWidth(),n.outerHeight(),A[0],A[1],0,L,c),B==="outside-to-node-or-label"||B==="outside-to-line-or-label"){var q=n._private.rscratch,Y=q.labelWidth,Q=q.labelHeight,J=q.labelX,_=q.labelY,j=Y/2,W=Q/2,z=n.pstyle("text-valign").value;z==="top"?_-=W:z==="bottom"&&(_+=W);var K=n.pstyle("text-halign").value;K==="left"?J-=j:K==="right"&&(J+=j);var X=ha(A[0],A[1],[J-j,_-W,J+j,_-W,J+j,_+W,J-j,_+W],s.x,s.y);if(X.length>0){var ae=i,he=ot(ae,Pt(t)),te=ot(ae,Pt(X)),re=he;if(te<he&&(t=X,re=te),X.length>2){var ve=ot(ae,{x:X[2],y:X[3]});ve<re&&(t=[X[2],X[3]])}}}var le=Ra(t,k,e.arrowShapes[o].spacing(r)+u),oe=Ra(t,k,e.arrowShapes[o].gap(r)+u);if(d.endX=oe[0],d.endY=oe[1],d.arrowEndX=le[0],d.arrowEndY=le[1],C==="inside-to-node")t=[i.x,i.y];else if(D.units)t=this.manualEndptToPx(a,D);else if(C==="outside-to-line")t=d.srcIntn;else if(C==="outside-to-node"||C==="outside-to-node-or-label"?R=O:(C==="outside-to-line"||C==="outside-to-line-or-label")&&(R=[s.x,s.y]),t=e.nodeShapes[this.getNodeShape(a)].intersectLine(i.x,i.y,a.outerWidth(),a.outerHeight(),R[0],R[1],0,M,f),C==="outside-to-node-or-label"||C==="outside-to-line-or-label"){var de=a._private.rscratch,Le=de.labelWidth,Ce=de.labelHeight,xe=de.labelX,Ae=de.labelY,Ee=Le/2,Pe=Ce/2,ee=a.pstyle("text-valign").value;ee==="top"?Ae-=Pe:ee==="bottom"&&(Ae+=Pe);var T=a.pstyle("text-halign").value;T==="left"?xe-=Ee:T==="right"&&(xe+=Ee);var $=ha(R[0],R[1],[xe-Ee,Ae-Pe,xe+Ee,Ae-Pe,xe+Ee,Ae+Pe,xe-Ee,Ae+Pe],i.x,i.y);if($.length>0){var U=s,N=ot(U,Pt(t)),H=ot(U,Pt($)),ie=N;if(H<N&&(t=[$[0],$[1]],ie=H),$.length>2){var Z=ot(U,{x:$[2],y:$[3]});Z<ie&&(t=[$[2],$[3]])}}}var se=Ra(t,O,e.arrowShapes[l].spacing(r)+v),ge=Ra(t,O,e.arrowShapes[l].gap(r)+v);d.startX=ge[0],d.startY=ge[1],d.arrowStartX=se[0],d.arrowStartY=se[1],E&&(!ne(d.startX)||!ne(d.startY)||!ne(d.endX)||!ne(d.endY)?d.badLine=!0:d.badLine=!1)};Da.getSourceEndpoint=function(r){var e=r[0]._private.rscratch;switch(this.recalculateRenderedStyle(r),e.edgeType){case"haystack":return{x:e.haystackPts[0],y:e.haystackPts[1]};default:return{x:e.arrowStartX,y:e.arrowStartY}}};Da.getTargetEndpoint=function(r){var e=r[0]._private.rscratch;switch(this.recalculateRenderedStyle(r),e.edgeType){case"haystack":return{x:e.haystackPts[2],y:e.haystackPts[3]};default:return{x:e.arrowEndX,y:e.arrowEndY}}};var ki={};function kg(r,e,t){for(var a=function(u,v,f,c){return Je(u,v,f,c)},n=e._private,i=n.rstyle.bezierPts,s=0;s<r.bezierProjPcts.length;s++){var o=r.bezierProjPcts[s];i.push({x:a(t[0],t[2],t[4],o),y:a(t[1],t[3],t[5],o)})}}ki.storeEdgeProjections=function(r){var e=r._private,t=e.rscratch,a=t.edgeType;if(e.rstyle.bezierPts=null,e.rstyle.linePts=null,e.rstyle.haystackPts=null,a==="multibezier"||a==="bezier"||a==="self"||a==="compound"){e.rstyle.bezierPts=[];for(var n=0;n+5<t.allpts.length;n+=4)kg(this,r,t.allpts.slice(n,n+6))}else if(a==="segments")for(var i=e.rstyle.linePts=[],n=0;n+1<t.allpts.length;n+=2)i.push({x:t.allpts[n],y:t.allpts[n+1]});else if(a==="haystack"){var s=t.haystackPts;e.rstyle.haystackPts=[{x:s[0],y:s[1]},{x:s[2],y:s[3]}]}e.rstyle.arrowWidth=this.getArrowWidth(r.pstyle("width").pfValue,r.pstyle("arrow-scale").value)*this.arrowShapeWidth};ki.recalculateEdgeProjections=function(r){this.findEdgeControlPoints(r)};var Nr={};Nr.recalculateNodeLabelProjection=function(r){var e=r.pstyle("label").strValue;if(!jr(e)){var t,a,n=r._private,i=r.width(),s=r.height(),o=r.padding(),l=r.position(),u=r.pstyle("text-halign").strValue,v=r.pstyle("text-valign").strValue,f=n.rscratch,c=n.rstyle;switch(u){case"left":t=l.x-i/2-o;break;case"right":t=l.x+i/2+o;break;default:t=l.x}switch(v){case"top":a=l.y-s/2-o;break;case"bottom":a=l.y+s/2+o;break;default:a=l.y}f.labelX=t,f.labelY=a,c.labelX=t,c.labelY=a,this.calculateLabelAngles(r),this.applyLabelDimensions(r)}};var du=function(e,t){var a=Math.atan(t/e);return e===0&&a<0&&(a=a*-1),a},hu=function(e,t){var a=t.x-e.x,n=t.y-e.y;return du(a,n)},Pg=function(e,t,a,n){var i=da(0,n-.001,1),s=da(0,n+.001,1),o=Mt(e,t,a,i),l=Mt(e,t,a,s);return hu(o,l)};Nr.recalculateEdgeLabelProjections=function(r){var e,t=r._private,a=t.rscratch,n=this,i={mid:r.pstyle("label").strValue,source:r.pstyle("source-label").strValue,target:r.pstyle("target-label").strValue};if(i.mid||i.source||i.target){e={x:a.midX,y:a.midY};var s=function(f,c,h){Zr(t.rscratch,f,c,h),Zr(t.rstyle,f,c,h)};s("labelX",null,e.x),s("labelY",null,e.y);var o=du(a.midDispX,a.midDispY);s("labelAutoAngle",null,o);var l=function v(){if(v.cache)return v.cache;for(var f=[],c=0;c+5<a.allpts.length;c+=4){var h={x:a.allpts[c],y:a.allpts[c+1]},d={x:a.allpts[c+2],y:a.allpts[c+3]},y={x:a.allpts[c+4],y:a.allpts[c+5]};f.push({p0:h,p1:d,p2:y,startDist:0,length:0,segments:[]})}var p=t.rstyle.bezierPts,g=n.bezierProjPcts.length;function m(x,D,C,M,P){var B=ht(D,C),L=x.segments[x.segments.length-1],k={p0:D,p1:C,t0:M,t1:P,startDist:L?L.startDist+L.length:0,length:B};x.segments.push(k),x.length+=B}for(var b=0;b<f.length;b++){var w=f[b],S=f[b-1];S&&(w.startDist=S.startDist+S.length),m(w,w.p0,p[b*g],0,n.bezierProjPcts[0]);for(var E=0;E<g-1;E++)m(w,p[b*g+E],p[b*g+E+1],n.bezierProjPcts[E],n.bezierProjPcts[E+1]);m(w,p[b*g+g-1],w.p2,n.bezierProjPcts[g-1],1)}return v.cache=f},u=function(f){var c,h=f==="source";if(i[f]){var d=r.pstyle(f+"-text-offset").pfValue;switch(a.edgeType){case"self":case"compound":case"bezier":case"multibezier":{for(var y=l(),p,g=0,m=0,b=0;b<y.length;b++){for(var w=y[h?b:y.length-1-b],S=0;S<w.segments.length;S++){var E=w.segments[h?S:w.segments.length-1-S],x=b===y.length-1&&S===w.segments.length-1;if(g=m,m+=E.length,m>=d||x){p={cp:w,segment:E};break}}if(p)break}var D=p.cp,C=p.segment,M=(d-g)/C.length,P=C.t1-C.t0,B=h?C.t0+P*M:C.t1-P*M;B=da(0,B,1),e=Mt(D.p0,D.p1,D.p2,B),c=Pg(D.p0,D.p1,D.p2,B);break}case"straight":case"segments":case"haystack":{for(var L=0,k,O,A,R,I=a.allpts.length,V=0;V+3<I&&(h?(A={x:a.allpts[V],y:a.allpts[V+1]},R={x:a.allpts[V+2],y:a.allpts[V+3]}):(A={x:a.allpts[I-2-V],y:a.allpts[I-1-V]},R={x:a.allpts[I-4-V],y:a.allpts[I-3-V]}),k=ht(A,R),O=L,L+=k,!(L>=d));V+=2);var G=d-O,F=G/k;F=da(0,F,1),e=Av(A,R,F),c=hu(A,R);break}}s("labelX",f,e.x),s("labelY",f,e.y),s("labelAutoAngle",f,c)}};u("source"),u("target"),this.applyLabelDimensions(r)}};Nr.applyLabelDimensions=function(r){this.applyPrefixedLabelDimensions(r),r.isEdge()&&(this.applyPrefixedLabelDimensions(r,"source"),this.applyPrefixedLabelDimensions(r,"target"))};Nr.applyPrefixedLabelDimensions=function(r,e){var t=r._private,a=this.getLabelText(r,e),n=this.calculateLabelDimensions(r,a),i=r.pstyle("line-height").pfValue,s=r.pstyle("text-wrap").strValue,o=kr(t.rscratch,"labelWrapCachedLines",e)||[],l=s!=="wrap"?1:Math.max(o.length,1),u=n.height/l,v=u*i,f=n.width,c=n.height+(l-1)*(i-1)*u;Zr(t.rstyle,"labelWidth",e,f),Zr(t.rscratch,"labelWidth",e,f),Zr(t.rstyle,"labelHeight",e,c),Zr(t.rscratch,"labelHeight",e,c),Zr(t.rscratch,"labelLineHeight",e,v)};Nr.getLabelText=function(r,e){var t=r._private,a=e?e+"-":"",n=r.pstyle(a+"label").strValue,i=r.pstyle("text-transform").value,s=function(Y,Q){return Q?(Zr(t.rscratch,Y,e,Q),Q):kr(t.rscratch,Y,e)};if(!n)return"";i=="none"||(i=="uppercase"?n=n.toUpperCase():i=="lowercase"&&(n=n.toLowerCase()));var o=r.pstyle("text-wrap").value;if(o==="wrap"){var l=s("labelKey");if(l!=null&&s("labelWrapKey")===l)return s("labelWrapCachedText");for(var u="​",v=n.split(`
`),f=r.pstyle("text-max-width").pfValue,c=r.pstyle("text-overflow-wrap").value,h=c==="anywhere",d=[],y=/[\s\u200b]+|$/g,p=0;p<v.length;p++){var g=v[p],m=this.calculateLabelDimensions(r,g),b=m.width;if(h){var w=g.split("").join(u);g=w}if(b>f){var S=g.matchAll(y),E="",x=0,D=Zs(S),C;try{for(D.s();!(C=D.n()).done;){var M=C.value,P=M[0],B=g.substring(x,M.index);x=M.index+P.length;var L=E.length===0?B:E+B+P,k=this.calculateLabelDimensions(r,L),O=k.width;O<=f?E+=B+P:(E&&d.push(E),E=B+P)}}catch(q){D.e(q)}finally{D.f()}E.match(/^[\s\u200b]+$/)||d.push(E)}else d.push(g)}s("labelWrapCachedLines",d),n=s("labelWrapCachedText",d.join(`
`)),s("labelWrapKey",l)}else if(o==="ellipsis"){var A=r.pstyle("text-max-width").pfValue,R="",I="…",V=!1;if(this.calculateLabelDimensions(r,n).width<A)return n;for(var G=0;G<n.length;G++){var F=this.calculateLabelDimensions(r,R+n[G]+I).width;if(F>A)break;R+=n[G],G===n.length-1&&(V=!0)}return V||(R+=I),R}return n};Nr.getLabelJustification=function(r){var e=r.pstyle("text-justification").strValue,t=r.pstyle("text-halign").strValue;if(e==="auto")if(r.isNode())switch(t){case"left":return"right";case"right":return"left";default:return"center"}else return"center";else return e};Nr.calculateLabelDimensions=function(r,e){var t=this,a=t.cy.window(),n=a.document,i=dt(e,r._private.labelDimsKey),s=t.labelDimCache||(t.labelDimCache=[]),o=s[i];if(o!=null)return o;var l=0,u=r.pstyle("font-style").strValue,v=r.pstyle("font-size").pfValue,f=r.pstyle("font-family").strValue,c=r.pstyle("font-weight").strValue,h=this.labelCalcCanvas,d=this.labelCalcCanvasContext;if(!h){h=this.labelCalcCanvas=n.createElement("canvas"),d=this.labelCalcCanvasContext=h.getContext("2d");var y=h.style;y.position="absolute",y.left="-9999px",y.top="-9999px",y.zIndex="-1",y.visibility="hidden",y.pointerEvents="none"}d.font="".concat(u," ").concat(c," ").concat(v,"px ").concat(f);for(var p=0,g=0,m=e.split(`
`),b=0;b<m.length;b++){var w=m[b],S=d.measureText(w),E=Math.ceil(S.width),x=v;p=Math.max(E,p),g+=x}return p+=l,g+=l,s[i]={width:p,height:g}};Nr.calculateLabelAngle=function(r,e){var t=r._private,a=t.rscratch,n=r.isEdge(),i=e?e+"-":"",s=r.pstyle(i+"text-rotation"),o=s.strValue;return o==="none"?0:n&&o==="autorotate"?a.labelAutoAngle:o==="autorotate"?0:s.pfValue};Nr.calculateLabelAngles=function(r){var e=this,t=r.isEdge(),a=r._private,n=a.rscratch;n.labelAngle=e.calculateLabelAngle(r),t&&(n.sourceLabelAngle=e.calculateLabelAngle(r,"source"),n.targetLabelAngle=e.calculateLabelAngle(r,"target"))};var gu={},Is=28,zs=!1;gu.getNodeShape=function(r){var e=this,t=r.pstyle("shape").value;if(t==="cutrectangle"&&(r.width()<Is||r.height()<Is))return zs||(Me("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),zs=!0),"rectangle";if(r.isParent())return t==="rectangle"||t==="roundrectangle"||t==="round-rectangle"||t==="cutrectangle"||t==="cut-rectangle"||t==="barrel"?t:"rectangle";if(t==="polygon"){var a=r.pstyle("shape-polygon-points").value;return e.nodeShapes.makePolygon(a).name}return t};var Dn={};Dn.registerCalculationListeners=function(){var r=this.cy,e=r.collection(),t=this,a=function(s){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(e.merge(s),o)for(var l=0;l<s.length;l++){var u=s[l],v=u._private,f=v.rstyle;f.clean=!1,f.cleanConnected=!1}};t.binder(r).on("bounds.* dirty.*",function(s){var o=s.target;a(o)}).on("style.* background.*",function(s){var o=s.target;a(o,!1)});var n=function(s){if(s){var o=t.onUpdateEleCalcsFns;e.cleanStyle();for(var l=0;l<e.length;l++){var u=e[l],v=u._private.rstyle;u.isNode()&&!v.cleanConnected&&(a(u.connectedEdges()),v.cleanConnected=!0)}if(o)for(var f=0;f<o.length;f++){var c=o[f];c(s,e)}t.recalculateRenderedStyle(e),e=r.collection()}};t.flushRenderedStyleQueue=function(){n(!0)},t.beforeRender(n,t.beforeRenderPriorities.eleCalcs)};Dn.onUpdateEleCalcs=function(r){var e=this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[];e.push(r)};Dn.recalculateRenderedStyle=function(r,e){var t=function(w){return w._private.rstyle.cleanConnected},a=[],n=[];if(!this.destroyed){e===void 0&&(e=!0);for(var i=0;i<r.length;i++){var s=r[i],o=s._private,l=o.rstyle;s.isEdge()&&(!t(s.source())||!t(s.target()))&&(l.clean=!1),!(e&&l.clean||s.removed())&&s.pstyle("display").value!=="none"&&(o.group==="nodes"?n.push(s):a.push(s),l.clean=!0)}for(var u=0;u<n.length;u++){var v=n[u],f=v._private,c=f.rstyle,h=v.position();this.recalculateNodeLabelProjection(v),c.nodeX=h.x,c.nodeY=h.y,c.nodeW=v.pstyle("width").pfValue,c.nodeH=v.pstyle("height").pfValue}this.recalculateEdgeProjections(a);for(var d=0;d<a.length;d++){var y=a[d],p=y._private,g=p.rstyle,m=p.rscratch;g.srcX=m.arrowStartX,g.srcY=m.arrowStartY,g.tgtX=m.arrowEndX,g.tgtY=m.arrowEndY,g.midX=m.midX,g.midY=m.midY,g.labelAngle=m.labelAngle,g.sourceLabelAngle=m.sourceLabelAngle,g.targetLabelAngle=m.targetLabelAngle}}};var kn={};kn.updateCachedGrabbedEles=function(){var r=this.cachedZSortedEles;if(r){r.drag=[],r.nondrag=[];for(var e=[],t=0;t<r.length;t++){var a=r[t],n=a._private.rscratch;a.grabbed()&&!a.isParent()?e.push(a):n.inDragLayer?r.drag.push(a):r.nondrag.push(a)}for(var t=0;t<e.length;t++){var a=e[t];r.drag.push(a)}}};kn.invalidateCachedZSortedEles=function(){this.cachedZSortedEles=null};kn.getCachedZSortedEles=function(r){if(r||!this.cachedZSortedEles){var e=this.cy.mutableElements().toArray();e.sort(_o),e.interactive=e.filter(function(t){return t.interactive()}),this.cachedZSortedEles=e,this.updateCachedGrabbedEles()}else e=this.cachedZSortedEles;return e};var pu={};[bt,un,ur,Da,ki,Nr,gu,Dn,kn].forEach(function(r){pe(pu,r)});var yu={};yu.getCachedImage=function(r,e,t){var a=this,n=a.imageCache=a.imageCache||{},i=n[r];if(i)return i.image.complete||i.image.addEventListener("load",t),i.image;i=n[r]=n[r]||{};var s=i.image=new Image;s.addEventListener("load",t),s.addEventListener("error",function(){s.error=!0});var o="data:",l=r.substring(0,o.length).toLowerCase()===o;return l||(e=e==="null"?null:e,s.crossOrigin=e),s.src=r,s};var Ut={};Ut.registerBinding=function(r,e,t,a){var n=Array.prototype.slice.apply(arguments,[1]);if(Array.isArray(r)){for(var i=[],s=0;s<r.length;s++){var o=r[s];if(o!==void 0){var l=this.binder(o);i.push(l.on.apply(l,n))}}return i}var l=this.binder(r);return l.on.apply(l,n)};Ut.binder=function(r){var e=this,t=e.cy.window(),a=r===t||r===t.document||r===t.document.body||_u(r);if(e.supportsPassiveEvents==null){var n=!1;try{var i=Object.defineProperty({},"passive",{get:function(){return n=!0,!0}});t.addEventListener("test",null,i)}catch{}e.supportsPassiveEvents=n}var s=function(l,u,v){var f=Array.prototype.slice.call(arguments);return a&&e.supportsPassiveEvents&&(f[2]={capture:v??!1,passive:!1,once:!1}),e.bindings.push({target:r,args:f}),(r.addEventListener||r.on).apply(r,f),this};return{on:s,addEventListener:s,addListener:s,bind:s}};Ut.nodeIsDraggable=function(r){return r&&r.isNode()&&!r.locked()&&r.grabbable()};Ut.nodeIsGrabbable=function(r){return this.nodeIsDraggable(r)&&r.interactive()};Ut.load=function(){var r=this,e=r.cy.window(),t=function(T){return T.selected()},a=function(T){var $=T.getRootNode();if($&&$.nodeType===11&&$.host!==void 0)return $},n=function(T,$,U,N){T==null&&(T=r.cy);for(var H=0;H<$.length;H++){var ie=$[H];T.emit({originalEvent:U,type:ie,position:N})}},i=function(T){return T.shiftKey||T.metaKey||T.ctrlKey},s=function(T,$){var U=!0;if(r.cy.hasCompoundNodes()&&T&&T.pannable())for(var N=0;$&&N<$.length;N++){var T=$[N];if(T.isNode()&&T.isParent()&&!T.pannable()){U=!1;break}}else U=!0;return U},o=function(T){T[0]._private.grabbed=!0},l=function(T){T[0]._private.grabbed=!1},u=function(T){T[0]._private.rscratch.inDragLayer=!0},v=function(T){T[0]._private.rscratch.inDragLayer=!1},f=function(T){T[0]._private.rscratch.isGrabTarget=!0},c=function(T){T[0]._private.rscratch.isGrabTarget=!1},h=function(T,$){var U=$.addToList,N=U.has(T);!N&&T.grabbable()&&!T.locked()&&(U.merge(T),o(T))},d=function(T,$){if(T.cy().hasCompoundNodes()&&!($.inDragLayer==null&&$.addToList==null)){var U=T.descendants();$.inDragLayer&&(U.forEach(u),U.connectedEdges().forEach(u)),$.addToList&&h(U,$)}},y=function(T,$){$=$||{};var U=T.cy().hasCompoundNodes();$.inDragLayer&&(T.forEach(u),T.neighborhood().stdFilter(function(N){return!U||N.isEdge()}).forEach(u)),$.addToList&&T.forEach(function(N){h(N,$)}),d(T,$),m(T,{inDragLayer:$.inDragLayer}),r.updateCachedGrabbedEles()},p=y,g=function(T){T&&(r.getCachedZSortedEles().forEach(function($){l($),v($),c($)}),r.updateCachedGrabbedEles())},m=function(T,$){if(!($.inDragLayer==null&&$.addToList==null)&&T.cy().hasCompoundNodes()){var U=T.ancestors().orphans();if(!U.same(T)){var N=U.descendants().spawnSelf().merge(U).unmerge(T).unmerge(T.descendants()),H=N.connectedEdges();$.inDragLayer&&(H.forEach(u),N.forEach(u)),$.addToList&&N.forEach(function(ie){h(ie,$)})}}},b=function(){document.activeElement!=null&&document.activeElement.blur!=null&&document.activeElement.blur()},w=typeof MutationObserver<"u",S=typeof ResizeObserver<"u";w?(r.removeObserver=new MutationObserver(function(ee){for(var T=0;T<ee.length;T++){var $=ee[T],U=$.removedNodes;if(U)for(var N=0;N<U.length;N++){var H=U[N];if(H===r.container){r.destroy();break}}}}),r.container.parentNode&&r.removeObserver.observe(r.container.parentNode,{childList:!0})):r.registerBinding(r.container,"DOMNodeRemoved",function(ee){r.destroy()});var E=cn(function(){r.cy.resize()},100);w&&(r.styleObserver=new MutationObserver(E),r.styleObserver.observe(r.container,{attributes:!0})),r.registerBinding(e,"resize",E),S&&(r.resizeObserver=new ResizeObserver(E),r.resizeObserver.observe(r.container));var x=function(T,$){for(;T!=null;)$(T),T=T.parentNode},D=function(){r.invalidateContainerClientCoordsCache()};x(r.container,function(ee){r.registerBinding(ee,"transitionend",D),r.registerBinding(ee,"animationend",D),r.registerBinding(ee,"scroll",D)}),r.registerBinding(r.container,"contextmenu",function(ee){ee.preventDefault()});var C=function(){return r.selection[4]!==0},M=function(T){for(var $=r.findContainerClientCoords(),U=$[0],N=$[1],H=$[2],ie=$[3],Z=T.touches?T.touches:[T],se=!1,ge=0;ge<Z.length;ge++){var we=Z[ge];if(U<=we.clientX&&we.clientX<=U+H&&N<=we.clientY&&we.clientY<=N+ie){se=!0;break}}if(!se)return!1;for(var fe=r.container,me=T.target,be=me.parentNode,ye=!1;be;){if(be===fe){ye=!0;break}be=be.parentNode}return!!ye};r.registerBinding(r.container,"mousedown",function(T){if(M(T)&&!(r.hoverData.which===1&&T.which!==1)){T.preventDefault(),b(),r.hoverData.capture=!0,r.hoverData.which=T.which;var $=r.cy,U=[T.clientX,T.clientY],N=r.projectIntoViewport(U[0],U[1]),H=r.selection,ie=r.findNearestElements(N[0],N[1],!0,!1),Z=ie[0],se=r.dragData.possibleDragElements;r.hoverData.mdownPos=N,r.hoverData.mdownGPos=U;var ge=function(){r.hoverData.tapholdCancelled=!1,clearTimeout(r.hoverData.tapholdTimeout),r.hoverData.tapholdTimeout=setTimeout(function(){if(!r.hoverData.tapholdCancelled){var Ie=r.hoverData.down;Ie?Ie.emit({originalEvent:T,type:"taphold",position:{x:N[0],y:N[1]}}):$.emit({originalEvent:T,type:"taphold",position:{x:N[0],y:N[1]}})}},r.tapholdDuration)};if(T.which==3){r.hoverData.cxtStarted=!0;var we={originalEvent:T,type:"cxttapstart",position:{x:N[0],y:N[1]}};Z?(Z.activate(),Z.emit(we),r.hoverData.down=Z):$.emit(we),r.hoverData.downTime=new Date().getTime(),r.hoverData.cxtDragged=!1}else if(T.which==1){Z&&Z.activate();{if(Z!=null&&r.nodeIsGrabbable(Z)){var fe=function(Ie){return{originalEvent:T,type:Ie,position:{x:N[0],y:N[1]}}},me=function(Ie){Ie.emit(fe("grab"))};if(f(Z),!Z.selected())se=r.dragData.possibleDragElements=$.collection(),p(Z,{addToList:se}),Z.emit(fe("grabon")).emit(fe("grab"));else{se=r.dragData.possibleDragElements=$.collection();var be=$.$(function(ye){return ye.isNode()&&ye.selected()&&r.nodeIsGrabbable(ye)});y(be,{addToList:se}),Z.emit(fe("grabon")),be.forEach(me)}r.redrawHint("eles",!0),r.redrawHint("drag",!0)}r.hoverData.down=Z,r.hoverData.downs=ie,r.hoverData.downTime=new Date().getTime()}n(Z,["mousedown","tapstart","vmousedown"],T,{x:N[0],y:N[1]}),Z==null?(H[4]=1,r.data.bgActivePosistion={x:N[0],y:N[1]},r.redrawHint("select",!0),r.redraw()):Z.pannable()&&(H[4]=1),ge()}H[0]=H[2]=N[0],H[1]=H[3]=N[1]}},!1);var P=a(r.container);r.registerBinding([e,P],"mousemove",function(T){var $=r.hoverData.capture;if(!(!$&&!M(T))){var U=!1,N=r.cy,H=N.zoom(),ie=[T.clientX,T.clientY],Z=r.projectIntoViewport(ie[0],ie[1]),se=r.hoverData.mdownPos,ge=r.hoverData.mdownGPos,we=r.selection,fe=null;!r.hoverData.draggingEles&&!r.hoverData.dragging&&!r.hoverData.selecting&&(fe=r.findNearestElement(Z[0],Z[1],!0,!1));var me=r.hoverData.last,be=r.hoverData.down,ye=[Z[0]-we[2],Z[1]-we[3]],Ie=r.dragData.possibleDragElements,Ve;if(ge){var gr=ie[0]-ge[0],pr=gr*gr,Ge=ie[1]-ge[1],Xe=Ge*Ge,Ze=pr+Xe;r.hoverData.isOverThresholdDrag=Ve=Ze>=r.desktopTapThreshold2}var lr=i(T);Ve&&(r.hoverData.tapholdCancelled=!0);var vr=function(){var Lr=r.hoverData.dragDelta=r.hoverData.dragDelta||[];Lr.length===0?(Lr.push(ye[0]),Lr.push(ye[1])):(Lr[0]+=ye[0],Lr[1]+=ye[1])};U=!0,n(fe,["mousemove","vmousemove","tapdrag"],T,{x:Z[0],y:Z[1]});var Br=function(){r.data.bgActivePosistion=void 0,r.hoverData.selecting||N.emit({originalEvent:T,type:"boxstart",position:{x:Z[0],y:Z[1]}}),we[4]=1,r.hoverData.selecting=!0,r.redrawHint("select",!0),r.redraw()};if(r.hoverData.which===3){if(Ve){var br={originalEvent:T,type:"cxtdrag",position:{x:Z[0],y:Z[1]}};be?be.emit(br):N.emit(br),r.hoverData.cxtDragged=!0,(!r.hoverData.cxtOver||fe!==r.hoverData.cxtOver)&&(r.hoverData.cxtOver&&r.hoverData.cxtOver.emit({originalEvent:T,type:"cxtdragout",position:{x:Z[0],y:Z[1]}}),r.hoverData.cxtOver=fe,fe&&fe.emit({originalEvent:T,type:"cxtdragover",position:{x:Z[0],y:Z[1]}}))}}else if(r.hoverData.dragging){if(U=!0,N.panningEnabled()&&N.userPanningEnabled()){var Mr;if(r.hoverData.justStartedPan){var Vr=r.hoverData.mdownPos;Mr={x:(Z[0]-Vr[0])*H,y:(Z[1]-Vr[1])*H},r.hoverData.justStartedPan=!1}else Mr={x:ye[0]*H,y:ye[1]*H};N.panBy(Mr),N.emit("dragpan"),r.hoverData.dragged=!0}Z=r.projectIntoViewport(T.clientX,T.clientY)}else if(we[4]==1&&(be==null||be.pannable())){if(Ve){if(!r.hoverData.dragging&&N.boxSelectionEnabled()&&(lr||!N.panningEnabled()||!N.userPanningEnabled()))Br();else if(!r.hoverData.selecting&&N.panningEnabled()&&N.userPanningEnabled()){var qr=s(be,r.hoverData.downs);qr&&(r.hoverData.dragging=!0,r.hoverData.justStartedPan=!0,we[4]=0,r.data.bgActivePosistion=Pt(se),r.redrawHint("select",!0),r.redraw())}be&&be.pannable()&&be.active()&&be.unactivate()}}else{if(be&&be.pannable()&&be.active()&&be.unactivate(),(!be||!be.grabbed())&&fe!=me&&(me&&n(me,["mouseout","tapdragout"],T,{x:Z[0],y:Z[1]}),fe&&n(fe,["mouseover","tapdragover"],T,{x:Z[0],y:Z[1]}),r.hoverData.last=fe),be)if(Ve){if(N.boxSelectionEnabled()&&lr)be&&be.grabbed()&&(g(Ie),be.emit("freeon"),Ie.emit("free"),r.dragData.didDrag&&(be.emit("dragfreeon"),Ie.emit("dragfree"))),Br();else if(be&&be.grabbed()&&r.nodeIsDraggable(be)){var ar=!r.dragData.didDrag;ar&&r.redrawHint("eles",!0),r.dragData.didDrag=!0,r.hoverData.draggingEles||y(Ie,{inDragLayer:!0});var Qe={x:0,y:0};if(ne(ye[0])&&ne(ye[1])&&(Qe.x+=ye[0],Qe.y+=ye[1],ar)){var cr=r.hoverData.dragDelta;cr&&ne(cr[0])&&ne(cr[1])&&(Qe.x+=cr[0],Qe.y+=cr[1])}r.hoverData.draggingEles=!0,Ie.silentShift(Qe).emit("position drag"),r.redrawHint("drag",!0),r.redraw()}}else vr();U=!0}if(we[2]=Z[0],we[3]=Z[1],U)return T.stopPropagation&&T.stopPropagation(),T.preventDefault&&T.preventDefault(),!1}},!1);var B,L,k;r.registerBinding(e,"mouseup",function(T){if(!(r.hoverData.which===1&&T.which!==1&&r.hoverData.capture)){var $=r.hoverData.capture;if($){r.hoverData.capture=!1;var U=r.cy,N=r.projectIntoViewport(T.clientX,T.clientY),H=r.selection,ie=r.findNearestElement(N[0],N[1],!0,!1),Z=r.dragData.possibleDragElements,se=r.hoverData.down,ge=i(T);if(r.data.bgActivePosistion&&(r.redrawHint("select",!0),r.redraw()),r.hoverData.tapholdCancelled=!0,r.data.bgActivePosistion=void 0,se&&se.unactivate(),r.hoverData.which===3){var we={originalEvent:T,type:"cxttapend",position:{x:N[0],y:N[1]}};if(se?se.emit(we):U.emit(we),!r.hoverData.cxtDragged){var fe={originalEvent:T,type:"cxttap",position:{x:N[0],y:N[1]}};se?se.emit(fe):U.emit(fe)}r.hoverData.cxtDragged=!1,r.hoverData.which=null}else if(r.hoverData.which===1){if(n(ie,["mouseup","tapend","vmouseup"],T,{x:N[0],y:N[1]}),!r.dragData.didDrag&&!r.hoverData.dragged&&!r.hoverData.selecting&&!r.hoverData.isOverThresholdDrag&&(n(se,["click","tap","vclick"],T,{x:N[0],y:N[1]}),L=!1,T.timeStamp-k<=U.multiClickDebounceTime()?(B&&clearTimeout(B),L=!0,k=null,n(se,["dblclick","dbltap","vdblclick"],T,{x:N[0],y:N[1]})):(B=setTimeout(function(){L||n(se,["oneclick","onetap","voneclick"],T,{x:N[0],y:N[1]})},U.multiClickDebounceTime()),k=T.timeStamp)),se==null&&!r.dragData.didDrag&&!r.hoverData.selecting&&!r.hoverData.dragged&&!i(T)&&(U.$(t).unselect(["tapunselect"]),Z.length>0&&r.redrawHint("eles",!0),r.dragData.possibleDragElements=Z=U.collection()),ie==se&&!r.dragData.didDrag&&!r.hoverData.selecting&&ie!=null&&ie._private.selectable&&(r.hoverData.dragging||(U.selectionType()==="additive"||ge?ie.selected()?ie.unselect(["tapunselect"]):ie.select(["tapselect"]):ge||(U.$(t).unmerge(ie).unselect(["tapunselect"]),ie.select(["tapselect"]))),r.redrawHint("eles",!0)),r.hoverData.selecting){var me=U.collection(r.getAllInBox(H[0],H[1],H[2],H[3]));r.redrawHint("select",!0),me.length>0&&r.redrawHint("eles",!0),U.emit({type:"boxend",originalEvent:T,position:{x:N[0],y:N[1]}});var be=function(Ve){return Ve.selectable()&&!Ve.selected()};U.selectionType()==="additive"||ge||U.$(t).unmerge(me).unselect(),me.emit("box").stdFilter(be).select().emit("boxselect"),r.redraw()}if(r.hoverData.dragging&&(r.hoverData.dragging=!1,r.redrawHint("select",!0),r.redrawHint("eles",!0),r.redraw()),!H[4]){r.redrawHint("drag",!0),r.redrawHint("eles",!0);var ye=se&&se.grabbed();g(Z),ye&&(se.emit("freeon"),Z.emit("free"),r.dragData.didDrag&&(se.emit("dragfreeon"),Z.emit("dragfree")))}}H[4]=0,r.hoverData.down=null,r.hoverData.cxtStarted=!1,r.hoverData.draggingEles=!1,r.hoverData.selecting=!1,r.hoverData.isOverThresholdDrag=!1,r.dragData.didDrag=!1,r.hoverData.dragged=!1,r.hoverData.dragDelta=[],r.hoverData.mdownPos=null,r.hoverData.mdownGPos=null,r.hoverData.which=null}}},!1);var O=function(T){if(!r.scrollingPage){var $=r.cy,U=$.zoom(),N=$.pan(),H=r.projectIntoViewport(T.clientX,T.clientY),ie=[H[0]*U+N.x,H[1]*U+N.y];if(r.hoverData.draggingEles||r.hoverData.dragging||r.hoverData.cxtStarted||C()){T.preventDefault();return}if($.panningEnabled()&&$.userPanningEnabled()&&$.zoomingEnabled()&&$.userZoomingEnabled()){T.preventDefault(),r.data.wheelZooming=!0,clearTimeout(r.data.wheelTimeout),r.data.wheelTimeout=setTimeout(function(){r.data.wheelZooming=!1,r.redrawHint("eles",!0),r.redraw()},150);var Z;T.deltaY!=null?Z=T.deltaY/-250:T.wheelDeltaY!=null?Z=T.wheelDeltaY/1e3:Z=T.wheelDelta/1e3,Z=Z*r.wheelSensitivity;var se=T.deltaMode===1;se&&(Z*=33);var ge=$.zoom()*Math.pow(10,Z);T.type==="gesturechange"&&(ge=r.gestureStartZoom*T.scale),$.zoom({level:ge,renderedPosition:{x:ie[0],y:ie[1]}}),$.emit(T.type==="gesturechange"?"pinchzoom":"scrollzoom")}}};r.registerBinding(r.container,"wheel",O,!0),r.registerBinding(e,"scroll",function(T){r.scrollingPage=!0,clearTimeout(r.scrollingPageTimeout),r.scrollingPageTimeout=setTimeout(function(){r.scrollingPage=!1},250)},!0),r.registerBinding(r.container,"gesturestart",function(T){r.gestureStartZoom=r.cy.zoom(),r.hasTouchStarted||T.preventDefault()},!0),r.registerBinding(r.container,"gesturechange",function(ee){r.hasTouchStarted||O(ee)},!0),r.registerBinding(r.container,"mouseout",function(T){var $=r.projectIntoViewport(T.clientX,T.clientY);r.cy.emit({originalEvent:T,type:"mouseout",position:{x:$[0],y:$[1]}})},!1),r.registerBinding(r.container,"mouseover",function(T){var $=r.projectIntoViewport(T.clientX,T.clientY);r.cy.emit({originalEvent:T,type:"mouseover",position:{x:$[0],y:$[1]}})},!1);var A,R,I,V,G,F,q,Y,Q,J,_,j,W,z=function(T,$,U,N){return Math.sqrt((U-T)*(U-T)+(N-$)*(N-$))},K=function(T,$,U,N){return(U-T)*(U-T)+(N-$)*(N-$)},X;r.registerBinding(r.container,"touchstart",X=function(T){if(r.hasTouchStarted=!0,!!M(T)){b(),r.touchData.capture=!0,r.data.bgActivePosistion=void 0;var $=r.cy,U=r.touchData.now,N=r.touchData.earlier;if(T.touches[0]){var H=r.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);U[0]=H[0],U[1]=H[1]}if(T.touches[1]){var H=r.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY);U[2]=H[0],U[3]=H[1]}if(T.touches[2]){var H=r.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY);U[4]=H[0],U[5]=H[1]}if(T.touches[1]){r.touchData.singleTouchMoved=!0,g(r.dragData.touchDragEles);var ie=r.findContainerClientCoords();Q=ie[0],J=ie[1],_=ie[2],j=ie[3],A=T.touches[0].clientX-Q,R=T.touches[0].clientY-J,I=T.touches[1].clientX-Q,V=T.touches[1].clientY-J,W=0<=A&&A<=_&&0<=I&&I<=_&&0<=R&&R<=j&&0<=V&&V<=j;var Z=$.pan(),se=$.zoom();G=z(A,R,I,V),F=K(A,R,I,V),q=[(A+I)/2,(R+V)/2],Y=[(q[0]-Z.x)/se,(q[1]-Z.y)/se];var ge=200,we=ge*ge;if(F<we&&!T.touches[2]){var fe=r.findNearestElement(U[0],U[1],!0,!0),me=r.findNearestElement(U[2],U[3],!0,!0);fe&&fe.isNode()?(fe.activate().emit({originalEvent:T,type:"cxttapstart",position:{x:U[0],y:U[1]}}),r.touchData.start=fe):me&&me.isNode()?(me.activate().emit({originalEvent:T,type:"cxttapstart",position:{x:U[0],y:U[1]}}),r.touchData.start=me):$.emit({originalEvent:T,type:"cxttapstart",position:{x:U[0],y:U[1]}}),r.touchData.start&&(r.touchData.start._private.grabbed=!1),r.touchData.cxt=!0,r.touchData.cxtDragged=!1,r.data.bgActivePosistion=void 0,r.redraw();return}}if(T.touches[2])$.boxSelectionEnabled()&&T.preventDefault();else if(!T.touches[1]){if(T.touches[0]){var be=r.findNearestElements(U[0],U[1],!0,!0),ye=be[0];if(ye!=null&&(ye.activate(),r.touchData.start=ye,r.touchData.starts=be,r.nodeIsGrabbable(ye))){var Ie=r.dragData.touchDragEles=$.collection(),Ve=null;r.redrawHint("eles",!0),r.redrawHint("drag",!0),ye.selected()?(Ve=$.$(function(Ze){return Ze.selected()&&r.nodeIsGrabbable(Ze)}),y(Ve,{addToList:Ie})):p(ye,{addToList:Ie}),f(ye);var gr=function(lr){return{originalEvent:T,type:lr,position:{x:U[0],y:U[1]}}};ye.emit(gr("grabon")),Ve?Ve.forEach(function(Ze){Ze.emit(gr("grab"))}):ye.emit(gr("grab"))}n(ye,["touchstart","tapstart","vmousedown"],T,{x:U[0],y:U[1]}),ye==null&&(r.data.bgActivePosistion={x:H[0],y:H[1]},r.redrawHint("select",!0),r.redraw()),r.touchData.singleTouchMoved=!1,r.touchData.singleTouchStartTime=+new Date,clearTimeout(r.touchData.tapholdTimeout),r.touchData.tapholdTimeout=setTimeout(function(){r.touchData.singleTouchMoved===!1&&!r.pinching&&!r.touchData.selecting&&n(r.touchData.start,["taphold"],T,{x:U[0],y:U[1]})},r.tapholdDuration)}}if(T.touches.length>=1){for(var pr=r.touchData.startPosition=[null,null,null,null,null,null],Ge=0;Ge<U.length;Ge++)pr[Ge]=N[Ge]=U[Ge];var Xe=T.touches[0];r.touchData.startGPosition=[Xe.clientX,Xe.clientY]}}},!1);var ae;r.registerBinding(e,"touchmove",ae=function(T){var $=r.touchData.capture;if(!(!$&&!M(T))){var U=r.selection,N=r.cy,H=r.touchData.now,ie=r.touchData.earlier,Z=N.zoom();if(T.touches[0]){var se=r.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);H[0]=se[0],H[1]=se[1]}if(T.touches[1]){var se=r.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY);H[2]=se[0],H[3]=se[1]}if(T.touches[2]){var se=r.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY);H[4]=se[0],H[5]=se[1]}var ge=r.touchData.startGPosition,we;if($&&T.touches[0]&&ge){for(var fe=[],me=0;me<H.length;me++)fe[me]=H[me]-ie[me];var be=T.touches[0].clientX-ge[0],ye=be*be,Ie=T.touches[0].clientY-ge[1],Ve=Ie*Ie,gr=ye+Ve;we=gr>=r.touchTapThreshold2}if($&&r.touchData.cxt){T.preventDefault();var pr=T.touches[0].clientX-Q,Ge=T.touches[0].clientY-J,Xe=T.touches[1].clientX-Q,Ze=T.touches[1].clientY-J,lr=K(pr,Ge,Xe,Ze),vr=lr/F,Br=150,br=Br*Br,Mr=1.5,Vr=Mr*Mr;if(vr>=Vr||lr>=br){r.touchData.cxt=!1,r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);var qr={originalEvent:T,type:"cxttapend",position:{x:H[0],y:H[1]}};r.touchData.start?(r.touchData.start.unactivate().emit(qr),r.touchData.start=null):N.emit(qr)}}if($&&r.touchData.cxt){var qr={originalEvent:T,type:"cxtdrag",position:{x:H[0],y:H[1]}};r.data.bgActivePosistion=void 0,r.redrawHint("select",!0),r.touchData.start?r.touchData.start.emit(qr):N.emit(qr),r.touchData.start&&(r.touchData.start._private.grabbed=!1),r.touchData.cxtDragged=!0;var ar=r.findNearestElement(H[0],H[1],!0,!0);(!r.touchData.cxtOver||ar!==r.touchData.cxtOver)&&(r.touchData.cxtOver&&r.touchData.cxtOver.emit({originalEvent:T,type:"cxtdragout",position:{x:H[0],y:H[1]}}),r.touchData.cxtOver=ar,ar&&ar.emit({originalEvent:T,type:"cxtdragover",position:{x:H[0],y:H[1]}}))}else if($&&T.touches[2]&&N.boxSelectionEnabled())T.preventDefault(),r.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,r.touchData.selecting||N.emit({originalEvent:T,type:"boxstart",position:{x:H[0],y:H[1]}}),r.touchData.selecting=!0,r.touchData.didSelect=!0,U[4]=1,!U||U.length===0||U[0]===void 0?(U[0]=(H[0]+H[2]+H[4])/3,U[1]=(H[1]+H[3]+H[5])/3,U[2]=(H[0]+H[2]+H[4])/3+1,U[3]=(H[1]+H[3]+H[5])/3+1):(U[2]=(H[0]+H[2]+H[4])/3,U[3]=(H[1]+H[3]+H[5])/3),r.redrawHint("select",!0),r.redraw();else if($&&T.touches[1]&&!r.touchData.didSelect&&N.zoomingEnabled()&&N.panningEnabled()&&N.userZoomingEnabled()&&N.userPanningEnabled()){T.preventDefault(),r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);var Qe=r.dragData.touchDragEles;if(Qe){r.redrawHint("drag",!0);for(var cr=0;cr<Qe.length;cr++){var Ba=Qe[cr]._private;Ba.grabbed=!1,Ba.rscratch.inDragLayer=!1}}var Lr=r.touchData.start,pr=T.touches[0].clientX-Q,Ge=T.touches[0].clientY-J,Xe=T.touches[1].clientX-Q,Ze=T.touches[1].clientY-J,Bi=z(pr,Ge,Xe,Ze),Ru=Bi/G;if(W){var Ou=pr-A,Iu=Ge-R,zu=Xe-I,Nu=Ze-V,Fu=(Ou+zu)/2,Vu=(Iu+Nu)/2,Zt=N.zoom(),Pn=Zt*Ru,Ma=N.pan(),Mi=Y[0]*Zt+Ma.x,Li=Y[1]*Zt+Ma.y,qu={x:-Pn/Zt*(Mi-Ma.x-Fu)+Mi,y:-Pn/Zt*(Li-Ma.y-Vu)+Li};if(Lr&&Lr.active()){var Qe=r.dragData.touchDragEles;g(Qe),r.redrawHint("drag",!0),r.redrawHint("eles",!0),Lr.unactivate().emit("freeon"),Qe.emit("free"),r.dragData.didDrag&&(Lr.emit("dragfreeon"),Qe.emit("dragfree"))}N.viewport({zoom:Pn,pan:qu,cancelOnFailedZoom:!0}),N.emit("pinchzoom"),G=Bi,A=pr,R=Ge,I=Xe,V=Ze,r.pinching=!0}if(T.touches[0]){var se=r.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);H[0]=se[0],H[1]=se[1]}if(T.touches[1]){var se=r.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY);H[2]=se[0],H[3]=se[1]}if(T.touches[2]){var se=r.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY);H[4]=se[0],H[5]=se[1]}}else if(T.touches[0]&&!r.touchData.didSelect){var Cr=r.touchData.start,Bn=r.touchData.last,ar;if(!r.hoverData.draggingEles&&!r.swipePanning&&(ar=r.findNearestElement(H[0],H[1],!0,!0)),$&&Cr!=null&&T.preventDefault(),$&&Cr!=null&&r.nodeIsDraggable(Cr))if(we){var Qe=r.dragData.touchDragEles,Ai=!r.dragData.didDrag;Ai&&y(Qe,{inDragLayer:!0}),r.dragData.didDrag=!0;var Qt={x:0,y:0};if(ne(fe[0])&&ne(fe[1])&&(Qt.x+=fe[0],Qt.y+=fe[1],Ai)){r.redrawHint("eles",!0);var Sr=r.touchData.dragDelta;Sr&&ne(Sr[0])&&ne(Sr[1])&&(Qt.x+=Sr[0],Qt.y+=Sr[1])}r.hoverData.draggingEles=!0,Qe.silentShift(Qt).emit("position drag"),r.redrawHint("drag",!0),r.touchData.startPosition[0]==ie[0]&&r.touchData.startPosition[1]==ie[1]&&r.redrawHint("eles",!0),r.redraw()}else{var Sr=r.touchData.dragDelta=r.touchData.dragDelta||[];Sr.length===0?(Sr.push(fe[0]),Sr.push(fe[1])):(Sr[0]+=fe[0],Sr[1]+=fe[1])}if(n(Cr||ar,["touchmove","tapdrag","vmousemove"],T,{x:H[0],y:H[1]}),(!Cr||!Cr.grabbed())&&ar!=Bn&&(Bn&&Bn.emit({originalEvent:T,type:"tapdragout",position:{x:H[0],y:H[1]}}),ar&&ar.emit({originalEvent:T,type:"tapdragover",position:{x:H[0],y:H[1]}})),r.touchData.last=ar,$)for(var cr=0;cr<H.length;cr++)H[cr]&&r.touchData.startPosition[cr]&&we&&(r.touchData.singleTouchMoved=!0);if($&&(Cr==null||Cr.pannable())&&N.panningEnabled()&&N.userPanningEnabled()){var $u=s(Cr,r.touchData.starts);$u&&(T.preventDefault(),r.data.bgActivePosistion||(r.data.bgActivePosistion=Pt(r.touchData.startPosition)),r.swipePanning?(N.panBy({x:fe[0]*Z,y:fe[1]*Z}),N.emit("dragpan")):we&&(r.swipePanning=!0,N.panBy({x:be*Z,y:Ie*Z}),N.emit("dragpan"),Cr&&(Cr.unactivate(),r.redrawHint("select",!0),r.touchData.start=null)));var se=r.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);H[0]=se[0],H[1]=se[1]}}for(var me=0;me<H.length;me++)ie[me]=H[me];$&&T.touches.length>0&&!r.hoverData.draggingEles&&!r.swipePanning&&r.data.bgActivePosistion!=null&&(r.data.bgActivePosistion=void 0,r.redrawHint("select",!0),r.redraw())}},!1);var he;r.registerBinding(e,"touchcancel",he=function(T){var $=r.touchData.start;r.touchData.capture=!1,$&&$.unactivate()});var te,re,ve,le;if(r.registerBinding(e,"touchend",te=function(T){var $=r.touchData.start,U=r.touchData.capture;if(U)T.touches.length===0&&(r.touchData.capture=!1),T.preventDefault();else return;var N=r.selection;r.swipePanning=!1,r.hoverData.draggingEles=!1;var H=r.cy,ie=H.zoom(),Z=r.touchData.now,se=r.touchData.earlier;if(T.touches[0]){var ge=r.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);Z[0]=ge[0],Z[1]=ge[1]}if(T.touches[1]){var ge=r.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY);Z[2]=ge[0],Z[3]=ge[1]}if(T.touches[2]){var ge=r.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY);Z[4]=ge[0],Z[5]=ge[1]}$&&$.unactivate();var we;if(r.touchData.cxt){if(we={originalEvent:T,type:"cxttapend",position:{x:Z[0],y:Z[1]}},$?$.emit(we):H.emit(we),!r.touchData.cxtDragged){var fe={originalEvent:T,type:"cxttap",position:{x:Z[0],y:Z[1]}};$?$.emit(fe):H.emit(fe)}r.touchData.start&&(r.touchData.start._private.grabbed=!1),r.touchData.cxt=!1,r.touchData.start=null,r.redraw();return}if(!T.touches[2]&&H.boxSelectionEnabled()&&r.touchData.selecting){r.touchData.selecting=!1;var me=H.collection(r.getAllInBox(N[0],N[1],N[2],N[3]));N[0]=void 0,N[1]=void 0,N[2]=void 0,N[3]=void 0,N[4]=0,r.redrawHint("select",!0),H.emit({type:"boxend",originalEvent:T,position:{x:Z[0],y:Z[1]}});var be=function(br){return br.selectable()&&!br.selected()};me.emit("box").stdFilter(be).select().emit("boxselect"),me.nonempty()&&r.redrawHint("eles",!0),r.redraw()}if($!=null&&$.unactivate(),T.touches[2])r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);else if(!T.touches[1]){if(!T.touches[0]){if(!T.touches[0]){r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);var ye=r.dragData.touchDragEles;if($!=null){var Ie=$._private.grabbed;g(ye),r.redrawHint("drag",!0),r.redrawHint("eles",!0),Ie&&($.emit("freeon"),ye.emit("free"),r.dragData.didDrag&&($.emit("dragfreeon"),ye.emit("dragfree"))),n($,["touchend","tapend","vmouseup","tapdragout"],T,{x:Z[0],y:Z[1]}),$.unactivate(),r.touchData.start=null}else{var Ve=r.findNearestElement(Z[0],Z[1],!0,!0);n(Ve,["touchend","tapend","vmouseup","tapdragout"],T,{x:Z[0],y:Z[1]})}var gr=r.touchData.startPosition[0]-Z[0],pr=gr*gr,Ge=r.touchData.startPosition[1]-Z[1],Xe=Ge*Ge,Ze=pr+Xe,lr=Ze*ie*ie;r.touchData.singleTouchMoved||($||H.$(":selected").unselect(["tapunselect"]),n($,["tap","vclick"],T,{x:Z[0],y:Z[1]}),re=!1,T.timeStamp-le<=H.multiClickDebounceTime()?(ve&&clearTimeout(ve),re=!0,le=null,n($,["dbltap","vdblclick"],T,{x:Z[0],y:Z[1]})):(ve=setTimeout(function(){re||n($,["onetap","voneclick"],T,{x:Z[0],y:Z[1]})},H.multiClickDebounceTime()),le=T.timeStamp)),$!=null&&!r.dragData.didDrag&&$._private.selectable&&lr<r.touchTapThreshold2&&!r.pinching&&(H.selectionType()==="single"?(H.$(t).unmerge($).unselect(["tapunselect"]),$.select(["tapselect"])):$.selected()?$.unselect(["tapunselect"]):$.select(["tapselect"]),r.redrawHint("eles",!0)),r.touchData.singleTouchMoved=!0}}}for(var vr=0;vr<Z.length;vr++)se[vr]=Z[vr];r.dragData.didDrag=!1,T.touches.length===0&&(r.touchData.dragDelta=[],r.touchData.startPosition=[null,null,null,null,null,null],r.touchData.startGPosition=null,r.touchData.didSelect=!1),T.touches.length<2&&(T.touches.length===1&&(r.touchData.startGPosition=[T.touches[0].clientX,T.touches[0].clientY]),r.pinching=!1,r.redrawHint("eles",!0),r.redraw())},!1),typeof TouchEvent>"u"){var oe=[],de=function(T){return{clientX:T.clientX,clientY:T.clientY,force:1,identifier:T.pointerId,pageX:T.pageX,pageY:T.pageY,radiusX:T.width/2,radiusY:T.height/2,screenX:T.screenX,screenY:T.screenY,target:T.target}},Le=function(T){return{event:T,touch:de(T)}},Ce=function(T){oe.push(Le(T))},xe=function(T){for(var $=0;$<oe.length;$++){var U=oe[$];if(U.event.pointerId===T.pointerId){oe.splice($,1);return}}},Ae=function(T){var $=oe.filter(function(U){return U.event.pointerId===T.pointerId})[0];$.event=T,$.touch=de(T)},Ee=function(T){T.touches=oe.map(function($){return $.touch})},Pe=function(T){return T.pointerType==="mouse"||T.pointerType===4};r.registerBinding(r.container,"pointerdown",function(ee){Pe(ee)||(ee.preventDefault(),Ce(ee),Ee(ee),X(ee))}),r.registerBinding(r.container,"pointerup",function(ee){Pe(ee)||(xe(ee),Ee(ee),te(ee))}),r.registerBinding(r.container,"pointercancel",function(ee){Pe(ee)||(xe(ee),Ee(ee),he(ee))}),r.registerBinding(r.container,"pointermove",function(ee){Pe(ee)||(ee.preventDefault(),Ae(ee),Ee(ee),ae(ee))})}};var Wr={};Wr.generatePolygon=function(r,e){return this.nodeShapes[r]={renderer:this,name:r,points:e,draw:function(a,n,i,s,o,l){this.renderer.nodeShapeImpl("polygon",a,n,i,s,o,this.points)},intersectLine:function(a,n,i,s,o,l,u,v){return ha(o,l,this.points,a,n,i/2,s/2,u)},checkPoint:function(a,n,i,s,o,l,u,v){return Gr(a,n,this.points,l,u,s,o,[0,-1],i)}}};Wr.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i)},intersectLine:function(e,t,a,n,i,s,o,l){return Wv(i,s,e,t,a/2+o,n/2+o)},checkPoint:function(e,t,a,n,i,s,o,l){return ft(e,t,n,i,s,o,a)}}};Wr.generateRoundPolygon=function(r,e){return this.nodeShapes[r]={renderer:this,name:r,points:e,getOrCreateCorners:function(a,n,i,s,o,l,u){if(l[u]!==void 0&&l[u+"-cx"]===a&&l[u+"-cy"]===n)return l[u];l[u]=new Array(e.length/2),l[u+"-cx"]=a,l[u+"-cy"]=n;var v=i/2,f=s/2;o=o==="auto"?wo(i,s):o;for(var c=new Array(e.length/2),h=0;h<e.length/2;h++)c[h]={x:a+v*e[h*2],y:n+f*e[h*2+1]};var d,y,p,g,m=c.length;for(y=c[m-1],d=0;d<m;d++)p=c[d%m],g=c[(d+1)%m],l[u][d]=Di(y,p,g,o),y=p,p=g;return l[u]},draw:function(a,n,i,s,o,l,u){this.renderer.nodeShapeImpl("round-polygon",a,n,i,s,o,this.points,this.getOrCreateCorners(n,i,s,o,l,u,"drawCorners"))},intersectLine:function(a,n,i,s,o,l,u,v,f){return Uv(o,l,this.points,a,n,i,s,u,this.getOrCreateCorners(a,n,i,s,v,f,"corners"))},checkPoint:function(a,n,i,s,o,l,u,v,f){return Kv(a,n,this.points,l,u,s,o,this.getOrCreateCorners(l,u,s,o,v,f,"corners"))}}};Wr.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:fr(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i,this.points,s)},intersectLine:function(e,t,a,n,i,s,o,l){return mo(i,s,e,t,a,n,o,l)},checkPoint:function(e,t,a,n,i,s,o,l){var u=n/2,v=i/2;l=l==="auto"?gt(n,i):l,l=Math.min(u,v,l);var f=l*2;return!!(Gr(e,t,this.points,s,o,n,i-f,[0,-1],a)||Gr(e,t,this.points,s,o,n-f,i,[0,-1],a)||ft(e,t,f,f,s-u+l,o-v+l,a)||ft(e,t,f,f,s+u-l,o-v+l,a)||ft(e,t,f,f,s+u-l,o+v-l,a)||ft(e,t,f,f,s-u+l,o+v-l,a))}}};Wr.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:hi(),points:fr(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i,null,s)},generateCutTrianglePts:function(e,t,a,n,i){var s=i==="auto"?this.cornerLength:i,o=t/2,l=e/2,u=a-l,v=a+l,f=n-o,c=n+o;return{topLeft:[u,f+s,u+s,f,u+s,f+s],topRight:[v-s,f,v,f+s,v-s,f+s],bottomRight:[v,c-s,v-s,c,v-s,c-s],bottomLeft:[u+s,c,u,c-s,u+s,c-s]}},intersectLine:function(e,t,a,n,i,s,o,l){var u=this.generateCutTrianglePts(a+2*o,n+2*o,e,t,l),v=[].concat.apply([],[u.topLeft.splice(0,4),u.topRight.splice(0,4),u.bottomRight.splice(0,4),u.bottomLeft.splice(0,4)]);return ha(i,s,v,e,t)},checkPoint:function(e,t,a,n,i,s,o,l){var u=l==="auto"?this.cornerLength:l;if(Gr(e,t,this.points,s,o,n,i-2*u,[0,-1],a)||Gr(e,t,this.points,s,o,n-2*u,i,[0,-1],a))return!0;var v=this.generateCutTrianglePts(n,i,s,o);return dr(e,t,v.topLeft)||dr(e,t,v.topRight)||dr(e,t,v.bottomRight)||dr(e,t,v.bottomLeft)}}};Wr.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:fr(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i)},intersectLine:function(e,t,a,n,i,s,o,l){var u=.15,v=.5,f=.85,c=this.generateBarrelBezierPts(a+2*o,n+2*o,e,t),h=function(p){var g=Mt({x:p[0],y:p[1]},{x:p[2],y:p[3]},{x:p[4],y:p[5]},u),m=Mt({x:p[0],y:p[1]},{x:p[2],y:p[3]},{x:p[4],y:p[5]},v),b=Mt({x:p[0],y:p[1]},{x:p[2],y:p[3]},{x:p[4],y:p[5]},f);return[p[0],p[1],g.x,g.y,m.x,m.y,b.x,b.y,p[4],p[5]]},d=[].concat(h(c.topLeft),h(c.topRight),h(c.bottomRight),h(c.bottomLeft));return ha(i,s,d,e,t)},generateBarrelBezierPts:function(e,t,a,n){var i=t/2,s=e/2,o=a-s,l=a+s,u=n-i,v=n+i,f=Kn(e,t),c=f.heightOffset,h=f.widthOffset,d=f.ctrlPtOffsetPct*e,y={topLeft:[o,u+c,o+d,u,o+h,u],topRight:[l-h,u,l-d,u,l,u+c],bottomRight:[l,v-c,l-d,v,l-h,v],bottomLeft:[o+h,v,o+d,v,o,v-c]};return y.topLeft.isTop=!0,y.topRight.isTop=!0,y.bottomLeft.isBottom=!0,y.bottomRight.isBottom=!0,y},checkPoint:function(e,t,a,n,i,s,o,l){var u=Kn(n,i),v=u.heightOffset,f=u.widthOffset;if(Gr(e,t,this.points,s,o,n,i-2*v,[0,-1],a)||Gr(e,t,this.points,s,o,n-2*f,i,[0,-1],a))return!0;for(var c=this.generateBarrelBezierPts(n,i,s,o),h=function(D,C,M){var P=M[4],B=M[2],L=M[0],k=M[5],O=M[1],A=Math.min(P,L),R=Math.max(P,L),I=Math.min(k,O),V=Math.max(k,O);if(A<=D&&D<=R&&I<=C&&C<=V){var G=Yv(P,B,L),F=qv(G[0],G[1],G[2],D),q=F.filter(function(Y){return 0<=Y&&Y<=1});if(q.length>0)return q[0]}return null},d=Object.keys(c),y=0;y<d.length;y++){var p=d[y],g=c[p],m=h(e,t,g);if(m!=null){var b=g[5],w=g[3],S=g[1],E=Je(b,w,S,m);if(g.isTop&&E<=t||g.isBottom&&t<=E)return!0}}return!1}}};Wr.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:fr(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i,this.points,s)},intersectLine:function(e,t,a,n,i,s,o,l){var u=e-(a/2+o),v=t-(n/2+o),f=v,c=e+(a/2+o),h=Qr(i,s,e,t,u,v,c,f,!1);return h.length>0?h:mo(i,s,e,t,a,n,o,l)},checkPoint:function(e,t,a,n,i,s,o,l){l=l==="auto"?gt(n,i):l;var u=2*l;if(Gr(e,t,this.points,s,o,n,i-u,[0,-1],a)||Gr(e,t,this.points,s,o,n-u,i,[0,-1],a))return!0;var v=n/2+2*a,f=i/2+2*a,c=[s-v,o-f,s-v,o,s+v,o,s+v,o-f];return!!(dr(e,t,c)||ft(e,t,u,u,s+n/2-l,o+i/2-l,a)||ft(e,t,u,u,s-n/2+l,o+i/2-l,a))}}};Wr.registerNodeShapes=function(){var r=this.nodeShapes={},e=this;this.generateEllipse(),this.generatePolygon("triangle",fr(3,0)),this.generateRoundPolygon("round-triangle",fr(3,0)),this.generatePolygon("rectangle",fr(4,0)),r.square=r.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();{var t=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",t),this.generateRoundPolygon("round-diamond",t)}this.generatePolygon("pentagon",fr(5,0)),this.generateRoundPolygon("round-pentagon",fr(5,0)),this.generatePolygon("hexagon",fr(6,0)),this.generateRoundPolygon("round-hexagon",fr(6,0)),this.generatePolygon("heptagon",fr(7,0)),this.generateRoundPolygon("round-heptagon",fr(7,0)),this.generatePolygon("octagon",fr(8,0)),this.generateRoundPolygon("round-octagon",fr(8,0));var a=new Array(20);{var n=Gn(5,0),i=Gn(5,Math.PI/5),s=.5*(3-Math.sqrt(5));s*=1.57;for(var o=0;o<i.length/2;o++)i[o*2]*=s,i[o*2+1]*=s;for(var o=0;o<20/4;o++)a[o*4]=n[o*2],a[o*4+1]=n[o*2+1],a[o*4+2]=i[o*2],a[o*4+3]=i[o*2+1]}a=bo(a),this.generatePolygon("star",a),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);{var l=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",l),this.generateRoundPolygon("round-tag",l)}r.makePolygon=function(u){var v=u.join("$"),f="polygon-"+v,c;return(c=this[f])?c:e.generatePolygon(f,u)}};var ka={};ka.timeToRender=function(){return this.redrawTotalTime/this.redrawCount};ka.redraw=function(r){r=r||co();var e=this;e.averageRedrawTime===void 0&&(e.averageRedrawTime=0),e.lastRedrawTime===void 0&&(e.lastRedrawTime=0),e.lastDrawTime===void 0&&(e.lastDrawTime=0),e.requestedFrame=!0,e.renderOptions=r};ka.beforeRender=function(r,e){if(!this.destroyed){e==null&&$e("Priority is not optional for beforeRender");var t=this.beforeRenderCallbacks;t.push({fn:r,priority:e}),t.sort(function(a,n){return n.priority-a.priority})}};var Ns=function(e,t,a){for(var n=e.beforeRenderCallbacks,i=0;i<n.length;i++)n[i].fn(t,a)};ka.startRenderLoop=function(){var r=this,e=r.cy;if(!r.renderLoopStarted){r.renderLoopStarted=!0;var t=function a(n){if(!r.destroyed){if(!e.batching())if(r.requestedFrame&&!r.skipFrame){Ns(r,!0,n);var i=Hr();r.render(r.renderOptions);var s=r.lastDrawTime=Hr();r.averageRedrawTime===void 0&&(r.averageRedrawTime=s-i),r.redrawCount===void 0&&(r.redrawCount=0),r.redrawCount++,r.redrawTotalTime===void 0&&(r.redrawTotalTime=0);var o=s-i;r.redrawTotalTime+=o,r.lastRedrawTime=o,r.averageRedrawTime=r.averageRedrawTime/2+o/2,r.requestedFrame=!1}else Ns(r,!1,n);r.skipFrame=!1,ja(a)}};ja(t)}};var Bg=function(e){this.init(e)},mu=Bg,Yt=mu.prototype;Yt.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"];Yt.init=function(r){var e=this;e.options=r,e.cy=r.cy;var t=e.container=r.cy.container(),a=e.cy.window();if(a){var n=a.document,i=n.head,s="__________cytoscape_stylesheet",o="__________cytoscape_container",l=n.getElementById(s)!=null;if(t.className.indexOf(o)<0&&(t.className=(t.className||"")+" "+o),!l){var u=n.createElement("style");u.id=s,u.textContent="."+o+" { position: relative; }",i.insertBefore(u,i.children[0])}var v=a.getComputedStyle(t),f=v.getPropertyValue("position");f==="static"&&Me("A Cytoscape container has style position:static and so can not use UI extensions properly")}e.selection=[void 0,void 0,void 0,void 0,0],e.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],e.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},e.dragData={possibleDragElements:[]},e.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},e.redraws=0,e.showFps=r.showFps,e.debug=r.debug,e.hideEdgesOnViewport=r.hideEdgesOnViewport,e.textureOnViewport=r.textureOnViewport,e.wheelSensitivity=r.wheelSensitivity,e.motionBlurEnabled=r.motionBlur,e.forcedPixelRatio=ne(r.pixelRatio)?r.pixelRatio:null,e.motionBlur=r.motionBlur,e.motionBlurOpacity=r.motionBlurOpacity,e.motionBlurTransparency=1-e.motionBlurOpacity,e.motionBlurPxRatio=1,e.mbPxRBlurry=1,e.minMbLowQualFrames=4,e.fullQualityMb=!1,e.clearedForMotionBlur=[],e.desktopTapThreshold=r.desktopTapThreshold,e.desktopTapThreshold2=r.desktopTapThreshold*r.desktopTapThreshold,e.touchTapThreshold=r.touchTapThreshold,e.touchTapThreshold2=r.touchTapThreshold*r.touchTapThreshold,e.tapholdDuration=500,e.bindings=[],e.beforeRenderCallbacks=[],e.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},e.registerNodeShapes(),e.registerArrowShapes(),e.registerCalculationListeners()};Yt.notify=function(r,e){var t=this,a=t.cy;if(!this.destroyed){if(r==="init"){t.load();return}if(r==="destroy"){t.destroy();return}(r==="add"||r==="remove"||r==="move"&&a.hasCompoundNodes()||r==="load"||r==="zorder"||r==="mount")&&t.invalidateCachedZSortedEles(),r==="viewport"&&t.redrawHint("select",!0),(r==="load"||r==="resize"||r==="mount")&&(t.invalidateContainerClientCoordsCache(),t.matchCanvasSize(t.container)),t.redrawHint("eles",!0),t.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()}};Yt.destroy=function(){var r=this;r.destroyed=!0,r.cy.stopAnimationLoop();for(var e=0;e<r.bindings.length;e++){var t=r.bindings[e],a=t,n=a.target;(n.off||n.removeEventListener).apply(n,a.args)}if(r.bindings=[],r.beforeRenderCallbacks=[],r.onUpdateEleCalcsFns=[],r.removeObserver&&r.removeObserver.disconnect(),r.styleObserver&&r.styleObserver.disconnect(),r.resizeObserver&&r.resizeObserver.disconnect(),r.labelCalcDiv)try{document.body.removeChild(r.labelCalcDiv)}catch{}};Yt.isHeadless=function(){return!1};[Ti,pu,yu,Ut,Wr,ka].forEach(function(r){pe(Yt,r)});var qn=1e3/60,bu={setupDequeueing:function(e){return function(){var a=this,n=this.renderer;if(!a.dequeueingSetup){a.dequeueingSetup=!0;var i=cn(function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()},e.deqRedrawThreshold),s=function(u,v){var f=Hr(),c=n.averageRedrawTime,h=n.lastRedrawTime,d=[],y=n.cy.extent(),p=n.getPixelRatio();for(u||n.flushRenderedStyleQueue();;){var g=Hr(),m=g-f,b=g-v;if(h<qn){var w=qn-(u?c:0);if(b>=e.deqFastCost*w)break}else if(u){if(m>=e.deqCost*h||m>=e.deqAvgCost*c)break}else if(b>=e.deqNoDrawCost*qn)break;var S=e.deq(a,p,y);if(S.length>0)for(var E=0;E<S.length;E++)d.push(S[E]);else break}d.length>0&&(e.onDeqd(a,d),!u&&e.shouldRedraw(a,d,p,y)&&i())},o=e.priority||vi;n.beforeRender(s,o(a))}}}},Mg=function(){function r(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:en;ii(this,r),this.idsByKey=new Ir,this.keyForId=new Ir,this.cachesByLvl=new Ir,this.lvls=[],this.getKey=e,this.doesEleInvalidateKey=t}return si(r,[{key:"getIdsFor",value:function(t){t==null&&$e("Can not get id list for null key");var a=this.idsByKey,n=this.idsByKey.get(t);return n||(n=new Vt,a.set(t,n)),n}},{key:"addIdForKey",value:function(t,a){t!=null&&this.getIdsFor(t).add(a)}},{key:"deleteIdForKey",value:function(t,a){t!=null&&this.getIdsFor(t).delete(a)}},{key:"getNumberOfIdsForKey",value:function(t){return t==null?0:this.getIdsFor(t).size}},{key:"updateKeyMappingFor",value:function(t){var a=t.id(),n=this.keyForId.get(a),i=this.getKey(t);this.deleteIdForKey(n,a),this.addIdForKey(i,a),this.keyForId.set(a,i)}},{key:"deleteKeyMappingFor",value:function(t){var a=t.id(),n=this.keyForId.get(a);this.deleteIdForKey(n,a),this.keyForId.delete(a)}},{key:"keyHasChangedFor",value:function(t){var a=t.id(),n=this.keyForId.get(a),i=this.getKey(t);return n!==i}},{key:"isInvalid",value:function(t){return this.keyHasChangedFor(t)||this.doesEleInvalidateKey(t)}},{key:"getCachesAt",value:function(t){var a=this.cachesByLvl,n=this.lvls,i=a.get(t);return i||(i=new Ir,a.set(t,i),n.push(t)),i}},{key:"getCache",value:function(t,a){return this.getCachesAt(a).get(t)}},{key:"get",value:function(t,a){var n=this.getKey(t),i=this.getCache(n,a);return i!=null&&this.updateKeyMappingFor(t),i}},{key:"getForCachedKey",value:function(t,a){var n=this.keyForId.get(t.id()),i=this.getCache(n,a);return i}},{key:"hasCache",value:function(t,a){return this.getCachesAt(a).has(t)}},{key:"has",value:function(t,a){var n=this.getKey(t);return this.hasCache(n,a)}},{key:"setCache",value:function(t,a,n){n.key=t,this.getCachesAt(a).set(t,n)}},{key:"set",value:function(t,a,n){var i=this.getKey(t);this.setCache(i,a,n),this.updateKeyMappingFor(t)}},{key:"deleteCache",value:function(t,a){this.getCachesAt(a).delete(t)}},{key:"delete",value:function(t,a){var n=this.getKey(t);this.deleteCache(n,a)}},{key:"invalidateKey",value:function(t){var a=this;this.lvls.forEach(function(n){return a.deleteCache(t,n)})}},{key:"invalidate",value:function(t){var a=t.id(),n=this.keyForId.get(a);this.deleteKeyMappingFor(t);var i=this.doesEleInvalidateKey(t);return i&&this.invalidateKey(n),i||this.getNumberOfIdsForKey(n)===0}}]),r}(),Fs=25,Ha=50,Ja=-4,ri=3,Lg=7.99,Ag=8,Rg=1024,Og=1024,Ig=1024,zg=.2,Ng=.8,Fg=10,Vg=.15,qg=.1,$g=.9,Hg=.9,Gg=100,Kg=1,Bt={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},Wg=rr({getKey:null,doesEleInvalidateKey:en,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:lo,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),sa=function(e,t){var a=this;a.renderer=e,a.onDequeues=[];var n=Wg(t);pe(a,n),a.lookup=new Mg(n.getKey,n.doesEleInvalidateKey),a.setupDequeueing()},Ye=sa.prototype;Ye.reasons=Bt;Ye.getTextureQueue=function(r){var e=this;return e.eleImgCaches=e.eleImgCaches||{},e.eleImgCaches[r]=e.eleImgCaches[r]||[]};Ye.getRetiredTextureQueue=function(r){var e=this,t=e.eleImgCaches.retired=e.eleImgCaches.retired||{},a=t[r]=t[r]||[];return a};Ye.getElementQueue=function(){var r=this,e=r.eleCacheQueue=r.eleCacheQueue||new Ca(function(t,a){return a.reqs-t.reqs});return e};Ye.getElementKeyToQueue=function(){var r=this,e=r.eleKeyToCacheQueue=r.eleKeyToCacheQueue||{};return e};Ye.getElement=function(r,e,t,a,n){var i=this,s=this.renderer,o=s.cy.zoom(),l=this.lookup;if(!e||e.w===0||e.h===0||isNaN(e.w)||isNaN(e.h)||!r.visible()||r.removed()||!i.allowEdgeTxrCaching&&r.isEdge()||!i.allowParentTxrCaching&&r.isParent())return null;if(a==null&&(a=Math.ceil(ci(o*t))),a<Ja)a=Ja;else if(o>=Lg||a>ri)return null;var u=Math.pow(2,a),v=e.h*u,f=e.w*u,c=s.eleTextBiggerThanMin(r,u);if(!this.isVisible(r,c))return null;var h=l.get(r,a);if(h&&h.invalidated&&(h.invalidated=!1,h.texture.invalidatedWidth-=h.width),h)return h;var d;if(v<=Fs?d=Fs:v<=Ha?d=Ha:d=Math.ceil(v/Ha)*Ha,v>Ig||f>Og)return null;var y=i.getTextureQueue(d),p=y[y.length-2],g=function(){return i.recycleTexture(d,f)||i.addTexture(d,f)};p||(p=y[y.length-1]),p||(p=g()),p.width-p.usedWidth<f&&(p=g());for(var m=function(A){return A&&A.scaledLabelShown===c},b=n&&n===Bt.dequeue,w=n&&n===Bt.highQuality,S=n&&n===Bt.downscale,E,x=a+1;x<=ri;x++){var D=l.get(r,x);if(D){E=D;break}}var C=E&&E.level===a+1?E:null,M=function(){p.context.drawImage(C.texture.canvas,C.x,0,C.width,C.height,p.usedWidth,0,f,v)};if(p.context.setTransform(1,0,0,1,0,0),p.context.clearRect(p.usedWidth,0,f,d),m(C))M();else if(m(E))if(w){for(var P=E.level;P>a;P--)C=i.getElement(r,e,t,P,Bt.downscale);M()}else return i.queueElement(r,E.level-1),E;else{var B;if(!b&&!w&&!S)for(var L=a-1;L>=Ja;L--){var k=l.get(r,L);if(k){B=k;break}}if(m(B))return i.queueElement(r,a),B;p.context.translate(p.usedWidth,0),p.context.scale(u,u),this.drawElement(p.context,r,e,c,!1),p.context.scale(1/u,1/u),p.context.translate(-p.usedWidth,0)}return h={x:p.usedWidth,texture:p,level:a,scale:u,width:f,height:v,scaledLabelShown:c},p.usedWidth+=Math.ceil(f+Ag),p.eleCaches.push(h),l.set(r,a,h),i.checkTextureFullness(p),h};Ye.invalidateElements=function(r){for(var e=0;e<r.length;e++)this.invalidateElement(r[e])};Ye.invalidateElement=function(r){var e=this,t=e.lookup,a=[],n=t.isInvalid(r);if(n){for(var i=Ja;i<=ri;i++){var s=t.getForCachedKey(r,i);s&&a.push(s)}var o=t.invalidate(r);if(o)for(var l=0;l<a.length;l++){var u=a[l],v=u.texture;v.invalidatedWidth+=u.width,u.invalidated=!0,e.checkTextureUtility(v)}e.removeFromQueue(r)}};Ye.checkTextureUtility=function(r){r.invalidatedWidth>=zg*r.width&&this.retireTexture(r)};Ye.checkTextureFullness=function(r){var e=this,t=e.getTextureQueue(r.height);r.usedWidth/r.width>Ng&&r.fullnessChecks>=Fg?et(t,r):r.fullnessChecks++};Ye.retireTexture=function(r){var e=this,t=r.height,a=e.getTextureQueue(t),n=this.lookup;et(a,r),r.retired=!0;for(var i=r.eleCaches,s=0;s<i.length;s++){var o=i[s];n.deleteCache(o.key,o.level)}fi(i);var l=e.getRetiredTextureQueue(t);l.push(r)};Ye.addTexture=function(r,e){var t=this,a=t.getTextureQueue(r),n={};return a.push(n),n.eleCaches=[],n.height=r,n.width=Math.max(Rg,e),n.usedWidth=0,n.invalidatedWidth=0,n.fullnessChecks=0,n.canvas=t.renderer.makeOffscreenCanvas(n.width,n.height),n.context=n.canvas.getContext("2d"),n};Ye.recycleTexture=function(r,e){for(var t=this,a=t.getTextureQueue(r),n=t.getRetiredTextureQueue(r),i=0;i<n.length;i++){var s=n[i];if(s.width>=e)return s.retired=!1,s.usedWidth=0,s.invalidatedWidth=0,s.fullnessChecks=0,fi(s.eleCaches),s.context.setTransform(1,0,0,1,0,0),s.context.clearRect(0,0,s.width,s.height),et(n,s),a.push(s),s}};Ye.queueElement=function(r,e){var t=this,a=t.getElementQueue(),n=t.getElementKeyToQueue(),i=this.getKey(r),s=n[i];if(s)s.level=Math.max(s.level,e),s.eles.merge(r),s.reqs++,a.updateItem(s);else{var o={eles:r.spawn().merge(r),level:e,reqs:1,key:i};a.push(o),n[i]=o}};Ye.dequeue=function(r){for(var e=this,t=e.getElementQueue(),a=e.getElementKeyToQueue(),n=[],i=e.lookup,s=0;s<Kg&&t.size()>0;s++){var o=t.pop(),l=o.key,u=o.eles[0],v=i.hasCache(u,o.level);if(a[l]=null,v)continue;n.push(o);var f=e.getBoundingBox(u);e.getElement(u,f,r,o.level,Bt.dequeue)}return n};Ye.removeFromQueue=function(r){var e=this,t=e.getElementQueue(),a=e.getElementKeyToQueue(),n=this.getKey(r),i=a[n];i!=null&&(i.eles.length===1?(i.reqs=li,t.updateItem(i),t.pop(),a[n]=null):i.eles.unmerge(r))};Ye.onDequeue=function(r){this.onDequeues.push(r)};Ye.offDequeue=function(r){et(this.onDequeues,r)};Ye.setupDequeueing=bu.setupDequeueing({deqRedrawThreshold:Gg,deqCost:Vg,deqAvgCost:qg,deqNoDrawCost:$g,deqFastCost:Hg,deq:function(e,t,a){return e.dequeue(t,a)},onDeqd:function(e,t){for(var a=0;a<e.onDequeues.length;a++){var n=e.onDequeues[a];n(t)}},shouldRedraw:function(e,t,a,n){for(var i=0;i<t.length;i++)for(var s=t[i].eles,o=0;o<s.length;o++){var l=s[o].boundingBox();if(di(l,n))return!0}return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}});var Ug=1,ua=-4,ln=2,Yg=3.99,Xg=50,Zg=50,Qg=.15,Jg=.1,_g=.9,jg=.9,ep=1,Vs=250,rp=4e3*4e3,qs=32767,tp=!0,wu=function(e){var t=this,a=t.renderer=e,n=a.cy;t.layersByLevel={},t.firstGet=!0,t.lastInvalidationTime=Hr()-2*Vs,t.skipping=!1,t.eleTxrDeqs=n.collection(),t.scheduleElementRefinement=cn(function(){t.refineElementTextures(t.eleTxrDeqs),t.eleTxrDeqs.unmerge(t.eleTxrDeqs)},Zg),a.beforeRender(function(s,o){o-t.lastInvalidationTime<=Vs?t.skipping=!0:t.skipping=!1},a.beforeRenderPriorities.lyrTxrSkip);var i=function(o,l){return l.reqs-o.reqs};t.layersQueue=new Ca(i),t.setupDequeueing()},tr=wu.prototype,$s=0,ap=Math.pow(2,53)-1;tr.makeLayer=function(r,e){var t=Math.pow(2,e),a=Math.ceil(r.w*t),n=Math.ceil(r.h*t),i=this.renderer.makeOffscreenCanvas(a,n),s={id:$s=++$s%ap,bb:r,level:e,width:a,height:n,canvas:i,context:i.getContext("2d"),eles:[],elesQueue:[],reqs:0},o=s.context,l=-s.bb.x1,u=-s.bb.y1;return o.scale(t,t),o.translate(l,u),s};tr.getLayers=function(r,e,t){var a=this,n=a.renderer,i=n.cy,s=i.zoom(),o=a.firstGet;if(a.firstGet=!1,t==null){if(t=Math.ceil(ci(s*e)),t<ua)t=ua;else if(s>=Yg||t>ln)return null}a.validateLayersElesOrdering(t,r);var l=a.layersByLevel,u=Math.pow(2,t),v=l[t]=l[t]||[],f,c=a.levelIsComplete(t,r),h,d=function(){var M=function(O){if(a.validateLayersElesOrdering(O,r),a.levelIsComplete(O,r))return h=l[O],!0},P=function(O){if(!h)for(var A=t+O;ua<=A&&A<=ln&&!M(A);A+=O);};P(1),P(-1);for(var B=v.length-1;B>=0;B--){var L=v[B];L.invalid&&et(v,L)}};if(!c)d();else return v;var y=function(){if(!f){f=hr();for(var M=0;M<r.length;M++)po(f,r[M].boundingBox())}return f},p=function(M){M=M||{};var P=M.after;y();var B=Math.ceil(f.w*u),L=Math.ceil(f.h*u);if(B>qs||L>qs)return null;var k=B*L;if(k>rp)return null;var O=a.makeLayer(f,t);if(P!=null){var A=v.indexOf(P)+1;v.splice(A,0,O)}else(M.insert===void 0||M.insert)&&v.unshift(O);return O};if(a.skipping&&!o)return null;for(var g=null,m=r.length/Ug,b=!o,w=0;w<r.length;w++){var S=r[w],E=S._private.rscratch,x=E.imgLayerCaches=E.imgLayerCaches||{},D=x[t];if(D){g=D;continue}if((!g||g.eles.length>=m||!yo(g.bb,S.boundingBox()))&&(g=p({insert:!0,after:g}),!g))return null;h||b?a.queueLayer(g,S):a.drawEleInLayer(g,S,t,e),g.eles.push(S),x[t]=g}return h||(b?null:v)};tr.getEleLevelForLayerLevel=function(r,e){return r};tr.drawEleInLayer=function(r,e,t,a){var n=this,i=this.renderer,s=r.context,o=e.boundingBox();o.w===0||o.h===0||!e.visible()||(t=n.getEleLevelForLayerLevel(t,a),i.setImgSmoothing(s,!1),i.drawCachedElement(s,e,null,null,t,tp),i.setImgSmoothing(s,!0))};tr.levelIsComplete=function(r,e){var t=this,a=t.layersByLevel[r];if(!a||a.length===0)return!1;for(var n=0,i=0;i<a.length;i++){var s=a[i];if(s.reqs>0||s.invalid)return!1;n+=s.eles.length}return n===e.length};tr.validateLayersElesOrdering=function(r,e){var t=this.layersByLevel[r];if(t)for(var a=0;a<t.length;a++){for(var n=t[a],i=-1,s=0;s<e.length;s++)if(n.eles[0]===e[s]){i=s;break}if(i<0){this.invalidateLayer(n);continue}for(var o=i,s=0;s<n.eles.length;s++)if(n.eles[s]!==e[o+s]){this.invalidateLayer(n);break}}};tr.updateElementsInLayers=function(r,e){for(var t=this,a=xa(r[0]),n=0;n<r.length;n++)for(var i=a?null:r[n],s=a?r[n]:r[n].ele,o=s._private.rscratch,l=o.imgLayerCaches=o.imgLayerCaches||{},u=ua;u<=ln;u++){var v=l[u];v&&(i&&t.getEleLevelForLayerLevel(v.level)!==i.level||e(v,s,i))}};tr.haveLayers=function(){for(var r=this,e=!1,t=ua;t<=ln;t++){var a=r.layersByLevel[t];if(a&&a.length>0){e=!0;break}}return e};tr.invalidateElements=function(r){var e=this;r.length!==0&&(e.lastInvalidationTime=Hr(),!(r.length===0||!e.haveLayers())&&e.updateElementsInLayers(r,function(a,n,i){e.invalidateLayer(a)}))};tr.invalidateLayer=function(r){if(this.lastInvalidationTime=Hr(),!r.invalid){var e=r.level,t=r.eles,a=this.layersByLevel[e];et(a,r),r.elesQueue=[],r.invalid=!0,r.replacement&&(r.replacement.invalid=!0);for(var n=0;n<t.length;n++){var i=t[n]._private.rscratch.imgLayerCaches;i&&(i[e]=null)}}};tr.refineElementTextures=function(r){var e=this;e.updateElementsInLayers(r,function(a,n,i){var s=a.replacement;if(s||(s=a.replacement=e.makeLayer(a.bb,a.level),s.replaces=a,s.eles=a.eles),!s.reqs)for(var o=0;o<s.eles.length;o++)e.queueLayer(s,s.eles[o])})};tr.enqueueElementRefinement=function(r){this.eleTxrDeqs.merge(r),this.scheduleElementRefinement()};tr.queueLayer=function(r,e){var t=this,a=t.layersQueue,n=r.elesQueue,i=n.hasId=n.hasId||{};if(!r.replacement){if(e){if(i[e.id()])return;n.push(e),i[e.id()]=!0}r.reqs?(r.reqs++,a.updateItem(r)):(r.reqs=1,a.push(r))}};tr.dequeue=function(r){for(var e=this,t=e.layersQueue,a=[],n=0;n<ep&&t.size()!==0;){var i=t.peek();if(i.replacement){t.pop();continue}if(i.replaces&&i!==i.replaces.replacement){t.pop();continue}if(i.invalid){t.pop();continue}var s=i.elesQueue.shift();s&&(e.drawEleInLayer(i,s,i.level,r),n++),a.length===0&&a.push(!0),i.elesQueue.length===0&&(t.pop(),i.reqs=0,i.replaces&&e.applyLayerReplacement(i),e.requestRedraw())}return a};tr.applyLayerReplacement=function(r){var e=this,t=e.layersByLevel[r.level],a=r.replaces,n=t.indexOf(a);if(!(n<0||a.invalid)){t[n]=r;for(var i=0;i<r.eles.length;i++){var s=r.eles[i]._private,o=s.imgLayerCaches=s.imgLayerCaches||{};o&&(o[r.level]=r)}e.requestRedraw()}};tr.requestRedraw=cn(function(){var r=this.renderer;r.redrawHint("eles",!0),r.redrawHint("drag",!0),r.redraw()},100);tr.setupDequeueing=bu.setupDequeueing({deqRedrawThreshold:Xg,deqCost:Qg,deqAvgCost:Jg,deqNoDrawCost:_g,deqFastCost:jg,deq:function(e,t){return e.dequeue(t)},onDeqd:vi,shouldRedraw:lo,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}});var xu={},Hs;function np(r,e){for(var t=0;t<e.length;t++){var a=e[t];r.lineTo(a.x,a.y)}}function ip(r,e,t){for(var a,n=0;n<e.length;n++){var i=e[n];n===0&&(a=i),r.lineTo(i.x,i.y)}r.quadraticCurveTo(t.x,t.y,a.x,a.y)}function Gs(r,e,t){r.beginPath&&r.beginPath();for(var a=e,n=0;n<a.length;n++){var i=a[n];r.lineTo(i.x,i.y)}var s=t,o=t[0];r.moveTo(o.x,o.y);for(var n=1;n<s.length;n++){var i=s[n];r.lineTo(i.x,i.y)}r.closePath&&r.closePath()}function sp(r,e,t,a,n){r.beginPath&&r.beginPath(),r.arc(t,a,n,0,Math.PI*2,!1);var i=e,s=i[0];r.moveTo(s.x,s.y);for(var o=0;o<i.length;o++){var l=i[o];r.lineTo(l.x,l.y)}r.closePath&&r.closePath()}function op(r,e,t,a){r.arc(e,t,a,0,Math.PI*2,!1)}xu.arrowShapeImpl=function(r){return(Hs||(Hs={polygon:np,"triangle-backcurve":ip,"triangle-tee":Gs,"circle-triangle":sp,"triangle-cross":Gs,circle:op}))[r]};var Fr={};Fr.drawElement=function(r,e,t,a,n,i){var s=this;e.isNode()?s.drawNode(r,e,t,a,n,i):s.drawEdge(r,e,t,a,n,i)};Fr.drawElementOverlay=function(r,e){var t=this;e.isNode()?t.drawNodeOverlay(r,e):t.drawEdgeOverlay(r,e)};Fr.drawElementUnderlay=function(r,e){var t=this;e.isNode()?t.drawNodeUnderlay(r,e):t.drawEdgeUnderlay(r,e)};Fr.drawCachedElementPortion=function(r,e,t,a,n,i,s,o){var l=this,u=t.getBoundingBox(e);if(!(u.w===0||u.h===0)){var v=t.getElement(e,u,a,n,i);if(v!=null){var f=o(l,e);if(f===0)return;var c=s(l,e),h=u.x1,d=u.y1,y=u.w,p=u.h,g,m,b,w,S;if(c!==0){var E=t.getRotationPoint(e);b=E.x,w=E.y,r.translate(b,w),r.rotate(c),S=l.getImgSmoothing(r),S||l.setImgSmoothing(r,!0);var x=t.getRotationOffset(e);g=x.x,m=x.y}else g=h,m=d;var D;f!==1&&(D=r.globalAlpha,r.globalAlpha=D*f),r.drawImage(v.texture.canvas,v.x,0,v.width,v.height,g,m,y,p),f!==1&&(r.globalAlpha=D),c!==0&&(r.rotate(-c),r.translate(-b,-w),S||l.setImgSmoothing(r,!1))}else t.drawElement(r,e)}};var up=function(){return 0},lp=function(e,t){return e.getTextAngle(t,null)},vp=function(e,t){return e.getTextAngle(t,"source")},fp=function(e,t){return e.getTextAngle(t,"target")},cp=function(e,t){return t.effectiveOpacity()},$n=function(e,t){return t.pstyle("text-opacity").pfValue*t.effectiveOpacity()};Fr.drawCachedElement=function(r,e,t,a,n,i){var s=this,o=s.data,l=o.eleTxrCache,u=o.lblTxrCache,v=o.slbTxrCache,f=o.tlbTxrCache,c=e.boundingBox(),h=i===!0?l.reasons.highQuality:null;if(!(c.w===0||c.h===0||!e.visible())&&(!a||di(c,a))){var d=e.isEdge(),y=e.element()._private.rscratch.badLine;s.drawElementUnderlay(r,e),s.drawCachedElementPortion(r,e,l,t,n,h,up,cp),(!d||!y)&&s.drawCachedElementPortion(r,e,u,t,n,h,lp,$n),d&&!y&&(s.drawCachedElementPortion(r,e,v,t,n,h,vp,$n),s.drawCachedElementPortion(r,e,f,t,n,h,fp,$n)),s.drawElementOverlay(r,e)}};Fr.drawElements=function(r,e){for(var t=this,a=0;a<e.length;a++){var n=e[a];t.drawElement(r,n)}};Fr.drawCachedElements=function(r,e,t,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];n.drawCachedElement(r,s,t,a)}};Fr.drawCachedNodes=function(r,e,t,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];s.isNode()&&n.drawCachedElement(r,s,t,a)}};Fr.drawLayeredElements=function(r,e,t,a){var n=this,i=n.data.lyrTxrCache.getLayers(e,t);if(i)for(var s=0;s<i.length;s++){var o=i[s],l=o.bb;l.w===0||l.h===0||r.drawImage(o.canvas,l.x1,l.y1,l.w,l.h)}else n.drawCachedElements(r,e,t,a)};var Ur={};Ur.drawEdge=function(r,e,t){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o=e._private.rscratch;if(!(i&&!e.visible())&&!(o.badLine||o.allpts==null||isNaN(o.allpts[0]))){var l;t&&(l=t,r.translate(-l.x1,-l.y1));var u=i?e.pstyle("opacity").value:1,v=i?e.pstyle("line-opacity").value:1,f=e.pstyle("curve-style").value,c=e.pstyle("line-style").value,h=e.pstyle("width").pfValue,d=e.pstyle("line-cap").value,y=e.pstyle("line-outline-width").value,p=e.pstyle("line-outline-color").value,g=u*v,m=u*v,b=function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:g;f==="straight-triangle"?(s.eleStrokeStyle(r,e,O),s.drawEdgeTrianglePath(e,r,o.allpts)):(r.lineWidth=h,r.lineCap=d,s.eleStrokeStyle(r,e,O),s.drawEdgePath(e,r,o.allpts,c),r.lineCap="butt")},w=function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:g;if(r.lineWidth=h+y,r.lineCap=d,y>0)s.colorStrokeStyle(r,p[0],p[1],p[2],O);else{r.lineCap="butt";return}f==="straight-triangle"?s.drawEdgeTrianglePath(e,r,o.allpts):(s.drawEdgePath(e,r,o.allpts,c),r.lineCap="butt")},S=function(){n&&s.drawEdgeOverlay(r,e)},E=function(){n&&s.drawEdgeUnderlay(r,e)},x=function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:m;s.drawArrowheads(r,e,O)},D=function(){s.drawElementText(r,e,null,a)};r.lineJoin="round";var C=e.pstyle("ghost").value==="yes";if(C){var M=e.pstyle("ghost-offset-x").pfValue,P=e.pstyle("ghost-offset-y").pfValue,B=e.pstyle("ghost-opacity").value,L=g*B;r.translate(M,P),b(L),x(L),r.translate(-M,-P)}else w();E(),b(),x(),S(),D(),t&&r.translate(l.x1,l.y1)}};var Eu=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,a){if(a.visible()){var n=a.pstyle("".concat(e,"-opacity")).value;if(n!==0){var i=this,s=i.usePaths(),o=a._private.rscratch,l=a.pstyle("".concat(e,"-padding")).pfValue,u=2*l,v=a.pstyle("".concat(e,"-color")).value;t.lineWidth=u,o.edgeType==="self"&&!s?t.lineCap="butt":t.lineCap="round",i.colorStrokeStyle(t,v[0],v[1],v[2],n),i.drawEdgePath(a,t,o.allpts,"solid")}}}};Ur.drawEdgeOverlay=Eu("overlay");Ur.drawEdgeUnderlay=Eu("underlay");Ur.drawEdgePath=function(r,e,t,a){var n=r._private.rscratch,i=e,s,o=!1,l=this.usePaths(),u=r.pstyle("line-dash-pattern").pfValue,v=r.pstyle("line-dash-offset").pfValue;if(l){var f=t.join("$"),c=n.pathCacheKey&&n.pathCacheKey===f;c?(s=e=n.pathCache,o=!0):(s=e=new Path2D,n.pathCacheKey=f,n.pathCache=s)}if(i.setLineDash)switch(a){case"dotted":i.setLineDash([1,1]);break;case"dashed":i.setLineDash(u),i.lineDashOffset=v;break;case"solid":i.setLineDash([]);break}if(!o&&!n.badLine)switch(e.beginPath&&e.beginPath(),e.moveTo(t[0],t[1]),n.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var h=2;h+3<t.length;h+=4)e.quadraticCurveTo(t[h],t[h+1],t[h+2],t[h+3]);break;case"straight":case"haystack":for(var d=2;d+1<t.length;d+=2)e.lineTo(t[d],t[d+1]);break;case"segments":if(n.isRound){var y=Zs(n.roundCorners),p;try{for(y.s();!(p=y.n()).done;){var g=p.value;fu(e,g)}}catch(b){y.e(b)}finally{y.f()}e.lineTo(t[t.length-2],t[t.length-1])}else for(var m=2;m+1<t.length;m+=2)e.lineTo(t[m],t[m+1]);break}e=i,l?e.stroke(s):e.stroke(),e.setLineDash&&e.setLineDash([])};Ur.drawEdgeTrianglePath=function(r,e,t){e.fillStyle=e.strokeStyle;for(var a=r.pstyle("width").pfValue,n=0;n+1<t.length;n+=2){var i=[t[n+2]-t[n],t[n+3]-t[n+1]],s=Math.sqrt(i[0]*i[0]+i[1]*i[1]),o=[i[1]/s,-i[0]/s],l=[o[0]*a/2,o[1]*a/2];e.beginPath(),e.moveTo(t[n]-l[0],t[n+1]-l[1]),e.lineTo(t[n]+l[0],t[n+1]+l[1]),e.lineTo(t[n+2],t[n+3]),e.closePath(),e.fill()}};Ur.drawArrowheads=function(r,e,t){var a=e._private.rscratch,n=a.edgeType==="haystack";n||this.drawArrowhead(r,e,"source",a.arrowStartX,a.arrowStartY,a.srcArrowAngle,t),this.drawArrowhead(r,e,"mid-target",a.midX,a.midY,a.midtgtArrowAngle,t),this.drawArrowhead(r,e,"mid-source",a.midX,a.midY,a.midsrcArrowAngle,t),n||this.drawArrowhead(r,e,"target",a.arrowEndX,a.arrowEndY,a.tgtArrowAngle,t)};Ur.drawArrowhead=function(r,e,t,a,n,i,s){if(!(isNaN(a)||a==null||isNaN(n)||n==null||isNaN(i)||i==null)){var o=this,l=e.pstyle(t+"-arrow-shape").value;if(l!=="none"){var u=e.pstyle(t+"-arrow-fill").value==="hollow"?"both":"filled",v=e.pstyle(t+"-arrow-fill").value,f=e.pstyle("width").pfValue,c=e.pstyle(t+"-arrow-width"),h=c.value==="match-line"?f:c.pfValue;c.units==="%"&&(h*=f);var d=e.pstyle("opacity").value;s===void 0&&(s=d);var y=r.globalCompositeOperation;(s!==1||v==="hollow")&&(r.globalCompositeOperation="destination-out",o.colorFillStyle(r,255,255,255,1),o.colorStrokeStyle(r,255,255,255,1),o.drawArrowShape(e,r,u,f,l,h,a,n,i),r.globalCompositeOperation=y);var p=e.pstyle(t+"-arrow-color").value;o.colorFillStyle(r,p[0],p[1],p[2],s),o.colorStrokeStyle(r,p[0],p[1],p[2],s),o.drawArrowShape(e,r,v,f,l,h,a,n,i)}}};Ur.drawArrowShape=function(r,e,t,a,n,i,s,o,l){var u=this,v=this.usePaths()&&n!=="triangle-cross",f=!1,c,h=e,d={x:s,y:o},y=r.pstyle("arrow-scale").value,p=this.getArrowWidth(a,y),g=u.arrowShapes[n];if(v){var m=u.arrowPathCache=u.arrowPathCache||[],b=dt(n),w=m[b];w!=null?(c=e=w,f=!0):(c=e=new Path2D,m[b]=c)}f||(e.beginPath&&e.beginPath(),v?g.draw(e,1,0,{x:0,y:0},1):g.draw(e,p,l,d,a),e.closePath&&e.closePath()),e=h,v&&(e.translate(s,o),e.rotate(l),e.scale(p,p)),(t==="filled"||t==="both")&&(v?e.fill(c):e.fill()),(t==="hollow"||t==="both")&&(e.lineWidth=i/(v?p:1),e.lineJoin="miter",v?e.stroke(c):e.stroke()),v&&(e.scale(1/p,1/p),e.rotate(-l),e.translate(-s,-o))};var Pi={};Pi.safeDrawImage=function(r,e,t,a,n,i,s,o,l,u){if(!(n<=0||i<=0||l<=0||u<=0))try{r.drawImage(e,t,a,n,i,s,o,l,u)}catch(v){Me(v)}};Pi.drawInscribedImage=function(r,e,t,a,n){var i=this,s=t.position(),o=s.x,l=s.y,u=t.cy().style(),v=u.getIndexedStyle.bind(u),f=v(t,"background-fit","value",a),c=v(t,"background-repeat","value",a),h=t.width(),d=t.height(),y=t.padding()*2,p=h+(v(t,"background-width-relative-to","value",a)==="inner"?0:y),g=d+(v(t,"background-height-relative-to","value",a)==="inner"?0:y),m=t._private.rscratch,b=v(t,"background-clip","value",a),w=b==="node",S=v(t,"background-image-opacity","value",a)*n,E=v(t,"background-image-smoothing","value",a),x=t.pstyle("corner-radius").value;x!=="auto"&&(x=t.pstyle("corner-radius").pfValue);var D=e.width||e.cachedW,C=e.height||e.cachedH;(D==null||C==null)&&(document.body.appendChild(e),D=e.cachedW=e.width||e.offsetWidth,C=e.cachedH=e.height||e.offsetHeight,document.body.removeChild(e));var M=D,P=C;if(v(t,"background-width","value",a)!=="auto"&&(v(t,"background-width","units",a)==="%"?M=v(t,"background-width","pfValue",a)*p:M=v(t,"background-width","pfValue",a)),v(t,"background-height","value",a)!=="auto"&&(v(t,"background-height","units",a)==="%"?P=v(t,"background-height","pfValue",a)*g:P=v(t,"background-height","pfValue",a)),!(M===0||P===0)){if(f==="contain"){var B=Math.min(p/M,g/P);M*=B,P*=B}else if(f==="cover"){var B=Math.max(p/M,g/P);M*=B,P*=B}var L=o-p/2,k=v(t,"background-position-x","units",a),O=v(t,"background-position-x","pfValue",a);k==="%"?L+=(p-M)*O:L+=O;var A=v(t,"background-offset-x","units",a),R=v(t,"background-offset-x","pfValue",a);A==="%"?L+=(p-M)*R:L+=R;var I=l-g/2,V=v(t,"background-position-y","units",a),G=v(t,"background-position-y","pfValue",a);V==="%"?I+=(g-P)*G:I+=G;var F=v(t,"background-offset-y","units",a),q=v(t,"background-offset-y","pfValue",a);F==="%"?I+=(g-P)*q:I+=q,m.pathCache&&(L-=o,I-=l,o=0,l=0);var Y=r.globalAlpha;r.globalAlpha=S;var Q=i.getImgSmoothing(r),J=!1;if(E==="no"&&Q?(i.setImgSmoothing(r,!1),J=!0):E==="yes"&&!Q&&(i.setImgSmoothing(r,!0),J=!0),c==="no-repeat")w&&(r.save(),m.pathCache?r.clip(m.pathCache):(i.nodeShapes[i.getNodeShape(t)].draw(r,o,l,p,g,x,m),r.clip())),i.safeDrawImage(r,e,0,0,D,C,L,I,M,P),w&&r.restore();else{var _=r.createPattern(e,c);r.fillStyle=_,i.nodeShapes[i.getNodeShape(t)].draw(r,o,l,p,g,x,m),r.translate(L,I),r.fill(),r.translate(-L,-I)}r.globalAlpha=Y,J&&i.setImgSmoothing(r,Q)}};var wt={};wt.eleTextBiggerThanMin=function(r,e){if(!e){var t=r.cy().zoom(),a=this.getPixelRatio(),n=Math.ceil(ci(t*a));e=Math.pow(2,n)}var i=r.pstyle("font-size").pfValue*e,s=r.pstyle("min-zoomed-font-size").pfValue;return!(i<s)};wt.drawElementText=function(r,e,t,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this;if(a==null){if(i&&!s.eleTextBiggerThanMin(e))return}else if(a===!1)return;if(e.isNode()){var o=e.pstyle("label");if(!o||!o.value)return;var l=s.getLabelJustification(e);r.textAlign=l,r.textBaseline="bottom"}else{var u=e.element()._private.rscratch.badLine,v=e.pstyle("label"),f=e.pstyle("source-label"),c=e.pstyle("target-label");if(u||(!v||!v.value)&&(!f||!f.value)&&(!c||!c.value))return;r.textAlign="center",r.textBaseline="bottom"}var h=!t,d;t&&(d=t,r.translate(-d.x1,-d.y1)),n==null?(s.drawText(r,e,null,h,i),e.isEdge()&&(s.drawText(r,e,"source",h,i),s.drawText(r,e,"target",h,i))):s.drawText(r,e,n,h,i),t&&r.translate(d.x1,d.y1)};wt.getFontCache=function(r){var e;this.fontCaches=this.fontCaches||[];for(var t=0;t<this.fontCaches.length;t++)if(e=this.fontCaches[t],e.context===r)return e;return e={context:r},this.fontCaches.push(e),e};wt.setupTextStyle=function(r,e){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,a=e.pstyle("font-style").strValue,n=e.pstyle("font-size").pfValue+"px",i=e.pstyle("font-family").strValue,s=e.pstyle("font-weight").strValue,o=t?e.effectiveOpacity()*e.pstyle("text-opacity").value:1,l=e.pstyle("text-outline-opacity").value*o,u=e.pstyle("color").value,v=e.pstyle("text-outline-color").value;r.font=a+" "+s+" "+n+" "+i,r.lineJoin="round",this.colorFillStyle(r,u[0],u[1],u[2],o),this.colorStrokeStyle(r,v[0],v[1],v[2],l)};function Hn(r,e,t,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:5,s=arguments.length>6?arguments[6]:void 0;r.beginPath(),r.moveTo(e+i,t),r.lineTo(e+a-i,t),r.quadraticCurveTo(e+a,t,e+a,t+i),r.lineTo(e+a,t+n-i),r.quadraticCurveTo(e+a,t+n,e+a-i,t+n),r.lineTo(e+i,t+n),r.quadraticCurveTo(e,t+n,e,t+n-i),r.lineTo(e,t+i),r.quadraticCurveTo(e,t,e+i,t),r.closePath(),s?r.stroke():r.fill()}wt.getTextAngle=function(r,e){var t,a=r._private,n=a.rscratch,i=e?e+"-":"",s=r.pstyle(i+"text-rotation"),o=kr(n,"labelAngle",e);return s.strValue==="autorotate"?t=r.isEdge()?o:0:s.strValue==="none"?t=0:t=s.pfValue,t};wt.drawText=function(r,e,t){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=e._private,s=i.rscratch,o=n?e.effectiveOpacity():1;if(!(n&&(o===0||e.pstyle("text-opacity").value===0))){t==="main"&&(t=null);var l=kr(s,"labelX",t),u=kr(s,"labelY",t),v,f,c=this.getLabelText(e,t);if(c!=null&&c!==""&&!isNaN(l)&&!isNaN(u)){this.setupTextStyle(r,e,n);var h=t?t+"-":"",d=kr(s,"labelWidth",t),y=kr(s,"labelHeight",t),p=e.pstyle(h+"text-margin-x").pfValue,g=e.pstyle(h+"text-margin-y").pfValue,m=e.isEdge(),b=e.pstyle("text-halign").value,w=e.pstyle("text-valign").value;m&&(b="center",w="center"),l+=p,u+=g;var S;switch(a?S=this.getTextAngle(e,t):S=0,S!==0&&(v=l,f=u,r.translate(v,f),r.rotate(S),l=0,u=0),w){case"top":break;case"center":u+=y/2;break;case"bottom":u+=y;break}var E=e.pstyle("text-background-opacity").value,x=e.pstyle("text-border-opacity").value,D=e.pstyle("text-border-width").pfValue,C=e.pstyle("text-background-padding").pfValue,M=e.pstyle("text-background-shape").strValue,P=M.indexOf("round")===0,B=2;if(E>0||D>0&&x>0){var L=l-C;switch(b){case"left":L-=d;break;case"center":L-=d/2;break}var k=u-y-C,O=d+2*C,A=y+2*C;if(E>0){var R=r.fillStyle,I=e.pstyle("text-background-color").value;r.fillStyle="rgba("+I[0]+","+I[1]+","+I[2]+","+E*o+")",P?Hn(r,L,k,O,A,B):r.fillRect(L,k,O,A),r.fillStyle=R}if(D>0&&x>0){var V=r.strokeStyle,G=r.lineWidth,F=e.pstyle("text-border-color").value,q=e.pstyle("text-border-style").value;if(r.strokeStyle="rgba("+F[0]+","+F[1]+","+F[2]+","+x*o+")",r.lineWidth=D,r.setLineDash)switch(q){case"dotted":r.setLineDash([1,1]);break;case"dashed":r.setLineDash([4,2]);break;case"double":r.lineWidth=D/4,r.setLineDash([]);break;case"solid":r.setLineDash([]);break}if(P?Hn(r,L,k,O,A,B,"stroke"):r.strokeRect(L,k,O,A),q==="double"){var Y=D/2;P?Hn(r,L+Y,k+Y,O-Y*2,A-Y*2,B,"stroke"):r.strokeRect(L+Y,k+Y,O-Y*2,A-Y*2)}r.setLineDash&&r.setLineDash([]),r.lineWidth=G,r.strokeStyle=V}}var Q=2*e.pstyle("text-outline-width").pfValue;if(Q>0&&(r.lineWidth=Q),e.pstyle("text-wrap").value==="wrap"){var J=kr(s,"labelWrapCachedLines",t),_=kr(s,"labelLineHeight",t),j=d/2,W=this.getLabelJustification(e);switch(W==="auto"||(b==="left"?W==="left"?l+=-d:W==="center"&&(l+=-j):b==="center"?W==="left"?l+=-j:W==="right"&&(l+=j):b==="right"&&(W==="center"?l+=j:W==="right"&&(l+=d))),w){case"top":u-=(J.length-1)*_;break;case"center":case"bottom":u-=(J.length-1)*_;break}for(var z=0;z<J.length;z++)Q>0&&r.strokeText(J[z],l,u),r.fillText(J[z],l,u),u+=_}else Q>0&&r.strokeText(c,l,u),r.fillText(c,l,u);S!==0&&(r.rotate(-S),r.translate(-v,-f))}}};var Xt={};Xt.drawNode=function(r,e,t){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o,l,u=e._private,v=u.rscratch,f=e.position();if(!(!ne(f.x)||!ne(f.y))&&!(i&&!e.visible())){var c=i?e.effectiveOpacity():1,h=s.usePaths(),d,y=!1,p=e.padding();o=e.width()+2*p,l=e.height()+2*p;var g;t&&(g=t,r.translate(-g.x1,-g.y1));for(var m=e.pstyle("background-image"),b=m.value,w=new Array(b.length),S=new Array(b.length),E=0,x=0;x<b.length;x++){var D=b[x],C=w[x]=D!=null&&D!=="none";if(C){var M=e.cy().style().getIndexedStyle(e,"background-image-crossorigin","value",x);E++,S[x]=s.getCachedImage(D,M,function(){u.backgroundTimestamp=Date.now(),e.emitAndNotify("background")})}}var P=e.pstyle("background-blacken").value,B=e.pstyle("border-width").pfValue,L=e.pstyle("background-opacity").value*c,k=e.pstyle("border-color").value,O=e.pstyle("border-style").value,A=e.pstyle("border-join").value,R=e.pstyle("border-cap").value,I=e.pstyle("border-position").value,V=e.pstyle("border-dash-pattern").pfValue,G=e.pstyle("border-dash-offset").pfValue,F=e.pstyle("border-opacity").value*c,q=e.pstyle("outline-width").pfValue,Y=e.pstyle("outline-color").value,Q=e.pstyle("outline-style").value,J=e.pstyle("outline-opacity").value*c,_=e.pstyle("outline-offset").value,j=e.pstyle("corner-radius").value;j!=="auto"&&(j=e.pstyle("corner-radius").pfValue);var W=function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:L;s.eleFillStyle(r,e,N)},z=function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:F;s.colorStrokeStyle(r,k[0],k[1],k[2],N)},K=function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:J;s.colorStrokeStyle(r,Y[0],Y[1],Y[2],N)},X=function(N,H,ie,Z){var se=s.nodePathCache=s.nodePathCache||[],ge=uo(ie==="polygon"?ie+","+Z.join(","):ie,""+H,""+N,""+j),we=se[ge],fe,me=!1;return we!=null?(fe=we,me=!0,v.pathCache=fe):(fe=new Path2D,se[ge]=v.pathCache=fe),{path:fe,cacheHit:me}},ae=e.pstyle("shape").strValue,he=e.pstyle("shape-polygon-points").pfValue;if(h){r.translate(f.x,f.y);var te=X(o,l,ae,he);d=te.path,y=te.cacheHit}var re=function(){if(!y){var N=f;h&&(N={x:0,y:0}),s.nodeShapes[s.getNodeShape(e)].draw(d||r,N.x,N.y,o,l,j,v)}h?r.fill(d):r.fill()},ve=function(){for(var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,ie=u.backgrounding,Z=0,se=0;se<S.length;se++){var ge=e.cy().style().getIndexedStyle(e,"background-image-containment","value",se);if(H&&ge==="over"||!H&&ge==="inside"){Z++;continue}w[se]&&S[se].complete&&!S[se].error&&(Z++,s.drawInscribedImage(r,S[se],e,se,N))}u.backgrounding=Z!==E,ie!==u.backgrounding&&e.updateStyle(!1)},le=function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:c;s.hasPie(e)&&(s.drawPie(r,e,H),N&&(h||s.nodeShapes[s.getNodeShape(e)].draw(r,f.x,f.y,o,l,j,v)))},oe=function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,H=(P>0?P:-P)*N,ie=P>0?0:255;P!==0&&(s.colorFillStyle(r,ie,ie,ie,H),h?r.fill(d):r.fill())},de=function(){if(B>0){if(r.lineWidth=B,r.lineCap=R,r.lineJoin=A,r.setLineDash)switch(O){case"dotted":r.setLineDash([1,1]);break;case"dashed":r.setLineDash(V),r.lineDashOffset=G;break;case"solid":case"double":r.setLineDash([]);break}if(I!=="center"){if(r.save(),r.lineWidth*=2,I==="inside")h?r.clip(d):r.clip();else{var N=new Path2D;N.rect(-o/2-B,-l/2-B,o+2*B,l+2*B),N.addPath(d),r.clip(N,"evenodd")}h?r.stroke(d):r.stroke(),r.restore()}else h?r.stroke(d):r.stroke();if(O==="double"){r.lineWidth=B/3;var H=r.globalCompositeOperation;r.globalCompositeOperation="destination-out",h?r.stroke(d):r.stroke(),r.globalCompositeOperation=H}r.setLineDash&&r.setLineDash([])}},Le=function(){if(q>0){if(r.lineWidth=q,r.lineCap="butt",r.setLineDash)switch(Q){case"dotted":r.setLineDash([1,1]);break;case"dashed":r.setLineDash([4,2]);break;case"solid":case"double":r.setLineDash([]);break}var N=f;h&&(N={x:0,y:0});var H=s.getNodeShape(e),ie=B;I==="inside"&&(ie=0),I==="outside"&&(ie*=2);var Z=(o+ie+(q+_))/o,se=(l+ie+(q+_))/l,ge=o*Z,we=l*se,fe=s.nodeShapes[H].points,me;if(h){var be=X(ge,we,H,fe);me=be.path}if(H==="ellipse")s.drawEllipsePath(me||r,N.x,N.y,ge,we);else if(["round-diamond","round-heptagon","round-hexagon","round-octagon","round-pentagon","round-polygon","round-triangle","round-tag"].includes(H)){var ye=0,Ie=0,Ve=0;H==="round-diamond"?ye=(ie+_+q)*1.4:H==="round-heptagon"?(ye=(ie+_+q)*1.075,Ve=-(ie/2+_+q)/35):H==="round-hexagon"?ye=(ie+_+q)*1.12:H==="round-pentagon"?(ye=(ie+_+q)*1.13,Ve=-(ie/2+_+q)/15):H==="round-tag"?(ye=(ie+_+q)*1.12,Ie=(ie/2+q+_)*.07):H==="round-triangle"&&(ye=(ie+_+q)*(Math.PI/2),Ve=-(ie+_/2+q)/Math.PI),ye!==0&&(Z=(o+ye)/o,ge=o*Z,["round-hexagon","round-tag"].includes(H)||(se=(l+ye)/l,we=l*se)),j=j==="auto"?wo(ge,we):j;for(var gr=ge/2,pr=we/2,Ge=j+(ie+q+_)/2,Xe=new Array(fe.length/2),Ze=new Array(fe.length/2),lr=0;lr<fe.length/2;lr++)Xe[lr]={x:N.x+Ie+gr*fe[lr*2],y:N.y+Ve+pr*fe[lr*2+1]};var vr,Br,br,Mr,Vr=Xe.length;for(Br=Xe[Vr-1],vr=0;vr<Vr;vr++)br=Xe[vr%Vr],Mr=Xe[(vr+1)%Vr],Ze[vr]=Di(Br,br,Mr,Ge),Br=br,br=Mr;s.drawRoundPolygonPath(me||r,N.x+Ie,N.y+Ve,o*Z,l*se,fe,Ze)}else if(["roundrectangle","round-rectangle"].includes(H))j=j==="auto"?gt(ge,we):j,s.drawRoundRectanglePath(me||r,N.x,N.y,ge,we,j+(ie+q+_)/2);else if(["cutrectangle","cut-rectangle"].includes(H))j=j==="auto"?hi():j,s.drawCutRectanglePath(me||r,N.x,N.y,ge,we,null,j+(ie+q+_)/4);else if(["bottomroundrectangle","bottom-round-rectangle"].includes(H))j=j==="auto"?gt(ge,we):j,s.drawBottomRoundRectanglePath(me||r,N.x,N.y,ge,we,j+(ie+q+_)/2);else if(H==="barrel")s.drawBarrelPath(me||r,N.x,N.y,ge,we);else if(H.startsWith("polygon")||["rhomboid","right-rhomboid","round-tag","tag","vee"].includes(H)){var qr=(ie+q+_)/o;fe=rn(tn(fe,qr)),s.drawPolygonPath(me||r,N.x,N.y,o,l,fe)}else{var ar=(ie+q+_)/o;fe=rn(tn(fe,-ar)),s.drawPolygonPath(me||r,N.x,N.y,o,l,fe)}if(h?r.stroke(me):r.stroke(),Q==="double"){r.lineWidth=ie/3;var Qe=r.globalCompositeOperation;r.globalCompositeOperation="destination-out",h?r.stroke(me):r.stroke(),r.globalCompositeOperation=Qe}r.setLineDash&&r.setLineDash([])}},Ce=function(){n&&s.drawNodeOverlay(r,e,f,o,l)},xe=function(){n&&s.drawNodeUnderlay(r,e,f,o,l)},Ae=function(){s.drawElementText(r,e,null,a)},Ee=e.pstyle("ghost").value==="yes";if(Ee){var Pe=e.pstyle("ghost-offset-x").pfValue,ee=e.pstyle("ghost-offset-y").pfValue,T=e.pstyle("ghost-opacity").value,$=T*c;r.translate(Pe,ee),K(),Le(),W(T*L),re(),ve($,!0),z(T*F),de(),le(P!==0||B!==0),ve($,!1),oe($),r.translate(-Pe,-ee)}h&&r.translate(-f.x,-f.y),xe(),h&&r.translate(f.x,f.y),K(),Le(),W(),re(),ve(c,!0),z(),de(),le(P!==0||B!==0),ve(c,!1),oe(),h&&r.translate(-f.x,-f.y),Ae(),Ce(),t&&r.translate(g.x1,g.y1)}};var Cu=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,a,n,i,s){var o=this;if(a.visible()){var l=a.pstyle("".concat(e,"-padding")).pfValue,u=a.pstyle("".concat(e,"-opacity")).value,v=a.pstyle("".concat(e,"-color")).value,f=a.pstyle("".concat(e,"-shape")).value,c=a.pstyle("".concat(e,"-corner-radius")).value;if(u>0){if(n=n||a.position(),i==null||s==null){var h=a.padding();i=a.width()+2*h,s=a.height()+2*h}o.colorFillStyle(t,v[0],v[1],v[2],u),o.nodeShapes[f].draw(t,n.x,n.y,i+l*2,s+l*2,c),t.fill()}}}};Xt.drawNodeOverlay=Cu("overlay");Xt.drawNodeUnderlay=Cu("underlay");Xt.hasPie=function(r){return r=r[0],r._private.hasPie};Xt.drawPie=function(r,e,t,a){e=e[0],a=a||e.position();var n=e.cy().style(),i=e.pstyle("pie-size"),s=a.x,o=a.y,l=e.width(),u=e.height(),v=Math.min(l,u)/2,f=0,c=this.usePaths();c&&(s=0,o=0),i.units==="%"?v=v*i.pfValue:i.pfValue!==void 0&&(v=i.pfValue/2);for(var h=1;h<=n.pieBackgroundN;h++){var d=e.pstyle("pie-"+h+"-background-size").value,y=e.pstyle("pie-"+h+"-background-color").value,p=e.pstyle("pie-"+h+"-background-opacity").value*t,g=d/100;g+f>1&&(g=1-f);var m=1.5*Math.PI+2*Math.PI*f,b=2*Math.PI*g,w=m+b;d===0||f>=1||f+g>1||(r.beginPath(),r.moveTo(s,o),r.arc(s,o,v,m,w),r.closePath(),this.colorFillStyle(r,y[0],y[1],y[2],p),r.fill(),f+=g)}};var mr={},dp=100;mr.getPixelRatio=function(){var r=this.data.contexts[0];if(this.forcedPixelRatio!=null)return this.forcedPixelRatio;var e=this.cy.window(),t=r.backingStorePixelRatio||r.webkitBackingStorePixelRatio||r.mozBackingStorePixelRatio||r.msBackingStorePixelRatio||r.oBackingStorePixelRatio||r.backingStorePixelRatio||1;return(e.devicePixelRatio||1)/t};mr.paintCache=function(r){for(var e=this.paintCaches=this.paintCaches||[],t=!0,a,n=0;n<e.length;n++)if(a=e[n],a.context===r){t=!1;break}return t&&(a={context:r},e.push(a)),a};mr.createGradientStyleFor=function(r,e,t,a,n){var i,s=this.usePaths(),o=t.pstyle(e+"-gradient-stop-colors").value,l=t.pstyle(e+"-gradient-stop-positions").pfValue;if(a==="radial-gradient")if(t.isEdge()){var u=t.sourceEndpoint(),v=t.targetEndpoint(),f=t.midpoint(),c=ht(u,f),h=ht(v,f);i=r.createRadialGradient(f.x,f.y,0,f.x,f.y,Math.max(c,h))}else{var d=s?{x:0,y:0}:t.position(),y=t.paddedWidth(),p=t.paddedHeight();i=r.createRadialGradient(d.x,d.y,0,d.x,d.y,Math.max(y,p))}else if(t.isEdge()){var g=t.sourceEndpoint(),m=t.targetEndpoint();i=r.createLinearGradient(g.x,g.y,m.x,m.y)}else{var b=s?{x:0,y:0}:t.position(),w=t.paddedWidth(),S=t.paddedHeight(),E=w/2,x=S/2,D=t.pstyle("background-gradient-direction").value;switch(D){case"to-bottom":i=r.createLinearGradient(b.x,b.y-x,b.x,b.y+x);break;case"to-top":i=r.createLinearGradient(b.x,b.y+x,b.x,b.y-x);break;case"to-left":i=r.createLinearGradient(b.x+E,b.y,b.x-E,b.y);break;case"to-right":i=r.createLinearGradient(b.x-E,b.y,b.x+E,b.y);break;case"to-bottom-right":case"to-right-bottom":i=r.createLinearGradient(b.x-E,b.y-x,b.x+E,b.y+x);break;case"to-top-right":case"to-right-top":i=r.createLinearGradient(b.x-E,b.y+x,b.x+E,b.y-x);break;case"to-bottom-left":case"to-left-bottom":i=r.createLinearGradient(b.x+E,b.y-x,b.x-E,b.y+x);break;case"to-top-left":case"to-left-top":i=r.createLinearGradient(b.x+E,b.y+x,b.x-E,b.y-x);break}}if(!i)return null;for(var C=l.length===o.length,M=o.length,P=0;P<M;P++)i.addColorStop(C?l[P]:P/(M-1),"rgba("+o[P][0]+","+o[P][1]+","+o[P][2]+","+n+")");return i};mr.gradientFillStyle=function(r,e,t,a){var n=this.createGradientStyleFor(r,"background",e,t,a);if(!n)return null;r.fillStyle=n};mr.colorFillStyle=function(r,e,t,a,n){r.fillStyle="rgba("+e+","+t+","+a+","+n+")"};mr.eleFillStyle=function(r,e,t){var a=e.pstyle("background-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientFillStyle(r,e,a,t);else{var n=e.pstyle("background-color").value;this.colorFillStyle(r,n[0],n[1],n[2],t)}};mr.gradientStrokeStyle=function(r,e,t,a){var n=this.createGradientStyleFor(r,"line",e,t,a);if(!n)return null;r.strokeStyle=n};mr.colorStrokeStyle=function(r,e,t,a,n){r.strokeStyle="rgba("+e+","+t+","+a+","+n+")"};mr.eleStrokeStyle=function(r,e,t){var a=e.pstyle("line-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientStrokeStyle(r,e,a,t);else{var n=e.pstyle("line-color").value;this.colorStrokeStyle(r,n[0],n[1],n[2],t)}};mr.matchCanvasSize=function(r){var e=this,t=e.data,a=e.findContainerClientCoords(),n=a[2],i=a[3],s=e.getPixelRatio(),o=e.motionBlurPxRatio;(r===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE]||r===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG])&&(s=o);var l=n*s,u=i*s,v;if(!(l===e.canvasWidth&&u===e.canvasHeight)){e.fontCaches=null;var f=t.canvasContainer;f.style.width=n+"px",f.style.height=i+"px";for(var c=0;c<e.CANVAS_LAYERS;c++)v=t.canvases[c],v.width=l,v.height=u,v.style.width=n+"px",v.style.height=i+"px";for(var c=0;c<e.BUFFER_COUNT;c++)v=t.bufferCanvases[c],v.width=l,v.height=u,v.style.width=n+"px",v.style.height=i+"px";e.textureMult=1,s<=1&&(v=t.bufferCanvases[e.TEXTURE_BUFFER],e.textureMult=2,v.width=l*e.textureMult,v.height=u*e.textureMult),e.canvasWidth=l,e.canvasHeight=u}};mr.renderTo=function(r,e,t,a){this.render({forcedContext:r,forcedZoom:e,forcedPan:t,drawAllLayers:!0,forcedPxRatio:a})};mr.render=function(r){r=r||co();var e=r.forcedContext,t=r.drawAllLayers,a=r.drawOnlyNodeLayer,n=r.forcedZoom,i=r.forcedPan,s=this,o=r.forcedPxRatio===void 0?this.getPixelRatio():r.forcedPxRatio,l=s.cy,u=s.data,v=u.canvasNeedsRedraw,f=s.textureOnViewport&&!e&&(s.pinching||s.hoverData.dragging||s.swipePanning||s.data.wheelZooming),c=r.motionBlur!==void 0?r.motionBlur:s.motionBlur,h=s.motionBlurPxRatio,d=l.hasCompoundNodes(),y=s.hoverData.draggingEles,p=!!(s.hoverData.selecting||s.touchData.selecting);c=c&&!e&&s.motionBlurEnabled&&!p;var g=c;e||(s.prevPxRatio!==o&&(s.invalidateContainerClientCoordsCache(),s.matchCanvasSize(s.container),s.redrawHint("eles",!0),s.redrawHint("drag",!0)),s.prevPxRatio=o),!e&&s.motionBlurTimeout&&clearTimeout(s.motionBlurTimeout),c&&(s.mbFrames==null&&(s.mbFrames=0),s.mbFrames++,s.mbFrames<3&&(g=!1),s.mbFrames>s.minMbLowQualFrames&&(s.motionBlurPxRatio=s.mbPxRBlurry)),s.clearingMotionBlur&&(s.motionBlurPxRatio=1),s.textureDrawLastFrame&&!f&&(v[s.NODE]=!0,v[s.SELECT_BOX]=!0);var m=l.style(),b=l.zoom(),w=n!==void 0?n:b,S=l.pan(),E={x:S.x,y:S.y},x={zoom:b,pan:{x:S.x,y:S.y}},D=s.prevViewport,C=D===void 0||x.zoom!==D.zoom||x.pan.x!==D.pan.x||x.pan.y!==D.pan.y;!C&&!(y&&!d)&&(s.motionBlurPxRatio=1),i&&(E=i),w*=o,E.x*=o,E.y*=o;var M=s.getCachedZSortedEles();function P(te,re,ve,le,oe){var de=te.globalCompositeOperation;te.globalCompositeOperation="destination-out",s.colorFillStyle(te,255,255,255,s.motionBlurTransparency),te.fillRect(re,ve,le,oe),te.globalCompositeOperation=de}function B(te,re){var ve,le,oe,de;!s.clearingMotionBlur&&(te===u.bufferContexts[s.MOTIONBLUR_BUFFER_NODE]||te===u.bufferContexts[s.MOTIONBLUR_BUFFER_DRAG])?(ve={x:S.x*h,y:S.y*h},le=b*h,oe=s.canvasWidth*h,de=s.canvasHeight*h):(ve=E,le=w,oe=s.canvasWidth,de=s.canvasHeight),te.setTransform(1,0,0,1,0,0),re==="motionBlur"?P(te,0,0,oe,de):!e&&(re===void 0||re)&&te.clearRect(0,0,oe,de),t||(te.translate(ve.x,ve.y),te.scale(le,le)),i&&te.translate(i.x,i.y),n&&te.scale(n,n)}if(f||(s.textureDrawLastFrame=!1),f){if(s.textureDrawLastFrame=!0,!s.textureCache){s.textureCache={},s.textureCache.bb=l.mutableElements().boundingBox(),s.textureCache.texture=s.data.bufferCanvases[s.TEXTURE_BUFFER];var L=s.data.bufferContexts[s.TEXTURE_BUFFER];L.setTransform(1,0,0,1,0,0),L.clearRect(0,0,s.canvasWidth*s.textureMult,s.canvasHeight*s.textureMult),s.render({forcedContext:L,drawOnlyNodeLayer:!0,forcedPxRatio:o*s.textureMult});var x=s.textureCache.viewport={zoom:l.zoom(),pan:l.pan(),width:s.canvasWidth,height:s.canvasHeight};x.mpan={x:(0-x.pan.x)/x.zoom,y:(0-x.pan.y)/x.zoom}}v[s.DRAG]=!1,v[s.NODE]=!1;var k=u.contexts[s.NODE],O=s.textureCache.texture,x=s.textureCache.viewport;k.setTransform(1,0,0,1,0,0),c?P(k,0,0,x.width,x.height):k.clearRect(0,0,x.width,x.height);var A=m.core("outside-texture-bg-color").value,R=m.core("outside-texture-bg-opacity").value;s.colorFillStyle(k,A[0],A[1],A[2],R),k.fillRect(0,0,x.width,x.height);var b=l.zoom();B(k,!1),k.clearRect(x.mpan.x,x.mpan.y,x.width/x.zoom/o,x.height/x.zoom/o),k.drawImage(O,x.mpan.x,x.mpan.y,x.width/x.zoom/o,x.height/x.zoom/o)}else s.textureOnViewport&&!e&&(s.textureCache=null);var I=l.extent(),V=s.pinching||s.hoverData.dragging||s.swipePanning||s.data.wheelZooming||s.hoverData.draggingEles||s.cy.animated(),G=s.hideEdgesOnViewport&&V,F=[];if(F[s.NODE]=!v[s.NODE]&&c&&!s.clearedForMotionBlur[s.NODE]||s.clearingMotionBlur,F[s.NODE]&&(s.clearedForMotionBlur[s.NODE]=!0),F[s.DRAG]=!v[s.DRAG]&&c&&!s.clearedForMotionBlur[s.DRAG]||s.clearingMotionBlur,F[s.DRAG]&&(s.clearedForMotionBlur[s.DRAG]=!0),v[s.NODE]||t||a||F[s.NODE]){var q=c&&!F[s.NODE]&&h!==1,k=e||(q?s.data.bufferContexts[s.MOTIONBLUR_BUFFER_NODE]:u.contexts[s.NODE]),Y=c&&!q?"motionBlur":void 0;B(k,Y),G?s.drawCachedNodes(k,M.nondrag,o,I):s.drawLayeredElements(k,M.nondrag,o,I),s.debug&&s.drawDebugPoints(k,M.nondrag),!t&&!c&&(v[s.NODE]=!1)}if(!a&&(v[s.DRAG]||t||F[s.DRAG])){var q=c&&!F[s.DRAG]&&h!==1,k=e||(q?s.data.bufferContexts[s.MOTIONBLUR_BUFFER_DRAG]:u.contexts[s.DRAG]);B(k,c&&!q?"motionBlur":void 0),G?s.drawCachedNodes(k,M.drag,o,I):s.drawCachedElements(k,M.drag,o,I),s.debug&&s.drawDebugPoints(k,M.drag),!t&&!c&&(v[s.DRAG]=!1)}if(s.showFps||!a&&v[s.SELECT_BOX]&&!t){var k=e||u.contexts[s.SELECT_BOX];if(B(k),s.selection[4]==1&&(s.hoverData.selecting||s.touchData.selecting)){var b=s.cy.zoom(),Q=m.core("selection-box-border-width").value/b;k.lineWidth=Q,k.fillStyle="rgba("+m.core("selection-box-color").value[0]+","+m.core("selection-box-color").value[1]+","+m.core("selection-box-color").value[2]+","+m.core("selection-box-opacity").value+")",k.fillRect(s.selection[0],s.selection[1],s.selection[2]-s.selection[0],s.selection[3]-s.selection[1]),Q>0&&(k.strokeStyle="rgba("+m.core("selection-box-border-color").value[0]+","+m.core("selection-box-border-color").value[1]+","+m.core("selection-box-border-color").value[2]+","+m.core("selection-box-opacity").value+")",k.strokeRect(s.selection[0],s.selection[1],s.selection[2]-s.selection[0],s.selection[3]-s.selection[1]))}if(u.bgActivePosistion&&!s.hoverData.selecting){var b=s.cy.zoom(),J=u.bgActivePosistion;k.fillStyle="rgba("+m.core("active-bg-color").value[0]+","+m.core("active-bg-color").value[1]+","+m.core("active-bg-color").value[2]+","+m.core("active-bg-opacity").value+")",k.beginPath(),k.arc(J.x,J.y,m.core("active-bg-size").pfValue/b,0,2*Math.PI),k.fill()}var _=s.lastRedrawTime;if(s.showFps&&_){_=Math.round(_);var j=Math.round(1e3/_);k.setTransform(1,0,0,1,0,0),k.fillStyle="rgba(255, 0, 0, 0.75)",k.strokeStyle="rgba(255, 0, 0, 0.75)",k.lineWidth=1,k.fillText("1 frame = "+_+" ms = "+j+" fps",0,20);var W=60;k.strokeRect(0,30,250,20),k.fillRect(0,30,250*Math.min(j/W,1),20)}t||(v[s.SELECT_BOX]=!1)}if(c&&h!==1){var z=u.contexts[s.NODE],K=s.data.bufferCanvases[s.MOTIONBLUR_BUFFER_NODE],X=u.contexts[s.DRAG],ae=s.data.bufferCanvases[s.MOTIONBLUR_BUFFER_DRAG],he=function(re,ve,le){re.setTransform(1,0,0,1,0,0),le||!g?re.clearRect(0,0,s.canvasWidth,s.canvasHeight):P(re,0,0,s.canvasWidth,s.canvasHeight);var oe=h;re.drawImage(ve,0,0,s.canvasWidth*oe,s.canvasHeight*oe,0,0,s.canvasWidth,s.canvasHeight)};(v[s.NODE]||F[s.NODE])&&(he(z,K,F[s.NODE]),v[s.NODE]=!1),(v[s.DRAG]||F[s.DRAG])&&(he(X,ae,F[s.DRAG]),v[s.DRAG]=!1)}s.prevViewport=x,s.clearingMotionBlur&&(s.clearingMotionBlur=!1,s.motionBlurCleared=!0,s.motionBlur=!0),c&&(s.motionBlurTimeout=setTimeout(function(){s.motionBlurTimeout=null,s.clearedForMotionBlur[s.NODE]=!1,s.clearedForMotionBlur[s.DRAG]=!1,s.motionBlur=!1,s.clearingMotionBlur=!f,s.mbFrames=0,v[s.NODE]=!0,v[s.DRAG]=!0,s.redraw()},dp)),e||l.emit("render")};var it={};it.drawPolygonPath=function(r,e,t,a,n,i){var s=a/2,o=n/2;r.beginPath&&r.beginPath(),r.moveTo(e+s*i[0],t+o*i[1]);for(var l=1;l<i.length/2;l++)r.lineTo(e+s*i[l*2],t+o*i[l*2+1]);r.closePath()};it.drawRoundPolygonPath=function(r,e,t,a,n,i,s){s.forEach(function(o){return fu(r,o)}),r.closePath()};it.drawRoundRectanglePath=function(r,e,t,a,n,i){var s=a/2,o=n/2,l=i==="auto"?gt(a,n):Math.min(i,o,s);r.beginPath&&r.beginPath(),r.moveTo(e,t-o),r.arcTo(e+s,t-o,e+s,t,l),r.arcTo(e+s,t+o,e,t+o,l),r.arcTo(e-s,t+o,e-s,t,l),r.arcTo(e-s,t-o,e,t-o,l),r.lineTo(e,t-o),r.closePath()};it.drawBottomRoundRectanglePath=function(r,e,t,a,n,i){var s=a/2,o=n/2,l=i==="auto"?gt(a,n):i;r.beginPath&&r.beginPath(),r.moveTo(e,t-o),r.lineTo(e+s,t-o),r.lineTo(e+s,t),r.arcTo(e+s,t+o,e,t+o,l),r.arcTo(e-s,t+o,e-s,t,l),r.lineTo(e-s,t-o),r.lineTo(e,t-o),r.closePath()};it.drawCutRectanglePath=function(r,e,t,a,n,i,s){var o=a/2,l=n/2,u=s==="auto"?hi():s;r.beginPath&&r.beginPath(),r.moveTo(e-o+u,t-l),r.lineTo(e+o-u,t-l),r.lineTo(e+o,t-l+u),r.lineTo(e+o,t+l-u),r.lineTo(e+o-u,t+l),r.lineTo(e-o+u,t+l),r.lineTo(e-o,t+l-u),r.lineTo(e-o,t-l+u),r.closePath()};it.drawBarrelPath=function(r,e,t,a,n){var i=a/2,s=n/2,o=e-i,l=e+i,u=t-s,v=t+s,f=Kn(a,n),c=f.widthOffset,h=f.heightOffset,d=f.ctrlPtOffsetPct*c;r.beginPath&&r.beginPath(),r.moveTo(o,u+h),r.lineTo(o,v-h),r.quadraticCurveTo(o+d,v,o+c,v),r.lineTo(l-c,v),r.quadraticCurveTo(l-d,v,l,v-h),r.lineTo(l,u+h),r.quadraticCurveTo(l-d,u,l-c,u),r.lineTo(o+c,u),r.quadraticCurveTo(o+d,u,o,u+h),r.closePath()};var Ks=Math.sin(0),Ws=Math.cos(0),ti={},ai={},Su=Math.PI/40;for(var Dt=0*Math.PI;Dt<2*Math.PI;Dt+=Su)ti[Dt]=Math.sin(Dt),ai[Dt]=Math.cos(Dt);it.drawEllipsePath=function(r,e,t,a,n){if(r.beginPath&&r.beginPath(),r.ellipse)r.ellipse(e,t,a/2,n/2,0,0,2*Math.PI);else for(var i,s,o=a/2,l=n/2,u=0*Math.PI;u<2*Math.PI;u+=Su)i=e-o*ti[u]*Ks+o*ai[u]*Ws,s=t+l*ai[u]*Ks+l*ti[u]*Ws,u===0?r.moveTo(i,s):r.lineTo(i,s);r.closePath()};var Pa={};Pa.createBuffer=function(r,e){var t=document.createElement("canvas");return t.width=r,t.height=e,[t,t.getContext("2d")]};Pa.bufferCanvasImage=function(r){var e=this.cy,t=e.mutableElements(),a=t.boundingBox(),n=this.findContainerClientCoords(),i=r.full?Math.ceil(a.w):n[2],s=r.full?Math.ceil(a.h):n[3],o=ne(r.maxWidth)||ne(r.maxHeight),l=this.getPixelRatio(),u=1;if(r.scale!==void 0)i*=r.scale,s*=r.scale,u=r.scale;else if(o){var v=1/0,f=1/0;ne(r.maxWidth)&&(v=u*r.maxWidth/i),ne(r.maxHeight)&&(f=u*r.maxHeight/s),u=Math.min(v,f),i*=u,s*=u}o||(i*=l,s*=l,u*=l);var c=document.createElement("canvas");c.width=i,c.height=s,c.style.width=i+"px",c.style.height=s+"px";var h=c.getContext("2d");if(i>0&&s>0){h.clearRect(0,0,i,s),h.globalCompositeOperation="source-over";var d=this.getCachedZSortedEles();if(r.full)h.translate(-a.x1*u,-a.y1*u),h.scale(u,u),this.drawElements(h,d),h.scale(1/u,1/u),h.translate(a.x1*u,a.y1*u);else{var y=e.pan(),p={x:y.x*u,y:y.y*u};u*=e.zoom(),h.translate(p.x,p.y),h.scale(u,u),this.drawElements(h,d),h.scale(1/u,1/u),h.translate(-p.x,-p.y)}r.bg&&(h.globalCompositeOperation="destination-over",h.fillStyle=r.bg,h.rect(0,0,i,s),h.fill())}return c};function hp(r,e){for(var t=atob(r),a=new ArrayBuffer(t.length),n=new Uint8Array(a),i=0;i<t.length;i++)n[i]=t.charCodeAt(i);return new Blob([a],{type:e})}function Us(r){var e=r.indexOf(",");return r.substr(e+1)}function Tu(r,e,t){var a=function(){return e.toDataURL(t,r.quality)};switch(r.output){case"blob-promise":return new qt(function(n,i){try{e.toBlob(function(s){s!=null?n(s):i(new Error("`canvas.toBlob()` sent a null value in its callback"))},t,r.quality)}catch(s){i(s)}});case"blob":return hp(Us(a()),t);case"base64":return Us(a());case"base64uri":default:return a()}}Pa.png=function(r){return Tu(r,this.bufferCanvasImage(r),"image/png")};Pa.jpg=function(r){return Tu(r,this.bufferCanvasImage(r),"image/jpeg")};var Du={};Du.nodeShapeImpl=function(r,e,t,a,n,i,s,o){switch(r){case"ellipse":return this.drawEllipsePath(e,t,a,n,i);case"polygon":return this.drawPolygonPath(e,t,a,n,i,s);case"round-polygon":return this.drawRoundPolygonPath(e,t,a,n,i,s,o);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(e,t,a,n,i,o);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(e,t,a,n,i,s,o);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(e,t,a,n,i,o);case"barrel":return this.drawBarrelPath(e,t,a,n,i)}};var gp=ku,De=ku.prototype;De.CANVAS_LAYERS=3;De.SELECT_BOX=0;De.DRAG=1;De.NODE=2;De.BUFFER_COUNT=3;De.TEXTURE_BUFFER=0;De.MOTIONBLUR_BUFFER_NODE=1;De.MOTIONBLUR_BUFFER_DRAG=2;function ku(r){var e=this,t=e.cy.window(),a=t.document;e.data={canvases:new Array(De.CANVAS_LAYERS),contexts:new Array(De.CANVAS_LAYERS),canvasNeedsRedraw:new Array(De.CANVAS_LAYERS),bufferCanvases:new Array(De.BUFFER_COUNT),bufferContexts:new Array(De.CANVAS_LAYERS)};var n="-webkit-tap-highlight-color",i="rgba(0,0,0,0)";e.data.canvasContainer=a.createElement("div");var s=e.data.canvasContainer.style;e.data.canvasContainer.style[n]=i,s.position="relative",s.zIndex="0",s.overflow="hidden";var o=r.cy.container();o.appendChild(e.data.canvasContainer),o.style[n]=i;var l={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};rl()&&(l["-ms-touch-action"]="none",l["touch-action"]="none");for(var u=0;u<De.CANVAS_LAYERS;u++){var v=e.data.canvases[u]=a.createElement("canvas");e.data.contexts[u]=v.getContext("2d"),Object.keys(l).forEach(function(W){v.style[W]=l[W]}),v.style.position="absolute",v.setAttribute("data-id","layer"+u),v.style.zIndex=String(De.CANVAS_LAYERS-u),e.data.canvasContainer.appendChild(v),e.data.canvasNeedsRedraw[u]=!1}e.data.topCanvas=e.data.canvases[0],e.data.canvases[De.NODE].setAttribute("data-id","layer"+De.NODE+"-node"),e.data.canvases[De.SELECT_BOX].setAttribute("data-id","layer"+De.SELECT_BOX+"-selectbox"),e.data.canvases[De.DRAG].setAttribute("data-id","layer"+De.DRAG+"-drag");for(var u=0;u<De.BUFFER_COUNT;u++)e.data.bufferCanvases[u]=a.createElement("canvas"),e.data.bufferContexts[u]=e.data.bufferCanvases[u].getContext("2d"),e.data.bufferCanvases[u].style.position="absolute",e.data.bufferCanvases[u].setAttribute("data-id","buffer"+u),e.data.bufferCanvases[u].style.zIndex=String(-u-1),e.data.bufferCanvases[u].style.visibility="hidden";e.pathsEnabled=!0;var f=hr(),c=function(z){return{x:(z.x1+z.x2)/2,y:(z.y1+z.y2)/2}},h=function(z){return{x:-z.w/2,y:-z.h/2}},d=function(z){var K=z[0]._private,X=K.oldBackgroundTimestamp===K.backgroundTimestamp;return!X},y=function(z){return z[0]._private.nodeKey},p=function(z){return z[0]._private.labelStyleKey},g=function(z){return z[0]._private.sourceLabelStyleKey},m=function(z){return z[0]._private.targetLabelStyleKey},b=function(z,K,X,ae,he){return e.drawElement(z,K,X,!1,!1,he)},w=function(z,K,X,ae,he){return e.drawElementText(z,K,X,ae,"main",he)},S=function(z,K,X,ae,he){return e.drawElementText(z,K,X,ae,"source",he)},E=function(z,K,X,ae,he){return e.drawElementText(z,K,X,ae,"target",he)},x=function(z){return z.boundingBox(),z[0]._private.bodyBounds},D=function(z){return z.boundingBox(),z[0]._private.labelBounds.main||f},C=function(z){return z.boundingBox(),z[0]._private.labelBounds.source||f},M=function(z){return z.boundingBox(),z[0]._private.labelBounds.target||f},P=function(z,K){return K},B=function(z){return c(x(z))},L=function(z,K,X){var ae=z?z+"-":"";return{x:K.x+X.pstyle(ae+"text-margin-x").pfValue,y:K.y+X.pstyle(ae+"text-margin-y").pfValue}},k=function(z,K,X){var ae=z[0]._private.rscratch;return{x:ae[K],y:ae[X]}},O=function(z){return L("",k(z,"labelX","labelY"),z)},A=function(z){return L("source",k(z,"sourceLabelX","sourceLabelY"),z)},R=function(z){return L("target",k(z,"targetLabelX","targetLabelY"),z)},I=function(z){return h(x(z))},V=function(z){return h(C(z))},G=function(z){return h(M(z))},F=function(z){var K=D(z),X=h(D(z));if(z.isNode()){switch(z.pstyle("text-halign").value){case"left":X.x=-K.w-(K.leftPad||0);break;case"right":X.x=-(K.rightPad||0);break}switch(z.pstyle("text-valign").value){case"top":X.y=-K.h-(K.topPad||0);break;case"bottom":X.y=-(K.botPad||0);break}}return X},q=e.data.eleTxrCache=new sa(e,{getKey:y,doesEleInvalidateKey:d,drawElement:b,getBoundingBox:x,getRotationPoint:B,getRotationOffset:I,allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),Y=e.data.lblTxrCache=new sa(e,{getKey:p,drawElement:w,getBoundingBox:D,getRotationPoint:O,getRotationOffset:F,isVisible:P}),Q=e.data.slbTxrCache=new sa(e,{getKey:g,drawElement:S,getBoundingBox:C,getRotationPoint:A,getRotationOffset:V,isVisible:P}),J=e.data.tlbTxrCache=new sa(e,{getKey:m,drawElement:E,getBoundingBox:M,getRotationPoint:R,getRotationOffset:G,isVisible:P}),_=e.data.lyrTxrCache=new wu(e);e.onUpdateEleCalcs(function(z,K){q.invalidateElements(K),Y.invalidateElements(K),Q.invalidateElements(K),J.invalidateElements(K),_.invalidateElements(K);for(var X=0;X<K.length;X++){var ae=K[X]._private;ae.oldBackgroundTimestamp=ae.backgroundTimestamp}});var j=function(z){for(var K=0;K<z.length;K++)_.enqueueElementRefinement(z[K].ele)};q.onDequeue(j),Y.onDequeue(j),Q.onDequeue(j),J.onDequeue(j)}De.redrawHint=function(r,e){var t=this;switch(r){case"eles":t.data.canvasNeedsRedraw[De.NODE]=e;break;case"drag":t.data.canvasNeedsRedraw[De.DRAG]=e;break;case"select":t.data.canvasNeedsRedraw[De.SELECT_BOX]=e;break}};var pp=typeof Path2D<"u";De.path2dEnabled=function(r){if(r===void 0)return this.pathsEnabled;this.pathsEnabled=!!r};De.usePaths=function(){return pp&&this.pathsEnabled};De.setImgSmoothing=function(r,e){r.imageSmoothingEnabled!=null?r.imageSmoothingEnabled=e:(r.webkitImageSmoothingEnabled=e,r.mozImageSmoothingEnabled=e,r.msImageSmoothingEnabled=e)};De.getImgSmoothing=function(r){return r.imageSmoothingEnabled!=null?r.imageSmoothingEnabled:r.webkitImageSmoothingEnabled||r.mozImageSmoothingEnabled||r.msImageSmoothingEnabled};De.makeOffscreenCanvas=function(r,e){var t;if((typeof OffscreenCanvas>"u"?"undefined":Ue(OffscreenCanvas))!=="undefined")t=new OffscreenCanvas(r,e);else{var a=this.cy.window(),n=a.document;t=n.createElement("canvas"),t.width=r,t.height=e}return t};[xu,Fr,Ur,Pi,wt,Xt,mr,it,Pa,Du].forEach(function(r){pe(De,r)});var yp=[{name:"null",impl:uu},{name:"base",impl:mu},{name:"canvas",impl:gp}],mp=[{type:"layout",extensions:Sg},{type:"renderer",extensions:yp}],Pu={},Bu={};function Mu(r,e,t){var a=t,n=function(D){Me("Can not register `"+e+"` for `"+r+"` since `"+D+"` already exists in the prototype and can not be overridden")};if(r==="core"){if(ba.prototype[e])return n(e);ba.prototype[e]=t}else if(r==="collection"){if(er.prototype[e])return n(e);er.prototype[e]=t}else if(r==="layout"){for(var i=function(D){this.options=D,t.call(this,D),Te(this._private)||(this._private={}),this._private.cy=D.cy,this._private.listeners=[],this.createEmitter()},s=i.prototype=Object.create(t.prototype),o=[],l=0;l<o.length;l++){var u=o[l];s[u]=s[u]||function(){return this}}s.start&&!s.run?s.run=function(){return this.start(),this}:!s.start&&s.run&&(s.start=function(){return this.run(),this});var v=t.prototype.stop;s.stop=function(){var x=this.options;if(x&&x.animate){var D=this.animations;if(D)for(var C=0;C<D.length;C++)D[C].stop()}return v?v.call(this):this.emit("layoutstop"),this},s.destroy||(s.destroy=function(){return this}),s.cy=function(){return this._private.cy};var f=function(D){return D._private.cy},c={addEventFields:function(D,C){C.layout=D,C.cy=f(D),C.target=D},bubble:function(){return!0},parent:function(D){return f(D)}};pe(s,{createEmitter:function(){return this._private.emitter=new wn(c,this),this},emitter:function(){return this._private.emitter},on:function(D,C){return this.emitter().on(D,C),this},one:function(D,C){return this.emitter().one(D,C),this},once:function(D,C){return this.emitter().one(D,C),this},removeListener:function(D,C){return this.emitter().removeListener(D,C),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},emit:function(D,C){return this.emitter().emit(D,C),this}}),Be.eventAliasesOn(s),a=i}else if(r==="renderer"&&e!=="null"&&e!=="base"){var h=Lu("renderer","base"),d=h.prototype,y=t,p=t.prototype,g=function(){h.apply(this,arguments),y.apply(this,arguments)},m=g.prototype;for(var b in d){var w=d[b],S=p[b]!=null;if(S)return n(b);m[b]=w}for(var E in p)m[E]=p[E];d.clientFunctions.forEach(function(x){m[x]=m[x]||function(){$e("Renderer does not implement `renderer."+x+"()` on its prototype")}}),a=g}else if(r==="__proto__"||r==="constructor"||r==="prototype")return $e(r+" is an illegal type to be registered, possibly lead to prototype pollutions");return ro({map:Pu,keys:[r,e],value:a})}function Lu(r,e){return to({map:Pu,keys:[r,e]})}function bp(r,e,t,a,n){return ro({map:Bu,keys:[r,e,t,a],value:n})}function wp(r,e,t,a){return to({map:Bu,keys:[r,e,t,a]})}var ni=function(){if(arguments.length===2)return Lu.apply(null,arguments);if(arguments.length===3)return Mu.apply(null,arguments);if(arguments.length===4)return wp.apply(null,arguments);if(arguments.length===5)return bp.apply(null,arguments);$e("Invalid extension access syntax")};ba.prototype.extension=ni;mp.forEach(function(r){r.extensions.forEach(function(e){Mu(r.type,e.name,e.impl)})});var Au=function r(){if(!(this instanceof r))return new r;this.length=0},mt=Au.prototype;mt.instanceString=function(){return"stylesheet"};mt.selector=function(r){var e=this.length++;return this[e]={selector:r,properties:[]},this};mt.css=function(r,e){var t=this.length-1;if(ce(r))this[t].properties.push({name:r,value:e});else if(Te(r))for(var a=r,n=Object.keys(a),i=0;i<n.length;i++){var s=n[i],o=a[s];if(o!=null){var l=nr.properties[s]||nr.properties[vn(s)];if(l!=null){var u=l.name,v=o;this[t].properties.push({name:u,value:v})}}}return this};mt.style=mt.css;mt.generateStyle=function(r){var e=new nr(r);return this.appendToStyle(e)};mt.appendToStyle=function(r){for(var e=0;e<this.length;e++){var t=this[e],a=t.selector,n=t.properties;r.selector(a);for(var i=0;i<n.length;i++){var s=n[i];r.css(s.name,s.value)}}return r};var xp="3.30.3",Ft=function(e){if(e===void 0&&(e={}),Te(e))return new ba(e);if(ce(e))return ni.apply(ni,arguments)};Ft.use=function(r){var e=Array.prototype.slice.call(arguments,1);return e.unshift(Ft),r.apply(null,e),this};Ft.warnings=function(r){return vo(r)};Ft.version=xp;Ft.stylesheet=Ft.Stylesheet=Au;export{Ft as c};
//# sourceMappingURL=cytoscape.esm-crFNGOXw.js.map
