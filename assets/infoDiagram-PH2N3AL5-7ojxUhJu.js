import{_ as t,l as n,H as s,k as i,I as d}from"./MermaidPreview-B7IzGWQB.js";import{p}from"./radar-MK3ICKWK-FbtA6Xak.js";import"./premium-DZfa0MBj.js";import"./user-config-BaX6AisS.js";import"./merge-RIL0UuGj.js";import"./map-CcMwK8nF.js";import"./_baseUniq-CDtg80eS.js";import"./uniqBy-DkZXKWgj.js";import"./min-DmxjSKgW.js";import"./reduce-DteGjNFQ.js";import"./clone-DVQebAoj.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},r=new e.Error().stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="f6534585-4d3c-42e9-aa74-0cb475cbcf34",e._sentryDebugIdIdentifier="sentry-dbid-f6534585-4d3c-42e9-aa74-0cb475cbcf34")}catch{}})();var g={parse:t(async e=>{const r=await p("info",e);n.debug(r)},"parse")},f={version:d.version},c=t(()=>f.version,"getVersion"),l={getVersion:c},m=t((e,r,a)=>{n.debug(`rendering info diagram
`+e);const o=s(r);i(o,100,400,!0),o.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${a}`)},"draw"),b={draw:m},T={parser:g,db:l,renderer:b};export{T as diagram};
//# sourceMappingURL=infoDiagram-PH2N3AL5-7ojxUhJu.js.map
