(function(){"use strict";function nt(R,S){return S.forEach(function(L){L&&typeof L!="string"&&!Array.isArray(L)&&Object.keys(L).forEach(function(h){if(h!=="default"&&!(h in R)){var e=Object.getOwnPropertyDescriptor(L,h);Object.defineProperty(R,h,e.get?e:{enumerable:!0,get:function(){return L[h]}})}})}),Object.freeze(R)}function Ne(R,S){return new Promise(function(L,h){let e;return rt(R).then(function(t){try{return e=t,L(new Blob([S.slice(0,2),e,S.slice(2)],{type:"image/jpeg"}))}catch(u){return h(u)}},h)})}const rt=R=>new Promise((S,L)=>{const h=new FileReader;h.addEventListener("load",({target:{result:e}})=>{const t=new DataView(e);let u=0;if(t.getUint16(u)!==65496)return L("not a valid JPEG");for(u+=2;;){const f=t.getUint16(u);if(f===65498)break;const p=t.getUint16(u+2);if(f===65505&&t.getUint32(u+4)===1165519206){const y=u+10;let r;switch(t.getUint16(y)){case 18761:r=!0;break;case 19789:r=!1;break;default:return L("TIFF header contains invalid endian")}if(t.getUint16(y+2,r)!==42)return L("TIFF header contains invalid version");const i=t.getUint32(y+4,r),n=y+i+2+12*t.getUint16(y+i,r);for(let a=y+i+2;a<n;a+=12)if(t.getUint16(a,r)==274){if(t.getUint16(a+2,r)!==3)return L("Orientation data type is invalid");if(t.getUint32(a+4,r)!==1)return L("Orientation data count is invalid");t.setUint16(a+8,1,r);break}return S(e.slice(u,u+2+p))}u+=2+p}return S(new Blob)}),h.readAsArrayBuffer(R)});var Re={},it={get exports(){return Re},set exports(R){Re=R}};(function(R){var S,L,h={};it.exports=h,h.parse=function(e,t){for(var u=h.bin.readUshort,f=h.bin.readUint,p=0,y={},r=new Uint8Array(e),i=r.length-4;f(r,i)!=101010256;)i--;p=i,p+=4;var n=u(r,p+=4);u(r,p+=2);var a=f(r,p+=2),d=f(r,p+=4);p+=4,p=d;for(var F=0;F<n;F++){f(r,p),p+=4,p+=4,p+=4,f(r,p+=4),a=f(r,p+=4);var C=f(r,p+=4),A=u(r,p+=4),H=u(r,p+2),T=u(r,p+4);p+=6;var I=f(r,p+=8);p+=4,p+=A+H+T,h._readLocal(r,I,y,a,C,t)}return y},h._readLocal=function(e,t,u,f,p,y){var r=h.bin.readUshort,i=h.bin.readUint;i(e,t),r(e,t+=4),r(e,t+=2);var n=r(e,t+=2);i(e,t+=2),i(e,t+=4),t+=4;var a=r(e,t+=8),d=r(e,t+=2);t+=2;var F=h.bin.readUTF8(e,t,a);if(t+=a,t+=d,y)u[F]={size:p,csize:f};else{var C=new Uint8Array(e.buffer,t);if(n==0)u[F]=new Uint8Array(C.buffer.slice(t,t+f));else{if(n!=8)throw"unknown compression method: "+n;var A=new Uint8Array(p);h.inflateRaw(C,A),u[F]=A}}},h.inflateRaw=function(e,t){return h.F.inflate(e,t)},h.inflate=function(e,t){return e[0],e[1],h.inflateRaw(new Uint8Array(e.buffer,e.byteOffset+2,e.length-6),t)},h.deflate=function(e,t){t==null&&(t={level:6});var u=0,f=new Uint8Array(50+Math.floor(1.1*e.length));f[u]=120,f[u+1]=156,u+=2,u=h.F.deflateRaw(e,f,u,t.level);var p=h.adler(e,0,e.length);return f[u+0]=p>>>24&255,f[u+1]=p>>>16&255,f[u+2]=p>>>8&255,f[u+3]=p>>>0&255,new Uint8Array(f.buffer,0,u+4)},h.deflateRaw=function(e,t){t==null&&(t={level:6});var u=new Uint8Array(50+Math.floor(1.1*e.length)),f=h.F.deflateRaw(e,u,f,t.level);return new Uint8Array(u.buffer,0,f)},h.encode=function(e,t){t==null&&(t=!1);var u=0,f=h.bin.writeUint,p=h.bin.writeUshort,y={};for(var r in e){var i=!h._noNeed(r)&&!t,n=e[r],a=h.crc.crc(n,0,n.length);y[r]={cpr:i,usize:n.length,crc:a,file:i?h.deflateRaw(n):n}}for(var r in y)u+=y[r].file.length+30+46+2*h.bin.sizeUTF8(r);u+=22;var d=new Uint8Array(u),F=0,C=[];for(var r in y){var A=y[r];C.push(F),F=h._writeHeader(d,F,r,A,0)}var H=0,T=F;for(var r in y)A=y[r],C.push(F),F=h._writeHeader(d,F,r,A,1,C[H++]);var I=F-T;return f(d,F,101010256),F+=4,p(d,F+=4,H),p(d,F+=2,H),f(d,F+=2,I),f(d,F+=4,T),F+=4,F+=2,d.buffer},h._noNeed=function(e){var t=e.split(".").pop().toLowerCase();return"png,jpg,jpeg,zip".indexOf(t)!=-1},h._writeHeader=function(e,t,u,f,p,y){var r=h.bin.writeUint,i=h.bin.writeUshort,n=f.file;return r(e,t,p==0?67324752:33639248),t+=4,p==1&&(t+=2),i(e,t,20),i(e,t+=2,0),i(e,t+=2,f.cpr?8:0),r(e,t+=2,0),r(e,t+=4,f.crc),r(e,t+=4,n.length),r(e,t+=4,f.usize),i(e,t+=4,h.bin.sizeUTF8(u)),i(e,t+=2,0),t+=2,p==1&&(t+=2,t+=2,r(e,t+=6,y),t+=4),t+=h.bin.writeUTF8(e,t,u),p==0&&(e.set(n,t),t+=n.length),t},h.crc={table:function(){for(var e=new Uint32Array(256),t=0;t<256;t++){for(var u=t,f=0;f<8;f++)1&u?u=3988292384^u>>>1:u>>>=1;e[t]=u}return e}(),update:function(e,t,u,f){for(var p=0;p<f;p++)e=h.crc.table[255&(e^t[u+p])]^e>>>8;return e},crc:function(e,t,u){return 4294967295^h.crc.update(4294967295,e,t,u)}},h.adler=function(e,t,u){for(var f=1,p=0,y=t,r=t+u;y<r;){for(var i=Math.min(y+5552,r);y<i;)p+=f+=e[y++];f%=65521,p%=65521}return p<<16|f},h.bin={readUshort:function(e,t){return e[t]|e[t+1]<<8},writeUshort:function(e,t,u){e[t]=255&u,e[t+1]=u>>8&255},readUint:function(e,t){return 16777216*e[t+3]+(e[t+2]<<16|e[t+1]<<8|e[t])},writeUint:function(e,t,u){e[t]=255&u,e[t+1]=u>>8&255,e[t+2]=u>>16&255,e[t+3]=u>>24&255},readASCII:function(e,t,u){for(var f="",p=0;p<u;p++)f+=String.fromCharCode(e[t+p]);return f},writeASCII:function(e,t,u){for(var f=0;f<u.length;f++)e[t+f]=u.charCodeAt(f)},pad:function(e){return e.length<2?"0"+e:e},readUTF8:function(e,t,u){for(var f,p="",y=0;y<u;y++)p+="%"+h.bin.pad(e[t+y].toString(16));try{f=decodeURIComponent(p)}catch{return h.bin.readASCII(e,t,u)}return f},writeUTF8:function(e,t,u){for(var f=u.length,p=0,y=0;y<f;y++){var r=u.charCodeAt(y);if(!(4294967168&r))e[t+p]=r,p++;else if(!(4294965248&r))e[t+p]=192|r>>6,e[t+p+1]=128|r>>0&63,p+=2;else if(!(4294901760&r))e[t+p]=224|r>>12,e[t+p+1]=128|r>>6&63,e[t+p+2]=128|r>>0&63,p+=3;else{if(4292870144&r)throw"e";e[t+p]=240|r>>18,e[t+p+1]=128|r>>12&63,e[t+p+2]=128|r>>6&63,e[t+p+3]=128|r>>0&63,p+=4}}return p},sizeUTF8:function(e){for(var t=e.length,u=0,f=0;f<t;f++){var p=e.charCodeAt(f);if(!(4294967168&p))u++;else if(!(4294965248&p))u+=2;else if(!(4294901760&p))u+=3;else{if(4292870144&p)throw"e";u+=4}}return u}},h.F={},h.F.deflateRaw=function(e,t,u,f){var p=[[0,0,0,0,0],[4,4,8,4,0],[4,5,16,8,0],[4,6,16,16,0],[4,10,16,32,0],[8,16,32,32,0],[8,16,128,128,0],[8,32,128,256,0],[32,128,258,1024,1],[32,258,258,4096,1]][f],y=h.F.U,r=h.F._goodIndex;h.F._hash;var i=h.F._putsE,n=0,a=u<<3,d=0,F=e.length;if(f==0){for(;n<F;)i(t,a,n+(m=Math.min(65535,F-n))==F?1:0),a=h.F._copyExact(e,n,m,t,a+8),n+=m;return a>>>3}var C=y.lits,A=y.strt,H=y.prev,T=0,I=0,O=0,g=0,x=0,l=0;for(F>2&&(A[l=h.F._hash(e,0)]=0),n=0;n<F;n++){if(x=l,n+1<F-2){l=h.F._hash(e,n+1);var s=n+1&32767;H[s]=A[l],A[l]=s}if(d<=n){(T>14e3||I>26697)&&F-n>100&&(d<n&&(C[T]=n-d,T+=2,d=n),a=h.F._writeBlock(n==F-1||d==F?1:0,C,T,g,e,O,n-O,t,a),T=I=g=0,O=n);var b=0;n<F-2&&(b=h.F._bestMatch(e,n,H,x,Math.min(p[2],F-n),p[3]));var m=b>>>16,w=65535&b;if(b!=0){w=65535&b;var U=r(m=b>>>16,y.of0);y.lhst[257+U]++;var v=r(w,y.df0);y.dhst[v]++,g+=y.exb[U]+y.dxb[v],C[T]=m<<23|n-d,C[T+1]=w<<16|U<<8|v,T+=2,d=n+m}else y.lhst[e[n]]++;I++}}for(O==n&&e.length!=0||(d<n&&(C[T]=n-d,T+=2,d=n),a=h.F._writeBlock(1,C,T,g,e,O,n-O,t,a),T=0,I=0,T=I=g=0,O=n);7&a;)a++;return a>>>3},h.F._bestMatch=function(e,t,u,f,p,y){var r=32767&t,i=u[r],n=r-i+32768&32767;if(i==r||f!=h.F._hash(e,t-n))return 0;for(var a=0,d=0,F=Math.min(32767,t);n<=F&&--y!=0&&i!=r;){if(a==0||e[t+a]==e[t+a-n]){var C=h.F._howLong(e,t,n);if(C>a){if(d=n,(a=C)>=p)break;n+2<C&&(C=n+2);for(var A=0,H=0;H<C-2;H++){var T=t-n+H+32768&32767,I=T-u[T]+32768&32767;I>A&&(A=I,i=T)}}}n+=(r=i)-(i=u[r])+32768&32767}return a<<16|d},h.F._howLong=function(e,t,u){if(e[t]!=e[t-u]||e[t+1]!=e[t+1-u]||e[t+2]!=e[t+2-u])return 0;var f=t,p=Math.min(e.length,t+258);for(t+=3;t<p&&e[t]==e[t-u];)t++;return t-f},h.F._hash=function(e,t){return(e[t]<<8|e[t+1])+(e[t+2]<<4)&65535},h.saved=0,h.F._writeBlock=function(e,t,u,f,p,y,r,i,n){var a,d,F,C,A,H,T,I,O,g=h.F.U,x=h.F._putsF,l=h.F._putsE;g.lhst[256]++,d=(a=h.F.getTrees())[0],F=a[1],C=a[2],A=a[3],H=a[4],T=a[5],I=a[6],O=a[7];var s=32+(n+3&7?8-(n+3&7):0)+(r<<3),b=f+h.F.contSize(g.fltree,g.lhst)+h.F.contSize(g.fdtree,g.dhst),m=f+h.F.contSize(g.ltree,g.lhst)+h.F.contSize(g.dtree,g.dhst);m+=14+3*T+h.F.contSize(g.itree,g.ihst)+(2*g.ihst[16]+3*g.ihst[17]+7*g.ihst[18]);for(var w=0;w<286;w++)g.lhst[w]=0;for(w=0;w<30;w++)g.dhst[w]=0;for(w=0;w<19;w++)g.ihst[w]=0;var U=s<b&&s<m?0:b<m?1:2;if(x(i,n,e),x(i,n+1,U),n+=3,U==0){for(;7&n;)n++;n=h.F._copyExact(p,y,r,i,n)}else{var v,_;if(U==1&&(v=g.fltree,_=g.fdtree),U==2){h.F.makeCodes(g.ltree,d),h.F.revCodes(g.ltree,d),h.F.makeCodes(g.dtree,F),h.F.revCodes(g.dtree,F),h.F.makeCodes(g.itree,C),h.F.revCodes(g.itree,C),v=g.ltree,_=g.dtree,l(i,n,A-257),l(i,n+=5,H-1),l(i,n+=5,T-4),n+=4;for(var c=0;c<T;c++)l(i,n+3*c,g.itree[1+(g.ordr[c]<<1)]);n+=3*T,n=h.F._codeTiny(I,g.itree,i,n),n=h.F._codeTiny(O,g.itree,i,n)}for(var o=y,P=0;P<u;P+=2){for(var E=t[P],k=E>>>23,B=o+(8388607&E);o<B;)n=h.F._writeLit(p[o++],v,i,n);if(k!=0){var z=t[P+1],Q=z>>16,D=z>>8&255,M=255&z;l(i,n=h.F._writeLit(257+D,v,i,n),k-g.of0[D]),n+=g.exb[D],x(i,n=h.F._writeLit(M,_,i,n),Q-g.df0[M]),n+=g.dxb[M],o+=k}}n=h.F._writeLit(256,v,i,n)}return n},h.F._copyExact=function(e,t,u,f,p){var y=p>>>3;return f[y]=u,f[y+1]=u>>>8,f[y+2]=255-f[y],f[y+3]=255-f[y+1],y+=4,f.set(new Uint8Array(e.buffer,t,u),y),p+(u+4<<3)},h.F.getTrees=function(){for(var e=h.F.U,t=h.F._hufTree(e.lhst,e.ltree,15),u=h.F._hufTree(e.dhst,e.dtree,15),f=[],p=h.F._lenCodes(e.ltree,f),y=[],r=h.F._lenCodes(e.dtree,y),i=0;i<f.length;i+=2)e.ihst[f[i]]++;for(i=0;i<y.length;i+=2)e.ihst[y[i]]++;for(var n=h.F._hufTree(e.ihst,e.itree,7),a=19;a>4&&e.itree[1+(e.ordr[a-1]<<1)]==0;)a--;return[t,u,n,p,r,a,f,y]},h.F.getSecond=function(e){for(var t=[],u=0;u<e.length;u+=2)t.push(e[u+1]);return t},h.F.nonZero=function(e){for(var t="",u=0;u<e.length;u+=2)e[u+1]!=0&&(t+=(u>>1)+",");return t},h.F.contSize=function(e,t){for(var u=0,f=0;f<t.length;f++)u+=t[f]*e[1+(f<<1)];return u},h.F._codeTiny=function(e,t,u,f){for(var p=0;p<e.length;p+=2){var y=e[p],r=e[p+1];f=h.F._writeLit(y,t,u,f);var i=y==16?2:y==17?3:7;y>15&&(h.F._putsE(u,f,r,i),f+=i)}return f},h.F._lenCodes=function(e,t){for(var u=e.length;u!=2&&e[u-1]==0;)u-=2;for(var f=0;f<u;f+=2){var p=e[f+1],y=f+3<u?e[f+3]:-1,r=f+5<u?e[f+5]:-1,i=f==0?-1:e[f-1];if(p==0&&y==p&&r==p){for(var n=f+5;n+2<u&&e[n+2]==p;)n+=2;(a=Math.min(n+1-f>>>1,138))<11?t.push(17,a-3):t.push(18,a-11),f+=2*a-2}else if(p==i&&y==p&&r==p){for(n=f+5;n+2<u&&e[n+2]==p;)n+=2;var a=Math.min(n+1-f>>>1,6);t.push(16,a-3),f+=2*a-2}else t.push(p,0)}return u>>>1},h.F._hufTree=function(e,t,u){var f=[],p=e.length,y=t.length,r=0;for(r=0;r<y;r+=2)t[r]=0,t[r+1]=0;for(r=0;r<p;r++)e[r]!=0&&f.push({lit:r,f:e[r]});var i=f.length,n=f.slice(0);if(i==0)return 0;if(i==1){var a=f[0].lit;return n=a==0?1:0,t[1+(a<<1)]=1,t[1+(n<<1)]=1,1}f.sort(function(I,O){return I.f-O.f});var d=f[0],F=f[1],C=0,A=1,H=2;for(f[0]={lit:-1,f:d.f+F.f,l:d,r:F,d:0};A!=i-1;)d=C!=A&&(H==i||f[C].f<f[H].f)?f[C++]:f[H++],F=C!=A&&(H==i||f[C].f<f[H].f)?f[C++]:f[H++],f[A++]={lit:-1,f:d.f+F.f,l:d,r:F};var T=h.F.setDepth(f[A-1],0);for(T>u&&(h.F.restrictDepth(n,u,T),T=u),r=0;r<i;r++)t[1+(n[r].lit<<1)]=n[r].d;return T},h.F.setDepth=function(e,t){return e.lit!=-1?(e.d=t,t):Math.max(h.F.setDepth(e.l,t+1),h.F.setDepth(e.r,t+1))},h.F.restrictDepth=function(e,t,u){var f=0,p=1<<u-t,y=0;for(e.sort(function(i,n){return n.d==i.d?i.f-n.f:n.d-i.d}),f=0;f<e.length&&e[f].d>t;f++){var r=e[f].d;e[f].d=t,y+=p-(1<<u-r)}for(y>>>=u-t;y>0;)(r=e[f].d)<t?(e[f].d++,y-=1<<t-r-1):f++;for(;f>=0;f--)e[f].d==t&&y<0&&(e[f].d--,y++);y!=0},h.F._goodIndex=function(e,t){var u=0;return t[16|u]<=e&&(u|=16),t[8|u]<=e&&(u|=8),t[4|u]<=e&&(u|=4),t[2|u]<=e&&(u|=2),t[1|u]<=e&&(u|=1),u},h.F._writeLit=function(e,t,u,f){return h.F._putsF(u,f,t[e<<1]),f+t[1+(e<<1)]},h.F.inflate=function(e,t){var u=Uint8Array;if(e[0]==3&&e[1]==0)return t||new u(0);var f=h.F,p=f._bitsF,y=f._bitsE,r=f._decodeTiny,i=f.makeCodes,n=f.codes2map,a=f._get17,d=f.U,F=t==null;F&&(t=new u(e.length>>>2<<3));for(var C,A,H=0,T=0,I=0,O=0,g=0,x=0,l=0,s=0,b=0;H==0;)if(H=p(e,b,1),T=p(e,b+1,2),b+=3,T!=0){if(F&&(t=h.F._check(t,s+(1<<17))),T==1&&(C=d.flmap,A=d.fdmap,x=511,l=31),T==2){I=y(e,b,5)+257,O=y(e,b+5,5)+1,g=y(e,b+10,4)+4,b+=14;for(var m=0;m<38;m+=2)d.itree[m]=0,d.itree[m+1]=0;var w=1;for(m=0;m<g;m++){var U=y(e,b+3*m,3);d.itree[1+(d.ordr[m]<<1)]=U,U>w&&(w=U)}b+=3*g,i(d.itree,w),n(d.itree,w,d.imap),C=d.lmap,A=d.dmap,b=r(d.imap,(1<<w)-1,I+O,e,b,d.ttree);var v=f._copyOut(d.ttree,0,I,d.ltree);x=(1<<v)-1;var _=f._copyOut(d.ttree,I,O,d.dtree);l=(1<<_)-1,i(d.ltree,v),n(d.ltree,v,C),i(d.dtree,_),n(d.dtree,_,A)}for(;;){var c=C[a(e,b)&x];b+=15&c;var o=c>>>4;if(!(o>>>8))t[s++]=o;else{if(o==256)break;var P=s+o-254;if(o>264){var E=d.ldef[o-257];P=s+(E>>>3)+y(e,b,7&E),b+=7&E}var k=A[a(e,b)&l];b+=15&k;var B=k>>>4,z=d.ddef[B],Q=(z>>>4)+p(e,b,15&z);for(b+=15&z,F&&(t=h.F._check(t,s+(1<<17)));s<P;)t[s]=t[s++-Q],t[s]=t[s++-Q],t[s]=t[s++-Q],t[s]=t[s++-Q];s=P}}}else{7&b&&(b+=8-(7&b));var D=4+(b>>>3),M=e[D-4]|e[D-3]<<8;F&&(t=h.F._check(t,s+M)),t.set(new u(e.buffer,e.byteOffset+D,M),s),b=D+M<<3,s+=M}return t.length==s?t:t.slice(0,s)},h.F._check=function(e,t){var u=e.length;if(t<=u)return e;var f=new Uint8Array(Math.max(u<<1,t));return f.set(e,0),f},h.F._decodeTiny=function(e,t,u,f,p,y){for(var r=h.F._bitsE,i=h.F._get17,n=0;n<u;){var a=e[i(f,p)&t];p+=15&a;var d=a>>>4;if(d<=15)y[n]=d,n++;else{var F=0,C=0;d==16?(C=3+r(f,p,2),p+=2,F=y[n-1]):d==17?(C=3+r(f,p,3),p+=3):d==18&&(C=11+r(f,p,7),p+=7);for(var A=n+C;n<A;)y[n]=F,n++}}return p},h.F._copyOut=function(e,t,u,f){for(var p=0,y=0,r=f.length>>>1;y<u;){var i=e[y+t];f[y<<1]=0,f[1+(y<<1)]=i,i>p&&(p=i),y++}for(;y<r;)f[y<<1]=0,f[1+(y<<1)]=0,y++;return p},h.F.makeCodes=function(e,t){for(var u,f,p,y,r=h.F.U,i=e.length,n=r.bl_count,a=0;a<=t;a++)n[a]=0;for(a=1;a<i;a+=2)n[e[a]]++;var d=r.next_code;for(u=0,n[0]=0,f=1;f<=t;f++)u=u+n[f-1]<<1,d[f]=u;for(p=0;p<i;p+=2)(y=e[p+1])!=0&&(e[p]=d[y],d[y]++)},h.F.codes2map=function(e,t,u){for(var f=e.length,p=h.F.U.rev15,y=0;y<f;y+=2)if(e[y+1]!=0)for(var r=y>>1,i=e[y+1],n=r<<4|i,a=t-i,d=e[y]<<a,F=d+(1<<a);d!=F;)u[p[d]>>>15-t]=n,d++},h.F.revCodes=function(e,t){for(var u=h.F.U.rev15,f=15-t,p=0;p<e.length;p+=2){var y=e[p]<<t-e[p+1];e[p]=u[y]>>>f}},h.F._putsE=function(e,t,u){u<<=7&t;var f=t>>>3;e[f]|=u,e[f+1]|=u>>>8},h.F._putsF=function(e,t,u){u<<=7&t;var f=t>>>3;e[f]|=u,e[f+1]|=u>>>8,e[f+2]|=u>>>16},h.F._bitsE=function(e,t,u){return(e[t>>>3]|e[1+(t>>>3)]<<8)>>>(7&t)&(1<<u)-1},h.F._bitsF=function(e,t,u){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16)>>>(7&t)&(1<<u)-1},h.F._get17=function(e,t){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16)>>>(7&t)},h.F._get25=function(e,t){return(e[t>>>3]|e[1+(t>>>3)]<<8|e[2+(t>>>3)]<<16|e[3+(t>>>3)]<<24)>>>(7&t)},h.F.U=(S=Uint16Array,L=Uint32Array,{next_code:new S(16),bl_count:new S(16),ordr:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],of0:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],exb:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],ldef:new S(32),df0:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],dxb:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],ddef:new L(32),flmap:new S(512),fltree:[],fdmap:new S(32),fdtree:[],lmap:new S(32768),ltree:[],ttree:[],dmap:new S(32768),dtree:[],imap:new S(512),itree:[],rev15:new S(32768),lhst:new L(286),dhst:new L(30),ihst:new L(19),lits:new L(15e3),strt:new S(65536),prev:new S(32768)}),function(){for(var e=h.F.U,t=0;t<32768;t++){var u=t;u=(4278255360&(u=(4042322160&(u=(3435973836&(u=(2863311530&u)>>>1|(1431655765&u)<<1))>>>2|(858993459&u)<<2))>>>4|(252645135&u)<<4))>>>8|(16711935&u)<<8,e.rev15[t]=(u>>>16|u<<16)>>>17}function f(p,y,r){for(;y--!=0;)p.push(0,r)}for(t=0;t<32;t++)e.ldef[t]=e.of0[t]<<3|e.exb[t],e.ddef[t]=e.df0[t]<<4|e.dxb[t];f(e.fltree,144,8),f(e.fltree,112,9),f(e.fltree,24,7),f(e.fltree,8,8),h.F.makeCodes(e.fltree,9),h.F.codes2map(e.fltree,9,e.flmap),h.F.revCodes(e.fltree,9),f(e.fdtree,32,5),h.F.makeCodes(e.fdtree,5),h.F.codes2map(e.fdtree,5,e.fdmap),h.F.revCodes(e.fdtree,5),f(e.itree,19,0),f(e.ltree,286,0),f(e.dtree,30,0),f(e.ttree,320,0)}()})();var ot=nt({__proto__:null,default:Re},[Re]);const ue=function(){var R={nextZero(r,i){for(;r[i]!=0;)i++;return i},readUshort:(r,i)=>r[i]<<8|r[i+1],writeUshort(r,i,n){r[i]=n>>8&255,r[i+1]=255&n},readUint:(r,i)=>16777216*r[i]+(r[i+1]<<16|r[i+2]<<8|r[i+3]),writeUint(r,i,n){r[i]=n>>24&255,r[i+1]=n>>16&255,r[i+2]=n>>8&255,r[i+3]=255&n},readASCII(r,i,n){let a="";for(let d=0;d<n;d++)a+=String.fromCharCode(r[i+d]);return a},writeASCII(r,i,n){for(let a=0;a<n.length;a++)r[i+a]=n.charCodeAt(a)},readBytes(r,i,n){const a=[];for(let d=0;d<n;d++)a.push(r[i+d]);return a},pad:r=>r.length<2?`0${r}`:r,readUTF8(r,i,n){let a,d="";for(let F=0;F<n;F++)d+=`%${R.pad(r[i+F].toString(16))}`;try{a=decodeURIComponent(d)}catch{return R.readASCII(r,i,n)}return a}};function S(r,i,n,a){const d=i*n,F=t(a),C=Math.ceil(i*F/8),A=new Uint8Array(4*d),H=new Uint32Array(A.buffer),{ctype:T}=a,{depth:I}=a,O=R.readUshort;if(T==6){const E=d<<2;if(I==8)for(var g=0;g<E;g+=4)A[g]=r[g],A[g+1]=r[g+1],A[g+2]=r[g+2],A[g+3]=r[g+3];if(I==16)for(g=0;g<E;g++)A[g]=r[g<<1]}else if(T==2){const E=a.tabs.tRNS;if(E==null){if(I==8)for(g=0;g<d;g++){var x=3*g;H[g]=255<<24|r[x+2]<<16|r[x+1]<<8|r[x]}if(I==16)for(g=0;g<d;g++)x=6*g,H[g]=255<<24|r[x+4]<<16|r[x+2]<<8|r[x]}else{var l=E[0];const k=E[1],B=E[2];if(I==8)for(g=0;g<d;g++){var s=g<<2;x=3*g,H[g]=255<<24|r[x+2]<<16|r[x+1]<<8|r[x],r[x]==l&&r[x+1]==k&&r[x+2]==B&&(A[s+3]=0)}if(I==16)for(g=0;g<d;g++)s=g<<2,x=6*g,H[g]=255<<24|r[x+4]<<16|r[x+2]<<8|r[x],O(r,x)==l&&O(r,x+2)==k&&O(r,x+4)==B&&(A[s+3]=0)}}else if(T==3){const E=a.tabs.PLTE,k=a.tabs.tRNS,B=k?k.length:0;if(I==1)for(var b=0;b<n;b++){var m=b*C,w=b*i;for(g=0;g<i;g++){s=w+g<<2;var U=3*(v=r[m+(g>>3)]>>7-((7&g)<<0)&1);A[s]=E[U],A[s+1]=E[U+1],A[s+2]=E[U+2],A[s+3]=v<B?k[v]:255}}if(I==2)for(b=0;b<n;b++)for(m=b*C,w=b*i,g=0;g<i;g++)s=w+g<<2,U=3*(v=r[m+(g>>2)]>>6-((3&g)<<1)&3),A[s]=E[U],A[s+1]=E[U+1],A[s+2]=E[U+2],A[s+3]=v<B?k[v]:255;if(I==4)for(b=0;b<n;b++)for(m=b*C,w=b*i,g=0;g<i;g++)s=w+g<<2,U=3*(v=r[m+(g>>1)]>>4-((1&g)<<2)&15),A[s]=E[U],A[s+1]=E[U+1],A[s+2]=E[U+2],A[s+3]=v<B?k[v]:255;if(I==8)for(g=0;g<d;g++){var v;s=g<<2,U=3*(v=r[g]),A[s]=E[U],A[s+1]=E[U+1],A[s+2]=E[U+2],A[s+3]=v<B?k[v]:255}}else if(T==4){if(I==8)for(g=0;g<d;g++){s=g<<2;var _=r[c=g<<1];A[s]=_,A[s+1]=_,A[s+2]=_,A[s+3]=r[c+1]}if(I==16)for(g=0;g<d;g++){var c;s=g<<2,_=r[c=g<<2],A[s]=_,A[s+1]=_,A[s+2]=_,A[s+3]=r[c+2]}}else if(T==0)for(l=a.tabs.tRNS?a.tabs.tRNS:-1,b=0;b<n;b++){const E=b*C,k=b*i;if(I==1)for(var o=0;o<i;o++){var P=(_=255*(r[E+(o>>>3)]>>>7-(7&o)&1))==255*l?0:255;H[k+o]=P<<24|_<<16|_<<8|_}else if(I==2)for(o=0;o<i;o++)P=(_=85*(r[E+(o>>>2)]>>>6-((3&o)<<1)&3))==85*l?0:255,H[k+o]=P<<24|_<<16|_<<8|_;else if(I==4)for(o=0;o<i;o++)P=(_=17*(r[E+(o>>>1)]>>>4-((1&o)<<2)&15))==17*l?0:255,H[k+o]=P<<24|_<<16|_<<8|_;else if(I==8)for(o=0;o<i;o++)P=(_=r[E+o])==l?0:255,H[k+o]=P<<24|_<<16|_<<8|_;else if(I==16)for(o=0;o<i;o++)_=r[E+(o<<1)],P=O(r,E+(o<<1))==l?0:255,H[k+o]=P<<24|_<<16|_<<8|_}return A}function L(r,i,n,a){const d=t(r),F=Math.ceil(n*d/8),C=new Uint8Array((F+1+r.interlace)*a);return i=r.tabs.CgBI?e(i,C):h(i,C),r.interlace==0?i=u(i,r,0,n,a):r.interlace==1&&(i=function(H,T){const I=T.width,O=T.height,g=t(T),x=g>>3,l=Math.ceil(I*g/8),s=new Uint8Array(O*l);let b=0;const m=[0,0,4,0,2,0,1],w=[0,4,0,2,0,1,0],U=[8,8,8,4,4,2,2],v=[8,8,4,4,2,2,1];let _=0;for(;_<7;){const o=U[_],P=v[_];let E=0,k=0,B=m[_];for(;B<O;)B+=o,k++;let z=w[_];for(;z<I;)z+=P,E++;const Q=Math.ceil(E*g/8);u(H,T,b,E,k);let D=0,M=m[_];for(;M<O;){let N=w[_],Z=b+D*Q<<3;for(;N<I;){var c;if(g==1&&(c=(c=H[Z>>3])>>7-(7&Z)&1,s[M*l+(N>>3)]|=c<<7-((7&N)<<0)),g==2&&(c=(c=H[Z>>3])>>6-(7&Z)&3,s[M*l+(N>>2)]|=c<<6-((3&N)<<1)),g==4&&(c=(c=H[Z>>3])>>4-(7&Z)&15,s[M*l+(N>>1)]|=c<<4-((1&N)<<2)),g>=8){const q=M*l+N*x;for(let W=0;W<x;W++)s[q+W]=H[(Z>>3)+W]}Z+=g,N+=P}D++,M+=o}E*k!=0&&(b+=k*(1+Q)),_+=1}return s}(i,r)),i}function h(r,i){return e(new Uint8Array(r.buffer,2,r.length-6),i)}var e=function(){const r={H:{}};return r.H.N=function(i,n){const a=Uint8Array;let d,F,C=0,A=0,H=0,T=0,I=0,O=0,g=0,x=0,l=0;if(i[0]==3&&i[1]==0)return n||new a(0);const s=r.H,b=s.b,m=s.e,w=s.R,U=s.n,v=s.A,_=s.Z,c=s.m,o=n==null;for(o&&(n=new a(i.length>>>2<<5));C==0;)if(C=b(i,l,1),A=b(i,l+1,2),l+=3,A!=0){if(o&&(n=r.H.W(n,x+(1<<17))),A==1&&(d=c.J,F=c.h,O=511,g=31),A==2){H=m(i,l,5)+257,T=m(i,l+5,5)+1,I=m(i,l+10,4)+4,l+=14;let E=1;for(var P=0;P<38;P+=2)c.Q[P]=0,c.Q[P+1]=0;for(P=0;P<I;P++){const z=m(i,l+3*P,3);c.Q[1+(c.X[P]<<1)]=z,z>E&&(E=z)}l+=3*I,U(c.Q,E),v(c.Q,E,c.u),d=c.w,F=c.d,l=w(c.u,(1<<E)-1,H+T,i,l,c.v);const k=s.V(c.v,0,H,c.C);O=(1<<k)-1;const B=s.V(c.v,H,T,c.D);g=(1<<B)-1,U(c.C,k),v(c.C,k,d),U(c.D,B),v(c.D,B,F)}for(;;){const E=d[_(i,l)&O];l+=15&E;const k=E>>>4;if(!(k>>>8))n[x++]=k;else{if(k==256)break;{let B=x+k-254;if(k>264){const N=c.q[k-257];B=x+(N>>>3)+m(i,l,7&N),l+=7&N}const z=F[_(i,l)&g];l+=15&z;const Q=z>>>4,D=c.c[Q],M=(D>>>4)+b(i,l,15&D);for(l+=15&D;x<B;)n[x]=n[x++-M],n[x]=n[x++-M],n[x]=n[x++-M],n[x]=n[x++-M];x=B}}}}else{7&l&&(l+=8-(7&l));const E=4+(l>>>3),k=i[E-4]|i[E-3]<<8;o&&(n=r.H.W(n,x+k)),n.set(new a(i.buffer,i.byteOffset+E,k),x),l=E+k<<3,x+=k}return n.length==x?n:n.slice(0,x)},r.H.W=function(i,n){const a=i.length;if(n<=a)return i;const d=new Uint8Array(a<<1);return d.set(i,0),d},r.H.R=function(i,n,a,d,F,C){const A=r.H.e,H=r.H.Z;let T=0;for(;T<a;){const I=i[H(d,F)&n];F+=15&I;const O=I>>>4;if(O<=15)C[T]=O,T++;else{let g=0,x=0;O==16?(x=3+A(d,F,2),F+=2,g=C[T-1]):O==17?(x=3+A(d,F,3),F+=3):O==18&&(x=11+A(d,F,7),F+=7);const l=T+x;for(;T<l;)C[T]=g,T++}}return F},r.H.V=function(i,n,a,d){let F=0,C=0;const A=d.length>>>1;for(;C<a;){const H=i[C+n];d[C<<1]=0,d[1+(C<<1)]=H,H>F&&(F=H),C++}for(;C<A;)d[C<<1]=0,d[1+(C<<1)]=0,C++;return F},r.H.n=function(i,n){const a=r.H.m,d=i.length;let F,C,A,H;const T=a.j;for(var I=0;I<=n;I++)T[I]=0;for(I=1;I<d;I+=2)T[i[I]]++;const O=a.K;for(F=0,T[0]=0,C=1;C<=n;C++)F=F+T[C-1]<<1,O[C]=F;for(A=0;A<d;A+=2)H=i[A+1],H!=0&&(i[A]=O[H],O[H]++)},r.H.A=function(i,n,a){const d=i.length,F=r.H.m.r;for(let C=0;C<d;C+=2)if(i[C+1]!=0){const A=C>>1,H=i[C+1],T=A<<4|H,I=n-H;let O=i[C]<<I;const g=O+(1<<I);for(;O!=g;)a[F[O]>>>15-n]=T,O++}},r.H.l=function(i,n){const a=r.H.m.r,d=15-n;for(let F=0;F<i.length;F+=2){const C=i[F]<<n-i[F+1];i[F]=a[C]>>>d}},r.H.M=function(i,n,a){a<<=7&n;const d=n>>>3;i[d]|=a,i[d+1]|=a>>>8},r.H.I=function(i,n,a){a<<=7&n;const d=n>>>3;i[d]|=a,i[d+1]|=a>>>8,i[d+2]|=a>>>16},r.H.e=function(i,n,a){return(i[n>>>3]|i[1+(n>>>3)]<<8)>>>(7&n)&(1<<a)-1},r.H.b=function(i,n,a){return(i[n>>>3]|i[1+(n>>>3)]<<8|i[2+(n>>>3)]<<16)>>>(7&n)&(1<<a)-1},r.H.Z=function(i,n){return(i[n>>>3]|i[1+(n>>>3)]<<8|i[2+(n>>>3)]<<16)>>>(7&n)},r.H.i=function(i,n){return(i[n>>>3]|i[1+(n>>>3)]<<8|i[2+(n>>>3)]<<16|i[3+(n>>>3)]<<24)>>>(7&n)},r.H.m=function(){const i=Uint16Array,n=Uint32Array;return{K:new i(16),j:new i(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new i(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new n(32),J:new i(512),_:[],h:new i(32),$:[],w:new i(32768),C:[],v:[],d:new i(32768),D:[],u:new i(512),Q:[],r:new i(32768),s:new n(286),Y:new n(30),a:new n(19),t:new n(15e3),k:new i(65536),g:new i(32768)}}(),function(){const i=r.H.m;for(var n=0;n<32768;n++){let d=n;d=(2863311530&d)>>>1|(1431655765&d)<<1,d=(3435973836&d)>>>2|(858993459&d)<<2,d=(4042322160&d)>>>4|(252645135&d)<<4,d=(4278255360&d)>>>8|(16711935&d)<<8,i.r[n]=(d>>>16|d<<16)>>>17}function a(d,F,C){for(;F--!=0;)d.push(0,C)}for(n=0;n<32;n++)i.q[n]=i.S[n]<<3|i.T[n],i.c[n]=i.p[n]<<4|i.z[n];a(i._,144,8),a(i._,112,9),a(i._,24,7),a(i._,8,8),r.H.n(i._,9),r.H.A(i._,9,i.J),r.H.l(i._,9),a(i.$,32,5),r.H.n(i.$,5),r.H.A(i.$,5,i.h),r.H.l(i.$,5),a(i.Q,19,0),a(i.C,286,0),a(i.D,30,0),a(i.v,320,0)}(),r.H.N}();function t(r){return[1,null,3,1,2,null,4][r.ctype]*r.depth}function u(r,i,n,a,d){let F=t(i);const C=Math.ceil(a*F/8);let A,H;F=Math.ceil(F/8);let T=r[n],I=0;if(T>1&&(r[n]=[0,0,1][T-2]),T==3)for(I=F;I<C;I++)r[I+1]=r[I+1]+(r[I+1-F]>>>1)&255;for(let O=0;O<d;O++)if(A=n+O*C,H=A+O+1,T=r[H-1],I=0,T==0)for(;I<C;I++)r[A+I]=r[H+I];else if(T==1){for(;I<F;I++)r[A+I]=r[H+I];for(;I<C;I++)r[A+I]=r[H+I]+r[A+I-F]}else if(T==2)for(;I<C;I++)r[A+I]=r[H+I]+r[A+I-C];else if(T==3){for(;I<F;I++)r[A+I]=r[H+I]+(r[A+I-C]>>>1);for(;I<C;I++)r[A+I]=r[H+I]+(r[A+I-C]+r[A+I-F]>>>1)}else{for(;I<F;I++)r[A+I]=r[H+I]+f(0,r[A+I-C],0);for(;I<C;I++)r[A+I]=r[H+I]+f(r[A+I-F],r[A+I-C],r[A+I-F-C])}return r}function f(r,i,n){const a=r+i-n,d=a-r,F=a-i,C=a-n;return d*d<=F*F&&d*d<=C*C?r:F*F<=C*C?i:n}function p(r,i,n){n.width=R.readUint(r,i),i+=4,n.height=R.readUint(r,i),i+=4,n.depth=r[i],i++,n.ctype=r[i],i++,n.compress=r[i],i++,n.filter=r[i],i++,n.interlace=r[i],i++}function y(r,i,n,a,d,F,C,A,H){const T=Math.min(i,d),I=Math.min(n,F);let O=0,g=0;for(let _=0;_<I;_++)for(let c=0;c<T;c++)if(C>=0&&A>=0?(O=_*i+c<<2,g=(A+_)*d+C+c<<2):(O=(-A+_)*i-C+c<<2,g=_*d+c<<2),H==0)a[g]=r[O],a[g+1]=r[O+1],a[g+2]=r[O+2],a[g+3]=r[O+3];else if(H==1){var x=r[O+3]*.00392156862745098,l=r[O]*x,s=r[O+1]*x,b=r[O+2]*x,m=a[g+3]*(1/255),w=a[g]*m,U=a[g+1]*m,v=a[g+2]*m;const o=1-x,P=x+m*o,E=P==0?0:1/P;a[g+3]=255*P,a[g+0]=(l+w*o)*E,a[g+1]=(s+U*o)*E,a[g+2]=(b+v*o)*E}else if(H==2)x=r[O+3],l=r[O],s=r[O+1],b=r[O+2],m=a[g+3],w=a[g],U=a[g+1],v=a[g+2],x==m&&l==w&&s==U&&b==v?(a[g]=0,a[g+1]=0,a[g+2]=0,a[g+3]=0):(a[g]=l,a[g+1]=s,a[g+2]=b,a[g+3]=x);else if(H==3){if(x=r[O+3],l=r[O],s=r[O+1],b=r[O+2],m=a[g+3],w=a[g],U=a[g+1],v=a[g+2],x==m&&l==w&&s==U&&b==v)continue;if(x<220&&m>20)return!1}return!0}return{decode:function(i){const n=new Uint8Array(i);let a=8;const d=R,F=d.readUshort,C=d.readUint,A={tabs:{},frames:[]},H=new Uint8Array(n.length);let T,I=0,O=0;const g=[137,80,78,71,13,10,26,10];for(var x=0;x<8;x++)if(n[x]!=g[x])throw"The input is not a PNG file!";for(;a<n.length;){const _=d.readUint(n,a);a+=4;const c=d.readASCII(n,a,4);if(a+=4,c=="IHDR")p(n,a,A);else if(c=="iCCP"){for(var l=a;n[l]!=0;)l++;d.readASCII(n,a,l-a),n[l+1];const o=n.slice(l+2,a+_);let P=null;try{P=h(o)}catch{P=e(o)}A.tabs[c]=P}else if(c=="CgBI")A.tabs[c]=n.slice(a,a+4);else if(c=="IDAT"){for(x=0;x<_;x++)H[I+x]=n[a+x];I+=_}else if(c=="acTL")A.tabs[c]={num_frames:C(n,a),num_plays:C(n,a+4)},T=new Uint8Array(n.length);else if(c=="fcTL"){O!=0&&((v=A.frames[A.frames.length-1]).data=L(A,T.slice(0,O),v.rect.width,v.rect.height),O=0);const o={x:C(n,a+12),y:C(n,a+16),width:C(n,a+4),height:C(n,a+8)};let P=F(n,a+22);P=F(n,a+20)/(P==0?100:P);const E={rect:o,delay:Math.round(1e3*P),dispose:n[a+24],blend:n[a+25]};A.frames.push(E)}else if(c=="fdAT"){for(x=0;x<_-4;x++)T[O+x]=n[a+x+4];O+=_-4}else if(c=="pHYs")A.tabs[c]=[d.readUint(n,a),d.readUint(n,a+4),n[a+8]];else if(c=="cHRM")for(A.tabs[c]=[],x=0;x<8;x++)A.tabs[c].push(d.readUint(n,a+4*x));else if(c=="tEXt"||c=="zTXt"){A.tabs[c]==null&&(A.tabs[c]={});var s=d.nextZero(n,a),b=d.readASCII(n,a,s-a),m=a+_-s-1;if(c=="tEXt")U=d.readASCII(n,s+1,m);else{var w=h(n.slice(s+2,s+2+m));U=d.readUTF8(w,0,w.length)}A.tabs[c][b]=U}else if(c=="iTXt"){A.tabs[c]==null&&(A.tabs[c]={}),s=0,l=a,s=d.nextZero(n,l),b=d.readASCII(n,l,s-l);const o=n[l=s+1];var U;n[l+1],l+=2,s=d.nextZero(n,l),d.readASCII(n,l,s-l),l=s+1,s=d.nextZero(n,l),d.readUTF8(n,l,s-l),m=_-((l=s+1)-a),o==0?U=d.readUTF8(n,l,m):(w=h(n.slice(l,l+m)),U=d.readUTF8(w,0,w.length)),A.tabs[c][b]=U}else if(c=="PLTE")A.tabs[c]=d.readBytes(n,a,_);else if(c=="hIST"){const o=A.tabs.PLTE.length/3;for(A.tabs[c]=[],x=0;x<o;x++)A.tabs[c].push(F(n,a+2*x))}else if(c=="tRNS")A.ctype==3?A.tabs[c]=d.readBytes(n,a,_):A.ctype==0?A.tabs[c]=F(n,a):A.ctype==2&&(A.tabs[c]=[F(n,a),F(n,a+2),F(n,a+4)]);else if(c=="gAMA")A.tabs[c]=d.readUint(n,a)/1e5;else if(c=="sRGB")A.tabs[c]=n[a];else if(c=="bKGD")A.ctype==0||A.ctype==4?A.tabs[c]=[F(n,a)]:A.ctype==2||A.ctype==6?A.tabs[c]=[F(n,a),F(n,a+2),F(n,a+4)]:A.ctype==3&&(A.tabs[c]=n[a]);else if(c=="IEND")break;a+=_,d.readUint(n,a),a+=4}var v;return O!=0&&((v=A.frames[A.frames.length-1]).data=L(A,T.slice(0,O),v.rect.width,v.rect.height)),A.data=L(A,H,A.width,A.height),delete A.compress,delete A.interlace,delete A.filter,A},toRGBA8:function(i){const n=i.width,a=i.height;if(i.tabs.acTL==null)return[S(i.data,n,a,i).buffer];const d=[];i.frames[0].data==null&&(i.frames[0].data=i.data);const F=n*a*4,C=new Uint8Array(F),A=new Uint8Array(F),H=new Uint8Array(F);for(let I=0;I<i.frames.length;I++){const O=i.frames[I],g=O.rect.x,x=O.rect.y,l=O.rect.width,s=O.rect.height,b=S(O.data,l,s,i);if(I!=0)for(var T=0;T<F;T++)H[T]=C[T];if(O.blend==0?y(b,l,s,C,n,a,g,x,0):O.blend==1&&y(b,l,s,C,n,a,g,x,1),d.push(C.buffer.slice(0)),O.dispose!=0){if(O.dispose==1)y(A,l,s,C,n,a,g,x,0);else if(O.dispose==2)for(T=0;T<F;T++)C[T]=H[T]}}return d},_paeth:f,_copyTile:y,_bin:R}}();(function(){const{_copyTile:R}=ue,{_bin:S}=ue,L=ue._paeth;var h={table:function(){const l=new Uint32Array(256);for(let s=0;s<256;s++){let b=s;for(let m=0;m<8;m++)1&b?b=3988292384^b>>>1:b>>>=1;l[s]=b}return l}(),update(l,s,b,m){for(let w=0;w<m;w++)l=h.table[255&(l^s[b+w])]^l>>>8;return l},crc:(l,s,b)=>4294967295^h.update(4294967295,l,s,b)};function e(l,s,b,m){s[b]+=l[0]*m>>4,s[b+1]+=l[1]*m>>4,s[b+2]+=l[2]*m>>4,s[b+3]+=l[3]*m>>4}function t(l){return Math.max(0,Math.min(255,l))}function u(l,s){const b=l[0]-s[0],m=l[1]-s[1],w=l[2]-s[2],U=l[3]-s[3];return b*b+m*m+w*w+U*U}function f(l,s,b,m,w,U,v){v==null&&(v=1);const _=m.length,c=[];for(var o=0;o<_;o++){const M=m[o];c.push([M>>>0&255,M>>>8&255,M>>>16&255,M>>>24&255])}for(o=0;o<_;o++){let M=4294967295;for(var P=0,E=0;E<_;E++){var k=u(c[o],c[E]);E!=o&&k<M&&(M=k,P=E)}}const B=new Uint32Array(w.buffer),z=new Int16Array(s*b*4),Q=[0,8,2,10,12,4,14,6,3,11,1,9,15,7,13,5];for(o=0;o<Q.length;o++)Q[o]=255*((Q[o]+.5)/16-.5);for(let M=0;M<b;M++)for(let N=0;N<s;N++){var D;o=4*(M*s+N),v!=2?D=[t(l[o]+z[o]),t(l[o+1]+z[o+1]),t(l[o+2]+z[o+2]),t(l[o+3]+z[o+3])]:(k=Q[4*(3&M)+(3&N)],D=[t(l[o]+k),t(l[o+1]+k),t(l[o+2]+k),t(l[o+3]+k)]),P=0;let Z=16777215;for(E=0;E<_;E++){const G=u(D,c[E]);G<Z&&(Z=G,P=E)}const q=c[P],W=[D[0]-q[0],D[1]-q[1],D[2]-q[2],D[3]-q[3]];v==1&&(N!=s-1&&e(W,z,o+4,7),M!=b-1&&(N!=0&&e(W,z,o+4*s-4,3),e(W,z,o+4*s,5),N!=s-1&&e(W,z,o+4*s+4,1))),U[o>>2]=P,B[o>>2]=m[P]}}function p(l,s,b,m,w){w==null&&(w={});const{crc:U}=h,v=S.writeUint,_=S.writeUshort,c=S.writeASCII;let o=8;const P=l.frames.length>1;let E,k=!1,B=33+(P?20:0);if(w.sRGB!=null&&(B+=13),w.pHYs!=null&&(B+=21),w.iCCP!=null&&(E=pako.deflate(w.iCCP),B+=21+E.length+4),l.ctype==3){for(var z=l.plte.length,Q=0;Q<z;Q++)l.plte[Q]>>>24!=255&&(k=!0);B+=8+3*z+4+(k?8+1*z+4:0)}for(var D=0;D<l.frames.length;D++)P&&(B+=38),B+=(q=l.frames[D]).cimg.length+12,D!=0&&(B+=4);B+=12;const M=new Uint8Array(B),N=[137,80,78,71,13,10,26,10];for(Q=0;Q<8;Q++)M[Q]=N[Q];if(v(M,o,13),o+=4,c(M,o,"IHDR"),o+=4,v(M,o,s),o+=4,v(M,o,b),o+=4,M[o]=l.depth,o++,M[o]=l.ctype,o++,M[o]=0,o++,M[o]=0,o++,M[o]=0,o++,v(M,o,U(M,o-17,17)),o+=4,w.sRGB!=null&&(v(M,o,1),o+=4,c(M,o,"sRGB"),o+=4,M[o]=w.sRGB,o++,v(M,o,U(M,o-5,5)),o+=4),w.iCCP!=null){const W=13+E.length;v(M,o,W),o+=4,c(M,o,"iCCP"),o+=4,c(M,o,"ICC profile"),o+=11,o+=2,M.set(E,o),o+=E.length,v(M,o,U(M,o-(W+4),W+4)),o+=4}if(w.pHYs!=null&&(v(M,o,9),o+=4,c(M,o,"pHYs"),o+=4,v(M,o,w.pHYs[0]),o+=4,v(M,o,w.pHYs[1]),o+=4,M[o]=w.pHYs[2],o++,v(M,o,U(M,o-13,13)),o+=4),P&&(v(M,o,8),o+=4,c(M,o,"acTL"),o+=4,v(M,o,l.frames.length),o+=4,v(M,o,w.loop!=null?w.loop:0),o+=4,v(M,o,U(M,o-12,12)),o+=4),l.ctype==3){for(v(M,o,3*(z=l.plte.length)),o+=4,c(M,o,"PLTE"),o+=4,Q=0;Q<z;Q++){const W=3*Q,G=l.plte[Q],V=255&G,ee=G>>>8&255,me=G>>>16&255;M[o+W+0]=V,M[o+W+1]=ee,M[o+W+2]=me}if(o+=3*z,v(M,o,U(M,o-3*z-4,3*z+4)),o+=4,k){for(v(M,o,z),o+=4,c(M,o,"tRNS"),o+=4,Q=0;Q<z;Q++)M[o+Q]=l.plte[Q]>>>24&255;o+=z,v(M,o,U(M,o-z-4,z+4)),o+=4}}let Z=0;for(D=0;D<l.frames.length;D++){var q=l.frames[D];P&&(v(M,o,26),o+=4,c(M,o,"fcTL"),o+=4,v(M,o,Z++),o+=4,v(M,o,q.rect.width),o+=4,v(M,o,q.rect.height),o+=4,v(M,o,q.rect.x),o+=4,v(M,o,q.rect.y),o+=4,_(M,o,m[D]),o+=2,_(M,o,1e3),o+=2,M[o]=q.dispose,o++,M[o]=q.blend,o++,v(M,o,U(M,o-30,30)),o+=4);const W=q.cimg;v(M,o,(z=W.length)+(D==0?0:4)),o+=4;const G=o;c(M,o,D==0?"IDAT":"fdAT"),o+=4,D!=0&&(v(M,o,Z++),o+=4),M.set(W,o),o+=z,v(M,o,U(M,G,o-G)),o+=4}return v(M,o,0),o+=4,c(M,o,"IEND"),o+=4,v(M,o,U(M,o-4,4)),o+=4,M.buffer}function y(l,s,b){for(let m=0;m<l.frames.length;m++){const w=l.frames[m];w.rect.width;const U=w.rect.height,v=new Uint8Array(U*w.bpl+U);w.cimg=a(w.img,U,w.bpp,w.bpl,v,s,b)}}function r(l,s,b,m,w){const U=w[0],v=w[1],_=w[2],c=w[3],o=w[4],P=w[5];let E=6,k=8,B=255;for(var z=0;z<l.length;z++){const re=new Uint8Array(l[z]);for(var Q=re.length,D=0;D<Q;D+=4)B&=re[D+3]}const M=B!=255,N=function($,j,te,oe,Y,se){const J=[];for(var K=0;K<$.length;K++){const ae=new Uint8Array($[K]),he=new Uint32Array(ae.buffer);var le;let ce=0,ve=0,pe=j,Fe=te,Qe=oe?1:0;if(K!=0){const ct=se||oe||K==1||J[K-2].dispose!=0?1:2;let Be=0,et=1e9;for(let Me=0;Me<ct;Me++){var be=new Uint8Array($[K-1-Me]);const ut=new Uint32Array($[K-1-Me]);let Ae=j,we=te,Ce=-1,xe=-1;for(let Ee=0;Ee<te;Ee++)for(let _e=0;_e<j;_e++)he[ie=Ee*j+_e]!=ut[ie]&&(_e<Ae&&(Ae=_e),_e>Ce&&(Ce=_e),Ee<we&&(we=Ee),Ee>xe&&(xe=Ee));Ce==-1&&(Ae=we=Ce=xe=0),Y&&((1&Ae)==1&&Ae--,(1&we)==1&&we--);const tt=(Ce-Ae+1)*(xe-we+1);tt<et&&(et=tt,Be=Me,ce=Ae,ve=we,pe=Ce-Ae+1,Fe=xe-we+1)}be=new Uint8Array($[K-1-Be]),Be==1&&(J[K-1].dispose=2),le=new Uint8Array(pe*Fe*4),R(be,j,te,le,pe,Fe,-ce,-ve,0),Qe=R(ae,j,te,le,pe,Fe,-ce,-ve,3)?1:0,Qe==1?n(ae,j,te,le,{x:ce,y:ve,width:pe,height:Fe}):R(ae,j,te,le,pe,Fe,-ce,-ve,0)}else le=ae.slice(0);J.push({rect:{x:ce,y:ve,width:pe,height:Fe},img:le,blend:Qe,dispose:0})}if(oe)for(K=0;K<J.length;K++){if((Ue=J[K]).blend==1)continue;const ae=Ue.rect,he=J[K-1].rect,ce=Math.min(ae.x,he.x),ve=Math.min(ae.y,he.y),pe={x:ce,y:ve,width:Math.max(ae.x+ae.width,he.x+he.width)-ce,height:Math.max(ae.y+ae.height,he.y+he.height)-ve};J[K-1].dispose=1,K-1!=0&&i($,j,te,J,K-1,pe,Y),i($,j,te,J,K,pe,Y)}let He=0;if($.length!=1)for(var ie=0;ie<J.length;ie++){var Ue;He+=(Ue=J[ie]).rect.width*Ue.rect.height}return J}(l,s,b,U,v,_),Z={},q=[],W=[];if(m!=0){const re=[];for(D=0;D<N.length;D++)re.push(N[D].img.buffer);const $=function(Y){let se=0;for(var J=0;J<Y.length;J++)se+=Y[J].byteLength;const K=new Uint8Array(se);let le=0;for(J=0;J<Y.length;J++){const be=new Uint8Array(Y[J]),He=be.length;for(let ie=0;ie<He;ie+=4){let Ue=be[ie],ae=be[ie+1],he=be[ie+2];const ce=be[ie+3];ce==0&&(Ue=ae=he=0),K[le+ie]=Ue,K[le+ie+1]=ae,K[le+ie+2]=he,K[le+ie+3]=ce}le+=He}return K.buffer}(re),j=F($,m);for(D=0;D<j.plte.length;D++)q.push(j.plte[D].est.rgba);let te=0;for(D=0;D<N.length;D++){const oe=(V=N[D]).img.length;var G=new Uint8Array(j.inds.buffer,te>>2,oe>>2);W.push(G);const Y=new Uint8Array(j.abuf,te,oe);P&&f(V.img,V.rect.width,V.rect.height,q,Y,G),V.img.set(Y),te+=oe}}else for(z=0;z<N.length;z++){var V=N[z];const re=new Uint32Array(V.img.buffer);var ee=V.rect.width;for(Q=re.length,G=new Uint8Array(Q),W.push(G),D=0;D<Q;D++){const $=re[D];if(D!=0&&$==re[D-1])G[D]=G[D-1];else if(D>ee&&$==re[D-ee])G[D]=G[D-ee];else{let j=Z[$];if(j==null&&(Z[$]=j=q.length,q.push($),q.length>=300))break;G[D]=j}}}const me=q.length;for(me<=256&&o==0&&(k=me<=2?1:me<=4?2:me<=16?4:8,k=Math.max(k,c)),z=0;z<N.length;z++){(V=N[z]).rect.x,V.rect.y,ee=V.rect.width;const re=V.rect.height;let $=V.img;new Uint32Array($.buffer);let j=4*ee,te=4;if(me<=256&&o==0){j=Math.ceil(k*ee/8);var ge=new Uint8Array(j*re);const oe=W[z];for(let Y=0;Y<re;Y++){D=Y*j;const se=Y*ee;if(k==8)for(var X=0;X<ee;X++)ge[D+X]=oe[se+X];else if(k==4)for(X=0;X<ee;X++)ge[D+(X>>1)]|=oe[se+X]<<4-4*(1&X);else if(k==2)for(X=0;X<ee;X++)ge[D+(X>>2)]|=oe[se+X]<<6-2*(3&X);else if(k==1)for(X=0;X<ee;X++)ge[D+(X>>3)]|=oe[se+X]<<7-1*(7&X)}$=ge,E=3,te=1}else if(M==0&&N.length==1){ge=new Uint8Array(ee*re*3);const oe=ee*re;for(D=0;D<oe;D++){const Y=3*D,se=4*D;ge[Y]=$[se],ge[Y+1]=$[se+1],ge[Y+2]=$[se+2]}$=ge,E=2,te=3,j=3*ee}V.img=$,V.bpl=j,V.bpp=te}return{ctype:E,depth:k,plte:q,frames:N}}function i(l,s,b,m,w,U,v){const _=Uint8Array,c=Uint32Array,o=new _(l[w-1]),P=new c(l[w-1]),E=w+1<l.length?new _(l[w+1]):null,k=new _(l[w]),B=new c(k.buffer);let z=s,Q=b,D=-1,M=-1;for(let Z=0;Z<U.height;Z++)for(let q=0;q<U.width;q++){const W=U.x+q,G=U.y+Z,V=G*s+W,ee=B[V];ee==0||m[w-1].dispose==0&&P[V]==ee&&(E==null||E[4*V+3]!=0)||(W<z&&(z=W),W>D&&(D=W),G<Q&&(Q=G),G>M&&(M=G))}D==-1&&(z=Q=D=M=0),v&&((1&z)==1&&z--,(1&Q)==1&&Q--),U={x:z,y:Q,width:D-z+1,height:M-Q+1};const N=m[w];N.rect=U,N.blend=1,N.img=new Uint8Array(U.width*U.height*4),m[w-1].dispose==0?(R(o,s,b,N.img,U.width,U.height,-U.x,-U.y,0),n(k,s,b,N.img,U)):R(k,s,b,N.img,U.width,U.height,-U.x,-U.y,0)}function n(l,s,b,m,w){R(l,s,b,m,w.width,w.height,-w.x,-w.y,2)}function a(l,s,b,m,w,U,v){const _=[];let c,o=[0,1,2,3,4];U!=-1?o=[U]:(s*m>5e5||b==1)&&(o=[0]),v&&(c={level:0});const P=ot;for(var E=0;E<o.length;E++){for(let z=0;z<s;z++)d(w,l,z,m,b,o[E]);_.push(P.deflate(w,c))}let k,B=1e9;for(E=0;E<_.length;E++)_[E].length<B&&(k=E,B=_[E].length);return _[k]}function d(l,s,b,m,w,U){const v=b*m;let _=v+b;if(l[_]=U,_++,U==0)if(m<500)for(var c=0;c<m;c++)l[_+c]=s[v+c];else l.set(new Uint8Array(s.buffer,v,m),_);else if(U==1){for(c=0;c<w;c++)l[_+c]=s[v+c];for(c=w;c<m;c++)l[_+c]=s[v+c]-s[v+c-w]+256&255}else if(b==0){for(c=0;c<w;c++)l[_+c]=s[v+c];if(U==2)for(c=w;c<m;c++)l[_+c]=s[v+c];if(U==3)for(c=w;c<m;c++)l[_+c]=s[v+c]-(s[v+c-w]>>1)+256&255;if(U==4)for(c=w;c<m;c++)l[_+c]=s[v+c]-L(s[v+c-w],0,0)+256&255}else{if(U==2)for(c=0;c<m;c++)l[_+c]=s[v+c]+256-s[v+c-m]&255;if(U==3){for(c=0;c<w;c++)l[_+c]=s[v+c]+256-(s[v+c-m]>>1)&255;for(c=w;c<m;c++)l[_+c]=s[v+c]+256-(s[v+c-m]+s[v+c-w]>>1)&255}if(U==4){for(c=0;c<w;c++)l[_+c]=s[v+c]+256-L(0,s[v+c-m],0)&255;for(c=w;c<m;c++)l[_+c]=s[v+c]+256-L(s[v+c-w],s[v+c-m],s[v+c-w-m])&255}}}function F(l,s){const b=new Uint8Array(l),m=b.slice(0),w=new Uint32Array(m.buffer),U=C(m,s),v=U[0],_=U[1],c=b.length,o=new Uint8Array(c>>2);let P;if(b.length<2e7)for(var E=0;E<c;E+=4)P=A(v,k=b[E]*(1/255),B=b[E+1]*(1/255),z=b[E+2]*(1/255),Q=b[E+3]*(1/255)),o[E>>2]=P.ind,w[E>>2]=P.est.rgba;else for(E=0;E<c;E+=4){var k=b[E]*.00392156862745098,B=b[E+1]*(1/255),z=b[E+2]*(1/255),Q=b[E+3]*(1/255);for(P=v;P.left;)P=H(P.est,k,B,z,Q)<=0?P.left:P.right;o[E>>2]=P.ind,w[E>>2]=P.est.rgba}return{abuf:m.buffer,inds:o,plte:_}}function C(l,s,b){b==null&&(b=1e-4);const m=new Uint32Array(l.buffer),w={i0:0,i1:l.length,bst:null,est:null,tdst:0,left:null,right:null};w.bst=O(l,w.i0,w.i1),w.est=g(w.bst);const U=[w];for(;U.length<s;){let _=0,c=0;for(var v=0;v<U.length;v++)U[v].est.L>_&&(_=U[v].est.L,c=v);if(_<b)break;const o=U[c],P=T(l,m,o.i0,o.i1,o.est.e,o.est.eMq255);if(o.i0>=P||o.i1<=P){o.est.L=0;continue}const E={i0:o.i0,i1:P,bst:null,est:null,tdst:0,left:null,right:null};E.bst=O(l,E.i0,E.i1),E.est=g(E.bst);const k={i0:P,i1:o.i1,bst:null,est:null,tdst:0,left:null,right:null};for(k.bst={R:[],m:[],N:o.bst.N-E.bst.N},v=0;v<16;v++)k.bst.R[v]=o.bst.R[v]-E.bst.R[v];for(v=0;v<4;v++)k.bst.m[v]=o.bst.m[v]-E.bst.m[v];k.est=g(k.bst),o.left=E,o.right=k,U[c]=E,U.push(k)}for(U.sort((_,c)=>c.bst.N-_.bst.N),v=0;v<U.length;v++)U[v].ind=v;return[w,U]}function A(l,s,b,m,w){if(l.left==null)return l.tdst=function(E,k,B,z,Q){const D=k-E[0],M=B-E[1],N=z-E[2],Z=Q-E[3];return D*D+M*M+N*N+Z*Z}(l.est.q,s,b,m,w),l;const U=H(l.est,s,b,m,w);let v=l.left,_=l.right;U>0&&(v=l.right,_=l.left);const c=A(v,s,b,m,w);if(c.tdst<=U*U)return c;const o=A(_,s,b,m,w);return o.tdst<c.tdst?o:c}function H(l,s,b,m,w){const{e:U}=l;return U[0]*s+U[1]*b+U[2]*m+U[3]*w-l.eMq}function T(l,s,b,m,w,U){for(m-=4;b<m;){for(;I(l,b,w)<=U;)b+=4;for(;I(l,m,w)>U;)m-=4;if(b>=m)break;const v=s[b>>2];s[b>>2]=s[m>>2],s[m>>2]=v,b+=4,m-=4}for(;I(l,b,w)>U;)b-=4;return b+4}function I(l,s,b){return l[s]*b[0]+l[s+1]*b[1]+l[s+2]*b[2]+l[s+3]*b[3]}function O(l,s,b){const m=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],w=[0,0,0,0],U=b-s>>2;for(let v=s;v<b;v+=4){const _=l[v]*.00392156862745098,c=l[v+1]*(1/255),o=l[v+2]*(1/255),P=l[v+3]*(1/255);w[0]+=_,w[1]+=c,w[2]+=o,w[3]+=P,m[0]+=_*_,m[1]+=_*c,m[2]+=_*o,m[3]+=_*P,m[5]+=c*c,m[6]+=c*o,m[7]+=c*P,m[10]+=o*o,m[11]+=o*P,m[15]+=P*P}return m[4]=m[1],m[8]=m[2],m[9]=m[6],m[12]=m[3],m[13]=m[7],m[14]=m[11],{R:m,m:w,N:U}}function g(l){const{R:s}=l,{m:b}=l,{N:m}=l,w=b[0],U=b[1],v=b[2],_=b[3],c=m==0?0:1/m,o=[s[0]-w*w*c,s[1]-w*U*c,s[2]-w*v*c,s[3]-w*_*c,s[4]-U*w*c,s[5]-U*U*c,s[6]-U*v*c,s[7]-U*_*c,s[8]-v*w*c,s[9]-v*U*c,s[10]-v*v*c,s[11]-v*_*c,s[12]-_*w*c,s[13]-_*U*c,s[14]-_*v*c,s[15]-_*_*c],P=o,E=x;let k=[Math.random(),Math.random(),Math.random(),Math.random()],B=0,z=0;if(m!=0)for(let D=0;D<16&&(k=E.multVec(P,k),z=Math.sqrt(E.dot(k,k)),k=E.sml(1/z,k),!(D!=0&&Math.abs(z-B)<1e-9));D++)B=z;const Q=[w*c,U*c,v*c,_*c];return{Cov:o,q:Q,e:k,L:B,eMq255:E.dot(E.sml(255,Q),k),eMq:E.dot(k,Q),rgba:(Math.round(255*Q[3])<<24|Math.round(255*Q[2])<<16|Math.round(255*Q[1])<<8|Math.round(255*Q[0])<<0)>>>0}}var x={multVec:(l,s)=>[l[0]*s[0]+l[1]*s[1]+l[2]*s[2]+l[3]*s[3],l[4]*s[0]+l[5]*s[1]+l[6]*s[2]+l[7]*s[3],l[8]*s[0]+l[9]*s[1]+l[10]*s[2]+l[11]*s[3],l[12]*s[0]+l[13]*s[1]+l[14]*s[2]+l[15]*s[3]],dot:(l,s)=>l[0]*s[0]+l[1]*s[1]+l[2]*s[2]+l[3]*s[3],sml:(l,s)=>[l*s[0],l*s[1],l*s[2],l*s[3]]};ue.encode=function(s,b,m,w,U,v,_){w==null&&(w=0),_==null&&(_=!1);const c=r(s,b,m,w,[!1,!1,!1,0,_,!1]);return y(c,-1),p(c,b,m,U,v)},ue.encodeLL=function(s,b,m,w,U,v,_,c){const o={ctype:0+(w==1?0:2)+(U==0?0:4),depth:v,frames:[]},P=(w+U)*v,E=P*b;for(let k=0;k<s.length;k++)o.frames.push({rect:{x:0,y:0,width:b,height:m},img:new Uint8Array(s[k]),blend:0,dispose:1,bpp:Math.ceil(P/8),bpl:Math.ceil(E/8)});return y(o,0,!0),p(o,b,m,_,c)},ue.encode.compress=r,ue.encode.dither=f,ue.quantize=F,ue.quantize.getKDtree=C,ue.quantize.getNearest=A})();const We={toArrayBuffer(R,S){const L=R.width,h=R.height,e=L<<2,t=R.getContext("2d").getImageData(0,0,L,h),u=new Uint32Array(t.data.buffer),f=(32*L+31)/32<<2,p=f*h,y=122+p,r=new ArrayBuffer(y),i=new DataView(r),n=1<<20;let a,d,F,C,A=n,H=0,T=0,I=0;function O(l){i.setUint16(T,l,!0),T+=2}function g(l){i.setUint32(T,l,!0),T+=4}function x(l){T+=l}O(19778),g(y),x(4),g(122),g(108),g(L),g(-h>>>0),O(1),O(32),g(3),g(p),g(2835),g(2835),x(8),g(16711680),g(65280),g(255),g(4278190080),g(1466527264),function l(){for(;H<h&&A>0;){for(C=122+H*f,a=0;a<e;)A--,d=u[I++],F=d>>>24,i.setUint32(C+a,d<<8|F),a+=4;H++}I<u.length?(A=n,setTimeout(l,We._dly)):S(r)}()},toBlob(R,S){this.toArrayBuffer(R,L=>{S(new Blob([L],{type:"image/bmp"}))})},_dly:9};var fe={CHROME:"CHROME",FIREFOX:"FIREFOX",DESKTOP_SAFARI:"DESKTOP_SAFARI",IE:"IE",IOS:"IOS",ETC:"ETC"},at={[fe.CHROME]:16384,[fe.FIREFOX]:11180,[fe.DESKTOP_SAFARI]:16384,[fe.IE]:8192,[fe.IOS]:4096,[fe.ETC]:8192};const Le=typeof window<"u",qe=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,Se=Le&&window.cordova&&window.cordova.require&&window.cordova.require("cordova/modulemapper"),ft=(Le||qe)&&(Se&&Se.getOriginalSymbol(window,"File")||typeof File<"u"&&File),Ge=(Le||qe)&&(Se&&Se.getOriginalSymbol(window,"FileReader")||typeof FileReader<"u"&&FileReader);function De(R,S,L=Date.now()){return new Promise(h=>{const e=R.split(","),t=e[0].match(/:(.*?);/)[1],u=globalThis.atob(e[1]);let f=u.length;const p=new Uint8Array(f);for(;f--;)p[f]=u.charCodeAt(f);const y=new Blob([p],{type:t});y.name=S,y.lastModified=L,h(y)})}function je(R){return new Promise((S,L)=>{const h=new Ge;h.onload=()=>S(h.result),h.onerror=e=>L(e),h.readAsDataURL(R)})}function Ke(R){return new Promise((S,L)=>{const h=new Image;h.onload=()=>S(h),h.onerror=e=>L(e),h.src=R})}function ye(){if(ye.cachedResult!==void 0)return ye.cachedResult;let R=fe.ETC;const{userAgent:S}=navigator;return/Chrom(e|ium)/i.test(S)?R=fe.CHROME:/iP(ad|od|hone)/i.test(S)&&/WebKit/i.test(S)?R=fe.IOS:/Safari/i.test(S)?R=fe.DESKTOP_SAFARI:/Firefox/i.test(S)?R=fe.FIREFOX:(/MSIE/i.test(S)||document.documentMode)&&(R=fe.IE),ye.cachedResult=R,ye.cachedResult}function Ze(R,S){const L=ye(),h=at[L];let e=R,t=S,u=e*t;const f=e>t?t/e:e/t;for(;u>h*h;){const p=(h+e)/2,y=(h+t)/2;p<y?(t=y,e=y*f):(t=p*f,e=p),u=e*t}return{width:e,height:t}}function Te(R,S){let L,h;try{if(L=new OffscreenCanvas(R,S),h=L.getContext("2d"),h===null)throw new Error("getContext of OffscreenCanvas returns null")}catch{L=document.createElement("canvas"),h=L.getContext("2d")}return L.width=R,L.height=S,[L,h]}function $e(R,S){const{width:L,height:h}=Ze(R.width,R.height),[e,t]=Te(L,h);return S&&/jpe?g/.test(S)&&(t.fillStyle="white",t.fillRect(0,0,e.width,e.height)),t.drawImage(R,0,0,e.width,e.height),e}function Pe(){return Pe.cachedResult!==void 0||(Pe.cachedResult=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&typeof document<"u"&&"ontouchend"in document),Pe.cachedResult}function ke(R,S={}){return new Promise(function(L,h){let e,t;var u=function(){try{return t=$e(e,S.fileType||R.type),L([e,t])}catch(p){return h(p)}},f=function(p){try{var y=function(r){try{throw r}catch(i){return h(i)}};try{let r;return je(R).then(function(i){try{return r=i,Ke(r).then(function(n){try{return e=n,function(){try{return u()}catch(a){return h(a)}}()}catch(a){return y(a)}},y)}catch(n){return y(n)}},y)}catch(r){y(r)}}catch(r){return h(r)}};try{if(Pe()||[fe.DESKTOP_SAFARI,fe.MOBILE_SAFARI].includes(ye()))throw new Error("Skip createImageBitmap on IOS and Safari");return createImageBitmap(R).then(function(p){try{return e=p,u()}catch{return f()}},f)}catch{f()}})}function Oe(R,S,L,h,e=1){return new Promise(function(t,u){let f;if(S==="image/png"){let y,r,i;return y=R.getContext("2d"),{data:r}=y.getImageData(0,0,R.width,R.height),i=ue.encode([r.buffer],R.width,R.height,4096*e),f=new Blob([i],{type:S}),f.name=L,f.lastModified=h,p.call(this)}{let y=function(){return p.call(this)};if(S==="image/bmp")return new Promise(r=>We.toBlob(R,r)).then((function(r){try{return f=r,f.name=L,f.lastModified=h,y.call(this)}catch(i){return u(i)}}).bind(this),u);{let r=function(){return y.call(this)};if(typeof OffscreenCanvas=="function"&&R instanceof OffscreenCanvas)return R.convertToBlob({type:S,quality:e}).then((function(i){try{return f=i,f.name=L,f.lastModified=h,r.call(this)}catch(n){return u(n)}}).bind(this),u);{let i;return i=R.toDataURL(S,e),De(i,L,h).then((function(n){try{return f=n,r.call(this)}catch(a){return u(a)}}).bind(this),u)}}}function p(){return t(f)}})}function de(R){R.width=0,R.height=0}function Ie(){return new Promise(function(R,S){let L,h,e,t;return Ie.cachedResult!==void 0?R(Ie.cachedResult):De("data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==","test.jpg",Date.now()).then(function(u){try{return L=u,ke(L).then(function(f){try{return h=f[1],Oe(h,L.type,L.name,L.lastModified).then(function(p){try{return e=p,de(h),ke(e).then(function(y){try{return t=y[0],Ie.cachedResult=t.width===1&&t.height===2,R(Ie.cachedResult)}catch(r){return S(r)}},S)}catch(y){return S(y)}},S)}catch(p){return S(p)}},S)}catch(f){return S(f)}},S)})}function Xe(R){return new Promise((S,L)=>{const h=new Ge;h.onload=e=>{const t=new DataView(e.target.result);if(t.getUint16(0,!1)!=65496)return S(-2);const u=t.byteLength;let f=2;for(;f<u;){if(t.getUint16(f+2,!1)<=8)return S(-1);const p=t.getUint16(f,!1);if(f+=2,p==65505){if(t.getUint32(f+=2,!1)!=1165519206)return S(-1);const y=t.getUint16(f+=6,!1)==18761;f+=t.getUint32(f+4,y);const r=t.getUint16(f,y);f+=2;for(let i=0;i<r;i++)if(t.getUint16(f+12*i,y)==274)return S(t.getUint16(f+12*i+8,y))}else{if((65280&p)!=65280)break;f+=t.getUint16(f,!1)}}return S(-1)},h.onerror=e=>L(e),h.readAsArrayBuffer(R)})}function Ve(R,S){const{width:L}=R,{height:h}=R,{maxWidthOrHeight:e}=S;let t,u=R;return isFinite(e)&&(L>e||h>e)&&([u,t]=Te(L,h),L>h?(u.width=e,u.height=h/L*e):(u.width=L/h*e,u.height=e),t.drawImage(R,0,0,u.width,u.height),de(R)),u}function Ye(R,S){const{width:L}=R,{height:h}=R,[e,t]=Te(L,h);switch(S>4&&S<9?(e.width=h,e.height=L):(e.width=L,e.height=h),S){case 2:t.transform(-1,0,0,1,L,0);break;case 3:t.transform(-1,0,0,-1,L,h);break;case 4:t.transform(1,0,0,-1,0,h);break;case 5:t.transform(0,1,1,0,0,0);break;case 6:t.transform(0,1,-1,0,h,0);break;case 7:t.transform(0,-1,-1,0,h,L);break;case 8:t.transform(0,-1,1,0,0,L)}return t.drawImage(R,0,0,L,h),de(R),e}function Je(R,S,L=0){return new Promise(function(h,e){let t,u,f,p,y,r,i,n,a,d,F,C,A,H,T,I,O,g,x,l;function s(m=5){if(S.signal&&S.signal.aborted)throw S.signal.reason;t+=m,S.onProgress(Math.min(t,100))}function b(m){if(S.signal&&S.signal.aborted)throw S.signal.reason;t=Math.min(Math.max(m,t),100),S.onProgress(t)}return t=L,u=S.maxIteration||10,f=1024*S.maxSizeMB*1024,s(),ke(R,S).then((function(m){try{return[,p]=m,s(),y=Ve(p,S),s(),new Promise(function(w,U){var v;if(!(v=S.exifOrientation))return Xe(R).then((function(c){try{return v=c,_.call(this)}catch(o){return U(o)}}).bind(this),U);function _(){return w(v)}return _.call(this)}).then((function(w){try{return r=w,s(),Ie().then((function(U){try{return i=U?y:Ye(y,r),s(),n=S.initialQuality||1,a=S.fileType||R.type,Oe(i,a,R.name,R.lastModified,n).then((function(v){try{{let c=function(){if(u--&&(T>f||T>A)){let P,E;return P=l?.95*x.width:x.width,E=l?.95*x.height:x.height,[O,g]=Te(P,E),g.drawImage(x,0,0,P,E),n*=a==="image/png"?.85:.95,Oe(O,a,R.name,R.lastModified,n).then(function(k){try{return I=k,de(x),x=O,T=I.size,b(Math.min(99,Math.floor((H-T)/(H-f)*100))),c}catch(B){return e(B)}},e)}return[1]},o=function(){return de(x),de(O),de(y),de(i),de(p),b(100),h(I)};if(d=v,s(),F=d.size>f,C=d.size>R.size,!F&&!C)return b(100),h(d);var _;return A=R.size,H=d.size,T=H,x=i,l=!S.alwaysKeepResolution&&F,(_=(function(P){for(;P;){if(P.then)return void P.then(_,e);try{if(P.pop){if(P.length)return P.pop()?o.call(this):P;P=c}else P=P.call(this)}catch(E){return e(E)}}}).bind(this))(c)}}catch(c){return e(c)}}).bind(this),e)}catch(v){return e(v)}}).bind(this),e)}catch(U){return e(U)}}).bind(this),e)}catch(w){return e(w)}}).bind(this),e)})}const st=`
let scriptImported = false
self.addEventListener('message', async (e) => {
  const { file, id, imageCompressionLibUrl, options } = e.data
  options.onProgress = (progress) => self.postMessage({ progress, id })
  try {
    if (!scriptImported) {
      // console.log('[worker] importScripts', imageCompressionLibUrl)
      self.importScripts(imageCompressionLibUrl)
      scriptImported = true
    }
    // console.log('[worker] self', self)
    const compressedFile = await imageCompression(file, options)
    self.postMessage({ file: compressedFile, id })
  } catch (e) {
    // console.error('[worker] error', e)
    self.postMessage({ error: e.message + '\\n' + e.stack, id })
  }
})
`;let ze;function lt(R,S){return new Promise((L,h)=>{ze||(ze=function(u){const f=[];return f.push(u),URL.createObjectURL(new Blob(f))}(st));const e=new Worker(ze);e.addEventListener("message",function(u){if(S.signal&&S.signal.aborted)e.terminate();else if(u.data.progress===void 0){if(u.data.error)return h(new Error(u.data.error)),void e.terminate();L(u.data.file),e.terminate()}else S.onProgress(u.data.progress)}),e.addEventListener("error",h),S.signal&&S.signal.addEventListener("abort",()=>{h(S.signal.reason),e.terminate()}),e.postMessage({file:R,imageCompressionLibUrl:S.libURL,options:{...S,onProgress:void 0,signal:void 0}})})}function ne(R,S){return new Promise(function(L,h){let e,t,u,f,p,y;if(e={...S},u=0,{onProgress:f}=e,e.maxSizeMB=e.maxSizeMB||Number.POSITIVE_INFINITY,p=typeof e.useWebWorker!="boolean"||e.useWebWorker,delete e.useWebWorker,e.onProgress=a=>{u=a,typeof f=="function"&&f(u)},!(R instanceof Blob||R instanceof ft))return h(new Error("The file given is not an instance of Blob or File"));if(!/^image/.test(R.type))return h(new Error("The file given is not an image"));if(y=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,!p||typeof Worker!="function"||y)return Je(R,e).then((function(a){try{return t=a,n.call(this)}catch(d){return h(d)}}).bind(this),h);var r=(function(){try{return n.call(this)}catch(a){return h(a)}}).bind(this),i=function(a){try{return Je(R,e).then(function(d){try{return t=d,r()}catch(F){return h(F)}},h)}catch(d){return h(d)}};try{return e.libURL=e.libURL||"@2.0.2/dist/browser-image-compression.js",lt(R,e).then(function(a){try{return t=a,r()}catch{return i()}},i)}catch{i()}function n(){try{t.name=R.name,t.lastModified=R.lastModified}catch{}try{e.preserveExif&&R.type==="image/jpeg"&&(!e.fileType||e.fileType&&e.fileType===R.type)&&(t=Ne(R,t))}catch{}return L(t)}})}ne.getDataUrlFromFile=je,ne.getFilefromDataUrl=De,ne.loadImage=Ke,ne.drawImageInCanvas=$e,ne.drawFileInCanvas=ke,ne.canvasToFile=Oe,ne.getExifOrientation=Xe,ne.handleMaxWidthOrHeight=Ve,ne.followExifOrientation=Ye,ne.cleanupCanvasMemory=de,ne.isAutoOrientationInBrowser=Ie,ne.approximateBelowMaximumCanvasSizeOfBrowser=Ze,ne.copyExifWithoutOrientation=Ne,ne.getBrowserName=ye,ne.version="2.0.2",self.addEventListener("message",async R=>{const S=await ne(R.data.image,{useWebWorker:!1,maxSizeMB:.5,maxWidthOrHeight:1024});postMessage(S)})})();
//# sourceMappingURL=worker-BT2Ku40G.js.map
