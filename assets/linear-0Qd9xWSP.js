import{aM as un,aN as T,aO as X,aP as Q,aQ as fn}from"./MermaidPreview-B7IzGWQB.js";import{i as cn}from"./init-DBeb8MCR.js";(function(){try{var n=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},t=new n.Error().stack;t&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[t]="0fb4b6ad-ff2b-4af4-8403-0811fee68da0",n._sentryDebugIdIdentifier="sentry-dbid-0fb4b6ad-ff2b-4af4-8403-0811fee68da0")}catch{}})();function I(n,t){return n==null||t==null?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function hn(n,t){return n==null||t==null?NaN:t<n?-1:t>n?1:t>=n?0:NaN}function W(n){let t,e,r;n.length!==2?(t=I,e=(u,c)=>I(n(u),c),r=(u,c)=>n(u)-c):(t=n===I||n===hn?n:sn,e=n,r=n);function i(u,c,o=0,m=u.length){if(o<m){if(t(c,c)!==0)return m;do{const h=o+m>>>1;e(u[h],c)<0?o=h+1:m=h}while(o<m)}return o}function f(u,c,o=0,m=u.length){if(o<m){if(t(c,c)!==0)return m;do{const h=o+m>>>1;e(u[h],c)<=0?o=h+1:m=h}while(o<m)}return o}function a(u,c,o=0,m=u.length){const h=i(u,c,o,m-1);return h>o&&r(u[h-1],c)>-r(u[h],c)?h-1:h}return{left:i,center:a,right:f}}function sn(){return 0}function ln(n){return n===null?NaN:+n}const mn=W(I),dn=mn.right;W(ln).center;const gn=Math.sqrt(50),yn=Math.sqrt(10),Mn=Math.sqrt(2);function $(n,t,e){const r=(t-n)/Math.max(0,e),i=Math.floor(Math.log10(r)),f=r/Math.pow(10,i),a=f>=gn?10:f>=yn?5:f>=Mn?2:1;let u,c,o;return i<0?(o=Math.pow(10,-i)/a,u=Math.round(n*o),c=Math.round(t*o),u/o<n&&++u,c/o>t&&--c,o=-o):(o=Math.pow(10,i)*a,u=Math.round(n/o),c=Math.round(t/o),u*o<n&&++u,c*o>t&&--c),c<u&&.5<=e&&e<2?$(n,t,e*2):[u,c,o]}function pn(n,t,e){if(t=+t,n=+n,e=+e,!(e>0))return[];if(n===t)return[n];const r=t<n,[i,f,a]=r?$(t,n,e):$(n,t,e);if(!(f>=i))return[];const u=f-i+1,c=new Array(u);if(r)if(a<0)for(let o=0;o<u;++o)c[o]=(f-o)/-a;else for(let o=0;o<u;++o)c[o]=(f-o)*a;else if(a<0)for(let o=0;o<u;++o)c[o]=(i+o)/-a;else for(let o=0;o<u;++o)c[o]=(i+o)*a;return c}function L(n,t,e){return t=+t,n=+n,e=+e,$(n,t,e)[2]}function wn(n,t,e){t=+t,n=+n,e=+e;const r=t<n,i=r?L(t,n,e):L(n,t,e);return(r?-1:1)*(i<0?1/-i:i)}function Nn(n,t){t||(t=[]);var e=n?Math.min(t.length,n.length):0,r=t.slice(),i;return function(f){for(i=0;i<e;++i)r[i]=n[i]*(1-f)+t[i]*f;return r}}function kn(n){return ArrayBuffer.isView(n)&&!(n instanceof DataView)}function bn(n,t){var e=t?t.length:0,r=n?Math.min(e,n.length):0,i=new Array(r),f=new Array(e),a;for(a=0;a<r;++a)i[a]=q(n[a],t[a]);for(;a<e;++a)f[a]=t[a];return function(u){for(a=0;a<r;++a)f[a]=i[a](u);return f}}function xn(n,t){var e=new Date;return n=+n,t=+t,function(r){return e.setTime(n*(1-r)+t*r),e}}function An(n,t){var e={},r={},i;(n===null||typeof n!="object")&&(n={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in n?e[i]=q(n[i],t[i]):r[i]=t[i];return function(f){for(i in e)r[i]=e[i](f);return r}}function q(n,t){var e=typeof t,r;return t==null||e==="boolean"?un(t):(e==="number"?T:e==="string"?(r=X(t))?(t=r,Q):fn:t instanceof X?Q:t instanceof Date?xn:kn(t)?Nn:Array.isArray(t)?bn:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?An:T)(n,t)}function vn(n,t){return n=+n,t=+t,function(e){return Math.round(n*(1-e)+t*e)}}function Sn(n){return Math.abs(n=Math.round(n))>=1e21?n.toLocaleString("en").replace(/,/g,""):n.toString(10)}function E(n,t){if((e=(n=t?n.toExponential(t-1):n.toExponential()).indexOf("e"))<0)return null;var e,r=n.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+n.slice(e+1)]}function A(n){return n=E(Math.abs(n)),n?n[1]:NaN}function Pn(n,t){return function(e,r){for(var i=e.length,f=[],a=0,u=n[0],c=0;i>0&&u>0&&(c+u+1>r&&(u=Math.max(1,r-c)),f.push(e.substring(i-=u,i+u)),!((c+=u+1)>r));)u=n[a=(a+1)%n.length];return f.reverse().join(t)}}function jn(n){return function(t){return t.replace(/[0-9]/g,function(e){return n[+e]})}}var zn=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function F(n){if(!(t=zn.exec(n)))throw new Error("invalid format: "+n);var t;return new C({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}F.prototype=C.prototype;function C(n){this.fill=n.fill===void 0?" ":n.fill+"",this.align=n.align===void 0?">":n.align+"",this.sign=n.sign===void 0?"-":n.sign+"",this.symbol=n.symbol===void 0?"":n.symbol+"",this.zero=!!n.zero,this.width=n.width===void 0?void 0:+n.width,this.comma=!!n.comma,this.precision=n.precision===void 0?void 0:+n.precision,this.trim=!!n.trim,this.type=n.type===void 0?"":n.type+""}C.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function Dn(n){n:for(var t=n.length,e=1,r=-1,i;e<t;++e)switch(n[e]){case".":r=i=e;break;case"0":r===0&&(r=e),i=e;break;default:if(!+n[e])break n;r>0&&(r=0);break}return r>0?n.slice(0,r)+n.slice(i+1):n}var nn;function In(n,t){var e=E(n,t);if(!e)return n+"";var r=e[0],i=e[1],f=i-(nn=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,a=r.length;return f===a?r:f>a?r+new Array(f-a+1).join("0"):f>0?r.slice(0,f)+"."+r.slice(f):"0."+new Array(1-f).join("0")+E(n,Math.max(0,t+f-1))[0]}function U(n,t){var e=E(n,t);if(!e)return n+"";var r=e[0],i=e[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}const Y={"%":(n,t)=>(n*100).toFixed(t),b:n=>Math.round(n).toString(2),c:n=>n+"",d:Sn,e:(n,t)=>n.toExponential(t),f:(n,t)=>n.toFixed(t),g:(n,t)=>n.toPrecision(t),o:n=>Math.round(n).toString(8),p:(n,t)=>U(n*100,t),r:U,s:In,X:n=>Math.round(n).toString(16).toUpperCase(),x:n=>Math.round(n).toString(16)};function Z(n){return n}var H=Array.prototype.map,J=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function $n(n){var t=n.grouping===void 0||n.thousands===void 0?Z:Pn(H.call(n.grouping,Number),n.thousands+""),e=n.currency===void 0?"":n.currency[0]+"",r=n.currency===void 0?"":n.currency[1]+"",i=n.decimal===void 0?".":n.decimal+"",f=n.numerals===void 0?Z:jn(H.call(n.numerals,String)),a=n.percent===void 0?"%":n.percent+"",u=n.minus===void 0?"−":n.minus+"",c=n.nan===void 0?"NaN":n.nan+"";function o(h){h=F(h);var l=h.fill,p=h.align,y=h.sign,v=h.symbol,k=h.zero,S=h.width,R=h.comma,w=h.precision,O=h.trim,d=h.type;d==="n"?(R=!0,d="g"):Y[d]||(w===void 0&&(w=12),O=!0,d="g"),(k||l==="0"&&p==="=")&&(k=!0,l="0",p="=");var en=v==="$"?e:v==="#"&&/[boxX]/.test(d)?"0"+d.toLowerCase():"",on=v==="$"?r:/[%p]/.test(d)?a:"",B=Y[d],an=/[defgprs%]/.test(d);w=w===void 0?6:/[gprs]/.test(d)?Math.max(1,Math.min(21,w)):Math.max(0,Math.min(20,w));function G(s){var N=en,g=on,b,V,P;if(d==="c")g=B(s)+g,s="";else{s=+s;var j=s<0||1/s<0;if(s=isNaN(s)?c:B(Math.abs(s),w),O&&(s=Dn(s)),j&&+s==0&&y!=="+"&&(j=!1),N=(j?y==="("?y:u:y==="-"||y==="("?"":y)+N,g=(d==="s"?J[8+nn/3]:"")+g+(j&&y==="("?")":""),an){for(b=-1,V=s.length;++b<V;)if(P=s.charCodeAt(b),48>P||P>57){g=(P===46?i+s.slice(b+1):s.slice(b))+g,s=s.slice(0,b);break}}}R&&!k&&(s=t(s,1/0));var z=N.length+s.length+g.length,M=z<S?new Array(S-z+1).join(l):"";switch(R&&k&&(s=t(M+s,M.length?S-g.length:1/0),M=""),p){case"<":s=N+s+g+M;break;case"=":s=N+M+s+g;break;case"^":s=M.slice(0,z=M.length>>1)+N+s+g+M.slice(z);break;default:s=M+N+s+g;break}return f(s)}return G.toString=function(){return h+""},G}function m(h,l){var p=o((h=F(h),h.type="f",h)),y=Math.max(-8,Math.min(8,Math.floor(A(l)/3)))*3,v=Math.pow(10,-y),k=J[8+y/3];return function(S){return p(v*S)+k}}return{format:o,formatPrefix:m}}var D,tn,rn;En({thousands:",",grouping:[3],currency:["$",""]});function En(n){return D=$n(n),tn=D.format,rn=D.formatPrefix,D}function Fn(n){return Math.max(0,-A(Math.abs(n)))}function Rn(n,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(A(t)/3)))*3-A(Math.abs(n)))}function Tn(n,t){return n=Math.abs(n),t=Math.abs(t)-n,Math.max(0,A(t)-A(n))+1}function Ln(n){return function(){return n}}function _n(n){return+n}var K=[0,1];function x(n){return n}function _(n,t){return(t-=n=+n)?function(e){return(e-n)/t}:Ln(isNaN(t)?NaN:.5)}function qn(n,t){var e;return n>t&&(e=n,n=t,t=e),function(r){return Math.max(n,Math.min(t,r))}}function Cn(n,t,e){var r=n[0],i=n[1],f=t[0],a=t[1];return i<r?(r=_(i,r),f=e(a,f)):(r=_(r,i),f=e(f,a)),function(u){return f(r(u))}}function On(n,t,e){var r=Math.min(n.length,t.length)-1,i=new Array(r),f=new Array(r),a=-1;for(n[r]<n[0]&&(n=n.slice().reverse(),t=t.slice().reverse());++a<r;)i[a]=_(n[a],n[a+1]),f[a]=e(t[a],t[a+1]);return function(u){var c=dn(n,u,1,r)-1;return f[c](i[c](u))}}function Bn(n,t){return t.domain(n.domain()).range(n.range()).interpolate(n.interpolate()).clamp(n.clamp()).unknown(n.unknown())}function Gn(){var n=K,t=K,e=q,r,i,f,a=x,u,c,o;function m(){var l=Math.min(n.length,t.length);return a!==x&&(a=qn(n[0],n[l-1])),u=l>2?On:Cn,c=o=null,h}function h(l){return l==null||isNaN(l=+l)?f:(c||(c=u(n.map(r),t,e)))(r(a(l)))}return h.invert=function(l){return a(i((o||(o=u(t,n.map(r),T)))(l)))},h.domain=function(l){return arguments.length?(n=Array.from(l,_n),m()):n.slice()},h.range=function(l){return arguments.length?(t=Array.from(l),m()):t.slice()},h.rangeRound=function(l){return t=Array.from(l),e=vn,m()},h.clamp=function(l){return arguments.length?(a=l?!0:x,m()):a!==x},h.interpolate=function(l){return arguments.length?(e=l,m()):e},h.unknown=function(l){return arguments.length?(f=l,h):f},function(l,p){return r=l,i=p,m()}}function Vn(){return Gn()(x,x)}function Xn(n,t,e,r){var i=wn(n,t,e),f;switch(r=F(r??",f"),r.type){case"s":{var a=Math.max(Math.abs(n),Math.abs(t));return r.precision==null&&!isNaN(f=Rn(i,a))&&(r.precision=f),rn(r,a)}case"":case"e":case"g":case"p":case"r":{r.precision==null&&!isNaN(f=Tn(i,Math.max(Math.abs(n),Math.abs(t))))&&(r.precision=f-(r.type==="e"));break}case"f":case"%":{r.precision==null&&!isNaN(f=Fn(i))&&(r.precision=f-(r.type==="%")*2);break}}return tn(r)}function Qn(n){var t=n.domain;return n.ticks=function(e){var r=t();return pn(r[0],r[r.length-1],e??10)},n.tickFormat=function(e,r){var i=t();return Xn(i[0],i[i.length-1],e??10,r)},n.nice=function(e){e==null&&(e=10);var r=t(),i=0,f=r.length-1,a=r[i],u=r[f],c,o,m=10;for(u<a&&(o=a,a=u,u=o,o=i,i=f,f=o);m-- >0;){if(o=L(a,u,e),o===c)return r[i]=a,r[f]=u,t(r);if(o>0)a=Math.floor(a/o)*o,u=Math.ceil(u/o)*o;else if(o<0)a=Math.ceil(a*o)/o,u=Math.floor(u*o)/o;else break;c=o}return n},n}function Un(){var n=Vn();return n.copy=function(){return Bn(n,Un())},cn.apply(n,arguments),Qn(n)}export{Bn as a,W as b,Vn as c,Un as l,wn as t};
//# sourceMappingURL=linear-0Qd9xWSP.js.map
