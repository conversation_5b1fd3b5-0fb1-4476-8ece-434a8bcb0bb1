import{_ as n,T as l,m as d}from"./MermaidPreview-B7IzGWQB.js";(function(){try{var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},t=new r.Error().stack;t&&(r._sentryDebugIds=r._sentryDebugIds||{},r._sentryDebugIds[t]="f68e2ede-87dd-4379-9087-3b8ba289d6c2",r._sentryDebugIdIdentifier="sentry-dbid-f68e2ede-87dd-4379-9087-3b8ba289d6c2")}catch{}})();var o=n((r,t)=>{const e=r.append("rect");if(e.attr("x",t.x),e.attr("y",t.y),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("width",t.width),e.attr("height",t.height),t.name&&e.attr("name",t.name),t.rx&&e.attr("rx",t.rx),t.ry&&e.attr("ry",t.ry),t.attrs!==void 0)for(const s in t.attrs)e.attr(s,t.attrs[s]);return t.class&&e.attr("class",t.class),e},"drawRect"),g=n((r,t)=>{const e={x:t.startx,y:t.starty,width:t.stopx-t.startx,height:t.stopy-t.starty,fill:t.fill,stroke:t.stroke,class:"rect"};o(r,e).lower()},"drawBackgroundRect"),f=n((r,t)=>{const e=t.text.replace(l," "),s=r.append("text");s.attr("x",t.x),s.attr("y",t.y),s.attr("class","legend"),s.style("text-anchor",t.anchor),t.class&&s.attr("class",t.class);const a=s.append("tspan");return a.attr("x",t.x+t.textMargin*2),a.text(e),s},"drawText"),y=n((r,t,e,s)=>{const a=r.append("image");a.attr("x",t),a.attr("y",e);const i=d(s);a.attr("xlink:href",i)},"drawImage"),x=n((r,t,e,s)=>{const a=r.append("use");a.attr("x",t),a.attr("y",e);const i=d(s);a.attr("xlink:href",`#${i}`)},"drawEmbeddedImage"),h=n(()=>({x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0}),"getNoteRect"),m=n(()=>({x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:!0}),"getTextObj");export{m as a,g as b,x as c,o as d,y as e,f,h as g};
//# sourceMappingURL=chunk-D6G4REZN-DU3WvR8M.js.map
