import{f as t}from"./map-CcMwK8nF.js";import{s as f,o as n}from"./user-config-BaX6AisS.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},d=new e.Error().stack;d&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[d]="edf9a161-adf2-40a4-909b-72f6d95a0d50",e._sentryDebugIdIdentifier="sentry-dbid-edf9a161-adf2-40a4-909b-72f6d95a0d50")}catch{}})();function r(e){return f(n(e,void 0,t),e+"")}export{r as f};
//# sourceMappingURL=_flatRest-CBW7P83M.js.map
