import{d as Se,g as De}from"./chunk-D6G4REZN-DU3WvR8M.js";import{_ as g,a as Pe,s as Be,g as Ie,b as Me,c as Le,d as Bt,w as Ne,e as $t,f as de,h as Tt,i as ge,j as jt,l as fe,k as Ye,m as je}from"./MermaidPreview-B7IzGWQB.js";import"./premium-DZfa0MBj.js";import"./user-config-BaX6AisS.js";import"./merge-RIL0UuGj.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},t=new e.Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="2c1b5944-64e3-4afe-a862-297b062e2749",e._sentryDebugIdIdentifier="sentry-dbid-2c1b5944-64e3-4afe-a862-297b062e2749")}catch{}})();var Ft=function(){var e=g(function(_t,x,m,v){for(m=m||{},v=_t.length;v--;m[_t[v]]=x);return m},"o"),t=[1,24],s=[1,25],o=[1,26],l=[1,27],a=[1,28],r=[1,63],n=[1,64],i=[1,65],u=[1,66],d=[1,67],f=[1,68],y=[1,69],E=[1,29],O=[1,30],S=[1,31],P=[1,32],M=[1,33],U=[1,34],H=[1,35],q=[1,36],G=[1,37],K=[1,38],J=[1,39],Z=[1,40],$=[1,41],tt=[1,42],et=[1,43],at=[1,44],it=[1,45],nt=[1,46],rt=[1,47],st=[1,48],lt=[1,50],ot=[1,51],ct=[1,52],ht=[1,53],ut=[1,54],dt=[1,55],ft=[1,56],pt=[1,57],yt=[1,58],gt=[1,59],bt=[1,60],Ct=[14,42],Qt=[14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],St=[12,14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],k=[1,82],A=[1,83],C=[1,84],w=[1,85],T=[12,14,42],le=[12,14,33,42],Mt=[12,14,33,42,76,77,79,80],vt=[12,33],Ht=[34,36,37,38,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],qt={trace:g(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mermaidDoc:4,direction:5,direction_tb:6,direction_bt:7,direction_rl:8,direction_lr:9,graphConfig:10,C4_CONTEXT:11,NEWLINE:12,statements:13,EOF:14,C4_CONTAINER:15,C4_COMPONENT:16,C4_DYNAMIC:17,C4_DEPLOYMENT:18,otherStatements:19,diagramStatements:20,otherStatement:21,title:22,accDescription:23,acc_title:24,acc_title_value:25,acc_descr:26,acc_descr_value:27,acc_descr_multiline_value:28,boundaryStatement:29,boundaryStartStatement:30,boundaryStopStatement:31,boundaryStart:32,LBRACE:33,ENTERPRISE_BOUNDARY:34,attributes:35,SYSTEM_BOUNDARY:36,BOUNDARY:37,CONTAINER_BOUNDARY:38,NODE:39,NODE_L:40,NODE_R:41,RBRACE:42,diagramStatement:43,PERSON:44,PERSON_EXT:45,SYSTEM:46,SYSTEM_DB:47,SYSTEM_QUEUE:48,SYSTEM_EXT:49,SYSTEM_EXT_DB:50,SYSTEM_EXT_QUEUE:51,CONTAINER:52,CONTAINER_DB:53,CONTAINER_QUEUE:54,CONTAINER_EXT:55,CONTAINER_EXT_DB:56,CONTAINER_EXT_QUEUE:57,COMPONENT:58,COMPONENT_DB:59,COMPONENT_QUEUE:60,COMPONENT_EXT:61,COMPONENT_EXT_DB:62,COMPONENT_EXT_QUEUE:63,REL:64,BIREL:65,REL_U:66,REL_D:67,REL_L:68,REL_R:69,REL_B:70,REL_INDEX:71,UPDATE_EL_STYLE:72,UPDATE_REL_STYLE:73,UPDATE_LAYOUT_CONFIG:74,attribute:75,STR:76,STR_KEY:77,STR_VALUE:78,ATTRIBUTE:79,ATTRIBUTE_EMPTY:80,$accept:0,$end:1},terminals_:{2:"error",6:"direction_tb",7:"direction_bt",8:"direction_rl",9:"direction_lr",11:"C4_CONTEXT",12:"NEWLINE",14:"EOF",15:"C4_CONTAINER",16:"C4_COMPONENT",17:"C4_DYNAMIC",18:"C4_DEPLOYMENT",22:"title",23:"accDescription",24:"acc_title",25:"acc_title_value",26:"acc_descr",27:"acc_descr_value",28:"acc_descr_multiline_value",33:"LBRACE",34:"ENTERPRISE_BOUNDARY",36:"SYSTEM_BOUNDARY",37:"BOUNDARY",38:"CONTAINER_BOUNDARY",39:"NODE",40:"NODE_L",41:"NODE_R",42:"RBRACE",44:"PERSON",45:"PERSON_EXT",46:"SYSTEM",47:"SYSTEM_DB",48:"SYSTEM_QUEUE",49:"SYSTEM_EXT",50:"SYSTEM_EXT_DB",51:"SYSTEM_EXT_QUEUE",52:"CONTAINER",53:"CONTAINER_DB",54:"CONTAINER_QUEUE",55:"CONTAINER_EXT",56:"CONTAINER_EXT_DB",57:"CONTAINER_EXT_QUEUE",58:"COMPONENT",59:"COMPONENT_DB",60:"COMPONENT_QUEUE",61:"COMPONENT_EXT",62:"COMPONENT_EXT_DB",63:"COMPONENT_EXT_QUEUE",64:"REL",65:"BIREL",66:"REL_U",67:"REL_D",68:"REL_L",69:"REL_R",70:"REL_B",71:"REL_INDEX",72:"UPDATE_EL_STYLE",73:"UPDATE_REL_STYLE",74:"UPDATE_LAYOUT_CONFIG",76:"STR",77:"STR_KEY",78:"STR_VALUE",79:"ATTRIBUTE",80:"ATTRIBUTE_EMPTY"},productions_:[0,[3,1],[3,1],[5,1],[5,1],[5,1],[5,1],[4,1],[10,4],[10,4],[10,4],[10,4],[10,4],[13,1],[13,1],[13,2],[19,1],[19,2],[19,3],[21,1],[21,1],[21,2],[21,2],[21,1],[29,3],[30,3],[30,3],[30,4],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[31,1],[20,1],[20,2],[20,3],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,1],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[35,1],[35,2],[75,1],[75,2],[75,1],[75,1]],performAction:g(function(x,m,v,b,R,h,Dt){var p=h.length-1;switch(R){case 3:b.setDirection("TB");break;case 4:b.setDirection("BT");break;case 5:b.setDirection("RL");break;case 6:b.setDirection("LR");break;case 8:case 9:case 10:case 11:case 12:b.setC4Type(h[p-3]);break;case 19:b.setTitle(h[p].substring(6)),this.$=h[p].substring(6);break;case 20:b.setAccDescription(h[p].substring(15)),this.$=h[p].substring(15);break;case 21:this.$=h[p].trim(),b.setTitle(this.$);break;case 22:case 23:this.$=h[p].trim(),b.setAccDescription(this.$);break;case 28:h[p].splice(2,0,"ENTERPRISE"),b.addPersonOrSystemBoundary(...h[p]),this.$=h[p];break;case 29:h[p].splice(2,0,"SYSTEM"),b.addPersonOrSystemBoundary(...h[p]),this.$=h[p];break;case 30:b.addPersonOrSystemBoundary(...h[p]),this.$=h[p];break;case 31:h[p].splice(2,0,"CONTAINER"),b.addContainerBoundary(...h[p]),this.$=h[p];break;case 32:b.addDeploymentNode("node",...h[p]),this.$=h[p];break;case 33:b.addDeploymentNode("nodeL",...h[p]),this.$=h[p];break;case 34:b.addDeploymentNode("nodeR",...h[p]),this.$=h[p];break;case 35:b.popBoundaryParseStack();break;case 39:b.addPersonOrSystem("person",...h[p]),this.$=h[p];break;case 40:b.addPersonOrSystem("external_person",...h[p]),this.$=h[p];break;case 41:b.addPersonOrSystem("system",...h[p]),this.$=h[p];break;case 42:b.addPersonOrSystem("system_db",...h[p]),this.$=h[p];break;case 43:b.addPersonOrSystem("system_queue",...h[p]),this.$=h[p];break;case 44:b.addPersonOrSystem("external_system",...h[p]),this.$=h[p];break;case 45:b.addPersonOrSystem("external_system_db",...h[p]),this.$=h[p];break;case 46:b.addPersonOrSystem("external_system_queue",...h[p]),this.$=h[p];break;case 47:b.addContainer("container",...h[p]),this.$=h[p];break;case 48:b.addContainer("container_db",...h[p]),this.$=h[p];break;case 49:b.addContainer("container_queue",...h[p]),this.$=h[p];break;case 50:b.addContainer("external_container",...h[p]),this.$=h[p];break;case 51:b.addContainer("external_container_db",...h[p]),this.$=h[p];break;case 52:b.addContainer("external_container_queue",...h[p]),this.$=h[p];break;case 53:b.addComponent("component",...h[p]),this.$=h[p];break;case 54:b.addComponent("component_db",...h[p]),this.$=h[p];break;case 55:b.addComponent("component_queue",...h[p]),this.$=h[p];break;case 56:b.addComponent("external_component",...h[p]),this.$=h[p];break;case 57:b.addComponent("external_component_db",...h[p]),this.$=h[p];break;case 58:b.addComponent("external_component_queue",...h[p]),this.$=h[p];break;case 60:b.addRel("rel",...h[p]),this.$=h[p];break;case 61:b.addRel("birel",...h[p]),this.$=h[p];break;case 62:b.addRel("rel_u",...h[p]),this.$=h[p];break;case 63:b.addRel("rel_d",...h[p]),this.$=h[p];break;case 64:b.addRel("rel_l",...h[p]),this.$=h[p];break;case 65:b.addRel("rel_r",...h[p]),this.$=h[p];break;case 66:b.addRel("rel_b",...h[p]),this.$=h[p];break;case 67:h[p].splice(0,1),b.addRel("rel",...h[p]),this.$=h[p];break;case 68:b.updateElStyle("update_el_style",...h[p]),this.$=h[p];break;case 69:b.updateRelStyle("update_rel_style",...h[p]),this.$=h[p];break;case 70:b.updateLayoutConfig("update_layout_config",...h[p]),this.$=h[p];break;case 71:this.$=[h[p]];break;case 72:h[p].unshift(h[p-1]),this.$=h[p];break;case 73:case 75:this.$=h[p].trim();break;case 74:let Et={};Et[h[p-1].trim()]=h[p].trim(),this.$=Et;break;case 76:this.$="";break}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],7:[1,6],8:[1,7],9:[1,8],10:4,11:[1,9],15:[1,10],16:[1,11],17:[1,12],18:[1,13]},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,7]},{1:[2,3]},{1:[2,4]},{1:[2,5]},{1:[2,6]},{12:[1,14]},{12:[1,15]},{12:[1,16]},{12:[1,17]},{12:[1,18]},{13:19,19:20,20:21,21:22,22:t,23:s,24:o,26:l,28:a,29:49,30:61,32:62,34:r,36:n,37:i,38:u,39:d,40:f,41:y,43:23,44:E,45:O,46:S,47:P,48:M,49:U,50:H,51:q,52:G,53:K,54:J,55:Z,56:$,57:tt,58:et,59:at,60:it,61:nt,62:rt,63:st,64:lt,65:ot,66:ct,67:ht,68:ut,69:dt,70:ft,71:pt,72:yt,73:gt,74:bt},{13:70,19:20,20:21,21:22,22:t,23:s,24:o,26:l,28:a,29:49,30:61,32:62,34:r,36:n,37:i,38:u,39:d,40:f,41:y,43:23,44:E,45:O,46:S,47:P,48:M,49:U,50:H,51:q,52:G,53:K,54:J,55:Z,56:$,57:tt,58:et,59:at,60:it,61:nt,62:rt,63:st,64:lt,65:ot,66:ct,67:ht,68:ut,69:dt,70:ft,71:pt,72:yt,73:gt,74:bt},{13:71,19:20,20:21,21:22,22:t,23:s,24:o,26:l,28:a,29:49,30:61,32:62,34:r,36:n,37:i,38:u,39:d,40:f,41:y,43:23,44:E,45:O,46:S,47:P,48:M,49:U,50:H,51:q,52:G,53:K,54:J,55:Z,56:$,57:tt,58:et,59:at,60:it,61:nt,62:rt,63:st,64:lt,65:ot,66:ct,67:ht,68:ut,69:dt,70:ft,71:pt,72:yt,73:gt,74:bt},{13:72,19:20,20:21,21:22,22:t,23:s,24:o,26:l,28:a,29:49,30:61,32:62,34:r,36:n,37:i,38:u,39:d,40:f,41:y,43:23,44:E,45:O,46:S,47:P,48:M,49:U,50:H,51:q,52:G,53:K,54:J,55:Z,56:$,57:tt,58:et,59:at,60:it,61:nt,62:rt,63:st,64:lt,65:ot,66:ct,67:ht,68:ut,69:dt,70:ft,71:pt,72:yt,73:gt,74:bt},{13:73,19:20,20:21,21:22,22:t,23:s,24:o,26:l,28:a,29:49,30:61,32:62,34:r,36:n,37:i,38:u,39:d,40:f,41:y,43:23,44:E,45:O,46:S,47:P,48:M,49:U,50:H,51:q,52:G,53:K,54:J,55:Z,56:$,57:tt,58:et,59:at,60:it,61:nt,62:rt,63:st,64:lt,65:ot,66:ct,67:ht,68:ut,69:dt,70:ft,71:pt,72:yt,73:gt,74:bt},{14:[1,74]},e(Ct,[2,13],{43:23,29:49,30:61,32:62,20:75,34:r,36:n,37:i,38:u,39:d,40:f,41:y,44:E,45:O,46:S,47:P,48:M,49:U,50:H,51:q,52:G,53:K,54:J,55:Z,56:$,57:tt,58:et,59:at,60:it,61:nt,62:rt,63:st,64:lt,65:ot,66:ct,67:ht,68:ut,69:dt,70:ft,71:pt,72:yt,73:gt,74:bt}),e(Ct,[2,14]),e(Qt,[2,16],{12:[1,76]}),e(Ct,[2,36],{12:[1,77]}),e(St,[2,19]),e(St,[2,20]),{25:[1,78]},{27:[1,79]},e(St,[2,23]),{35:80,75:81,76:k,77:A,79:C,80:w},{35:86,75:81,76:k,77:A,79:C,80:w},{35:87,75:81,76:k,77:A,79:C,80:w},{35:88,75:81,76:k,77:A,79:C,80:w},{35:89,75:81,76:k,77:A,79:C,80:w},{35:90,75:81,76:k,77:A,79:C,80:w},{35:91,75:81,76:k,77:A,79:C,80:w},{35:92,75:81,76:k,77:A,79:C,80:w},{35:93,75:81,76:k,77:A,79:C,80:w},{35:94,75:81,76:k,77:A,79:C,80:w},{35:95,75:81,76:k,77:A,79:C,80:w},{35:96,75:81,76:k,77:A,79:C,80:w},{35:97,75:81,76:k,77:A,79:C,80:w},{35:98,75:81,76:k,77:A,79:C,80:w},{35:99,75:81,76:k,77:A,79:C,80:w},{35:100,75:81,76:k,77:A,79:C,80:w},{35:101,75:81,76:k,77:A,79:C,80:w},{35:102,75:81,76:k,77:A,79:C,80:w},{35:103,75:81,76:k,77:A,79:C,80:w},{35:104,75:81,76:k,77:A,79:C,80:w},e(T,[2,59]),{35:105,75:81,76:k,77:A,79:C,80:w},{35:106,75:81,76:k,77:A,79:C,80:w},{35:107,75:81,76:k,77:A,79:C,80:w},{35:108,75:81,76:k,77:A,79:C,80:w},{35:109,75:81,76:k,77:A,79:C,80:w},{35:110,75:81,76:k,77:A,79:C,80:w},{35:111,75:81,76:k,77:A,79:C,80:w},{35:112,75:81,76:k,77:A,79:C,80:w},{35:113,75:81,76:k,77:A,79:C,80:w},{35:114,75:81,76:k,77:A,79:C,80:w},{35:115,75:81,76:k,77:A,79:C,80:w},{20:116,29:49,30:61,32:62,34:r,36:n,37:i,38:u,39:d,40:f,41:y,43:23,44:E,45:O,46:S,47:P,48:M,49:U,50:H,51:q,52:G,53:K,54:J,55:Z,56:$,57:tt,58:et,59:at,60:it,61:nt,62:rt,63:st,64:lt,65:ot,66:ct,67:ht,68:ut,69:dt,70:ft,71:pt,72:yt,73:gt,74:bt},{12:[1,118],33:[1,117]},{35:119,75:81,76:k,77:A,79:C,80:w},{35:120,75:81,76:k,77:A,79:C,80:w},{35:121,75:81,76:k,77:A,79:C,80:w},{35:122,75:81,76:k,77:A,79:C,80:w},{35:123,75:81,76:k,77:A,79:C,80:w},{35:124,75:81,76:k,77:A,79:C,80:w},{35:125,75:81,76:k,77:A,79:C,80:w},{14:[1,126]},{14:[1,127]},{14:[1,128]},{14:[1,129]},{1:[2,8]},e(Ct,[2,15]),e(Qt,[2,17],{21:22,19:130,22:t,23:s,24:o,26:l,28:a}),e(Ct,[2,37],{19:20,20:21,21:22,43:23,29:49,30:61,32:62,13:131,22:t,23:s,24:o,26:l,28:a,34:r,36:n,37:i,38:u,39:d,40:f,41:y,44:E,45:O,46:S,47:P,48:M,49:U,50:H,51:q,52:G,53:K,54:J,55:Z,56:$,57:tt,58:et,59:at,60:it,61:nt,62:rt,63:st,64:lt,65:ot,66:ct,67:ht,68:ut,69:dt,70:ft,71:pt,72:yt,73:gt,74:bt}),e(St,[2,21]),e(St,[2,22]),e(T,[2,39]),e(le,[2,71],{75:81,35:132,76:k,77:A,79:C,80:w}),e(Mt,[2,73]),{78:[1,133]},e(Mt,[2,75]),e(Mt,[2,76]),e(T,[2,40]),e(T,[2,41]),e(T,[2,42]),e(T,[2,43]),e(T,[2,44]),e(T,[2,45]),e(T,[2,46]),e(T,[2,47]),e(T,[2,48]),e(T,[2,49]),e(T,[2,50]),e(T,[2,51]),e(T,[2,52]),e(T,[2,53]),e(T,[2,54]),e(T,[2,55]),e(T,[2,56]),e(T,[2,57]),e(T,[2,58]),e(T,[2,60]),e(T,[2,61]),e(T,[2,62]),e(T,[2,63]),e(T,[2,64]),e(T,[2,65]),e(T,[2,66]),e(T,[2,67]),e(T,[2,68]),e(T,[2,69]),e(T,[2,70]),{31:134,42:[1,135]},{12:[1,136]},{33:[1,137]},e(vt,[2,28]),e(vt,[2,29]),e(vt,[2,30]),e(vt,[2,31]),e(vt,[2,32]),e(vt,[2,33]),e(vt,[2,34]),{1:[2,9]},{1:[2,10]},{1:[2,11]},{1:[2,12]},e(Qt,[2,18]),e(Ct,[2,38]),e(le,[2,72]),e(Mt,[2,74]),e(T,[2,24]),e(T,[2,35]),e(Ht,[2,25]),e(Ht,[2,26],{12:[1,138]}),e(Ht,[2,27])],defaultActions:{2:[2,1],3:[2,2],4:[2,7],5:[2,3],6:[2,4],7:[2,5],8:[2,6],74:[2,8],126:[2,9],127:[2,10],128:[2,11],129:[2,12]},parseError:g(function(x,m){if(m.recoverable)this.trace(x);else{var v=new Error(x);throw v.hash=m,v}},"parseError"),parse:g(function(x){var m=this,v=[0],b=[],R=[null],h=[],Dt=this.table,p="",Et=0,oe=0,we=2,ce=1,Te=h.slice.call(arguments,1),D=Object.create(this.lexer),kt={yy:{}};for(var Gt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Gt)&&(kt.yy[Gt]=this.yy[Gt]);D.setInput(x,kt.yy),kt.yy.lexer=D,kt.yy.parser=this,typeof D.yylloc>"u"&&(D.yylloc={});var Kt=D.yylloc;h.push(Kt);var Oe=D.options&&D.options.ranges;typeof kt.yy.parseError=="function"?this.parseError=kt.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Re(L){v.length=v.length-2*L,R.length=R.length-L,h.length=h.length-L}g(Re,"popStack");function he(){var L;return L=b.pop()||D.lex()||ce,typeof L!="number"&&(L instanceof Array&&(b=L,L=b.pop()),L=m.symbols_[L]||L),L}g(he,"lex");for(var I,At,N,Jt,wt={},Nt,W,ue,Yt;;){if(At=v[v.length-1],this.defaultActions[At]?N=this.defaultActions[At]:((I===null||typeof I>"u")&&(I=he()),N=Dt[At]&&Dt[At][I]),typeof N>"u"||!N.length||!N[0]){var Zt="";Yt=[];for(Nt in Dt[At])this.terminals_[Nt]&&Nt>we&&Yt.push("'"+this.terminals_[Nt]+"'");D.showPosition?Zt="Parse error on line "+(Et+1)+`:
`+D.showPosition()+`
Expecting `+Yt.join(", ")+", got '"+(this.terminals_[I]||I)+"'":Zt="Parse error on line "+(Et+1)+": Unexpected "+(I==ce?"end of input":"'"+(this.terminals_[I]||I)+"'"),this.parseError(Zt,{text:D.match,token:this.terminals_[I]||I,line:D.yylineno,loc:Kt,expected:Yt})}if(N[0]instanceof Array&&N.length>1)throw new Error("Parse Error: multiple actions possible at state: "+At+", token: "+I);switch(N[0]){case 1:v.push(I),R.push(D.yytext),h.push(D.yylloc),v.push(N[1]),I=null,oe=D.yyleng,p=D.yytext,Et=D.yylineno,Kt=D.yylloc;break;case 2:if(W=this.productions_[N[1]][1],wt.$=R[R.length-W],wt._$={first_line:h[h.length-(W||1)].first_line,last_line:h[h.length-1].last_line,first_column:h[h.length-(W||1)].first_column,last_column:h[h.length-1].last_column},Oe&&(wt._$.range=[h[h.length-(W||1)].range[0],h[h.length-1].range[1]]),Jt=this.performAction.apply(wt,[p,oe,Et,kt.yy,N[1],R,h].concat(Te)),typeof Jt<"u")return Jt;W&&(v=v.slice(0,-1*W*2),R=R.slice(0,-1*W),h=h.slice(0,-1*W)),v.push(this.productions_[N[1]][0]),R.push(wt.$),h.push(wt._$),ue=Dt[v[v.length-2]][v[v.length-1]],v.push(ue);break;case 3:return!0}}return!0},"parse")},Ce=function(){var _t={EOF:1,parseError:g(function(m,v){if(this.yy.parser)this.yy.parser.parseError(m,v);else throw new Error(m)},"parseError"),setInput:g(function(x,m){return this.yy=m||this.yy||{},this._input=x,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:g(function(){var x=this._input[0];this.yytext+=x,this.yyleng++,this.offset++,this.match+=x,this.matched+=x;var m=x.match(/(?:\r\n?|\n).*/g);return m?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),x},"input"),unput:g(function(x){var m=x.length,v=x.split(/(?:\r\n?|\n)/g);this._input=x+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-m),this.offset-=m;var b=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),v.length-1&&(this.yylineno-=v.length-1);var R=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:v?(v.length===b.length?this.yylloc.first_column:0)+b[b.length-v.length].length-v[0].length:this.yylloc.first_column-m},this.options.ranges&&(this.yylloc.range=[R[0],R[0]+this.yyleng-m]),this.yyleng=this.yytext.length,this},"unput"),more:g(function(){return this._more=!0,this},"more"),reject:g(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:g(function(x){this.unput(this.match.slice(x))},"less"),pastInput:g(function(){var x=this.matched.substr(0,this.matched.length-this.match.length);return(x.length>20?"...":"")+x.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:g(function(){var x=this.match;return x.length<20&&(x+=this._input.substr(0,20-x.length)),(x.substr(0,20)+(x.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:g(function(){var x=this.pastInput(),m=new Array(x.length+1).join("-");return x+this.upcomingInput()+`
`+m+"^"},"showPosition"),test_match:g(function(x,m){var v,b,R;if(this.options.backtrack_lexer&&(R={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(R.yylloc.range=this.yylloc.range.slice(0))),b=x[0].match(/(?:\r\n?|\n).*/g),b&&(this.yylineno+=b.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:b?b[b.length-1].length-b[b.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+x[0].length},this.yytext+=x[0],this.match+=x[0],this.matches=x,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(x[0].length),this.matched+=x[0],v=this.performAction.call(this,this.yy,this,m,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),v)return v;if(this._backtrack){for(var h in R)this[h]=R[h];return!1}return!1},"test_match"),next:g(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var x,m,v,b;this._more||(this.yytext="",this.match="");for(var R=this._currentRules(),h=0;h<R.length;h++)if(v=this._input.match(this.rules[R[h]]),v&&(!m||v[0].length>m[0].length)){if(m=v,b=h,this.options.backtrack_lexer){if(x=this.test_match(v,R[h]),x!==!1)return x;if(this._backtrack){m=!1;continue}else return!1}else if(!this.options.flex)break}return m?(x=this.test_match(m,R[b]),x!==!1?x:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:g(function(){var m=this.next();return m||this.lex()},"lex"),begin:g(function(m){this.conditionStack.push(m)},"begin"),popState:g(function(){var m=this.conditionStack.length-1;return m>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:g(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:g(function(m){return m=this.conditionStack.length-1-Math.abs(m||0),m>=0?this.conditionStack[m]:"INITIAL"},"topState"),pushState:g(function(m){this.begin(m)},"pushState"),stateStackSize:g(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:g(function(m,v,b,R){switch(b){case 0:return 6;case 1:return 7;case 2:return 8;case 3:return 9;case 4:return 22;case 5:return 23;case 6:return this.begin("acc_title"),24;case 7:return this.popState(),"acc_title_value";case 8:return this.begin("acc_descr"),26;case 9:return this.popState(),"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:break;case 14:c;break;case 15:return 12;case 16:break;case 17:return 11;case 18:return 15;case 19:return 16;case 20:return 17;case 21:return 18;case 22:return this.begin("person_ext"),45;case 23:return this.begin("person"),44;case 24:return this.begin("system_ext_queue"),51;case 25:return this.begin("system_ext_db"),50;case 26:return this.begin("system_ext"),49;case 27:return this.begin("system_queue"),48;case 28:return this.begin("system_db"),47;case 29:return this.begin("system"),46;case 30:return this.begin("boundary"),37;case 31:return this.begin("enterprise_boundary"),34;case 32:return this.begin("system_boundary"),36;case 33:return this.begin("container_ext_queue"),57;case 34:return this.begin("container_ext_db"),56;case 35:return this.begin("container_ext"),55;case 36:return this.begin("container_queue"),54;case 37:return this.begin("container_db"),53;case 38:return this.begin("container"),52;case 39:return this.begin("container_boundary"),38;case 40:return this.begin("component_ext_queue"),63;case 41:return this.begin("component_ext_db"),62;case 42:return this.begin("component_ext"),61;case 43:return this.begin("component_queue"),60;case 44:return this.begin("component_db"),59;case 45:return this.begin("component"),58;case 46:return this.begin("node"),39;case 47:return this.begin("node"),39;case 48:return this.begin("node_l"),40;case 49:return this.begin("node_r"),41;case 50:return this.begin("rel"),64;case 51:return this.begin("birel"),65;case 52:return this.begin("rel_u"),66;case 53:return this.begin("rel_u"),66;case 54:return this.begin("rel_d"),67;case 55:return this.begin("rel_d"),67;case 56:return this.begin("rel_l"),68;case 57:return this.begin("rel_l"),68;case 58:return this.begin("rel_r"),69;case 59:return this.begin("rel_r"),69;case 60:return this.begin("rel_b"),70;case 61:return this.begin("rel_index"),71;case 62:return this.begin("update_el_style"),72;case 63:return this.begin("update_rel_style"),73;case 64:return this.begin("update_layout_config"),74;case 65:return"EOF_IN_STRUCT";case 66:return this.begin("attribute"),"ATTRIBUTE_EMPTY";case 67:this.begin("attribute");break;case 68:this.popState(),this.popState();break;case 69:return 80;case 70:break;case 71:return 80;case 72:this.begin("string");break;case 73:this.popState();break;case 74:return"STR";case 75:this.begin("string_kv");break;case 76:return this.begin("string_kv_key"),"STR_KEY";case 77:this.popState(),this.begin("string_kv_value");break;case 78:return"STR_VALUE";case 79:this.popState(),this.popState();break;case 80:return"STR";case 81:return"LBRACE";case 82:return"RBRACE";case 83:return"SPACE";case 84:return"EOL";case 85:return 14}},"anonymous"),rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:title\s[^#\n;]+)/,/^(?:accDescription\s[^#\n;]+)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:C4Context\b)/,/^(?:C4Container\b)/,/^(?:C4Component\b)/,/^(?:C4Dynamic\b)/,/^(?:C4Deployment\b)/,/^(?:Person_Ext\b)/,/^(?:Person\b)/,/^(?:SystemQueue_Ext\b)/,/^(?:SystemDb_Ext\b)/,/^(?:System_Ext\b)/,/^(?:SystemQueue\b)/,/^(?:SystemDb\b)/,/^(?:System\b)/,/^(?:Boundary\b)/,/^(?:Enterprise_Boundary\b)/,/^(?:System_Boundary\b)/,/^(?:ContainerQueue_Ext\b)/,/^(?:ContainerDb_Ext\b)/,/^(?:Container_Ext\b)/,/^(?:ContainerQueue\b)/,/^(?:ContainerDb\b)/,/^(?:Container\b)/,/^(?:Container_Boundary\b)/,/^(?:ComponentQueue_Ext\b)/,/^(?:ComponentDb_Ext\b)/,/^(?:Component_Ext\b)/,/^(?:ComponentQueue\b)/,/^(?:ComponentDb\b)/,/^(?:Component\b)/,/^(?:Deployment_Node\b)/,/^(?:Node\b)/,/^(?:Node_L\b)/,/^(?:Node_R\b)/,/^(?:Rel\b)/,/^(?:BiRel\b)/,/^(?:Rel_Up\b)/,/^(?:Rel_U\b)/,/^(?:Rel_Down\b)/,/^(?:Rel_D\b)/,/^(?:Rel_Left\b)/,/^(?:Rel_L\b)/,/^(?:Rel_Right\b)/,/^(?:Rel_R\b)/,/^(?:Rel_Back\b)/,/^(?:RelIndex\b)/,/^(?:UpdateElementStyle\b)/,/^(?:UpdateRelStyle\b)/,/^(?:UpdateLayoutConfig\b)/,/^(?:$)/,/^(?:[(][ ]*[,])/,/^(?:[(])/,/^(?:[)])/,/^(?:,,)/,/^(?:,)/,/^(?:[ ]*["]["])/,/^(?:[ ]*["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:[ ]*[\$])/,/^(?:[^=]*)/,/^(?:[=][ ]*["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:[^,]+)/,/^(?:\{)/,/^(?:\})/,/^(?:[\s]+)/,/^(?:[\n\r]+)/,/^(?:$)/],conditions:{acc_descr_multiline:{rules:[11,12],inclusive:!1},acc_descr:{rules:[9],inclusive:!1},acc_title:{rules:[7],inclusive:!1},string_kv_value:{rules:[78,79],inclusive:!1},string_kv_key:{rules:[77],inclusive:!1},string_kv:{rules:[76],inclusive:!1},string:{rules:[73,74],inclusive:!1},attribute:{rules:[68,69,70,71,72,75,80],inclusive:!1},update_layout_config:{rules:[65,66,67,68],inclusive:!1},update_rel_style:{rules:[65,66,67,68],inclusive:!1},update_el_style:{rules:[65,66,67,68],inclusive:!1},rel_b:{rules:[65,66,67,68],inclusive:!1},rel_r:{rules:[65,66,67,68],inclusive:!1},rel_l:{rules:[65,66,67,68],inclusive:!1},rel_d:{rules:[65,66,67,68],inclusive:!1},rel_u:{rules:[65,66,67,68],inclusive:!1},rel_bi:{rules:[],inclusive:!1},rel:{rules:[65,66,67,68],inclusive:!1},node_r:{rules:[65,66,67,68],inclusive:!1},node_l:{rules:[65,66,67,68],inclusive:!1},node:{rules:[65,66,67,68],inclusive:!1},index:{rules:[],inclusive:!1},rel_index:{rules:[65,66,67,68],inclusive:!1},component_ext_queue:{rules:[],inclusive:!1},component_ext_db:{rules:[65,66,67,68],inclusive:!1},component_ext:{rules:[65,66,67,68],inclusive:!1},component_queue:{rules:[65,66,67,68],inclusive:!1},component_db:{rules:[65,66,67,68],inclusive:!1},component:{rules:[65,66,67,68],inclusive:!1},container_boundary:{rules:[65,66,67,68],inclusive:!1},container_ext_queue:{rules:[65,66,67,68],inclusive:!1},container_ext_db:{rules:[65,66,67,68],inclusive:!1},container_ext:{rules:[65,66,67,68],inclusive:!1},container_queue:{rules:[65,66,67,68],inclusive:!1},container_db:{rules:[65,66,67,68],inclusive:!1},container:{rules:[65,66,67,68],inclusive:!1},birel:{rules:[65,66,67,68],inclusive:!1},system_boundary:{rules:[65,66,67,68],inclusive:!1},enterprise_boundary:{rules:[65,66,67,68],inclusive:!1},boundary:{rules:[65,66,67,68],inclusive:!1},system_ext_queue:{rules:[65,66,67,68],inclusive:!1},system_ext_db:{rules:[65,66,67,68],inclusive:!1},system_ext:{rules:[65,66,67,68],inclusive:!1},system_queue:{rules:[65,66,67,68],inclusive:!1},system_db:{rules:[65,66,67,68],inclusive:!1},system:{rules:[65,66,67,68],inclusive:!1},person_ext:{rules:[65,66,67,68],inclusive:!1},person:{rules:[65,66,67,68],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,81,82,83,84,85],inclusive:!0}}};return _t}();qt.lexer=Ce;function Lt(){this.yy={}}return g(Lt,"Parser"),Lt.prototype=qt,qt.Parser=Lt,new Lt}();Ft.parser=Ft;var Ue=Ft,V=[],xt=[""],B="global",F="",X=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}],It=[],ie="",ne=!1,Vt=4,zt=2,be,Fe=g(function(){return be},"getC4Type"),Ve=g(function(e){be=ge(e,Bt())},"setC4Type"),ze=g(function(e,t,s,o,l,a,r,n,i){if(e==null||t===void 0||t===null||s===void 0||s===null||o===void 0||o===null)return;let u={};const d=It.find(f=>f.from===t&&f.to===s);if(d?u=d:It.push(u),u.type=e,u.from=t,u.to=s,u.label={text:o},l==null)u.techn={text:""};else if(typeof l=="object"){let[f,y]=Object.entries(l)[0];u[f]={text:y}}else u.techn={text:l};if(a==null)u.descr={text:""};else if(typeof a=="object"){let[f,y]=Object.entries(a)[0];u[f]={text:y}}else u.descr={text:a};if(typeof r=="object"){let[f,y]=Object.entries(r)[0];u[f]=y}else u.sprite=r;if(typeof n=="object"){let[f,y]=Object.entries(n)[0];u[f]=y}else u.tags=n;if(typeof i=="object"){let[f,y]=Object.entries(i)[0];u[f]=y}else u.link=i;u.wrap=mt()},"addRel"),Xe=g(function(e,t,s,o,l,a,r){if(t===null||s===null)return;let n={};const i=V.find(u=>u.alias===t);if(i&&t===i.alias?n=i:(n.alias=t,V.push(n)),s==null?n.label={text:""}:n.label={text:s},o==null)n.descr={text:""};else if(typeof o=="object"){let[u,d]=Object.entries(o)[0];n[u]={text:d}}else n.descr={text:o};if(typeof l=="object"){let[u,d]=Object.entries(l)[0];n[u]=d}else n.sprite=l;if(typeof a=="object"){let[u,d]=Object.entries(a)[0];n[u]=d}else n.tags=a;if(typeof r=="object"){let[u,d]=Object.entries(r)[0];n[u]=d}else n.link=r;n.typeC4Shape={text:e},n.parentBoundary=B,n.wrap=mt()},"addPersonOrSystem"),We=g(function(e,t,s,o,l,a,r,n){if(t===null||s===null)return;let i={};const u=V.find(d=>d.alias===t);if(u&&t===u.alias?i=u:(i.alias=t,V.push(i)),s==null?i.label={text:""}:i.label={text:s},o==null)i.techn={text:""};else if(typeof o=="object"){let[d,f]=Object.entries(o)[0];i[d]={text:f}}else i.techn={text:o};if(l==null)i.descr={text:""};else if(typeof l=="object"){let[d,f]=Object.entries(l)[0];i[d]={text:f}}else i.descr={text:l};if(typeof a=="object"){let[d,f]=Object.entries(a)[0];i[d]=f}else i.sprite=a;if(typeof r=="object"){let[d,f]=Object.entries(r)[0];i[d]=f}else i.tags=r;if(typeof n=="object"){let[d,f]=Object.entries(n)[0];i[d]=f}else i.link=n;i.wrap=mt(),i.typeC4Shape={text:e},i.parentBoundary=B},"addContainer"),Qe=g(function(e,t,s,o,l,a,r,n){if(t===null||s===null)return;let i={};const u=V.find(d=>d.alias===t);if(u&&t===u.alias?i=u:(i.alias=t,V.push(i)),s==null?i.label={text:""}:i.label={text:s},o==null)i.techn={text:""};else if(typeof o=="object"){let[d,f]=Object.entries(o)[0];i[d]={text:f}}else i.techn={text:o};if(l==null)i.descr={text:""};else if(typeof l=="object"){let[d,f]=Object.entries(l)[0];i[d]={text:f}}else i.descr={text:l};if(typeof a=="object"){let[d,f]=Object.entries(a)[0];i[d]=f}else i.sprite=a;if(typeof r=="object"){let[d,f]=Object.entries(r)[0];i[d]=f}else i.tags=r;if(typeof n=="object"){let[d,f]=Object.entries(n)[0];i[d]=f}else i.link=n;i.wrap=mt(),i.typeC4Shape={text:e},i.parentBoundary=B},"addComponent"),He=g(function(e,t,s,o,l){if(e===null||t===null)return;let a={};const r=X.find(n=>n.alias===e);if(r&&e===r.alias?a=r:(a.alias=e,X.push(a)),t==null?a.label={text:""}:a.label={text:t},s==null)a.type={text:"system"};else if(typeof s=="object"){let[n,i]=Object.entries(s)[0];a[n]={text:i}}else a.type={text:s};if(typeof o=="object"){let[n,i]=Object.entries(o)[0];a[n]=i}else a.tags=o;if(typeof l=="object"){let[n,i]=Object.entries(l)[0];a[n]=i}else a.link=l;a.parentBoundary=B,a.wrap=mt(),F=B,B=e,xt.push(F)},"addPersonOrSystemBoundary"),qe=g(function(e,t,s,o,l){if(e===null||t===null)return;let a={};const r=X.find(n=>n.alias===e);if(r&&e===r.alias?a=r:(a.alias=e,X.push(a)),t==null?a.label={text:""}:a.label={text:t},s==null)a.type={text:"container"};else if(typeof s=="object"){let[n,i]=Object.entries(s)[0];a[n]={text:i}}else a.type={text:s};if(typeof o=="object"){let[n,i]=Object.entries(o)[0];a[n]=i}else a.tags=o;if(typeof l=="object"){let[n,i]=Object.entries(l)[0];a[n]=i}else a.link=l;a.parentBoundary=B,a.wrap=mt(),F=B,B=e,xt.push(F)},"addContainerBoundary"),Ge=g(function(e,t,s,o,l,a,r,n){if(t===null||s===null)return;let i={};const u=X.find(d=>d.alias===t);if(u&&t===u.alias?i=u:(i.alias=t,X.push(i)),s==null?i.label={text:""}:i.label={text:s},o==null)i.type={text:"node"};else if(typeof o=="object"){let[d,f]=Object.entries(o)[0];i[d]={text:f}}else i.type={text:o};if(l==null)i.descr={text:""};else if(typeof l=="object"){let[d,f]=Object.entries(l)[0];i[d]={text:f}}else i.descr={text:l};if(typeof r=="object"){let[d,f]=Object.entries(r)[0];i[d]=f}else i.tags=r;if(typeof n=="object"){let[d,f]=Object.entries(n)[0];i[d]=f}else i.link=n;i.nodeType=e,i.parentBoundary=B,i.wrap=mt(),F=B,B=t,xt.push(F)},"addDeploymentNode"),Ke=g(function(){B=F,xt.pop(),F=xt.pop(),xt.push(F)},"popBoundaryParseStack"),Je=g(function(e,t,s,o,l,a,r,n,i,u,d){let f=V.find(y=>y.alias===t);if(!(f===void 0&&(f=X.find(y=>y.alias===t),f===void 0))){if(s!=null)if(typeof s=="object"){let[y,E]=Object.entries(s)[0];f[y]=E}else f.bgColor=s;if(o!=null)if(typeof o=="object"){let[y,E]=Object.entries(o)[0];f[y]=E}else f.fontColor=o;if(l!=null)if(typeof l=="object"){let[y,E]=Object.entries(l)[0];f[y]=E}else f.borderColor=l;if(a!=null)if(typeof a=="object"){let[y,E]=Object.entries(a)[0];f[y]=E}else f.shadowing=a;if(r!=null)if(typeof r=="object"){let[y,E]=Object.entries(r)[0];f[y]=E}else f.shape=r;if(n!=null)if(typeof n=="object"){let[y,E]=Object.entries(n)[0];f[y]=E}else f.sprite=n;if(i!=null)if(typeof i=="object"){let[y,E]=Object.entries(i)[0];f[y]=E}else f.techn=i;if(u!=null)if(typeof u=="object"){let[y,E]=Object.entries(u)[0];f[y]=E}else f.legendText=u;if(d!=null)if(typeof d=="object"){let[y,E]=Object.entries(d)[0];f[y]=E}else f.legendSprite=d}},"updateElStyle"),Ze=g(function(e,t,s,o,l,a,r){const n=It.find(i=>i.from===t&&i.to===s);if(n!==void 0){if(o!=null)if(typeof o=="object"){let[i,u]=Object.entries(o)[0];n[i]=u}else n.textColor=o;if(l!=null)if(typeof l=="object"){let[i,u]=Object.entries(l)[0];n[i]=u}else n.lineColor=l;if(a!=null)if(typeof a=="object"){let[i,u]=Object.entries(a)[0];n[i]=parseInt(u)}else n.offsetX=parseInt(a);if(r!=null)if(typeof r=="object"){let[i,u]=Object.entries(r)[0];n[i]=parseInt(u)}else n.offsetY=parseInt(r)}},"updateRelStyle"),$e=g(function(e,t,s){let o=Vt,l=zt;if(typeof t=="object"){const a=Object.values(t)[0];o=parseInt(a)}else o=parseInt(t);if(typeof s=="object"){const a=Object.values(s)[0];l=parseInt(a)}else l=parseInt(s);o>=1&&(Vt=o),l>=1&&(zt=l)},"updateLayoutConfig"),t0=g(function(){return Vt},"getC4ShapeInRow"),e0=g(function(){return zt},"getC4BoundaryInRow"),a0=g(function(){return B},"getCurrentBoundaryParse"),i0=g(function(){return F},"getParentBoundaryParse"),_e=g(function(e){return e==null?V:V.filter(t=>t.parentBoundary===e)},"getC4ShapeArray"),n0=g(function(e){return V.find(t=>t.alias===e)},"getC4Shape"),r0=g(function(e){return Object.keys(_e(e))},"getC4ShapeKeys"),xe=g(function(e){return e==null?X:X.filter(t=>t.parentBoundary===e)},"getBoundaries"),s0=xe,l0=g(function(){return It},"getRels"),o0=g(function(){return ie},"getTitle"),c0=g(function(e){ne=e},"setWrap"),mt=g(function(){return ne},"autoWrap"),h0=g(function(){V=[],X=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}],F="",B="global",xt=[""],It=[],xt=[""],ie="",ne=!1,Vt=4,zt=2},"clear"),u0={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25},d0={FILLED:0,OPEN:1},f0={LEFTOF:0,RIGHTOF:1,OVER:2},p0=g(function(e){ie=ge(e,Bt())},"setTitle"),te={addPersonOrSystem:Xe,addPersonOrSystemBoundary:He,addContainer:We,addContainerBoundary:qe,addComponent:Qe,addDeploymentNode:Ge,popBoundaryParseStack:Ke,addRel:ze,updateElStyle:Je,updateRelStyle:Ze,updateLayoutConfig:$e,autoWrap:mt,setWrap:c0,getC4ShapeArray:_e,getC4Shape:n0,getC4ShapeKeys:r0,getBoundaries:xe,getBoundarys:s0,getCurrentBoundaryParse:a0,getParentBoundaryParse:i0,getRels:l0,getTitle:o0,getC4Type:Fe,getC4ShapeInRow:t0,getC4BoundaryInRow:e0,setAccTitle:Be,getAccTitle:Ie,getAccDescription:Me,setAccDescription:Le,getConfig:g(()=>Bt().c4,"getConfig"),clear:h0,LINETYPE:u0,ARROWTYPE:d0,PLACEMENT:f0,setTitle:p0,setC4Type:Ve},re=g(function(e,t){return Se(e,t)},"drawRect"),me=g(function(e,t,s,o,l,a){const r=e.append("image");r.attr("width",t),r.attr("height",s),r.attr("x",o),r.attr("y",l);let n=a.startsWith("data:image/png;base64")?a:je(a);r.attr("xlink:href",n)},"drawImage"),y0=g((e,t,s)=>{const o=e.append("g");let l=0;for(let a of t){let r=a.textColor?a.textColor:"#444444",n=a.lineColor?a.lineColor:"#444444",i=a.offsetX?parseInt(a.offsetX):0,u=a.offsetY?parseInt(a.offsetY):0,d="";if(l===0){let y=o.append("line");y.attr("x1",a.startPoint.x),y.attr("y1",a.startPoint.y),y.attr("x2",a.endPoint.x),y.attr("y2",a.endPoint.y),y.attr("stroke-width","1"),y.attr("stroke",n),y.style("fill","none"),a.type!=="rel_b"&&y.attr("marker-end","url("+d+"#arrowhead)"),(a.type==="birel"||a.type==="rel_b")&&y.attr("marker-start","url("+d+"#arrowend)"),l=-1}else{let y=o.append("path");y.attr("fill","none").attr("stroke-width","1").attr("stroke",n).attr("d","Mstartx,starty Qcontrolx,controly stopx,stopy ".replaceAll("startx",a.startPoint.x).replaceAll("starty",a.startPoint.y).replaceAll("controlx",a.startPoint.x+(a.endPoint.x-a.startPoint.x)/2-(a.endPoint.x-a.startPoint.x)/4).replaceAll("controly",a.startPoint.y+(a.endPoint.y-a.startPoint.y)/2).replaceAll("stopx",a.endPoint.x).replaceAll("stopy",a.endPoint.y)),a.type!=="rel_b"&&y.attr("marker-end","url("+d+"#arrowhead)"),(a.type==="birel"||a.type==="rel_b")&&y.attr("marker-start","url("+d+"#arrowend)")}let f=s.messageFont();Q(s)(a.label.text,o,Math.min(a.startPoint.x,a.endPoint.x)+Math.abs(a.endPoint.x-a.startPoint.x)/2+i,Math.min(a.startPoint.y,a.endPoint.y)+Math.abs(a.endPoint.y-a.startPoint.y)/2+u,a.label.width,a.label.height,{fill:r},f),a.techn&&a.techn.text!==""&&(f=s.messageFont(),Q(s)("["+a.techn.text+"]",o,Math.min(a.startPoint.x,a.endPoint.x)+Math.abs(a.endPoint.x-a.startPoint.x)/2+i,Math.min(a.startPoint.y,a.endPoint.y)+Math.abs(a.endPoint.y-a.startPoint.y)/2+s.messageFontSize+5+u,Math.max(a.label.width,a.techn.width),a.techn.height,{fill:r,"font-style":"italic"},f))}},"drawRels"),g0=g(function(e,t,s){const o=e.append("g");let l=t.bgColor?t.bgColor:"none",a=t.borderColor?t.borderColor:"#444444",r=t.fontColor?t.fontColor:"black",n={"stroke-width":1,"stroke-dasharray":"7.0,7.0"};t.nodeType&&(n={"stroke-width":1});let i={x:t.x,y:t.y,fill:l,stroke:a,width:t.width,height:t.height,rx:2.5,ry:2.5,attrs:n};re(o,i);let u=s.boundaryFont();u.fontWeight="bold",u.fontSize=u.fontSize+2,u.fontColor=r,Q(s)(t.label.text,o,t.x,t.y+t.label.Y,t.width,t.height,{fill:"#444444"},u),t.type&&t.type.text!==""&&(u=s.boundaryFont(),u.fontColor=r,Q(s)(t.type.text,o,t.x,t.y+t.type.Y,t.width,t.height,{fill:"#444444"},u)),t.descr&&t.descr.text!==""&&(u=s.boundaryFont(),u.fontSize=u.fontSize-2,u.fontColor=r,Q(s)(t.descr.text,o,t.x,t.y+t.descr.Y,t.width,t.height,{fill:"#444444"},u))},"drawBoundary"),b0=g(function(e,t,s){var f;let o=t.bgColor?t.bgColor:s[t.typeC4Shape.text+"_bg_color"],l=t.borderColor?t.borderColor:s[t.typeC4Shape.text+"_border_color"],a=t.fontColor?t.fontColor:"#FFFFFF",r="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";switch(t.typeC4Shape.text){case"person":r="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";break;case"external_person":r="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=";break}const n=e.append("g");n.attr("class","person-man");const i=De();switch(t.typeC4Shape.text){case"person":case"external_person":case"system":case"external_system":case"container":case"external_container":case"component":case"external_component":i.x=t.x,i.y=t.y,i.fill=o,i.width=t.width,i.height=t.height,i.stroke=l,i.rx=2.5,i.ry=2.5,i.attrs={"stroke-width":.5},re(n,i);break;case"system_db":case"external_system_db":case"container_db":case"external_container_db":case"component_db":case"external_component_db":n.append("path").attr("fill",o).attr("stroke-width","0.5").attr("stroke",l).attr("d","Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height".replaceAll("startx",t.x).replaceAll("starty",t.y).replaceAll("half",t.width/2).replaceAll("height",t.height)),n.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",l).attr("d","Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10".replaceAll("startx",t.x).replaceAll("starty",t.y).replaceAll("half",t.width/2));break;case"system_queue":case"external_system_queue":case"container_queue":case"external_container_queue":case"component_queue":case"external_component_queue":n.append("path").attr("fill",o).attr("stroke-width","0.5").attr("stroke",l).attr("d","Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half".replaceAll("startx",t.x).replaceAll("starty",t.y).replaceAll("width",t.width).replaceAll("half",t.height/2)),n.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",l).attr("d","Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half".replaceAll("startx",t.x+t.width).replaceAll("starty",t.y).replaceAll("half",t.height/2));break}let u=w0(s,t.typeC4Shape.text);switch(n.append("text").attr("fill",a).attr("font-family",u.fontFamily).attr("font-size",u.fontSize-2).attr("font-style","italic").attr("lengthAdjust","spacing").attr("textLength",t.typeC4Shape.width).attr("x",t.x+t.width/2-t.typeC4Shape.width/2).attr("y",t.y+t.typeC4Shape.Y).text("<<"+t.typeC4Shape.text+">>"),t.typeC4Shape.text){case"person":case"external_person":me(n,48,48,t.x+t.width/2-24,t.y+t.image.Y,r);break}let d=s[t.typeC4Shape.text+"Font"]();return d.fontWeight="bold",d.fontSize=d.fontSize+2,d.fontColor=a,Q(s)(t.label.text,n,t.x,t.y+t.label.Y,t.width,t.height,{fill:a},d),d=s[t.typeC4Shape.text+"Font"](),d.fontColor=a,t.techn&&((f=t.techn)==null?void 0:f.text)!==""?Q(s)(t.techn.text,n,t.x,t.y+t.techn.Y,t.width,t.height,{fill:a,"font-style":"italic"},d):t.type&&t.type.text!==""&&Q(s)(t.type.text,n,t.x,t.y+t.type.Y,t.width,t.height,{fill:a,"font-style":"italic"},d),t.descr&&t.descr.text!==""&&(d=s.personFont(),d.fontColor=a,Q(s)(t.descr.text,n,t.x,t.y+t.descr.Y,t.width,t.height,{fill:a},d)),t.height},"drawC4Shape"),_0=g(function(e){e.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},"insertDatabaseIcon"),x0=g(function(e){e.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},"insertComputerIcon"),m0=g(function(e){e.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},"insertClockIcon"),v0=g(function(e){e.append("defs").append("marker").attr("id","arrowhead").attr("refX",9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z")},"insertArrowHead"),E0=g(function(e){e.append("defs").append("marker").attr("id","arrowend").attr("refX",1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 10 0 L 0 5 L 10 10 z")},"insertArrowEnd"),k0=g(function(e){e.append("defs").append("marker").attr("id","filled-head").attr("refX",18).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"insertArrowFilledHead"),A0=g(function(e){e.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},"insertDynamicNumber"),C0=g(function(e){const s=e.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",16).attr("refY",4);s.append("path").attr("fill","black").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 9,2 V 6 L16,4 Z"),s.append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 0,1 L 6,7 M 6,1 L 0,7")},"insertArrowCrossHead"),w0=g((e,t)=>({fontFamily:e[t+"FontFamily"],fontSize:e[t+"FontSize"],fontWeight:e[t+"FontWeight"]}),"getC4ShapeFont"),Q=function(){function e(l,a,r,n,i,u,d){const f=a.append("text").attr("x",r+i/2).attr("y",n+u/2+5).style("text-anchor","middle").text(l);o(f,d)}g(e,"byText");function t(l,a,r,n,i,u,d,f){const{fontSize:y,fontFamily:E,fontWeight:O}=f,S=l.split($t.lineBreakRegex);for(let P=0;P<S.length;P++){const M=P*y-y*(S.length-1)/2,U=a.append("text").attr("x",r+i/2).attr("y",n).style("text-anchor","middle").attr("dominant-baseline","middle").style("font-size",y).style("font-weight",O).style("font-family",E);U.append("tspan").attr("dy",M).text(S[P]).attr("alignment-baseline","mathematical"),o(U,d)}}g(t,"byTspan");function s(l,a,r,n,i,u,d,f){const y=a.append("switch"),O=y.append("foreignObject").attr("x",r).attr("y",n).attr("width",i).attr("height",u).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");O.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(l),t(l,y,r,n,i,u,d,f),o(O,d)}g(s,"byFo");function o(l,a){for(const r in a)a.hasOwnProperty(r)&&l.attr(r,a[r])}return g(o,"_setTextAttrs"),function(l){return l.textPlacement==="fo"?s:l.textPlacement==="old"?e:t}}(),z={drawRect:re,drawBoundary:g0,drawC4Shape:b0,drawRels:y0,drawImage:me,insertArrowHead:v0,insertArrowEnd:E0,insertArrowFilledHead:k0,insertDynamicNumber:A0,insertArrowCrossHead:C0,insertDatabaseIcon:_0,insertComputerIcon:x0,insertClockIcon:m0},Xt=0,Wt=0,ve=4,ee=2;Ft.yy=te;var _={},Ot,Ee=(Ot=class{constructor(t){this.name="",this.data={},this.data.startx=void 0,this.data.stopx=void 0,this.data.starty=void 0,this.data.stopy=void 0,this.data.widthLimit=void 0,this.nextData={},this.nextData.startx=void 0,this.nextData.stopx=void 0,this.nextData.starty=void 0,this.nextData.stopy=void 0,this.nextData.cnt=0,ae(t.db.getConfig())}setData(t,s,o,l){this.nextData.startx=this.data.startx=t,this.nextData.stopx=this.data.stopx=s,this.nextData.starty=this.data.starty=o,this.nextData.stopy=this.data.stopy=l}updateVal(t,s,o,l){t[s]===void 0?t[s]=o:t[s]=l(o,t[s])}insert(t){this.nextData.cnt=this.nextData.cnt+1;let s=this.nextData.startx===this.nextData.stopx?this.nextData.stopx+t.margin:this.nextData.stopx+t.margin*2,o=s+t.width,l=this.nextData.starty+t.margin*2,a=l+t.height;(s>=this.data.widthLimit||o>=this.data.widthLimit||this.nextData.cnt>ve)&&(s=this.nextData.startx+t.margin+_.nextLinePaddingX,l=this.nextData.stopy+t.margin*2,this.nextData.stopx=o=s+t.width,this.nextData.starty=this.nextData.stopy,this.nextData.stopy=a=l+t.height,this.nextData.cnt=1),t.x=s,t.y=l,this.updateVal(this.data,"startx",s,Math.min),this.updateVal(this.data,"starty",l,Math.min),this.updateVal(this.data,"stopx",o,Math.max),this.updateVal(this.data,"stopy",a,Math.max),this.updateVal(this.nextData,"startx",s,Math.min),this.updateVal(this.nextData,"starty",l,Math.min),this.updateVal(this.nextData,"stopx",o,Math.max),this.updateVal(this.nextData,"stopy",a,Math.max)}init(t){this.name="",this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,widthLimit:void 0},this.nextData={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,cnt:0},ae(t.db.getConfig())}bumpLastMargin(t){this.data.stopx+=t,this.data.stopy+=t}},g(Ot,"Bounds"),Ot),ae=g(function(e){Pe(_,e),e.fontFamily&&(_.personFontFamily=_.systemFontFamily=_.messageFontFamily=e.fontFamily),e.fontSize&&(_.personFontSize=_.systemFontSize=_.messageFontSize=e.fontSize),e.fontWeight&&(_.personFontWeight=_.systemFontWeight=_.messageFontWeight=e.fontWeight)},"setConf"),Pt=g((e,t)=>({fontFamily:e[t+"FontFamily"],fontSize:e[t+"FontSize"],fontWeight:e[t+"FontWeight"]}),"c4ShapeFont"),Ut=g(e=>({fontFamily:e.boundaryFontFamily,fontSize:e.boundaryFontSize,fontWeight:e.boundaryFontWeight}),"boundaryFont"),T0=g(e=>({fontFamily:e.messageFontFamily,fontSize:e.messageFontSize,fontWeight:e.messageFontWeight}),"messageFont");function j(e,t,s,o,l){if(!t[e].width)if(s)t[e].text=Ne(t[e].text,l,o),t[e].textLines=t[e].text.split($t.lineBreakRegex).length,t[e].width=l,t[e].height=de(t[e].text,o);else{let a=t[e].text.split($t.lineBreakRegex);t[e].textLines=a.length;let r=0;t[e].height=0,t[e].width=0;for(const n of a)t[e].width=Math.max(Tt(n,o),t[e].width),r=de(n,o),t[e].height=t[e].height+r}}g(j,"calcC4ShapeTextWH");var ke=g(function(e,t,s){t.x=s.data.startx,t.y=s.data.starty,t.width=s.data.stopx-s.data.startx,t.height=s.data.stopy-s.data.starty,t.label.y=_.c4ShapeMargin-35;let o=t.wrap&&_.wrap,l=Ut(_);l.fontSize=l.fontSize+2,l.fontWeight="bold";let a=Tt(t.label.text,l);j("label",t,o,l,a),z.drawBoundary(e,t,_)},"drawBoundary"),Ae=g(function(e,t,s,o){let l=0;for(const a of o){l=0;const r=s[a];let n=Pt(_,r.typeC4Shape.text);switch(n.fontSize=n.fontSize-2,r.typeC4Shape.width=Tt("«"+r.typeC4Shape.text+"»",n),r.typeC4Shape.height=n.fontSize+2,r.typeC4Shape.Y=_.c4ShapePadding,l=r.typeC4Shape.Y+r.typeC4Shape.height-4,r.image={width:0,height:0,Y:0},r.typeC4Shape.text){case"person":case"external_person":r.image.width=48,r.image.height=48,r.image.Y=l,l=r.image.Y+r.image.height;break}r.sprite&&(r.image.width=48,r.image.height=48,r.image.Y=l,l=r.image.Y+r.image.height);let i=r.wrap&&_.wrap,u=_.width-_.c4ShapePadding*2,d=Pt(_,r.typeC4Shape.text);if(d.fontSize=d.fontSize+2,d.fontWeight="bold",j("label",r,i,d,u),r.label.Y=l+8,l=r.label.Y+r.label.height,r.type&&r.type.text!==""){r.type.text="["+r.type.text+"]";let E=Pt(_,r.typeC4Shape.text);j("type",r,i,E,u),r.type.Y=l+5,l=r.type.Y+r.type.height}else if(r.techn&&r.techn.text!==""){r.techn.text="["+r.techn.text+"]";let E=Pt(_,r.techn.text);j("techn",r,i,E,u),r.techn.Y=l+5,l=r.techn.Y+r.techn.height}let f=l,y=r.label.width;if(r.descr&&r.descr.text!==""){let E=Pt(_,r.typeC4Shape.text);j("descr",r,i,E,u),r.descr.Y=l+20,l=r.descr.Y+r.descr.height,y=Math.max(r.label.width,r.descr.width),f=l-r.descr.textLines*5}y=y+_.c4ShapePadding,r.width=Math.max(r.width||_.width,y,_.width),r.height=Math.max(r.height||_.height,f,_.height),r.margin=r.margin||_.c4ShapeMargin,e.insert(r),z.drawC4Shape(t,r,_)}e.bumpLastMargin(_.c4ShapeMargin)},"drawC4ShapeArray"),Rt,Y=(Rt=class{constructor(t,s){this.x=t,this.y=s}},g(Rt,"Point"),Rt),pe=g(function(e,t){let s=e.x,o=e.y,l=t.x,a=t.y,r=s+e.width/2,n=o+e.height/2,i=Math.abs(s-l),u=Math.abs(o-a),d=u/i,f=e.height/e.width,y=null;return o==a&&s<l?y=new Y(s+e.width,n):o==a&&s>l?y=new Y(s,n):s==l&&o<a?y=new Y(r,o+e.height):s==l&&o>a&&(y=new Y(r,o)),s>l&&o<a?f>=d?y=new Y(s,n+d*e.width/2):y=new Y(r-i/u*e.height/2,o+e.height):s<l&&o<a?f>=d?y=new Y(s+e.width,n+d*e.width/2):y=new Y(r+i/u*e.height/2,o+e.height):s<l&&o>a?f>=d?y=new Y(s+e.width,n-d*e.width/2):y=new Y(r+e.height/2*i/u,o):s>l&&o>a&&(f>=d?y=new Y(s,n-e.width/2*d):y=new Y(r-e.height/2*i/u,o)),y},"getIntersectPoint"),O0=g(function(e,t){let s={x:0,y:0};s.x=t.x+t.width/2,s.y=t.y+t.height/2;let o=pe(e,s);s.x=e.x+e.width/2,s.y=e.y+e.height/2;let l=pe(t,s);return{startPoint:o,endPoint:l}},"getIntersectPoints"),R0=g(function(e,t,s,o){let l=0;for(let a of t){l=l+1;let r=a.wrap&&_.wrap,n=T0(_);o.db.getC4Type()==="C4Dynamic"&&(a.label.text=l+": "+a.label.text);let u=Tt(a.label.text,n);j("label",a,r,n,u),a.techn&&a.techn.text!==""&&(u=Tt(a.techn.text,n),j("techn",a,r,n,u)),a.descr&&a.descr.text!==""&&(u=Tt(a.descr.text,n),j("descr",a,r,n,u));let d=s(a.from),f=s(a.to),y=O0(d,f);a.startPoint=y.startPoint,a.endPoint=y.endPoint}z.drawRels(e,t,_)},"drawRels");function se(e,t,s,o,l){let a=new Ee(l);a.data.widthLimit=s.data.widthLimit/Math.min(ee,o.length);for(let[r,n]of o.entries()){let i=0;n.image={width:0,height:0,Y:0},n.sprite&&(n.image.width=48,n.image.height=48,n.image.Y=i,i=n.image.Y+n.image.height);let u=n.wrap&&_.wrap,d=Ut(_);if(d.fontSize=d.fontSize+2,d.fontWeight="bold",j("label",n,u,d,a.data.widthLimit),n.label.Y=i+8,i=n.label.Y+n.label.height,n.type&&n.type.text!==""){n.type.text="["+n.type.text+"]";let O=Ut(_);j("type",n,u,O,a.data.widthLimit),n.type.Y=i+5,i=n.type.Y+n.type.height}if(n.descr&&n.descr.text!==""){let O=Ut(_);O.fontSize=O.fontSize-2,j("descr",n,u,O,a.data.widthLimit),n.descr.Y=i+20,i=n.descr.Y+n.descr.height}if(r==0||r%ee===0){let O=s.data.startx+_.diagramMarginX,S=s.data.stopy+_.diagramMarginY+i;a.setData(O,O,S,S)}else{let O=a.data.stopx!==a.data.startx?a.data.stopx+_.diagramMarginX:a.data.startx,S=a.data.starty;a.setData(O,O,S,S)}a.name=n.alias;let f=l.db.getC4ShapeArray(n.alias),y=l.db.getC4ShapeKeys(n.alias);y.length>0&&Ae(a,e,f,y),t=n.alias;let E=l.db.getBoundarys(t);E.length>0&&se(e,t,a,E,l),n.alias!=="global"&&ke(e,n,a),s.data.stopy=Math.max(a.data.stopy+_.c4ShapeMargin,s.data.stopy),s.data.stopx=Math.max(a.data.stopx+_.c4ShapeMargin,s.data.stopx),Xt=Math.max(Xt,s.data.stopx),Wt=Math.max(Wt,s.data.stopy)}}g(se,"drawInsideBoundary");var S0=g(function(e,t,s,o){_=Bt().c4;const l=Bt().securityLevel;let a;l==="sandbox"&&(a=jt("#i"+t));const r=l==="sandbox"?jt(a.nodes()[0].contentDocument.body):jt("body");let n=o.db;o.db.setWrap(_.wrap),ve=n.getC4ShapeInRow(),ee=n.getC4BoundaryInRow(),fe.debug(`C:${JSON.stringify(_,null,2)}`);const i=l==="sandbox"?r.select(`[id="${t}"]`):jt(`[id="${t}"]`);z.insertComputerIcon(i),z.insertDatabaseIcon(i),z.insertClockIcon(i);let u=new Ee(o);u.setData(_.diagramMarginX,_.diagramMarginX,_.diagramMarginY,_.diagramMarginY),u.data.widthLimit=screen.availWidth,Xt=_.diagramMarginX,Wt=_.diagramMarginY;const d=o.db.getTitle();let f=o.db.getBoundarys("");se(i,"",u,f,o),z.insertArrowHead(i),z.insertArrowEnd(i),z.insertArrowCrossHead(i),z.insertArrowFilledHead(i),R0(i,o.db.getRels(),o.db.getC4Shape,o),u.data.stopx=Xt,u.data.stopy=Wt;const y=u.data;let O=y.stopy-y.starty+2*_.diagramMarginY;const P=y.stopx-y.startx+2*_.diagramMarginX;d&&i.append("text").text(d).attr("x",(y.stopx-y.startx)/2-4*_.diagramMarginX).attr("y",y.starty+_.diagramMarginY),Ye(i,O,P,_.useMaxWidth);const M=d?60:0;i.attr("viewBox",y.startx-_.diagramMarginX+" -"+(_.diagramMarginY+M)+" "+P+" "+(O+M)),fe.debug("models:",y)},"draw"),ye={drawPersonOrSystemArray:Ae,drawBoundary:ke,setConf:ae,draw:S0},D0=g(e=>`.person {
    stroke: ${e.personBorder};
    fill: ${e.personBkg};
  }
`,"getStyles"),P0=D0,Y0={parser:Ue,db:te,renderer:ye,styles:P0,init:g(({c4:e,wrap:t})=>{ye.setConf(e),te.setWrap(t)},"init")};export{Y0 as diagram};
//# sourceMappingURL=c4Diagram-VJAJSXHY-BpORWx-R.js.map
