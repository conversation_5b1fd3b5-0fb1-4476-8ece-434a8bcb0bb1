import{_ as s,j as r,k as l,l as g}from"./MermaidPreview-B7IzGWQB.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},t=new e.Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="83201853-1f6a-4d76-9beb-169c9f6f9406",e._sentryDebugIdIdentifier="sentry-dbid-83201853-1f6a-4d76-9beb-169c9f6f9406")}catch{}})();var w=s((e,t)=>{let o;return t==="sandbox"&&(o=r("#i"+e)),(t==="sandbox"?r(o.nodes()[0].contentDocument.body):r("body")).select(`[id="${e}"]`)},"getDiagramElement"),y=s((e,t,o,n)=>{e.attr("class",o);const{width:i,height:a,x:c,y:f}=b(e,t);l(e,a,i,n);const d=u(c,f,i,a,t);e.attr("viewBox",d),g.debug(`viewBox configured: ${d} with padding: ${t}`)},"setupViewPortForSVG"),b=s((e,t)=>{var n;const o=((n=e.node())==null?void 0:n.getBBox())||{width:0,height:0,x:0,y:0};return{width:o.width+t*2,height:o.height+t*2,x:o.x,y:o.y}},"calculateDimensionsWithPadding"),u=s((e,t,o,n,i)=>`${e-i} ${t-i} ${o} ${n}`,"createViewBox");export{w as g,y as s};
//# sourceMappingURL=chunk-RZ5BOZE2-MW50_vUu.js.map
