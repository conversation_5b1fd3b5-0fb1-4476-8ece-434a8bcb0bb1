import{B as a,g as n,A as i,a as c}from"./user-config-BaX6AisS.js";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},t=new e.Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="c5b6193f-3bf7-430e-b0b0-dc3917115bca",e._sentryDebugIdIdentifier="sentry-dbid-c5b6193f-3bf7-430e-b0b0-dc3917115bca")}catch{}})();globalThis.jotaiAtomCache=globalThis.jotaiAtomCache||{cache:new Map,get(e,t){return this.cache.has(e)?this.cache.get(e):(this.cache.set(e,t),t)}};chrome.storage.session.setAccessLevel({accessLevel:"TRUSTED_AND_UNTRUSTED_CONTEXTS"});async function s(){const{startupPage:e}=await n(),t=e===i||!(e in c)?"/":`/chat/${e}`;await a.tabs.create({url:`app.html#${t}`})}a.action.onClicked.addListener(async()=>{await s()});a.commands.onCommand.addListener(async e=>{e==="open-app"&&await s()});a.runtime.onInstalled.addListener(async e=>{e.reason==="install"&&await Promise.allSettled([a.tabs.create({url:"https://chatgpt.com/",active:!1}),a.tabs.create({url:"app.html#/setting?install=true"})])});a.runtime.setUninstallURL("https://chathub.gg/uninstall");
//# sourceMappingURL=index.ts-KexYS2RX.js.map
