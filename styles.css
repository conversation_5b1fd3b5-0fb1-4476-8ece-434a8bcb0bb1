:root {
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f5;
  --text-primary: #333333;
  --border-color: #ddd;
  --button-bg: #007acc;
  --button-text: #ffffff;
}

[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --text-primary: #ffffff;
  --border-color: #444;
  --button-bg: #0066cc;
  --button-text: #ffffff;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
}

#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 10px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.controls button {
  background: var(--button-bg);
  color: var(--button-text);
  border: none;
  padding: 8px 12px;
  margin-left: 5px;
  border-radius: 4px;
  cursor: pointer;
}

.settings-panel {
  background: var(--bg-secondary);
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
}

.settings-panel.hidden {
  display: none;
}

.setting-group {
  margin-bottom: 15px;
}

.checkbox-group label {
  display: block;
  margin: 5px 0;
}

.chat-container {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1px;
  background: var(--border-color);
}

.chat-container.single {
  grid-template-columns: 1fr;
}

.chat-container.triple {
  grid-template-columns: 1fr 1fr 1fr;
}

.chat-container.quad {
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

.chat-frame {
  border: none;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
}

.tab-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tab-container.hidden {
  display: none;
}

.tab-buttons {
  display: flex;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.tab-button {
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-primary);
}

.tab-button.active {
  background: var(--button-bg);
  color: var(--button-text);
}

.tab-content {
  flex: 1;
  position: relative;
}

.tab-pane {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
}

.tab-pane.active {
  display: block;
}