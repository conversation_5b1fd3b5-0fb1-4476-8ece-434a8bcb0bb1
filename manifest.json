{"update_url": "https://clients2.google.com/service/update2/crx", "manifest_version": 3, "name": "__MSG_appName__", "description": "__MSG_appDesc__", "default_locale": "en", "version": "3.96.0", "icons": {"16": "src/assets/icon.png", "32": "src/assets/icon.png", "48": "src/assets/icon.png", "128": "src/assets/icon.png"}, "background": {"service_worker": "service-worker-loader.js", "type": "module"}, "action": {}, "host_permissions": ["<all_urls>"], "permissions": ["storage", "unlimitedStorage", "sidePanel", "declarativeNetRequestWithHostAccess"], "commands": {"open-app": {"suggested_key": {"default": "Alt+J", "windows": "Alt+J", "linux": "Alt+J", "mac": "Command+J"}, "description": "Open ChatHub app"}}, "side_panel": {"default_path": "sidepanel.html"}, "declarative_net_request": {"rule_resources": [{"id": "ruleset_x_frame_options", "enabled": true, "path": "src/rules/x-frame-options.json"}, {"id": "ruleset_chatgpt", "enabled": true, "path": "src/rules/chatgpt.json"}, {"id": "ruleset_bing", "enabled": true, "path": "src/rules/bing.json"}, {"id": "ruleset_ddg", "enabled": true, "path": "src/rules/ddg.json"}, {"id": "ruleset_qianwen", "enabled": true, "path": "src/rules/qianwen.json"}, {"id": "ruleset_ollama", "enabled": true, "path": "src/rules/ollama.json"}, {"id": "ruleset_pplx", "enabled": true, "path": "src/rules/pplx.json"}, {"id": "ruleset_anthropic", "enabled": true, "path": "src/rules/anthropic.json"}]}}