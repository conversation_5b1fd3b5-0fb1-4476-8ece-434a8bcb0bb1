var arkoseLabsClientApi2e161da7;!function(){var e={7983:function(e,t){"use strict";t.N=void 0;var n=/^([^\w]*)(javascript|data|vbscript)/im,r=/&#(\w+)(^\w|;)?/g,o=/&tab;/gi,i=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim,a=/^.+(:|&colon;)/gim,c=[".","/"];t.N=function(e){var t,s=(t=e||"",(t=t.replace(o,"&#9;")).replace(r,(function(e,t){return String.fromCharCode(t)}))).replace(i,"").trim();if(!s)return"about:blank";if(function(e){return c.indexOf(e[0])>-1}(s))return s;var u=s.match(a);if(!u)return s;var l=u[0];return n.test(l)?"about:blank":s}},3940:function(e,t){var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)){if(n.length){var a=o.apply(null,n);a&&e.push(a)}}else if("object"===i)if(n.toString===Object.prototype.toString)for(var c in n)r.call(n,c)&&n[c]&&e.push(c);else e.push(n.toString())}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},8645:function(e){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",r=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,r,o,i){"string"==typeof e&&(e=[[null,e,void 0]]);var a={};if(r)for(var c=0;c<this.length;c++){var s=this[c][0];null!=s&&(a[s]=!0)}for(var u=0;u<e.length;u++){var l=[].concat(e[u]);r&&a[l[0]]||(void 0!==i&&(void 0===l[5]||(l[1]="@layer".concat(l[5].length>0?" ".concat(l[5]):""," {").concat(l[1],"}")),l[5]=i),n&&(l[2]?(l[1]="@media ".concat(l[2]," {").concat(l[1],"}"),l[2]=n):l[2]=n),o&&(l[4]?(l[1]="@supports (".concat(l[4],") {").concat(l[1],"}"),l[4]=o):l[4]="".concat(o)),t.push(l))}},t}},3835:function(e){"use strict";e.exports=function(e){return e[1]}},913:function(e,t,n){var r,o,i;!function(a,c){"use strict";o=[n(4486)],void 0===(i="function"==typeof(r=function(e){var t=/(^|@)\S+:\d+/,n=/^\s*at .*(\S+:\d+|\(native\))/m,r=/^(eval@)?(\[native code])?$/;return{parse:function(e){if(void 0!==e.stacktrace||void 0!==e["opera#sourceloc"])return this.parseOpera(e);if(e.stack&&e.stack.match(n))return this.parseV8OrIE(e);if(e.stack)return this.parseFFOrSafari(e);throw new Error("Cannot parse given Error object")},extractLocation:function(e){if(-1===e.indexOf(":"))return[e];var t=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(e.replace(/[()]/g,""));return[t[1],t[2]||void 0,t[3]||void 0]},parseV8OrIE:function(t){return t.stack.split("\n").filter((function(e){return!!e.match(n)}),this).map((function(t){t.indexOf("(eval ")>-1&&(t=t.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(,.*$)/g,""));var n=t.replace(/^\s+/,"").replace(/\(eval code/g,"(").replace(/^.*?\s+/,""),r=n.match(/ (\(.+\)$)/);n=r?n.replace(r[0],""):n;var o=this.extractLocation(r?r[1]:n),i=r&&n||void 0,a=["eval","<anonymous>"].indexOf(o[0])>-1?void 0:o[0];return new e({functionName:i,fileName:a,lineNumber:o[1],columnNumber:o[2],source:t})}),this)},parseFFOrSafari:function(t){return t.stack.split("\n").filter((function(e){return!e.match(r)}),this).map((function(t){if(t.indexOf(" > eval")>-1&&(t=t.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),-1===t.indexOf("@")&&-1===t.indexOf(":"))return new e({functionName:t});var n=/((.*".+"[^@]*)?[^@]*)(?:@)/,r=t.match(n),o=r&&r[1]?r[1]:void 0,i=this.extractLocation(t.replace(n,""));return new e({functionName:o,fileName:i[0],lineNumber:i[1],columnNumber:i[2],source:t})}),this)},parseOpera:function(e){return!e.stacktrace||e.message.indexOf("\n")>-1&&e.message.split("\n").length>e.stacktrace.split("\n").length?this.parseOpera9(e):e.stack?this.parseOpera11(e):this.parseOpera10(e)},parseOpera9:function(t){for(var n=/Line (\d+).*script (?:in )?(\S+)/i,r=t.message.split("\n"),o=[],i=2,a=r.length;i<a;i+=2){var c=n.exec(r[i]);c&&o.push(new e({fileName:c[2],lineNumber:c[1],source:r[i]}))}return o},parseOpera10:function(t){for(var n=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,r=t.stacktrace.split("\n"),o=[],i=0,a=r.length;i<a;i+=2){var c=n.exec(r[i]);c&&o.push(new e({functionName:c[3]||void 0,fileName:c[2],lineNumber:c[1],source:r[i]}))}return o},parseOpera11:function(n){return n.stack.split("\n").filter((function(e){return!!e.match(t)&&!e.match(/^Error created at/)}),this).map((function(t){var n,r=t.split("@"),o=this.extractLocation(r.pop()),i=r.shift()||"",a=i.replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;i.match(/\(([^)]*)\)/)&&(n=i.replace(/^[^(]+\(([^)]*)\)$/,"$1"));var c=void 0===n||"[arguments not available]"===n?void 0:n.split(",");return new e({functionName:a,args:c,fileName:o[0],lineNumber:o[1],columnNumber:o[2],source:t})}),this)}}})?r.apply(t,o):r)||(e.exports=i)}()},2265:function(e){"use strict";var t=Object.prototype.hasOwnProperty,n="~";function r(){}function o(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function i(e,t,r,i,a){if("function"!=typeof r)throw new TypeError("The listener must be a function");var c=new o(r,i||e,a),s=n?n+t:t;return e._events[s]?e._events[s].fn?e._events[s]=[e._events[s],c]:e._events[s].push(c):(e._events[s]=c,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function c(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),c.prototype.eventNames=function(){var e,r,o=[];if(0===this._eventsCount)return o;for(r in e=this._events)t.call(e,r)&&o.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},c.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var o=0,i=r.length,a=new Array(i);o<i;o++)a[o]=r[o].fn;return a},c.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},c.prototype.emit=function(e,t,r,o,i,a){var c=n?n+e:e;if(!this._events[c])return!1;var s,u,l=this._events[c],f=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),f){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,r),!0;case 4:return l.fn.call(l.context,t,r,o),!0;case 5:return l.fn.call(l.context,t,r,o,i),!0;case 6:return l.fn.call(l.context,t,r,o,i,a),!0}for(u=1,s=new Array(f-1);u<f;u++)s[u-1]=arguments[u];l.fn.apply(l.context,s)}else{var p,d=l.length;for(u=0;u<d;u++)switch(l[u].once&&this.removeListener(e,l[u].fn,void 0,!0),f){case 1:l[u].fn.call(l[u].context);break;case 2:l[u].fn.call(l[u].context,t);break;case 3:l[u].fn.call(l[u].context,t,r);break;case 4:l[u].fn.call(l[u].context,t,r,o);break;default:if(!s)for(p=1,s=new Array(f-1);p<f;p++)s[p-1]=arguments[p];l[u].fn.apply(l[u].context,s)}}return!0},c.prototype.on=function(e,t,n){return i(this,e,t,n,!1)},c.prototype.once=function(e,t,n){return i(this,e,t,n,!0)},c.prototype.removeListener=function(e,t,r,o){var i=n?n+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==t||o&&!c.once||r&&c.context!==r||a(this,i);else{for(var s=0,u=[],l=c.length;s<l;s++)(c[s].fn!==t||o&&!c[s].once||r&&c[s].context!==r)&&u.push(c[s]);u.length?this._events[i]=1===u.length?u[0]:u:a(this,i)}return this},c.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&a(this,t)):(this._events=new r,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=n,c.EventEmitter=c,e.exports=c},1640:function(e,t,n){e=n.nmd(e);var r="__lodash_hash_undefined__",o=9007199254740991,i="[object Arguments]",a="[object Boolean]",c="[object Date]",s="[object Function]",u="[object GeneratorFunction]",l="[object Map]",f="[object Number]",p="[object Object]",d="[object Promise]",v="[object RegExp]",h="[object Set]",m="[object String]",y="[object Symbol]",g="[object WeakMap]",b="[object ArrayBuffer]",w="[object DataView]",O="[object Float32Array]",x="[object Float64Array]",E="[object Int8Array]",j="[object Int16Array]",S="[object Int32Array]",_="[object Uint8Array]",k="[object Uint8ClampedArray]",A="[object Uint16Array]",I="[object Uint32Array]",T=/\w*$/,P=/^\[object .+?Constructor\]$/,L=/^(?:0|[1-9]\d*)$/,C={};C[i]=C["[object Array]"]=C[b]=C[w]=C[a]=C[c]=C[O]=C[x]=C[E]=C[j]=C[S]=C[l]=C[f]=C[p]=C[v]=C[h]=C[m]=C[y]=C[_]=C[k]=C[A]=C[I]=!0,C["[object Error]"]=C[s]=C[g]=!1;var R="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,M="object"==typeof self&&self&&self.Object===Object&&self,N=R||M||Function("return this")(),D=t&&!t.nodeType&&t,F=D&&e&&!e.nodeType&&e,K=F&&F.exports===D;function q(e,t){return e.set(t[0],t[1]),e}function H(e,t){return e.add(t),e}function W(e,t,n,r){var o=-1,i=e?e.length:0;for(r&&i&&(n=e[++o]);++o<i;)n=t(n,e[o],o,e);return n}function U(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}function z(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}function B(e,t){return function(n){return e(t(n))}}function G(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}var $,V=Array.prototype,X=Function.prototype,Y=Object.prototype,Z=N["__core-js_shared__"],J=($=/[^.]+$/.exec(Z&&Z.keys&&Z.keys.IE_PROTO||""))?"Symbol(src)_1."+$:"",Q=X.toString,ee=Y.hasOwnProperty,te=Y.toString,ne=RegExp("^"+Q.call(ee).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),re=K?N.Buffer:void 0,oe=N.Symbol,ie=N.Uint8Array,ae=B(Object.getPrototypeOf,Object),ce=Object.create,se=Y.propertyIsEnumerable,ue=V.splice,le=Object.getOwnPropertySymbols,fe=re?re.isBuffer:void 0,pe=B(Object.keys,Object),de=Fe(N,"DataView"),ve=Fe(N,"Map"),he=Fe(N,"Promise"),me=Fe(N,"Set"),ye=Fe(N,"WeakMap"),ge=Fe(Object,"create"),be=Ue(de),we=Ue(ve),Oe=Ue(he),xe=Ue(me),Ee=Ue(ye),je=oe?oe.prototype:void 0,Se=je?je.valueOf:void 0;function _e(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function ke(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Ae(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Ie(e){this.__data__=new ke(e)}function Te(e,t){var n=Be(e)||function(e){return function(e){return function(e){return!!e&&"object"==typeof e}(e)&&Ge(e)}(e)&&ee.call(e,"callee")&&(!se.call(e,"callee")||te.call(e)==i)}(e)?function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}(e.length,String):[],r=n.length,o=!!r;for(var a in e)!t&&!ee.call(e,a)||o&&("length"==a||He(a,r))||n.push(a);return n}function Pe(e,t,n){var r=e[t];ee.call(e,t)&&ze(r,n)&&(void 0!==n||t in e)||(e[t]=n)}function Le(e,t){for(var n=e.length;n--;)if(ze(e[n][0],t))return n;return-1}function Ce(e,t,n,r,o,d,g){var P;if(r&&(P=d?r(e,o,d,g):r(e)),void 0!==P)return P;if(!Xe(e))return e;var L=Be(e);if(L){if(P=function(e){var t=e.length,n=e.constructor(t);t&&"string"==typeof e[0]&&ee.call(e,"index")&&(n.index=e.index,n.input=e.input);return n}(e),!t)return function(e,t){var n=-1,r=e.length;t||(t=Array(r));for(;++n<r;)t[n]=e[n];return t}(e,P)}else{var R=qe(e),M=R==s||R==u;if($e(e))return function(e,t){if(t)return e.slice();var n=new e.constructor(e.length);return e.copy(n),n}(e,t);if(R==p||R==i||M&&!d){if(U(e))return d?e:{};if(P=function(e){return"function"!=typeof e.constructor||We(e)?{}:(t=ae(e),Xe(t)?ce(t):{});var t}(M?{}:e),!t)return function(e,t){return Ne(e,Ke(e),t)}(e,function(e,t){return e&&Ne(t,Ye(t),e)}(P,e))}else{if(!C[R])return d?e:{};P=function(e,t,n,r){var o=e.constructor;switch(t){case b:return Me(e);case a:case c:return new o(+e);case w:return function(e,t){var n=t?Me(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}(e,r);case O:case x:case E:case j:case S:case _:case k:case A:case I:return function(e,t){var n=t?Me(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}(e,r);case l:return function(e,t,n){var r=t?n(z(e),!0):z(e);return W(r,q,new e.constructor)}(e,r,n);case f:case m:return new o(e);case v:return function(e){var t=new e.constructor(e.source,T.exec(e));return t.lastIndex=e.lastIndex,t}(e);case h:return function(e,t,n){var r=t?n(G(e),!0):G(e);return W(r,H,new e.constructor)}(e,r,n);case y:return i=e,Se?Object(Se.call(i)):{}}var i}(e,R,Ce,t)}}g||(g=new Ie);var N=g.get(e);if(N)return N;if(g.set(e,P),!L)var D=n?function(e){return function(e,t,n){var r=t(e);return Be(e)?r:function(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}(r,n(e))}(e,Ye,Ke)}(e):Ye(e);return function(e,t){for(var n=-1,r=e?e.length:0;++n<r&&!1!==t(e[n],n,e););}(D||e,(function(o,i){D&&(o=e[i=o]),Pe(P,i,Ce(o,t,n,r,i,e,g))})),P}function Re(e){return!(!Xe(e)||(t=e,J&&J in t))&&(Ve(e)||U(e)?ne:P).test(Ue(e));var t}function Me(e){var t=new e.constructor(e.byteLength);return new ie(t).set(new ie(e)),t}function Ne(e,t,n,r){n||(n={});for(var o=-1,i=t.length;++o<i;){var a=t[o],c=r?r(n[a],e[a],a,n,e):void 0;Pe(n,a,void 0===c?e[a]:c)}return n}function De(e,t){var n,r,o=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?o["string"==typeof t?"string":"hash"]:o.map}function Fe(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return Re(n)?n:void 0}_e.prototype.clear=function(){this.__data__=ge?ge(null):{}},_e.prototype.delete=function(e){return this.has(e)&&delete this.__data__[e]},_e.prototype.get=function(e){var t=this.__data__;if(ge){var n=t[e];return n===r?void 0:n}return ee.call(t,e)?t[e]:void 0},_e.prototype.has=function(e){var t=this.__data__;return ge?void 0!==t[e]:ee.call(t,e)},_e.prototype.set=function(e,t){return this.__data__[e]=ge&&void 0===t?r:t,this},ke.prototype.clear=function(){this.__data__=[]},ke.prototype.delete=function(e){var t=this.__data__,n=Le(t,e);return!(n<0)&&(n==t.length-1?t.pop():ue.call(t,n,1),!0)},ke.prototype.get=function(e){var t=this.__data__,n=Le(t,e);return n<0?void 0:t[n][1]},ke.prototype.has=function(e){return Le(this.__data__,e)>-1},ke.prototype.set=function(e,t){var n=this.__data__,r=Le(n,e);return r<0?n.push([e,t]):n[r][1]=t,this},Ae.prototype.clear=function(){this.__data__={hash:new _e,map:new(ve||ke),string:new _e}},Ae.prototype.delete=function(e){return De(this,e).delete(e)},Ae.prototype.get=function(e){return De(this,e).get(e)},Ae.prototype.has=function(e){return De(this,e).has(e)},Ae.prototype.set=function(e,t){return De(this,e).set(e,t),this},Ie.prototype.clear=function(){this.__data__=new ke},Ie.prototype.delete=function(e){return this.__data__.delete(e)},Ie.prototype.get=function(e){return this.__data__.get(e)},Ie.prototype.has=function(e){return this.__data__.has(e)},Ie.prototype.set=function(e,t){var n=this.__data__;if(n instanceof ke){var r=n.__data__;if(!ve||r.length<199)return r.push([e,t]),this;n=this.__data__=new Ae(r)}return n.set(e,t),this};var Ke=le?B(le,Object):function(){return[]},qe=function(e){return te.call(e)};function He(e,t){return!!(t=null==t?o:t)&&("number"==typeof e||L.test(e))&&e>-1&&e%1==0&&e<t}function We(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Y)}function Ue(e){if(null!=e){try{return Q.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function ze(e,t){return e===t||e!=e&&t!=t}(de&&qe(new de(new ArrayBuffer(1)))!=w||ve&&qe(new ve)!=l||he&&qe(he.resolve())!=d||me&&qe(new me)!=h||ye&&qe(new ye)!=g)&&(qe=function(e){var t=te.call(e),n=t==p?e.constructor:void 0,r=n?Ue(n):void 0;if(r)switch(r){case be:return w;case we:return l;case Oe:return d;case xe:return h;case Ee:return g}return t});var Be=Array.isArray;function Ge(e){return null!=e&&function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=o}(e.length)&&!Ve(e)}var $e=fe||function(){return!1};function Ve(e){var t=Xe(e)?te.call(e):"";return t==s||t==u}function Xe(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function Ye(e){return Ge(e)?Te(e):function(e){if(!We(e))return pe(e);var t=[];for(var n in Object(e))ee.call(e,n)&&"constructor"!=n&&t.push(n);return t}(e)}e.exports=function(e){return Ce(e,!0,!0)}},4486:function(e,t){var n,r,o;!function(i,a){"use strict";r=[],void 0===(o="function"==typeof(n=function(){function e(e){return!isNaN(parseFloat(e))&&isFinite(e)}function t(e){return e.charAt(0).toUpperCase()+e.substring(1)}function n(e){return function(){return this[e]}}var r=["isConstructor","isEval","isNative","isToplevel"],o=["columnNumber","lineNumber"],i=["fileName","functionName","source"],a=["args"],c=["evalOrigin"],s=r.concat(o,i,a,c);function u(e){if(e)for(var n=0;n<s.length;n++)void 0!==e[s[n]]&&this["set"+t(s[n])](e[s[n]])}u.prototype={getArgs:function(){return this.args},setArgs:function(e){if("[object Array]"!==Object.prototype.toString.call(e))throw new TypeError("Args must be an Array");this.args=e},getEvalOrigin:function(){return this.evalOrigin},setEvalOrigin:function(e){if(e instanceof u)this.evalOrigin=e;else{if(!(e instanceof Object))throw new TypeError("Eval Origin must be an Object or StackFrame");this.evalOrigin=new u(e)}},toString:function(){var e=this.getFileName()||"",t=this.getLineNumber()||"",n=this.getColumnNumber()||"",r=this.getFunctionName()||"";return this.getIsEval()?e?"[eval] ("+e+":"+t+":"+n+")":"[eval]:"+t+":"+n:r?r+" ("+e+":"+t+":"+n+")":e+":"+t+":"+n}},u.fromString=function(e){var t=e.indexOf("("),n=e.lastIndexOf(")"),r=e.substring(0,t),o=e.substring(t+1,n).split(","),i=e.substring(n+1);if(0===i.indexOf("@"))var a=/@(.+?)(?::(\d+))?(?::(\d+))?$/.exec(i,""),c=a[1],s=a[2],l=a[3];return new u({functionName:r,args:o||void 0,fileName:c,lineNumber:s||void 0,columnNumber:l||void 0})};for(var l=0;l<r.length;l++)u.prototype["get"+t(r[l])]=n(r[l]),u.prototype["set"+t(r[l])]=function(e){return function(t){this[e]=Boolean(t)}}(r[l]);for(var f=0;f<o.length;f++)u.prototype["get"+t(o[f])]=n(o[f]),u.prototype["set"+t(o[f])]=function(t){return function(n){if(!e(n))throw new TypeError(t+" must be a Number");this[t]=Number(n)}}(o[f]);for(var p=0;p<i.length;p++)u.prototype["get"+t(i[p])]=n(i[p]),u.prototype["set"+t(i[p])]=function(e){return function(t){this[e]=String(t)}}(i[p]);return u})?n.apply(t,r):n)||(e.exports=o)}()},2476:function(){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.closest=function(e){var t=this;do{if(Element.prototype.matches.call(t,e))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return null})},903:function(e,t,n){"use strict";var r=n(3835),o=n.n(r),i=n(8645),a=n.n(i)()(o());a.push([e.id,".r34K7X1zGgAi6DllVF3T{box-sizing:border-box;border:0;margin:0;padding:0;overflow:hidden;z-index:2147483647;pointer-events:none;visibility:hidden;opacity:0;transition:opacity 300ms linear;height:0;width:0;max-height:0;overflow:hidden;display:block}.r34K7X1zGgAi6DllVF3T.active{display:block;visibility:visible;max-height:none;overflow:visible}.r34K7X1zGgAi6DllVF3T.active.show{opacity:1;pointer-events:inherit;position:inherit}.r34K7X1zGgAi6DllVF3T.active.show.in-situ{width:inherit;height:inherit}.r34K7X1zGgAi6DllVF3T.active.show.lightbox{position:fixed;width:100% !important;height:100% !important;top:0;right:0;bottom:0;left:0}@-moz-document url-prefix(''){.r34K7X1zGgAi6DllVF3T{visibility:visible;display:block}}\n",""]),a.locals={container:"r34K7X1zGgAi6DllVF3T"},t.Z=a},3379:function(e){"use strict";var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var i={},a=[],c=0;c<e.length;c++){var s=e[c],u=r.base?s[0]+r.base:s[0],l=i[u]||0,f="".concat(u," ").concat(l);i[u]=l+1;var p=n(f),d={css:s[1],media:s[2],sourceMap:s[3],supports:s[4],layer:s[5]};if(-1!==p)t[p].references++,t[p].updater(d);else{var v=o(d,r);r.byIndex=c,t.splice(c,0,{identifier:f,updater:v,references:1})}a.push(f)}return a}function o(e,t){var n=t.domAPI(t);n.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,o){var i=r(e=e||[],o=o||{});return function(e){e=e||[];for(var a=0;a<i.length;a++){var c=n(i[a]);t[c].references--}for(var s=r(e,o),u=0;u<i.length;u++){var l=n(i[u]);0===t[l].references&&(t[l].updater(),t.splice(l,1))}i=s}}},569:function(e){"use strict";var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},9216:function(e){"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},3565:function(e,t,n){"use strict";e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},7795:function(e){"use strict";e.exports=function(e){var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var o=void 0!==n.layer;o&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,o&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var i=n.sourceMap;i&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},4589:function(e){"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},7395:function(e,t,n){var r=n(2880).default;function o(){"use strict";e.exports=o=function(){return t},e.exports.__esModule=!0,e.exports.default=e.exports;var t={},n=Object.prototype,i=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},c="function"==typeof Symbol?Symbol:{},s=c.iterator||"@@iterator",u=c.asyncIterator||"@@asyncIterator",l=c.toStringTag||"@@toStringTag";function f(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(e){f=function(e,t,n){return e[t]=n}}function p(e,t,n,r){var o=t&&t.prototype instanceof h?t:h,i=Object.create(o.prototype),c=new A(r||[]);return a(i,"_invoke",{value:j(e,n,c)}),i}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var v={};function h(){}function m(){}function y(){}var g={};f(g,s,(function(){return this}));var b=Object.getPrototypeOf,w=b&&b(b(I([])));w&&w!==n&&i.call(w,s)&&(g=w);var O=y.prototype=h.prototype=Object.create(g);function x(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function n(o,a,c,s){var u=d(e[o],e,a);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==r(f)&&i.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,c,s)}),(function(e){n("throw",e,c,s)})):t.resolve(f).then((function(e){l.value=e,c(l)}),(function(e){return n("throw",e,c,s)}))}s(u.arg)}var o;a(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,o){n(e,r,t,o)}))}return o=o?o.then(i,i):i()}})}function j(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return T()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var c=S(a,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=d(e,t,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}function S(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=d(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,v;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,v):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,v)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function I(e){if(e){var t=e[s];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,r=function t(){for(;++n<e.length;)if(i.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:T}}function T(){return{value:void 0,done:!0}}return m.prototype=y,a(O,"constructor",{value:y,configurable:!0}),a(y,"constructor",{value:m,configurable:!0}),m.displayName=f(y,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,f(e,l,"GeneratorFunction")),e.prototype=Object.create(O),e},t.awrap=function(e){return{__await:e}},x(E.prototype),f(E.prototype,u,(function(){return this})),t.AsyncIterator=E,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new E(p(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},x(O),f(O,l,"Generator"),f(O,s,(function(){return this})),f(O,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=I,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],a=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var c=i.call(o,"catchLoc"),s=i.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),k(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;k(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:I(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),v}},t}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports},2880:function(e){function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},5809:function(e,t,n){var r=n(7395)();e.exports=r;try{regeneratorRuntime=r}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={id:r,loaded:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.loaded=!0,i.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e},n.nc=void 0;var r={};!function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t){var n=function(t,n){if("object"!==e(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,n||"default");if("object"!==e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"===e(n)?n:String(n)}function o(e,n){for(var r=0;r<n.length;r++){var o=n[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,t(o.key),o)}}function i(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,n,r){return(n=t(n))in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}n.r(r);var s=n(1640),u=n.n(s),l=(n(2476),"arkose"),f="production",p="2.9.0",d="inline",v="Verification challenge",h=("data-".concat(l,"-challenge-api-url"),"data-".concat(l,"-event-blocked")),m="data-".concat(l,"-event-completed"),y="data-".concat(l,"-event-hide"),g="data-".concat(l,"-event-ready"),b="data-".concat(l,"-event-ready-inline"),w="data-".concat(l,"-event-reset"),O="data-".concat(l,"-event-show"),x="data-".concat(l,"-event-suppress"),E="data-".concat(l,"-event-shown"),j="data-".concat(l,"-event-error"),S="data-".concat(l,"-event-warning"),_="data-".concat(l,"-event-resize"),k="data-".concat(l,"-event-data-request"),A="enforcement resize",I="enforcement loaded",T="challenge shown",P="config",L="data_response",C="settings loaded",R={API:"api",ENFORCEMENT:"enforcement"},M="CAPI_RELOAD_EC",N="observability timer",D="data collected",F="update_frame_attributes",K="BB_RX",q="BB_TX",H="js_ready",W="default",U="ark",z="API_REQUEST_SOURCE_VALIDATION",B="onAPILoad",G="onReady",$="onShown",V="onComplete",X="apiExecute",Y="enforcementLoad";function Z(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var J=n(913),Q=n.n(J),ee=["o","g","c",null,"t","a",3,"b","g","t","o","h","b","a",3,9,"ye"],te=window,ne=me;!function(e,t){for(var n=461,r=490,o=444,i=484,a=469,c=485,s=432,u=453,l=446,f=me,p=e();;)try{if(435602===parseInt(f(n))/1+parseInt(f(r))/2+-parseInt(f(o))/3+parseInt(f(i))/4+parseInt(f(a))/5*(parseInt(f(c))/6)+-parseInt(f(s))/7+-parseInt(f(u))/8*(parseInt(f(l))/9))break;p.push(p.shift())}catch(e){p.push(p.shift())}}(ye);var re=function(){var e=451,t=!0;return function(n,r){var o=t?function(){if(r){var t=r[me(e)](n,arguments);return r=null,t}}:function(){};return t=!1,o}}(),oe=re(void 0,(function(){var e=430,t=489,n=456,r=429,o=435,i=480,a=456,c=429,s=me;return oe[s(e)+"ng"]()[s(t)](s(n)+s(r))[s(e)+"ng"]()[s(o)+s(i)](oe)[s(t)](s(a)+s(c))}));oe();var ie,ae=ne(481),ce=ne(499)+ne(482)+ne(476),se=ne(471)+ne(449)+ne(443)+ne(479),ue=ne(474)+ne(442)+ne(493)+ne(496),le=ne(464)+"2t",fe=ne(465)+ne(501),pe=ne(426)+ne(455),de=function(e){var t=439,n=ne;return 4===(e[n(462)](/-/g)||[])[n(t)]},ve=[function(e){var t,n,r,o,i=452,a=434,c=439,s=497,u=452,l=ne,f=["YJbNbFc5b0Xr3lGiyj2=",ce,ue,le,se],p=e[l(486)]("."),d=p[l(i)+"f"](ae),v=p[l(a)](d-1,p[l(c)])[l(s)](".");return f[l(u)+"f"]((t=v,n=te["".concat(ee[7]).concat(ee[4]).concat(ee[0]).concat(ee[5])](t).match(/[\s\S]{1,2}/g),r="",o="",n.forEach((function(e){var t=e.split(""),n=t.pop();r+=t.join(""),o+=n})),"".concat(r).concat(o)))>-1}][ne(436)]((ie=f===ne(488)+ne(478)?[function(e){return[fe,pe][ne(452)+"f"](e)>-1}]:[],function(e){if(Array.isArray(e))return Z(e)}(ie)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(ie)||function(e,t){if(e){if("string"==typeof e)return Z(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Z(e,t):void 0}}(ie)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())),he=function(e){var t,n,r,o,i,a,c,s=486,u=439,l=486,f=447,p=502,d=467,v=ne,h=!0,m=e;try{var y=e[v(s)]("/"),g=y[y[v(u)]-1][v(l)](":")[0][v(f)+v(p)]();h=ve[v(d)]((function(e){return e(g)}))}catch(e){h=!1}return!h&&(n=(t="aRcML9blbQYBLFa9ZxYMY9H0H6yjGlntXpmy2zWhnu2t").length,r=Math.ceil(n/2),o=[t.substring(0,r),t.substring(r,n)],i=o[0].split(""),a=o[1].split(""),c="",i.forEach((function(e,t){c+=e,a[t]&&(c+=a[t])})),m=te["".concat(ee[13]).concat(ee[9]).concat(ee[10]).concat(ee[12])](c)||""),m};function me(e,t){var n=ye();return me=function(e,t){return n[e-=426]},me(e,t)}function ye(){var e=["120cKLWnS","filter","al.com","(((.+)","/v2/","Empty ","key","labs.c","512215TuKsmF","match"," URL","dYY92u","localh","ing","some","Invali","5RrApYK","extHos","YJbNbF","Key","hash","bFcVcl","trim","mvQ=","api","pment","yj2uX=","uctor","com","b=3lmp","nment","2835696HwCxRC","2547636ZWnBMP","split","host","develo","search","93540icYNMA","EMENT","public","WjXhml","funcap","tcha.c","mvShQ=","join","charAt","bBbFLN","src","ost","rCase","locati","bs-loc","enviro","toUppe","+)+)+$","toStri","ENFORC","1226848uIQqTD","substr","slice","constr","concat","URL","file","length","exec","nt-API","LNb5d=","Xr3lGi","1444362crHxJj","\\//","360117XOsqse","toLowe","arkose","c5b0YU","d Clie","apply","indexO"];return(ye=function(){return e})()}var ge=function(){var e=439,t=477,n=500,r=431,o=491,i=503,a=473,c=498,s=433,u=466,l=486,f=459,p=459,d=ne,v=arguments[d(e)]>0&&void 0!==arguments[0]?arguments[0]:d(t),h=function(e){if(document.currentScript)return document.currentScript;var t="enforcement"===e?'script[id="enforcementScript"]':'script[src*="v2"][src*="api.js"][data-callback]',n=document.querySelectorAll(t);if(n&&1===n.length)return n[0];try{throw new Error}catch(e){try{var r=Q().parse(e)[0].fileName;return document.querySelector('script[src="'.concat(r,'"]'))}catch(e){return null}}}(v);if(!h)return null;var m=h[d(n)],y={};try{y=function(e){var t=437,n=447,r=502,o=486,i=457,a=454,c=439,s=468,u=450,l=441,f=463,p=486,d=428,v=502,h=487,m=459,y=470,g=ne;if(!e)throw new Error(g(458)+g(t));var b=e[g(n)+g(r)]()[g(o)](g(i))[g(a)]((function(e){return""!==e}));if(b[g(c)]<2)throw new Error(g(s)+g(u)+g(l)+g(f));var w=he(b[0]),O=b[1][g(p)]("/")[g(a)]((function(e){return""!==e})),x=de(O[0])?O[0][g(d)+g(v)]():null,E={};return E[g(h)]=w,E[g(m)]=x,E[g(y)+"t"]=w,E}(m)}catch(e){}if(v===R[d(r)+d(o)]){var g=window[d(i)+"on"][d(a)];if(g[d(e)]>0){var b=("#"===g[d(c)](0)?g[d(s)+d(u)](1):g)[d(l)]("&"),w=b[0];y[d(f)]=de(w)?w:y[d(p)],y.id=b[1]}}return y}(),be=function(e,t){for(var n,r=0;r<e.length;r+=1){var o=e[r],i=String(o.getAttribute("src"));if((i.match(t)||i.match("v2/api.js"))&&o.hasAttribute("data-callback")){n=o;break}}return n}(document.querySelectorAll("script"),ge.key||null);if(be){var we=be.nonce,Oe=be.getAttribute?be.getAttribute("data-nonce"):null,xe=we||Oe;xe&&(n.nc=xe)}var Ee=function(e){return"function"==typeof e},je=function(e,t,n){try{var r=t.split("."),o=e;return r.forEach((function(e){o=o[e]})),o||n}catch(e){return n}},Se=function(t){var n=t,r=e(t);return("string"!==r||"string"===r&&-1===t.indexOf("px")&&-1===t.indexOf("vw")&&-1===t.indexOf("vh"))&&(n="".concat(t,"px")),n},_e=function(e,t){if(e[U])e[U][t]||(e[U][t]={});else{var n=t?c({},t,{}):{};Object.defineProperty(e,U,{value:n,writable:!0})}},ke=function(e,t,n,r){e[U]&&e[U][t]||_e(e,t),e[U][t][n]=r};function Ae(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var Ie=n(2265),Te=n.n(Ie),Pe=n(7983);function Le(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Le(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Le(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Re=["settings","styling","token"],Me=function t(n){return"object"===e(n)&&null!==n?Object.keys(n).reduce((function(r,o){var i,a=n[o],s=e(a),u=a;return-1===Re.indexOf(o)&&("string"===s&&(u=""===(i=a)?i:(0,Pe.N)(i)),"object"===s&&(u=Array.isArray(a)?a:t(a))),Ce(Ce({},r),{},c({},o,u))}),{}):n};function Ne(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function De(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ne(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ne(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Fe=function(){function e(){var t=this;a(this,e),this.config={context:null,target:"*",identifier:null,iframePosition:null},this.emitter=new(Te()),this.messageListener=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{var n=function(e){return JSON.parse(e)}(e.data),r=n||{},o=r.data,i=r.key,a=r.message,c=r.type,s=Me(o);if(a&&i===t.config.identifier)return t.emitter.emit(a,s),"broadcast"===c&&t.postMessageToParent({data:s,key:i,message:a}),void("emit"===c&&t.postMessageToChildren({data:s,key:i,message:a}));n&&"FunCaptcha-action"===n.msg&&t.postMessageToChildren({data:De(De({},n),n.payload||{})})}catch(n){if(e.data===H)return void t.emitter.emit(H,{});if(e.data===M)return void t.emitter.emit(M,{});if(e.data.msg===F)return void t.emitter.emit(F,{});"string"==typeof e.data&&-1!==e.data.indexOf("key_pressed_")&&t.config.iframePosition===R.ENFORCEMENT&&window.parent&&"function"==typeof window.parent.postMessage&&window.parent.postMessage(e.data,"*")}}}return i(e,[{key:"context",set:function(e){this.config.context=e}},{key:"identifier",set:function(e){this.config.identifier=e}},{key:"setup",value:function(e,t){var n,r,o;this.config.identifier!==this.identifier&&(n=window,r=this.config.identifier,(o=n[U])&&o[r]&&(o[r].listener&&window.removeEventListener("message",o[r].listener),o[r].error&&window.removeEventListener("error",o[r].error),delete o[r])),this.config.identifier=e,this.config.iframePosition=t,_e(window,this.config.identifier);var i=window[U][this.config.identifier].listener;i&&window.removeEventListener("message",i),ke(window,this.config.identifier,"listener",this.messageListener),window.addEventListener("message",window[U][this.config.identifier].listener)}},{key:"postMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=t.data,r=t.key,o=t.message,i=t.type;if(Ee(e.postMessage)){var a=De(De({},n),{},{data:n,key:r,message:o,type:i});e.postMessage(function(e){return JSON.stringify(e)}(a),this.config.target)}}},{key:"postMessageToChildren",value:function(e){for(var t=e.data,n=e.key,r=e.message,o=document.querySelectorAll("iframe"),i=[],a=0;a<o.length;a+=1){var c=o[a].contentWindow;c&&i.push(c)}for(var s=0;s<i.length;s+=1){var u=i[s];this.postMessage(u,{data:t,key:n,message:r,type:"emit"},this.config.target)}}},{key:"postMessageToParent",value:function(e){var t=e.data,n=e.key,r=e.message;window.parent!==window&&this.postMessage(window.parent,{data:t,key:n,message:r,type:"broadcast"})}},{key:"emit",value:function(e,t){this.emitter.emit(e,t),this.postMessageToParent({message:e,data:t,key:this.config.identifier}),this.postMessageToChildren({message:e,data:t,key:this.config.identifier})}},{key:"off",value:function(){var e;(e=this.emitter).removeListener.apply(e,arguments)}},{key:"on",value:function(){var e;(e=this.emitter).on.apply(e,arguments)}},{key:"once",value:function(){var e;(e=this.emitter).once.apply(e,arguments)}}]),e}(),Ke=new Fe,qe=["logged"];function He(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function We(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?He(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):He(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ue="sampled",ze="error",Be={enabled:{type:"boolean",default:!1},windowErrorEnabled:{type:"boolean",default:!0},samplePercentage:{type:"float",default:1}},Ge=function(e,t,n,r){Ke.emit(N,{action:e,timerId:t,subTimerId:n||null,time:Date.now(),info:r})};document.addEventListener("securitypolicyviolation",(function(e){try{if("connect-src"!==e.effectiveDirective)return;var t={blockedURI:e.blockedURI,documentURI:e.documentURI,effectiveDirective:e.effectiveDirective,originalPolicy:e.originalPolicy,violatedDirective:e.violatedDirective};Ke.emit("observability error",t)}catch(e){}}));var $e=n(3940),Ve=n.n($e),Xe=lt;!function(e,t){for(var n=216,r=232,o=207,i=208,a=225,c=204,s=237,u=218,l=249,f=236,p=235,d=244,v=lt,h=e();;)try{if(145274===parseInt(v(n))/1*(-parseInt(v(r))/2)+-parseInt(v(o))/3+-parseInt(v(i))/4*(parseInt(v(a))/5)+-parseInt(v(c))/6*(-parseInt(v(s))/7)+-parseInt(v(u))/8*(parseInt(v(l))/9)+parseInt(v(f))/10+parseInt(v(p))/11*(parseInt(v(d))/12))break;h.push(h.shift())}catch(e){h.push(h.shift())}}(ot);var Ye,Ze=(Ye=!0,function(e,t){var n=255,r=Ye?function(){if(t){var r=t[lt(n)](e,arguments);return t=null,r}}:function(){};return Ye=!1,r}),Je=Ze(void 0,(function(){var e=203,t=209,n=253,r=226,o=250,i=254,a=209,c=253,s=226,u=lt;return Je[u(e)+"ng"]()[u(t)](u(n)+u(r))[u(e)+"ng"]()[u(o)+u(i)](Je)[u(a)](u(c)+u(s))}));Je();var Qe=[Xe(206)+"ox",Xe(230)+Xe(238)],et={};et[Xe(214)+"t"]=!0;var tt={};tt[Xe(214)+"t"]=!1;var nt={};nt[Xe(251)+Xe(205)]=et,nt[Xe(242)+Xe(210)+Xe(252)]=tt;var rt={};function ot(){var e=["defaul","ngeCom","1QviFen","length","1256xgevqo","protot","challe","abilit","imeout","report","enable","125jxVoWK","+)+)+$","pleteT","featur","set","ECResp","apeOff","296668Ksxfvl","forEac","option","4674417uWZXyR","2884640ICKXBE","133dNMAcp","onsive","theme","landsc","hasOwn","hideCl","keys","12Wtkwoh","ype","MaxDim","observ","call","14706FqhIvO","constr","closeO","ton","(((.+)","uctor","apply","settin","toStri","46356tisdNz","nEsc","lightb","202377DcmYfG","38816QksoIl","search","oseBut","ension","Proper","eFlags"];return(ot=function(){return e})()}rt[Xe(214)+"t"]=!0;var it={};it[Xe(214)+"t"]=70;var at={};at[Xe(224)+"d"]=rt,at[Xe(240)+Xe(231)+Xe(229)]=it;var ct={};ct[Xe(214)+"t"]={};var st={};st[Xe(234)+"al"]=!0;var ut={};function lt(e,t){var n=ot();return lt=function(e,t){return n[e-=203]},lt(e,t)}ut[Xe(214)+"t"]={};var ft={};ft[Xe(214)+"t"]=2e3;var pt={};pt[Xe(214)+"t"]=!1,pt[Xe(234)+"al"]=!0;var dt={};dt[Xe(206)+"ox"]=nt,dt[Xe(230)+Xe(238)]=at,dt[Xe(247)+Xe(221)+"y"]=ct,dt.f=st,dt[Xe(228)+Xe(213)]=ut,dt[Xe(220)+Xe(215)+Xe(227)+Xe(222)]=ft,dt[Xe(223)+Xe(246)+Xe(211)+"s"]=pt;var vt=dt,ht=function(){var e=239,t=256,n=206,r=230,o=238,i=220,a=215,c=227,s=222,u=223,l=246,f=211,p=238,d=220,v=222,h=233,m=230,y=243,g=233,b=219,w=245,O=241,x=212,E=248,j=234,S=214,_=243,k=233,A=219,I=245,T=241,P=212,L=248,C=214,R=Xe,M=arguments[R(217)]>0&&void 0!==arguments[0]?arguments[0]:{},N=M[R(e)],D=void 0===N?null:N,F=M[R(t)+"gs"]||M,K={};K[R(n)+"ox"]={},K[R(r)+R(o)]={},K[R(i)+R(a)+R(c)+R(s)]={},K[R(u)+R(l)+R(f)+"s"]=!1;var q=K;[R(n)+"ox",R(r)+R(p),R(d)+R(a)+R(c)+R(v)][R(h)+"h"]((function(e){var t=R,n=F[e]||{},r=vt[e];Object[t(_)](r)[t(k)+"h"]((function(o){var i=t;Object[i(A)+i(I)][i(T)+i(P)+"ty"][i(L)](n,o)?q[e][o]=n[o]:q[e][o]=r[o][i(C)+"t"]}))})),D&&(q[R(e)]=D);vt[R(n)+"ox"],vt[R(m)+R(p)];var H=Ae(vt,Qe);return Object[R(y)](H)[R(g)+"h"]((function(e){var t=R;Object[t(b)+t(w)][t(O)+t(x)+"ty"][t(E)](F,e)?q[e]=F[e]:!0!==vt[e][t(j)+"al"]&&(q[e]=vt[e][t(S)+"t"])})),q},mt=n(3379),yt=n.n(mt),gt=n(7795),bt=n.n(gt),wt=n(569),Ot=n.n(wt),xt=n(3565),Et=n.n(xt),jt=n(9216),St=n.n(jt),_t=n(4589),kt=n.n(_t),At=n(903),It={};It.styleTagTransform=kt(),It.setAttributes=Et(),It.insert=Ot().bind(null,"head"),It.domAPI=bt(),It.insertStyleElement=St();yt()(At.Z,It);var Tt=At.Z&&At.Z.locals?At.Z.locals:void 0;function Pt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var Lt={show:!1,isActive:void 0,element:void 0,frame:void 0,mode:void 0,ECResponsive:!0,enforcementUrl:null},Ct=function(e,t){e.setAttribute("class",t)},Rt=function(){return Ve()(Tt.container,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Pt(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({show:!!Lt.show,active:!!Lt.isActive},Lt.mode?c({},Lt.mode,!0):{}))};Ke.on("challenge iframe",(function(e){var t=e.width,n=e.height,r=e.minWidth,o=e.minHeight,i=e.maxWidth,a=e.maxHeight;if(Lt.frame){Lt.show=!0;var c=Rt();Ct(Lt.frame,c);var s=n,u=t;if(Lt.ECResponsive){var l=function(e){var t=e.width,n=e.height,r=e.minWidth,o=e.maxWidth,i=e.minHeight,a=e.maxHeight,c=e.landscapeOffset,s=t,u=n;if(!r||!o)return{height:u,width:s};if(window.screen&&window.screen.width&&window.screen.height){var l=window.screen.availHeight||window.screen.height,f=window.screen.availWidth||window.screen.width,p=l-(!window.orientation||90!==window.orientation&&-90!==window.orientation?0:c);s=f,u=i&&a?p:n,f>=parseInt(o,10)&&(s=o),f<=parseInt(r,10)&&(s=r),a&&p>=parseInt(a,10)&&(u=a),i&&p<=parseInt(i,10)&&(u=i)}return s=Se(s),{height:u=Se(u),width:s}}({width:t,height:n,minWidth:r,maxWidth:i,minHeight:o,maxHeight:a,landscapeOffset:Lt.ECResponsive.landscapeOffset||0});u=l.width,s=l.height}var f=!1;if(t&&t!==Lt.frame.style.width&&(Lt.frame.style.width=t,f=!0),n&&n!==Lt.frame.style.height&&(Lt.frame.style.height=n,f=!0),Lt.mode===d&&(r&&r!==Lt.frame.style["min-width"]&&(Lt.frame.style["min-width"]=r,f=!0),o&&o!==Lt.frame.style["min-height"]&&(Lt.frame.style["min-height"]=o,f=!0),i&&i!==Lt.frame.style["max-width"]&&(Lt.frame.style["max-width"]=i,f=!0),a&&a!==Lt.frame.style["max-height"]&&(Lt.frame.style["max-height"]=a,f=!0)),f){var p={width:u,height:s};Lt.reportMaxDimensions&&(p.maxWidth=i,p.maxHeight=a),Ke.emit(A,p)}document.activeElement!==Lt.element&&!1===Lt.mode&&Lt.frame.focus()}}));var Mt=function(e){var t=e.host,n=e.id,r=e.publicKey,o=e.element,i=e.config,a=e.isActive,c=e.isReady,s=e.capiObserver,u=je(i,"mode");Lt.mode=u,Lt.element=o,Lt.isActive=a,Lt.show=c,Lt.ECResponsive=je(ht(i.settings),"ECResponsive",{}),Lt.accessibilitySettings=je(i,"accessibilitySettings"),Lt.reportMaxDimensions=je(i.settings,"reportMaxDimensions");var l=Rt(),p=function(e){var t=492,n=472,r=438,o=427,i=483,a=488,c=478,s=436,u=457,l=436,f=436,p=ne,d=e[p(487)],v=e[p(t)+p(n)],h=e.id,m=e[p(r)];return e[p(o)+p(i)]===p(a)+p(c)?""[p(s)](m,"#")[p(s)](v||"","&")[p(s)](h):""[p(s)](d,p(u))[p(l)](m,"#")[p(s)](v||"","&")[p(f)](h)}({host:'https://tcr9i.chat.openai.com',publicKey:r,id:n,file:"2.9.0/enforcement.b3b1c9343f2ef3887d61d74272d6a3af.html",environment:f});if(je(Lt.element,"children",[]).length<1){Lt.enforcementUrl=p;var d=document.createElement("iframe");d.setAttribute("src",p),d.setAttribute("class",l),d.setAttribute("title",v),d.setAttribute("aria-label",v),d.setAttribute("data-e2e","enforcement-frame"),d.style.width="0px",d.style.height="0px",d.addEventListener("load",(function(){s.subTimerEnd(G,Y)})),s.subTimerStart(G,Y),Lt.element.appendChild(d),Lt.frame=d}else p!==Lt.enforcementUrl&&(Lt.frame.setAttribute("src",p),Lt.enforcementUrl=p),Ct(Lt.frame,l),Lt.isActive||(Lt.frame.style.width=0,Lt.frame.style.height=0)},Nt={boolean:function(e){return"boolean"==typeof e?e:"string"==typeof e&&"true"===e.toLowerCase()}},Dt=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={},n=["publicKey","data","isSDK","language","mode","onDataRequest","onCompleted","onHide","onReady","onReset","onResize","onShow","onShown","onSuppress","onError","onWarning","onFailed","onResize","settings","selector","accessibilitySettings","styleTheme","uaTheme","apiLoadTime","enableDirectionalInput","inlineRunOnTrigger","noSuppress"];return Object.keys(e).filter((function(e){return-1!==n.indexOf(e)})).forEach((function(n){t[n]=e[n]})),[{value:"noSuppress",type:"boolean"}].forEach((function(n){var r=n.value,o=n.type;Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=Nt[o](e[r]))})),t};function Ft(){var e=["map","apply","+)+)+$","split","9784ySWkaB","13030WYGBAb","uctor","constr","99AbpSaB","4137924rUHCuD","942cfsuzO","39654IgBGOG","1090IFUkke","3240916EfZDap","8mrACoh","number","toStri","917624NMCZSI","string","join","(((.+)","475702BqYYvI","values","55mUORGv","search"];return(Ft=function(){return e})()}!function(e,t){for(var n=252,r=248,o=264,i=260,a=261,c=241,s=244,u=245,l=242,f=243,p=254,d=240,v=Ht,h=e();;)try{if(282495===-parseInt(v(n))/1+-parseInt(v(r))/2+-parseInt(v(o))/3*(parseInt(v(i))/4)+parseInt(v(a))/5*(-parseInt(v(c))/6)+parseInt(v(s))/7*(parseInt(v(u))/8)+parseInt(v(l))/9*(-parseInt(v(f))/10)+-parseInt(v(p))/11*(-parseInt(v(d))/12))break;h.push(h.shift())}catch(e){h.push(h.shift())}}(Ft);var Kt=function(){var e=257,t=!0;return function(n,r){var o=t?function(){if(r){var t=r[Ht(e)](n,arguments);return r=null,t}}:function(){};return t=!1,o}}(),qt=Kt(void 0,(function(){var e=247,t=255,n=251,r=258,o=263,i=262,a=Ht;return qt[a(e)+"ng"]()[a(t)](a(n)+a(r))[a(e)+"ng"]()[a(o)+a(i)](qt)[a(t)](a(n)+a(r))}));qt();function Ht(e,t){var n=Ft();return Ht=function(e,t){return n[e-=240]},Ht(e,t)}!function(e,t){for(var n=473,r=492,o=482,i=479,a=496,c=495,s=484,u=472,l=474,f=$t,p=e();;)try{if(719630===parseInt(f(n))/1*(-parseInt(f(r))/2)+parseInt(f(o))/3+parseInt(f(i))/4+-parseInt(f(a))/5+-parseInt(f(c))/6+parseInt(f(s))/7*(-parseInt(f(u))/8)+parseInt(f(l))/9)break;p.push(p.shift())}catch(e){p.push(p.shift())}}(Vt);var Wt=function(){var e=!0;return function(t,n){var r=478,o=e?function(){if(n){var e=n[$t(r)](t,arguments);return n=null,e}}:function(){};return e=!1,o}}(),Ut=Wt(void 0,(function(){var e=483,t=485,n=475,r=490,o=486,i=480,a=475,c=490,s=$t;return Ut[s(e)+"ng"]()[s(t)](s(n)+s(r))[s(e)+"ng"]()[s(o)+s(i)](Ut)[s(t)](s(a)+s(c))}));Ut();var zt=function(){var e=489,t=487,n=$t;return window[n(487)+"on"][n(e)]?function(e){var t=259,n=Ht;return e||typeof e===n(249)?e[n(t)]("?")[0]:null}(window[n(t)+"on"][n(e)]):null},Bt=function(e){return typeof e==$t(497)+"n"?e:null},Gt=function(){var e=494,t=$t;return!!window[t(491)+t(e)]};function $t(e,t){var n=Vt();return $t=function(e,t){return n[e-=472]},$t(e,t)}function Vt(){var e=["13KEfanJ","18206010uQTegg","(((.+)","isSDK","langua","apply","4056528EiUYAh","uctor","inline","2284548BxkvNe","toStri","233219jnlLWf","search","constr","locati","RunOnT","href","+)+)+$","__nigh","70450EKqIhI","rigger","tmare","1686174LMvIWg","5702710Xmyrkj","boolea","288QWlOGY"];return(Vt=function(){return e})()}function Xt(e,t,n,r,o,i,a){try{var c=e[i](a),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(r,o)}var Yt=n(5809),Zt=n.n(Yt),Jt=en;!function(e,t){for(var n=110,r=178,o=139,i=182,a=136,c=157,s=188,u=144,l=132,f=en,p=e();;)try{if(794336===-parseInt(f(n))/1+parseInt(f(r))/2*(-parseInt(f(o))/3)+-parseInt(f(i))/4+-parseInt(f(a))/5+parseInt(f(c))/6*(-parseInt(f(s))/7)+parseInt(f(u))/8+parseInt(f(l))/9)break;p.push(p.shift())}catch(e){p.push(p.shift())}}(ln);var Qt={"4ca87df3d1":[],"867e25e5d4":[],d4a306884c:[],timestamp:Date[Jt(112)]()};function en(e,t){var n=ln();return en=function(e,t){return n[e-=108]},en(e,t)}var tn={};tn[Jt(147)+Jt(152)]="";var nn={};nn[Jt(120)+Jt(117)]="";var rn={};rn[Jt(118)+Jt(164)]="";var on,an=[tn,nn,rn],cn=(function(){var e=137,t=138,n=142,r=130,o=123,i=159,a=168,c=161,s=130,u=159,l=Jt,f=function(){var e=137,t=!0;return function(n,r){var o=t?function(){if(r){var t=r[en(e)](n,arguments);return r=null,t}}:function(){};return t=!1,o}}(),p=f(this,(function(){var e=en;return p[e(n)+"ng"]()[e(r)](e(o)+e(i))[e(n)+"ng"]()[e(a)+e(c)](p)[e(s)](e(o)+e(u))}));p();var d,v=(d=Zt()[l(186)]((function e(n){var r=135,o=141,i=125,a=170,c=149,s=156,u=162,f=l;return Zt()[f(t)]((function(e){for(var t=f;;)switch(e[t(r)]=e[t(o)]){case 0:return Ke[t(i)](K),e[t(a)](t(c),new Promise((function(e){Ke.on(q,(function(t){t&&e(t)})),setTimeout((function(){e(an)}),n)})));case 2:case t(s):return e[t(u)]()}}),e)})),function(){var e=this,t=arguments;return new Promise((function(n,r){var o=d.apply(e,t);function i(e){Xt(o,n,r,i,a,"next",e)}function a(e){Xt(o,n,r,i,a,"throw",e)}i(void 0)}))})}(),function(e){var t=147,n=152,r=116,o=185,i=165,a=165,c=167,s=167,u=147,l=152,f=183,p=112,d=176,v=126,h=167;return function(m){var y=112,g=176,b=126,w=165,O=167,x=147,E=152,j=183,S=en,_=function(){var t=en,n={timestamp:Date[t(y)]()-Qt[t(g)+t(b)],type:e,x:m[t(w)],y:m[t(O)]};Qt[t(x)+t(E)][t(j)](n),on=n};if(!(Qt[S(t)+S(n)][S(r)]>=75)){if(0===e)return on?void(Math[S(o)]((m[S(i)]-on.x)*(m[S(a)]-on.x)+(m[S(c)]-on.y)*(m[S(s)]-on.y))>5&&_()):void _();Qt[S(u)+S(l)][S(f)]({timestamp:Date[S(p)]()-Qt[S(d)+S(v)],type:e,x:m[S(i)],y:m[S(h)]})}}}),sn=function(e){var t=154,n=116,r=120,o=117,i=116,a=183,c=112,s=176,u=126,l=108,f=165,p=108,d=167;return function(v){for(var h=en,m=0;m<v[h(t)+"s"][h(n)];m+=1)Qt[h(r)+h(o)][h(i)]<75&&Qt[h(r)+h(o)][h(a)]({timestamp:Date[h(c)]()-Qt[h(s)+h(u)],type:e,x:Math[h(l)](v[h(t)+"s"][m][h(f)]),y:Math[h(p)](v[h(t)+"s"][m][h(d)])})}},un=function(e){var t=174,n=172,r=129,o=187,i=114,a=175,c=184,s=127,u=148,l=163,f=177,p=124,d=140,v=121,h=113,m=169,y=145,g=181,b=118,w=164,O=116,x=164,E=183,j=112,S=176,_=126,k=150;return function(A){var I=en,T={};T[I(t)]=0,T[I(n)]=1,T[I(r)]=3,T[I(o)+I(i)]=4,T[I(a)+I(c)]=5,T[I(s)+I(u)]=6,T[I(s)+I(l)]=7,T[I(f)+"ft"]=8,T[I(p)+I(d)]=9,T[I(v)+"t"]=10,T[I(h)+"ht"]=11,T[I(m)+I(y)]=12,T[I(g)]=13;var P,L=T;Qt[I(b)+I(w)][I(O)]<75&&Qt[I(b)+I(x)][I(E)]({timestamp:Date[I(j)]()-Qt[I(S)+I(_)],type:e,code:null!==(P=L[A[I(k)]])&&void 0!==P?P:14})}};function ln(){var e=["234HSFHaP","ght","next","toStri","ntList","1764304FZMUUJ","ace","keys","4ca87d","lLeft","return","code","ener","f3d1","mousem","touche","keydow","end","114fPVotu","concat","+)+)+$","btoa","uctor","stop","lRight","884c","pageX","ancel","pageY","constr","Backsp","abrupt","passiv","Enter","forEac","Tab","ShiftR","timest","MetaLe","230Smfdaa","moused","own","Escape","1341160swZMZV","push","ight","sqrt","mark","ShiftL","555030eARSdu","floor","touchc","520483KwTCGs","filter","now","AltRig","eft","touchs","length","e5d4","d4a306","keyup","867e25","AltLef","ove","(((.+)","MetaRi","emit","amp","Contro","addEve","Space","search","mouseu","35116164sjaIUH","tart","touchm","prev","4783725fMVMIz","apply","wrap"];return(ln=function(){return e})()}var fn;function pn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function dn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?pn(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}Ke.on(K,(function(){var e=111,t=173,n=125,r=160,o=158,i=183,a=176,c=126,s=Jt,u=[];return Qt?Object[s(146)](Qt)[s(e)]((function(e){var t=s;return e!==t(a)+t(c)}))[s(t)+"h"]((function(e){var t=s,n={},a=function(e){var t=250,n=253,r=250,o=Ht;return e[o(256)]((function(e){var t=o;return Object[t(n)](e)[t(r)](",")}))[o(t)](";")}(Qt[e]);n[e]=window[t(r)](""[t(o)](a,";")),u[t(i)](n)})):u=an,Ke[s(n)](q,u),u})),function(){var e=128,t=143,n=151,r=153,o=122,i=151,a=179,c=180,s=143,u=131,l=171,f=143,p=151,d=115,v=133,h=128,m=143,y=154,g=171,b=143,w=134,O=109,x=166,E=128,j=143,S=155,_=151,k=119,A=Jt;document[A(e)+A(t)+A(n)](A(r)+A(o),cn(0)),document[A(e)+A(t)+A(i)](A(a)+A(c),cn(1)),document[A(e)+A(s)+A(n)](A(u)+"p",cn(2));var I={};I[A(l)+"e"]=!1,document[A(e)+A(f)+A(p)](A(d)+A(v),sn(0),I);var T={};T[A(l)+"e"]=!1,document[A(h)+A(m)+A(i)](A(y)+"nd",sn(1),T);var P={};P[A(g)+"e"]=!1,document[A(h)+A(b)+A(n)](A(w)+A(o),sn(2),P);var L={};L[A(g)+"e"]=!1,document[A(h)+A(b)+A(i)](A(O)+A(x),sn(99),L),document[A(E)+A(j)+A(p)](A(S)+"n",un(0)),document[A(e)+A(s)+A(_)](A(k),un(1))}();var vn=ge.key,hn=ge.host,mn=ge.extHost,yn=window&&window.crypto&&"function"==typeof window.crypto.getRandomValues?([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(function(e){return(e^crypto.getRandomValues(new Uint8Array(1))[0]&15>>e/4).toString(16)})):"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})),gn=function(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:5e3,i=n,a=r,s=function(){var e={},t=window.navigator;if(e.platform=t.platform,e.language=t.language,t.connection)try{e.connection={effectiveType:t.connection.effectiveType,rtt:t.connection.rtt,downlink:t.connection.downlink}}catch(e){}return e}(),u={},l={},f=t,p={},d=null,v=null,h={timerCheckInterval:o},m=!1,y=!1,g=!1,b=!1,w=function(){var e=function(){var e=window.location;return{origin:e.origin,pathname:e.pathname}},t=e(),n=t.origin,r=t.pathname;return window.addEventListener("popstate",(function(){var t=e();n=t.origin,r=t.pathname})),function(){return{origin:n,pathname:r}}}(),O=function(){var e;if(g){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];"string"==typeof n[0]&&(n[0]="Observability - ".concat(n[0])),(e=console).log.apply(e,n)}},x=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.timerId,r=t.type;if(!0===h.enabled){var o,m=n?c({},n,u[n]):u,y=Object.keys(m).reduce((function(e,t){m[t].logged=!0;var n=m[t],r=(n.logged,Ae(n,qe));return We(We({},e),{},c({},t,r))}),{}),g=w(),x=g.origin,E=g.pathname;"onReady"===n&&(o=function(){try{if(!window.performance||!window.performance.getEntries)return{error:"Not supported."};for(var e,t,n=window.performance.getEntries(),r=0;r<n.length;r+=1)"navigation"===n[r].entryType?e=n[r]:n[r].name.indexOf("api.js")>-1&&(t=n[r]);return{DOM:{totalTime:Math.round(e.duration),dnsLoadTime:Math.round(e.domainLookupEnd-e.domainLookupStart),tlsLoadTime:Math.round(e.connectEnd-e.connectStart),timeToStartRequest:Math.round(e.requestStart-e.connectEnd),requestTime:Math.round(e.responseStart-e.requestStart),responseTime:Math.round(e.responseEnd-e.responseStart),domLoadTime:Math.round(e.domContentLoadedEventEnd-e.responseEnd),domCompleteTime:Math.round(e.domComplete-e.domContentLoadedEventEnd),httpProtocol:e.nextHopProtocol,deliveryType:e.deliveryType,requestCached:0===e.transferSize},apiJS:{totalTime:Math.round(t.duration),dnsLoadTime:Math.round(t.domainLookupEnd-t.domainLookupStart),tlsLoadTime:Math.round(t.connectEnd-t.connectStart),timeToStartRequest:Math.round(t.requestStart-t.connectEnd),requestTime:Math.round(t.responseStart-t.requestStart),responseTime:Math.round(t.responseEnd-t.responseStart),httpProtocol:t.nextHopProtocol,encodedBodySize:t.encodedBodySize,decodedBodySize:t.decodedBodySize,requestCached:0===t.transferSize}}}catch(e){return{error:e.message}}}());var j={id:e,publicKey:f,capiVersion:a,mode:v,suppressed:b,device:s,error:p,windowError:l,sessionId:d,performance:o,locationOrigin:x,locationPathname:E,timers:y,sampled:r===Ue};O("Logging Metrics:",j);try{var S=new XMLHttpRequest;S.open("POST",i),S.send(JSON.stringify(j))}catch(e){}}},E=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return We(We({},{start:null,end:null,diff:null,logged:!1,metrics:{}}),e)},j=function(){return{id:e,publicKey:f,sessionId:d,mode:v,settings:h,device:s,error:p,windowError:l,timers:u,debugEnabled:g}};try{"true"===window.localStorage.getItem("capiDebug")&&(g=!0,window.capiObserver={getValues:j})}catch(e){}return{getValues:j,timerStart:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now(),n=u[e]||{};n.start||(O("".concat(e," started:"),t),u[e]=E(We(We({},n),{},{start:t})))},timerEnd:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now(),n=u[e];n&&!n.end&&(n.end=t,n.diff=n.end-n.start,O("".concat(e," ended:"),t,n.diff),y&&x({timerId:e,type:Ue}))},subTimerStart:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Date.now(),r=arguments.length>3?arguments[3]:void 0,o=u[e];o||(o=E()),o.end||(o.metrics[t]=We({start:n,end:null,diff:null},r&&{info:r}),u[e]=o,O("".concat(e,".").concat(t," started:"),n))},subTimerEnd:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Date.now(),r=u[e];if(r&&!r.end){var o=r.metrics[t];o&&(o.end=n,o.diff=o.end-o.start,O("".concat(e,".").concat(t," ended:"),n,o.diff))}},setup:function(e,t){h=We(We({},h),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(Be).reduce((function(t,n){var r=e[n],o=Be[n];if("boolean"===o.type)return We(We({},t),{},c({},n,"boolean"==typeof r?r:o.default));var i="float"===o.type?parseFloat(r,0):parseInt(r,10);return We(We({},t),{},c({},n,isNaN(i)?o.default:i))}),{})}(e)),v=t;var n,r=h.samplePercentage;n=r,y=Math.random()<=n/100,O("Session sampled:",y)},setSession:function(e){d=e},logError:function(e){m||(p=e,m=!0,x({type:ze}))},logWindowError:function(e,t,n,r){h&&!0!==h.windowErrorEnabled||(l[e]={message:t,filename:n,stack:r})},debugLog:O,setSuppressed:function(){b=!0},setPublicKey:function(e){f=e,m=!1,p={},["onShown","onComplete"].forEach((function(e){u[e]&&(u[e]=E())}))},observabilityTimer:Ge,apiLoadTimerSetup:function(e,t){u[e]=We(We({},t),{},{logged:!1}),y&&x({timerId:e,type:Ue})}}}(yn,vn,"".concat(mn).concat("/metrics/ui"),p,5e3);gn.subTimerStart(G,X);var bn=function(e){return"arkose-".concat(e,"-wrapper")},wn={},On="onCompleted",xn="onHide",En="onReady",jn="onReset",Sn="onShow",_n="onShown",kn="onSuppress",An="onFailed",In="onError",Tn="onWarning",Pn="onResize",Ln="onDataRequest",Cn=(c(c(c(c(c(c(c(c(c(c(fn={},m,On),y,xn),g,En),b,En),w,jn),O,Sn),E,_n),x,kn),h,An),j,In),c(c(c(fn,S,Tn),_,Pn),k,Ln)),Rn=i((function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.completed,r=t.token,o=t.suppressed,i=t.error,c=t.warning,s=t.width,u=t.height,l=t.maxWidth,f=t.maxHeight,p=t.requested;a(this,e),this.completed=!!n,this.token=r||null,this.suppressed=!!o,this.error=i||null,this.warning=c||null,this.width=s||0,this.height=u||0,this.requested=p||null,null!=l&&""!==l&&(this.maxWidth=l),null!=f&&""!==f&&(this.maxHeight=f)})),Mn=function(e){var t=document.createElement("div");return t.setAttribute("aria-hidden",!0),t.setAttribute("class",bn(e||vn)),t},Nn=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return dn(dn({element:Mn(),inactiveElement:null,bodyElement:document.querySelector("body"),savedActiveElement:null,modifiedSiblings:[],challengeLoadedEvents:[],container:null,elements:function(){return document.querySelectorAll(wn.config.selector)},initialSetupCompleted:!1,enforcementLoaded:!1,enforcementReady:!1,getPublicKeyTimeout:null,isActive:!1,isHidden:!1,isReady:!1,isConfigured:!1,suppressed:!1,isResettingChallenge:!1,lastResetTimestamp:0,isCompleteReset:!1,fpData:null,onReadyEventCheck:[],width:0,height:0,token:null,externalRequested:!1},t),{},{config:dn(dn({},vn?{publicKey:vn}:{}),{},{selector:(e=vn,"[data-".concat(l,'-public-key="').concat(e,'"]')),styleTheme:t.config&&t.config.styleTheme||W,siteData:{location:{...window.location,origin:"https://chatgpt.com"}},apiLoadTime:null,settings:{},accessibilitySettings:{lockFocusToModal:!0}},t.config),events:dn({},t.events)})},Dn=function(e){var t=wn.events[Cn[e]];if(Ee(t)){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];t.apply(void 0,r)}},Fn=function(){Mt({host:hn,id:wn.id,publicKey:wn.config.publicKey,element:wn.element,config:wn.config,isActive:wn.isActive,isReady:wn.isReady,capiObserver:gn})},Kn=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=wn,n=t.element,r=t.bodyElement,o=t.container,i=t.events,a=t.lastResetTimestamp,c=t.config;if(c.publicKey){var s=Date.now();if(!(s-a<100)){wn.lastResetTimestamp=s,wn.isActive=!1,wn.completed=!1,wn.token=null,wn.isReady=!1,wn.onReadyEventCheck=[],Fn(),r&&i&&(r.removeEventListener("click",i.bodyClicked),window.removeEventListener("keyup",i.escapePressed),wn.events.bodyClicked=null,wn.events.escapePressed=null);var l=n;wn.inactiveElement=l,wn.element=void 0,wn.element=Mn(c.publicKey),o&&l&&o.contains(l)&&(Ke.emit("enforcement detach"),l.style.display="none",setTimeout((function(){try{o.removeChild(l)}catch(e){}}),5e3)),wn=Nn(u()(wn)),e||Dn(w,new Rn(wn)),Gn()}}},qn=function(e){wn.element.setAttribute("aria-hidden",e)},Hn=function(){wn.enforcementReady&&!wn.isActive&&(Ke.emit("trigger show"),wn.isHidden&&(wn.isHidden=!1,wn.isReady&&Ke.emit(T,{token:wn.token})))},Wn=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).manual;wn.isActive=!1,e&&(wn.isHidden=!0),Dn(y,new Rn(wn)),wn.savedActiveElement&&(wn.savedActiveElement.focus(),wn.savedActiveElement=null),je(wn,"config.mode")!==d&&function(){for(var e=wn.modifiedSiblings,t=0;t<e.length;t+=1){var n=e[t],r=n.elem,o=n.ariaHiddenState;r!==wn.appEl&&(null===o?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",o))}}(),Fn(),qn(!0)},Un=function(e){return{source:e.source,error:e.error}},zn=function(e){e.target.closest(wn.config.selector)&&Hn()},Bn=function(e){return 27!==je(e,"keyCode")?null:Wn({manual:!0})},Gn=function(){return je(wn,"config.mode")===d?(wn.container=document.querySelector(je(wn,"config.selector","")),void(wn.container&&(wn.container.contains(wn.element)||(wn.container.appendChild(wn.element),Fn())))):(wn.container=wn.bodyElement,wn.events.bodyClicked||(wn.events.bodyClicked=zn,wn.bodyElement.addEventListener("click",wn.events.bodyClicked)),wn.events.escapePressed||(wn.events.escapePressed=Bn,window.addEventListener("keyup",wn.events.escapePressed)),void(wn.container&&(wn.container.contains(wn.element)||(wn.container.appendChild(wn.element),Fn()))))},$n=function(){gn.subTimerEnd(G,X),_e(window,yn),Ke.setup(yn,R.API),function(e){if(e){var t=window[U][e].error;t&&window.removeEventListener("error",t)}ke(window,e,"error",(function(e){var t=e.message,n=e.filename,r=e.error;if(n&&"string"==typeof n&&n.indexOf("api.js")>=0&&n.indexOf(wn.config.publicKey)>=0){var o=r.stack;gn.logWindowError("integration",t,n,o)}})),window.addEventListener("error",window[U][e].error)}(yn),wn=Nn({id:yn})},Vn=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};wn.initialSetupCompleted=!0;var n=function(e){return e===d?d:"lightbox"}(t.mode||je(wn,"config.mode")),r=t.styleTheme||W,o=wn.isConfigured&&r!==wn.config.styleTheme;wn.isConfigured=!0;var i=vn||wn.config.publicKey||null,a=!1;t.publicKey&&i!==t.publicKey&&(!function(e){ke(window,wn.id,"publicKey",e),gn.setPublicKey(e),wn.element&&wn.element.getAttribute&&(wn.element.getAttribute("class").match(e)||wn.element.setAttribute("class",bn(e)))}(t.publicKey),i=t.publicKey,wn.config.publicKey&&wn.config.publicKey!==t.publicKey&&(a=!0)),wn.config=dn(dn(dn(dn({},wn.config),t),{mode:n}),{},{styleTheme:r,publicKey:i,language:""!==t.language?t.language||wn.config.language:void 0}),wn.events=dn(dn({},wn.events),{},(c(c(c(c(c(c(c(c(c(c(e={},On,t[On]||wn.events[On]),An,t[An]||wn.events[An]),xn,t[xn]||wn.events[xn]),En,t[En]||wn.events[En]),jn,t[jn]||wn.events[jn]),Sn,t[Sn]||wn.events[Sn]),_n,t[_n]||wn.events[_n]),kn,t[kn]||wn.events[kn]),In,t[In]||wn.events[In]),Tn,t[Tn]||wn.events[Tn]),c(c(e,Pn,t[Pn]||wn.events[Pn]),Ln,t[Ln]||wn.events[Ln]))),wn.config.pageLevel=function(e){var t,n=477,r=476,o=481,i=488,a=493,c=$t;return{chref:"https://chatgpt.com",clang:null!==(t=e[c(n)+"ge"])&&void 0!==t?t:null,surl:null,sdk:Bt(e[c(r)])||!1,nm:Gt(),triggeredInline:e[c(o)+c(i)+c(a)]||!1}}(wn.config),Ke.emit(P,wn.config),o||a?Kn(!0):Gn(),"lightbox"===n&&(wn.element.setAttribute("aria-modal",!0),wn.element.setAttribute("role","dialog"))},Xn=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.event,n=e.observability;if(wn.onReadyEventCheck.push(t),n){var r=n.timerId,o=n.subTimerId,i=n.time;gn.subTimerEnd(r,o,i)}var a=[I,D,C],c=function(e,t){var n,r,o=[],i=e.length,a=t.length;for(n=0;n<i;n+=1)for(r=0;r<a;r+=1)e[n]===t[r]&&o.push(e[n]);return o}(a,wn.onReadyEventCheck);c.length===a.length&&(wn.enforcementReady=!0,wn.onReadyEventCheck=[],wn.isCompleteReset||(gn.timerEnd(G),Dn(g,new Rn(wn))),wn.isCompleteReset=!1)},Yn=function(e){var t=e.token;if(t){wn.token=t;var n=t.split("|"),r=n.length?n[0]:null;gn.setSession(r)}},Zn={setConfig:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};gn.timerStart(G),Vn(Dt(e))},getConfig:function(){return u()(wn.config)},dataResponse:function(e){if(wn.requested){var t={message:L,data:e,key:wn.config.publicKey,type:"emit"};Ke.emit(L,t),wn.requested=null}},reset:function(){Kn()},run:Hn,version:p},Jn=be.getAttribute("data-callback");Ke.on("show enforcement",(function(){wn.isReady||(gn.timerStart($),gn.timerStart(V)),wn.isActive=!0,wn.savedActiveElement=document.activeElement,Dn(O,new Rn(wn)),je(wn,"config.mode")!==d&&function(){var e=wn.bodyElement.children;wn.modifiedSiblings=[];for(var t=0;t<e.length;t+=1){var n=e.item(t),r=n.getAttribute("aria-hidden");n!==wn.appEl&&"true"!==r&&(wn.modifiedSiblings.push({elem:n,ariaHiddenState:r}),n.setAttribute("aria-hidden",!0))}}(),Fn(),qn(!1)})),Ke.on(T,(function(e){var t=e.token;wn.isReady=!0,wn.token=t,wn.isHidden||(wn.isActive=!0,Fn(),gn.timerEnd($),Dn(E,new Rn(wn)))})),Ke.on("challenge completed",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};wn.completed=!0,wn.token=e.token,gn.timerEnd(V),Dn(m,new Rn(wn)),je(wn,"config.mode")!==d&&(wn.isCompleteReset=!0,Kn())})),Ke.on("hide enforcement",Wn),Ke.on(A,(function(e){var t=e.width,n=e.height,r=e.maxWidth,o=e.maxHeight;wn.width=t,wn.height=n,void 0!==r&&(wn.maxWidth=r),void 0!==o&&(wn.maxHeight=o),Dn(_,new Rn(wn))})),Ke.on(I,(function(){wn.enforcementLoaded=!0,Xn({event:I}),wn.initialSetupCompleted&&Ke.emit(P,wn.config)})),Ke.on("challenge suppressed",(function(e){var t=e.token;wn.isActive=!1,wn.suppressed=!0,Yn({token:t}),gn.setSuppressed(),gn.timerEnd($),Dn(x,new Rn(wn))})),Ke.on("data initial",Xn),Ke.on("settings fp collected",Xn),Ke.on("challenge token",Yn),Ke.on("challenge window error",(function(e){var t=e.message,n=e.source,r=e.stack;gn.logWindowError("challenge",t,n,r)})),Ke.on(C,(function(e){var t=e.event,n=void 0===t?{}:t,r=e.settings,o=void 0===r?{}:r,i=e.observability;wn.config.settings=o;var a=function(e){return je(e,"observability",{})}(wn.config.settings);gn.setup(a,wn.config.mode);var c=je(wn,"config.apiLoadTime");c&&gn.apiLoadTimerSetup(B,c),Xn({event:n,observability:i}),Fn()})),Ke.on("challenge fail number limit reached",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};wn.isActive=!1,wn.isHidden=!0,wn.token=e.token,Dn(h,new Rn(wn),e)})),Ke.on("error",(function(e){var t=e.error,n=dn({source:null},t);wn.error=Un(n),t.error!==z&&gn.logError(n),Dn(j,new Rn(wn)),Wn()})),Ke.on("warning",(function(e){var t=dn({source:null},e.warning);wn.warning=Un(t),gn.logError(t),Dn(S,new Rn(wn))})),Ke.on("data_request",(function(e){e.sdk&&(wn.requested=e,Dn(k,new Rn(wn)))})),Ke.on(D,Xn),Ke.on(N,(function(e){var t=e.action,n=e.timerId,r=e.subTimerId,o=e.time,i=e.info,a="".concat(r?"subTimer":"timer").concat("end"===t?"End":"Start"),c=r?[n,r,o,i]:[n,o];gn[a].apply(gn,c)})),Ke.on("force reset",(function(){Kn()})),Ke.on("redraw challenge",(function(){wn.element&&(wn.element.querySelector("iframe").style.display="inline")})),Jn?function e(){if(!Ee(window[Jn]))return setTimeout(e,1e3);var t=document.querySelectorAll(".".concat(bn(vn)));return t&&t.length&&Array.prototype.slice.call(t).forEach((function(e){try{e.parentNode.removeChild(e)}catch(e){}})),$n(),window[Jn](Zn)}():$n()}(),arkoseLabsClientApi2e161da7=r}();