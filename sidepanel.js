class SimpleChatHub {
  constructor() {
    this.chatbots = {
      chatgpt: { name: 'ChatGPT', url: 'https://chatgpt.com', enabled: true },
      gemini: { name: '<PERSON>', url: 'https://gemini.google.com', enabled: true },
      claude: { name: '<PERSON>', url: 'https://claude.ai', enabled: false },
      copilot: { name: 'Copilot', url: 'https://copilot.microsoft.com', enabled: false }
    };
    
    this.currentTheme = 'light';
    this.currentLayout = 'grid';
    this.init();
  }

  async init() {
    await this.loadSettings();
    this.setupEventListeners();
    this.applyTheme();
    this.updateLayout();
  }

  setupEventListeners() {
    document.getElementById('themeBtn').addEventListener('click', () => this.toggleTheme());
    document.getElementById('settingsBtn').addEventListener('click', () => this.toggleSettings());
    document.getElementById('saveSettings').addEventListener('click', () => this.saveSettings());
    document.getElementById('layoutSelect').addEventListener('change', (e) => {
      this.currentLayout = e.target.value;
      this.updateLayout();
    });

    // Chatbot checkboxes
    Object.keys(this.chatbots).forEach(bot => {
      const checkbox = document.getElementById(bot);
      if (checkbox) {
        checkbox.addEventListener('change', (e) => {
          this.chatbots[bot].enabled = e.target.checked;
          this.updateChatbots();
        });
      }
    });
  }

  toggleTheme() {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme();
    this.saveSettings();
  }

  applyTheme() {
    document.body.setAttribute('data-theme', this.currentTheme);
    const themeBtn = document.getElementById('themeBtn');
    themeBtn.textContent = this.currentTheme === 'light' ? '🌙' : '☀️';
  }

  toggleSettings() {
    const panel = document.getElementById('settingsPanel');
    panel.classList.toggle('hidden');
  }

  updateLayout() {
    const gridContainer = document.getElementById('chatContainer');
    const tabContainer = document.getElementById('tabContainer');
    
    if (this.currentLayout === 'grid') {
      gridContainer.classList.remove('hidden');
      tabContainer.classList.add('hidden');
      this.updateGridLayout();
    } else {
      gridContainer.classList.add('hidden');
      tabContainer.classList.remove('hidden');
      this.updateTabLayout();
    }
  }

  updateGridLayout() {
    const container = document.getElementById('chatContainer');
    const enabledBots = Object.entries(this.chatbots).filter(([_, bot]) => bot.enabled);
    
    // Update grid columns based on number of enabled bots
    const columns = Math.min(enabledBots.length, 2);
    container.className = `chat-container ${this.getGridClass(enabledBots.length)}`;
    
    this.updateChatbots();
  }

  updateTabLayout() {
    const tabButtons = document.getElementById('tabButtons');
    const tabContent = document.getElementById('tabContent');
    
    tabButtons.innerHTML = '';
    tabContent.innerHTML = '';
    
    const enabledBots = Object.entries(this.chatbots).filter(([_, bot]) => bot.enabled);
    
    enabledBots.forEach(([key, bot], index) => {
      // Create tab button
      const button = document.createElement('button');
      button.className = `tab-button ${index === 0 ? 'active' : ''}`;
      button.textContent = bot.name;
      button.addEventListener('click', () => this.switchTab(key));
      tabButtons.appendChild(button);
      
      // Create tab pane
      const pane = document.createElement('div');
      pane.className = `tab-pane ${index === 0 ? 'active' : ''}`;
      pane.id = `tab-${key}`;
      
      const iframe = document.createElement('iframe');
      iframe.src = bot.url;
      iframe.className = 'chat-frame';
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      iframe.style.border = 'none';
      
      pane.appendChild(iframe);
      tabContent.appendChild(pane);
    });
  }

  switchTab(activeKey) {
    // Update button states
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // Update pane visibility
    document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
    document.getElementById(`tab-${activeKey}`).classList.add('active');
  }

  getGridClass(count) {
    if (count === 1) return 'single';
    if (count === 3) return 'triple';
    if (count === 4) return 'quad';
    return '';
  }

  updateChatbots() {
    if (this.currentLayout === 'grid') {
      const container = document.getElementById('chatContainer');
      container.innerHTML = '';
      
      Object.entries(this.chatbots).forEach(([key, bot]) => {
        if (bot.enabled) {
          const iframe = document.createElement('iframe');
          iframe.id = `${key}-frame`;
          iframe.src = bot.url;
          iframe.className = 'chat-frame';
          iframe.setAttribute('data-bot', key);
          container.appendChild(iframe);
        }
      });
    } else {
      this.updateTabLayout();
    }
  }

  async saveSettings() {
    const settings = {
      theme: this.currentTheme,
      layout: this.currentLayout,
      chatbots: this.chatbots
    };
    
    await chrome.storage.local.set({ settings });
    this.toggleSettings();
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.local.get(['settings']);
      if (result.settings) {
        this.currentTheme = result.settings.theme || 'light';
        this.currentLayout = result.settings.layout || 'grid';
        this.chatbots = { ...this.chatbots, ...result.settings.chatbots };
        
        // Update UI
        document.getElementById('layoutSelect').value = this.currentLayout;
        Object.keys(this.chatbots).forEach(bot => {
          const checkbox = document.getElementById(bot);
          if (checkbox) {
            checkbox.checked = this.chatbots[bot].enabled;
          }
        });
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }
}

// Initialize the app
new SimpleChatHub();